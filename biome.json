{"$schema": "https://biomejs.dev/schemas/2.0.4/schema.json", "formatter": {"enabled": true, "formatWithErrors": true, "indentStyle": "space", "indentWidth": 2, "lineEnding": "lf", "lineWidth": 120, "attributePosition": "auto", "includes": ["**", "!**/src/assets/**/*"]}, "assist": {"actions": {"source": {"organizeImports": "on"}}}, "linter": {"enabled": true, "rules": {"recommended": false, "a11y": {}, "complexity": {"noBannedTypes": "error", "noExtraBooleanCast": "error", "noUselessCatch": "error", "noUselessTypeConstraint": "error", "noAdjacentSpacesInRegex": "error"}, "correctness": {"noChildrenProp": "error", "noConstAssign": "error", "noConstantCondition": "error", "noEmptyCharacterClassInRegex": "error", "noEmptyPattern": "error", "noGlobalObjectCalls": "error", "noInnerDeclarations": "error", "noInvalidConstructorSuper": "error", "noNonoctalDecimalEscape": "error", "noPrecisionLoss": "error", "noSelfAssign": "error", "noSetterReturn": "error", "noSwitchDeclarations": "error", "noUndeclaredVariables": "error", "noUnreachable": "error", "noUnreachableSuper": "error", "noUnsafeFinally": "error", "noUnsafeOptionalChaining": "error", "noUnusedLabels": "error", "noUnusedVariables": "error", "useIsNan": "error", "useJsxKeyInIterable": "error", "useValidForDirection": "error", "useYield": "error", "noInvalidBuiltinInstantiation": "error", "useValidTypeof": "error"}, "security": {"noDangerouslySetInnerHtmlWithChildren": "error", "noBlankTarget": "error"}, "style": {"noNamespace": "error", "useAsConstAssertion": "error", "useArrayLiterals": "off"}, "suspicious": {"noAsyncPromiseExecutor": "error", "noCatchAssign": "error", "noClassAssign": "error", "noCommentText": "error", "noCompareNegZero": "error", "noControlCharactersInRegex": "error", "noDebugger": "error", "noDuplicateCase": "error", "noDuplicateClassMembers": "error", "noDuplicateJsxProps": "error", "noDuplicateObjectKeys": "error", "noDuplicateParameters": "error", "noEmptyBlockStatements": "warn", "noExplicitAny": "warn", "noExtraNonNullAssertion": "error", "noFallthroughSwitchClause": "error", "noFunctionAssign": "error", "noGlobalAssign": "error", "noImportAssign": "error", "noMisleadingCharacterClass": "error", "noMisleadingInstantiator": "error", "noPrototypeBuiltins": "error", "noRedeclare": "error", "noShadowRestrictedNames": "warn", "noUnsafeDeclarationMerging": "error", "noUnsafeNegation": "error", "useGetterReturn": "error", "noWith": "error"}}, "includes": ["**", "!**/src/assets/**/*"]}, "javascript": {"formatter": {"jsxQuoteStyle": "double", "quoteProperties": "asNeeded", "trailingCommas": "all", "semicolons": "always", "arrowParentheses": "always", "bracketSpacing": true, "bracketSameLine": false, "quoteStyle": "double", "attributePosition": "auto"}}, "overrides": [{"includes": ["**/*.ts", "**/*.tsx", "**/*.mts", "**/*.cts"], "linter": {"rules": {"correctness": {"noConstAssign": "off", "noGlobalObjectCalls": "off", "noInvalidConstructorSuper": "off", "noSetterReturn": "off", "noUndeclaredVariables": "off", "noUnreachable": "off", "noUnreachableSuper": "off", "noInvalidBuiltinInstantiation": "off"}, "style": {"useConst": "error"}, "suspicious": {"noDuplicateClassMembers": "off", "noDuplicateObjectKeys": "off", "noDuplicateParameters": "off", "noFunctionAssign": "off", "noImportAssign": "off", "noRedeclare": "off", "noUnsafeNegation": "off", "useGetterReturn": "off", "noVar": "error"}, "complexity": {"noArguments": "error"}}}}]}