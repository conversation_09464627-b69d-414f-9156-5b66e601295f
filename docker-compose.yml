version: "3.9"

volumes:
  shared_volume:

services:
  effi-hr:
    container_name: ui-effi-hr
    restart: always
    build: .
    expose:
      - 3000
    ports:
      - "3000:3000"
    volumes:
      - shared_volume:/effi-hr/ui-effi-hr/dist/ui-effi-hr:rw
  nginx:
    image: nginx
    ports:
      - "4000:80"
    volumes:
      - ./webserver/nginx.conf:/etc/nginx/nginx.conf:ro
      - shared_volume:/usr/share/nginx/html:ro