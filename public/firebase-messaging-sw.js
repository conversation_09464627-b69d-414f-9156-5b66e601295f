importScripts("https://www.gstatic.com/firebasejs/9.0.0/firebase-app-compat.js");
importScripts("https://www.gstatic.com/firebasejs/9.0.0/firebase-messaging-compat.js");

firebase.initializeApp({
  apiKey: "AIzaSyC6GNlwamXz-Zb4V0ph2b5n_gKOORqSuPE",
  authDomain: "effihr-push-notification.firebaseapp.com",
  projectId: "effihr-push-notification",
  storageBucket: "effihr-push-notification.appspot.com",
  messagingSenderId: "98227897032",
  appId: "1:98227897032:web:2f2f9ad5738a3188ba5bfd",
  measurementId: "G-B9NX0TVJYC",
});

const messaging = firebase.messaging();

const channel = new BroadcastChannel('notifications-channel');

messaging.onBackgroundMessage(function (payload) {
  console.log("[firebase-messaging-sw.js] Received background message ", payload);

  const notificationTitle = payload.notification?.title || "Background Message Title";
  const notificationOptions = {
    body: payload.notification?.body || "Background Message body.",
    icon: "/firebase-logo.png",
    tag: 'notification-tag',
  };

  channel.postMessage({
    type: 'NEW_NOTIFICATION',
    payload: payload
  });

  self.registration
    .showNotification(notificationTitle, notificationOptions)
    .then(() => console.log("Notification shown successfully"))
    .catch((err) => console.log("Error showing notification:", err));
});
