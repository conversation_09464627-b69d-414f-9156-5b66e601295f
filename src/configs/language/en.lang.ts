// @ts-ignore
import packageJson from "packageJSON";

export default {
  version: `Version: ${packageJson?.version}`,
  copyright: `Copyright ${new Date().getFullYear()} effiHR`,
  leads: {
    title: "Leads",
    subtitle: "Update your business persona",
    button: {
      addLead: "Add Lead",
    },
    table: {
      button: {
        resendLink: "Resend Link",
      },
    },
    modals: {
      addLeads: {
        title: "Add Lead",
        subtitle: "Update your business persona",
        textfields: {
          orgName: "Company Name",
          spocEmail: "SPOC Email",
        },
        button: {
          submit: "Invite",
        },
      },
      activationLink: {
        title: "Activation Link Resent!",
        subtitle: "The activation link has been successfully resent to the tenants email address.",
        button: {
          confirm: "Ok",
        },
      },
    },
  },
  tenants: {
    title: "Tenant Management",
    subtitle: "Update your business persona",
    assignLinkModalTitle: "Assign Link",
    costCenter: {
      title: "Cost Centers",
      subtitle: "Update your business persona",
      modalTitle: "Add Cost Center",
      modalSubtitle: "Update your business persona",
    },
    workRoles: {
      title: "Work Roles",
      subtitle: "Update your business persona",
      modalTitle: "Import File",
      sampleFileTitle: "Sample Format",
      sampleFileSubtitle: "You can download the attached example and use them as the starting point of your own file.",
    },
    modals: {
      import: {
        title: "Import File",
        subtitle: "Update your business persona",
        sampleFileTitle: "Sample Format",
        sampleFileSubtitle:
          "You can download the attached example and use them as the starting point of your own file.",
        buttons: {
          import: "Import",
          cancel: "Cancel",
        },
      },
    },
    button: {
      addTenant: "Add Tenant",
      createCostCenter: "Add Cost Center",
      import: "Import",
      save: "Save",
      addMore: "Add More",
      create: "Create",
      addWorkRole: "Add Work Role",
    },
    tenantSettings: {
      commonLabel: "Name",
      descriptionLable: "Description",
      button: {
        saveTitle: "Save",
        nextTitle: "Next",
        addTitle: "Add",
        createTitle: "Create",
      },
      businessUnits: {
        title: "Business Units",
        inputTitle: "Business Unit",
        addBusinessUnit: "Add Business Unit",
        editBusinessUnit: "Edit Business Unit",
        deleteBusinessUnit: "Delete Business Unit",
        selectBusinessUnit: "Select Business Unit",
        enterBusinessUnit: "Enter Business Unit",
      },
      departments: {
        title: "Departments",
        inputTitle: "Department",
        editDepartment: "Edit Department",
        deleteDepartment: "Delete Department",
        addDepartment: "Add Department",
        selectDepartment: "Select Department",
        enterDepartment: "Enter Department",
      },
      teams: {
        title: "Teams",
        inputTitle: "Team",
        addTeam: "Add Team",
        editTeam: "Edit Team",
        deleteTeam: "Delete Team",
        selectTeam: "Select Team",
        enterTeam: "Enter Team Name",
      },
      jobFamilies: {
        title: "Job Families",
        inputTitle: "Job Family",
        addJobFamily: "Add Job Family",
        editJobFamily: "Edit Job Family",
        deleteJobFamily: "Delete Job Family",
        selectJobFamily: "Select Job Family",
        enterJobFamily: "Enter Job Family",
      },
      jobTitles: {
        title: "Job Titles",
        inputTitle: "Job Title",
        addJobTitle: "Add Job Title",
        editJobTitle: "Edit Job Title",
        deleteJobTitle: "Delete Job Title",
        selectJobTitle: "Select Job Title",
        enterJobTitle: "Enter Job Title",
      },
      subDepartments: {
        title: "Sub Departments",
        inputTitle: "Sub Department",
        addSubDepartment: "Add Sub Department",
        editSubDepartment: "Edit Sub Department",
        deleteSubDepartment: "Delete Sub Department",
        selectSubDepartment: "Select Sub Department",
        enterSubDepartment: "Enter Sub Department",
      },
      employeeConfig: {
        title: "Employee Config",
        inputTitle: "Employee Config",
        addEmployeeConfig: "Add Employee Config",
        editEmployeeConfig: "Edit Employee Config",
        deleteEmployeeConfig: "Delete Employee Config",
        selectEmployeeConfig: "Select Employee Config",
        enterEmployeeConfig: "Enter Employee Config",
        enterPrefix: "Enter Prefix",
        prefixTitle: "Prefix",
        enterNextEmployeeNumber: "Enter Next Employee Number",
        nextEmployeeNumber: "Next Employee Number",
        lengthTitle: "Length",
        lengthTooltip: "Total length of employee code. (Eg. EMP2300-> Length is 7)",
        employeeIDConfiguration: "Employee ID Configuration",
        submit: "Submit",
      },
      attendanceConfig: {
        title: "Attendance Config",
        inputTitle: "Attendance Config",
        numberOfWorkingDays: {
          label: "Number of Working Days",
          placeholder: "Enter Number of Days",
        },
        workStartTime: {
          label: "Work Start Time",
          placeholder: "",
        },
        workEndTime: {
          label: "Work End Time",
          placeholder: "",
        },
        businessUnit: {
          label: "Business Unit",
          placeholder: "Select Business Unit",
        },
        department: {
          label: "Department",
          placeholder: "Select Department",
        },
        minHoursHalfDay: {
          label: "Minimum Hours For Half Day",
          placeholder: "",
        },
        minHoursFullDay: {
          label: "Minimum Hours For Full Day",
          placeholder: "",
        },
        weekOffLabel: "Week Off",
        enforceWorking: {
          title: "Enforce working hours",
          subTitle: "If minimum number of hours are not met",
          enforceLop: {
            title: "Enforce LOP",
            value: "Enforce LOP",
          },
          normalization: {
            title: "Allow Regularisation ",
            value: "Allow Regularisation",
          },
        },
        days: [
          { label: "Sun", value: "SUNDAY" },
          { label: "Mon", value: "MONDAY" },
          { label: "Tue", value: "TUESDAY" },
          { label: "Wed", value: "WEDNESDAY" },
          { label: "Thu", value: "THURSDAY" },
          { label: "Fri", value: "FRIDAY" },
          { label: "Sat", value: "SATURDAY" },
        ],
        successText: "Attendance saved successfully",
        attendanceIDConfiguration: "Attendance Configuration",
        save: "Save",
      },
      finishForm: {
        title: "Your Organisation details has been submitted successfully!",
        subTitle:
          "Thank you for choosing effiHR. We look forward to serving you and helping you achieve your HR management goals.",
        button: {
          title: "Go To Dashboard",
        },
      },
      welcomeScreen: {
        title:
          "Welcome to effiHR! Let's begin by establishing the configuration of information and relationships between our entities to ensure smooth management.",
        button: {
          title: "Continue",
        },
      },
      workRole: {
        title: "Work Role",
        inputTitle: "Work Role",
        addWorkRole: "Add Work Role",
        selectWorkRole: "Select Work Role",
        enterWorkRole: "Enter Work Role",
      },
    },
  },
  employees: {
    title: "Manage Employees",
    subtitle: "Streamline Employee Management with Ease and Efficiency",
    button: {
      addEmployee: "Add Employee",
      import: "Import",
    },
    modals: {
      filterModal: {
        title: "Filters",
        buttons: {
          save: "Save",
          cancel: "Cancel",
        },
      },
      import: {
        title: "Import File",
        subtitle: "Update your business persona",
        sampleFileTitle: "Sample Format",
        sampleFileSubtitle:
          "You can download the attached example and use them as the starting point of your own file.",
        buttons: {
          import: "Import",
          cancel: "Cancel",
        },
      },
      addEmployee: {
        title: "Add Employee",
        buttons: {
          cancel: "Cancel",
        },
      },
      addedEmployee: {
        title: "Add Employee",
        subtitle: "You have successfully added the employee !",
        button: {
          confirm: "Ok",
        },
      },
    },
  },
  calendar: {
    today: "Today",
    day: "Day",
    week: "Week",
    month: "Month",
    calendarText: "Calendar",
  },
  new_joinees: {
    title: "Manage New Joiners",
    subtitle: "Onboard candidates to the application",
    button: {
      addNewCandidate: "Add New Joiner",
    },
    modals: {
      joiningFormSentModal: {
        title: "Joining form sent!",
        subtitle: "The Joining form has been successfully sent to the candidate email address.",
        button: {
          confirm: "Ok",
        },
      },
      addNewJoinees: {
        title: "Add New Joiners",
        subtitle: "Add new joiners for your company",
        textfields: {
          firstName: "First Name",
          lastName: "Last Name",
          personalEmail: "Personal Email",
          joiningDate: "Joining Date",
        },
        button: {
          submit: "Submit",
          invite: "Invite",
          update: "Update",
        },
      },
    },
    settings: {
      modals: {
        changeJoiningDate: {
          title: "Change Joining Date",
          subtitle: "Change your joining date to some other date",
          button: {
            submit: "Submit",
            invite: "Invite",
            update: "Update",
          },
        },
        joiningFormResentModal: {
          title: "Joining form resent!",
          subtitle: "The Joining form has been successfully sent to the candidate email address.",
          button: {
            confirm: "Ok",
          },
          error: {
            title: "Oops! Something went wrong",
            subtitle: "The Joining form was not sent to the candidate email address.",
          },
        },
      },
    },
  },
  dataTable: {
    searchPrefix: "Search on",
    noResultsDefaultMessage: "No results found!",
    filterTooltip: "Filter",
  },
  userRoleManagement: {
    screenTitle: "Manage User Roles",
    title: "User Role",
    addUserRole: "Add User Role",
    editUserRole: "Edit User Role",
    deleteUserRole: "Delete User Role",
    roleTitle: "Role",
    userNameTitle: "User Name",
  },
  employeeDocuments: {
    title: "Employee Documents",
    documentType: "Document File",
    documentName: "Document Name",
    documentStatus: "Document Status",
    editDocument: "Edit Document",
    deleteDocument: "Delete Document",
    uploadDocument: "Upload Document",
  },
  companyDocuments: {
    title: "Company Documents",
  },
};
