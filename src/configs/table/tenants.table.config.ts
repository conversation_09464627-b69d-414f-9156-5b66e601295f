import { MRT_Row, MRT_RowData } from "material-react-table";
import { TenantDetailsModel } from "src/services/api_definitions/tenants";
import { formatDateTime } from "src/utils/dateUtils";
import { tableConfigs } from "./tablecell.config";

export type CallbackMappings = Map<string, (row: TenantDetailsModel) => void | React.ReactNode>;

const createColumnConfig = (callbackMappings: CallbackMappings) => {
  return [
    {
      accessorKey: "logo",
      header: "Logo",
      Cell: ({ row }: { row: MRT_Row<TenantDetailsModel> }) =>
        tableConfigs.getImageNode(row.original.logo, {
          alt: row.original.organisation_name,
          width: 100,
          height: 54,
        }),
    },
    {
      accessorKey: "organisation_name",
      header: "Name",
      muiTableBodyCellProps: {
        align: "left",
      },
      Cell: ({ cell }: { cell: MRT_RowData }) =>
        tableConfigs.getAnchorText(cell.row.original, "organisation_name", callbackMappings.get("organisation_name")),
    },
    {
      accessorKey: "auth_signatory_email",
      header: "Email Id",
    },
    {
      accessorKey: "auth_signatory_name",
      header: "Signatory Name",
    },
    {
      accessorKey: "auth_signatory_mobile",
      header: "Phone Number",
      // biome-ignore lint/suspicious/noExplicitAny: <explanation>
      Cell: ({ cell }: { cell: MRT_RowData }) =>
        tableConfigs.getCustomNode(
          cell.row.original,
          "auth_signatory_mobile",
          callbackMappings.get("auth_signatory_mobile") as any,
        ),
    },
    {
      accessorKey: "created_by",
      header: "Created By",
    },
    {
      accessorKey: "tenant_url",
      header: "URL",
      Cell: ({ cell }: { cell: MRT_RowData }) =>
        tableConfigs.getCustomNode(cell.row.original, "tenant_url", callbackMappings.get("tenant_url") as never),
    },
    {
      accessorKey: "status",
      header: "Status",
      Cell: ({ cell }: { cell: MRT_RowData }) => tableConfigs.getStatusNode(cell.row.original.status),
    },
    {
      accessorKey: "activated_at",
      header: "Activated At",
      Cell: ({ cell }: { cell: MRT_RowData }) =>
        cell.row.original.activated_at ? formatDateTime(cell.row.original.activated_at) : "-",
    },
  ];
};

export { createColumnConfig };
