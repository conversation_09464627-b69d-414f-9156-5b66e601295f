// import { ColumnConfig } from 'src/modules/Common/Table/datatable.types';
import { MRT_ColumnDef } from "material-react-table";

type Row = Record<string, unknown>;
export type CallbackMappings = Map<string, (row: Row) => void | React.ReactNode>;

const createColumnConfig = (): MRT_ColumnDef<Record<string, string>, unknown>[] => {
  return [
    {
      accessorKey: "organisation_name",
      header: "Organisation Name",
    },
    {
      accessorKey: "email_id",
      header: "Email Id",
    },
    {
      accessorKey: "created_by",
      header: "Created by",
    },
    {
      accessorKey: "created_on",
      header: "Created on",
    },
  ];
};

export { createColumnConfig };
