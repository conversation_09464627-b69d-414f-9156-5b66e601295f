import { MRT_ColumnDef } from "material-react-table";
import { formatDateToDayMonthYear } from "src/utils/dateUtils";
import { tableConfigs } from "./tablecell.config";

type Row = Record<string, unknown>;
export type CallbackMappings = Map<string, (row: Row) => void | React.ReactNode>;

const createColumnConfig = (callbackMappings: CallbackMappings): MRT_ColumnDef<Record<string, string>, unknown>[] => {
  return [
    {
      accessorKey: "first_name",
      header: "First Name",
    },
    {
      accessorKey: "last_name",
      header: "Last Name",
    },
    {
      accessorKey: "employee_name",
      header: "Employee Name",
      Cell: ({ cell }) =>
        tableConfigs.getCustomNode(cell.row.original, "employee_name", callbackMappings.get("employee_name") as never),
    },
    {
      accessorKey: "personal_email",
      header: "Personal Email",
    },
    {
      accessorKey: "job_title",
      header: "Job Title",
    },
    {
      accessorKey: "department",
      header: "Department",
    },
    {
      accessorKey: "reporting_manager",
      header: "Reporting Manager",
    },
    {
      accessorKey: "employment_status",
      header: "Employee Status",
      Cell: ({ cell }) =>
        tableConfigs.getCustomNode(
          cell.row.original,
          "employment_status",
          callbackMappings.get("employment_status") as never,
        ),
    },
    {
      accessorKey: "employee_code",
      header: "Employee Code",
    },
    {
      accessorKey: "employee_type",
      header: "Employee Type",
    },
    {
      accessorKey: "aadhar",
      header: "Aadhar Card No.",
    },
    {
      accessorKey: "blood_group",
      header: "Blood Group",
    },
    {
      accessorKey: "date_of_birth",
      header: "Date of Birth",
    },
    {
      accessorKey: "date_of_joining",
      header: "Date of Joining",
      Cell: ({ cell }) => formatDateToDayMonthYear(cell.row.original?.date_of_joining),
    },
    {
      accessorKey: "email",
      header: "Email Id",
    },
    {
      accessorKey: "gender",
      header: "Gender",
    },
    {
      accessorKey: "location",
      header: "Location",
    },
    {
      accessorKey: "marital_status",
      header: "Marital Status",
    },
    {
      accessorKey: "nationality",
      header: "Nationality",
    },
    {
      accessorKey: "organisation",
      header: "Organisation",
    },
    {
      accessorKey: "pan",
      header: "Pan",
    },
    {
      accessorKey: "passport",
      header: "Passport",
    },
    {
      accessorKey: "phone",
      header: "Phone",
    },
    {
      accessorKey: "uan",
      header: "UAN",
    },
    {
      accessorKey: "bank_account",
      header: "Bank Account",
    },
    {
      accessorKey: "current_address",
      header: "Current Address",
    },
    {
      accessorKey: "permanent_address",
      header: "Permanent Address",
    },
    {
      accessorKey: "emergency_contacts",
      header: "Emergency Contacts",
    },
    {
      accessorKey: "reportees",
      header: "Reportees",
      size: 280,
    },
    {
      accessorKey: "form_status",
      header: "Form Status",
      Cell: ({ cell }) => tableConfigs.getStatusNode(cell.row.original?.form_status),
    },
  ];
};

export { createColumnConfig };
