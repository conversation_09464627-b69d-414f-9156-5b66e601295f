import { Link, Tooltip, Typography } from "@mui/material";
import React from "react";
import { getStatusColors } from "src/utils/typographyUtils";

const tableConfigs = {
  getStatusNode: (status: string) => {
    return <Typography sx={{ color: getStatusColors(status), fontSize: 14 }}>{status}</Typography>;
  },

  getAnchorText: <T extends Record<string, string>>(row: T, columnName: string, callback?: (row: T) => void) => {
    if (!callback) {
      return <Typography>{row[columnName]}</Typography>;
    }
    return (
      <Tooltip title={`click to edit tenant details for ${row[columnName]}`}>
        <Link
          sx={{
            textTransform: "none",
            fontWeight: 600,
          }}
          onClick={() => callback(row)}
        >
          <Typography>{row[columnName]}</Typography>
        </Link>
      </Tooltip>
    );
  },

  getCustomNode: <T extends Record<string, string>>(
    row: T,
    columnName: string,
    callback?: (row: T) => React.ReactNode,
  ) => {
    if (!callback) {
      return <Typography>{row[columnName]}</Typography>;
    }
    return callback(row);
  },

  getImageNode: (src: string, otherProps: Record<string, unknown>) => {
    return <img src={src} {...otherProps} />;
  },
};

export { tableConfigs };
