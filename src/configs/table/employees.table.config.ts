import { MRT_ColumnDef } from "material-react-table";
import { EmployeeDetails } from "src/services/api_definitions/employees";
import { formatDateToDayMonthYear } from "src/utils/dateUtils";
import { tableConfigs } from "./tablecell.config";

type Row = Record<string, unknown>;
export type CallbackMappings = Map<unknown, (row: Row) => void | React.ReactNode>;

const createColumnConfig = (callbackMappings: CallbackMappings): MRT_ColumnDef<EmployeeDetails, unknown>[] => {
  return [
    {
      accessorKey: "employee_code",
      header: "Code",
      size: 120,
    },
    {
      accessorKey: "employee_name",
      header: "Name",
      size: 220,
      Cell: ({ cell }) =>
        tableConfigs.getCustomNode(
          cell.row.original as any,
          "employee_name",
          callbackMappings.get("employee_name") as never,
        ),
    },
    {
      accessorKey: "job_title",
      header: "Job Title",
    },
    {
      accessorKey: "department",
      header: "Department",
    },
    {
      accessorKey: "business_unit",
      header: "Business Unit",
    },
    {
      accessorKey: "cost_center",
      header: "Cost Center",
    },
    {
      accessorKey: "reporting_manager",
      header: "Reporting Manager",
    },
    {
      accessorKey: "employment_status",
      header: "Employee Status",
      Cell: ({ cell }) => tableConfigs.getStatusNode(cell.row.original?.employment_status),
    },
    {
      accessorKey: "employee_type",
      header: "Employee Type",
    },
    {
      accessorKey: "aadhar",
      header: "Aadhar Card No.",
    },
    {
      accessorKey: "blood_group",
      header: "Blood Group",
    },
    {
      accessorKey: "date_of_birth",
      header: "Date of Birth",
      Cell: ({ cell }) => formatDateToDayMonthYear(cell.row.original?.date_of_birth),
    },
    {
      accessorKey: "date_of_joining",
      header: "Date of Joining",
      Cell: ({ cell }) => formatDateToDayMonthYear(cell.row.original?.date_of_joining),
    },
    {
      accessorKey: "date_of_confirmation",
      header: "Date of Confirmation",
      size: 250,
      Cell: ({ cell }) => formatDateToDayMonthYear(cell.row.original?.date_of_confirmation),
    },
    {
      accessorKey: "email",
      header: "Email Id",
    },
    {
      accessorKey: "hrbpEmail",
      header: "HRBP Email",
    },
    {
      accessorKey: "gender",
      header: "Gender",
    },
    {
      accessorKey: "office_address.display_address",
      header: "Office Address",
      size: 400,
    },
    {
      accessorKey: "marital_status",
      header: "Marital Status",
    },
    {
      accessorKey: "nationality",
      header: "Nationality",
    },
    {
      accessorKey: "organisation",
      header: "Organisation",
    },
    {
      accessorKey: "pan",
      header: "Pan",
    },
    {
      accessorKey: "passport",
      header: "Passport",
    },
    {
      accessorKey: "personal_email",
      header: "Personal Email",
    },
    {
      accessorKey: "phone",
      header: "Phone",
    },
    {
      accessorKey: "uan",
      header: "UAN",
    },
    {
      accessorKey: "bank_account",
      header: "Bank Account",
    },
    {
      accessorKey: "current_address",
      header: "Current Address",
    },
    {
      accessorKey: "permanent_address",
      header: "Permanent Address",
    },
    {
      accessorKey: "emergency_contacts",
      header: "Emergency Contacts",
    },
    {
      accessorKey: "reportees",
      header: "Reportees",
      size: 280,
    },
  ];
};

export { createColumnConfig };
