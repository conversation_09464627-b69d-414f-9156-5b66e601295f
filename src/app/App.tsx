import React, { Profiler, ProfilerOnRenderCallback } from "react";
import "react-toastify/dist/ReactToastify.css";
import { CssBaseline, ThemeProvider } from "@mui/material";
import { LocalizationProvider } from "@mui/x-date-pickers";
import { AdapterDateFns } from "@mui/x-date-pickers/AdapterDateFnsV3";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { ErrorBoundary, FallbackProps } from "react-error-boundary";
import { Provider as GlobalStoreProvider } from "react-redux";
import { BrowserRouter } from "react-router-dom";
import { ToastContainer } from "react-toastify";
import routingConfig from "src/configs/routes.config";
import { RouteConfigProvider } from "src/contexts/RoutesConfigContext";
import useAppThemeConfig from "src/customHooks/useAppThemeConfig";
import useOnlineStatus from "src/customHooks/useOnlineStatus";
import { RootLayout } from "src/pages";
import Offline from "src/pages/Offline";
import { store } from "src/store/store.config";

const localeType = Intl.DateTimeFormat().resolvedOptions().locale;
const ModuleName = require(`date-fns/locale`);

export const queryClient = new QueryClient();

const App = () => {
  const { theme } = useAppThemeConfig({
    mode: "light",
  });
  const isOnline = useOnlineStatus();

  if (!isOnline) {
    return <Offline />;
  }

  const fallbackRender = ({ error, resetErrorBoundary }: FallbackProps) => {
    return (
      <div>
        <h1>An error occurred</h1>
        <p>{error.message}</p>
        <button onClick={() => resetErrorBoundary()}>Try again</button>
      </div>
    );
  };

  const onRender: ProfilerOnRenderCallback = () => {};

  return (
    <GlobalStoreProvider store={store}>
      <Profiler id="app" onRender={onRender}>
        <ErrorBoundary fallbackRender={fallbackRender}>
          <ThemeProvider theme={theme}>
            <ToastContainer />
            <CssBaseline />
            <QueryClientProvider client={queryClient}>
              <BrowserRouter>
                <RouteConfigProvider routesConfig={routingConfig}>
                  <LocalizationProvider
                    dateAdapter={AdapterDateFns}
                    adapterLocale={ModuleName[localeType.split("-").join("")]}
                  >
                    <RootLayout />
                  </LocalizationProvider>
                </RouteConfigProvider>
              </BrowserRouter>
            </QueryClientProvider>
          </ThemeProvider>
        </ErrorBoundary>
      </Profiler>
    </GlobalStoreProvider>
  );
};

export default App;
