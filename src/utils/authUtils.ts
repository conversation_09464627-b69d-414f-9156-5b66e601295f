export function getAllowedScreens() {
  return JSON.parse(window.localStorage.getItem("allowed-screens") || "[]");
}

// export function setAllowedScreens(__allowedScreens: string[]) {
//   const allowedScreens1 = ['home', 'profile', 'onboarding'];
//   window.localStorage.setItem('allowed-screens', JSON.stringify(allowedScreens1));
// }

export function setCurrentTenantId(tenantId: string) {
  window.localStorage.setItem("tenant-id", tenantId);
}

export function getCurrentTenantId() {
  return window.localStorage.getItem("tenant-id") || "";
}

export function isRightTenant(allowedTenants: string[]) {
  const current = getCurrentTenantId();

  return allowedTenants.includes(current || "");
}

export function getRolePriority(roles: string[]) {
  const priority = ["Employee", "HR Admin", "Super Admin"];

  if (roles?.length === 1) {
    return roles[0];
  }

  for (let i = 0; i < priority.length; i++) {
    if (roles?.includes(priority[i])) {
      return priority[i];
    }
  }

  // If none of the priority roles are found, return the first role (default behavior)
  return roles?.[0];
}

// Check if token is expired by decoding JWT
export const isTokenExpired = (token: string, expiryBuffer: number) => {
  try {
    const [, payload] = token.split(".");
    const decodedPayload = JSON.parse(atob(payload));
    const expirationTime = decodedPayload.exp; // Convert to milliseconds
    const currentTime = Date.now() / 1000;
    const expiryWithBuffer = expirationTime - expiryBuffer;
    return expiryWithBuffer < currentTime;
  } catch (_error) {
    return true; // If we can't decode the token, consider it expired
  }
};
