export type ValidFileExtensions =
  | ".xlsx"
  | ".xls"
  | ".doc"
  | ".docx"
  | ".ppt"
  | ".pptx"
  | ".pdf"
  | ".jpg"
  | ".png"
  | ".gif"
  | ".txt"
  | ".csv"
  | ".mp3"
  | ".mp4"
  | ".avi"
  | ".zip"
  | ".rar";

const MIME_TYPES: Record<ValidFileExtensions, string> = {
  ".xlsx": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
  ".xls": "application/vnd.ms-excel",
  ".doc": "application/msword",
  ".docx": "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
  ".ppt": "application/vnd.ms-powerpoint",
  ".pptx": "application/vnd.openxmlformats-officedocument.presentationml.presentation",
  ".pdf": "application/pdf",
  ".jpg": "image/jpeg",
  ".png": "image/png",
  ".gif": "image/gif",
  ".txt": "text/plain",
  ".csv": "text/csv",
  ".mp3": "audio/mpeg",
  ".mp4": "video/mp4",
  ".avi": "video/x-msvideo",
  ".zip": "application/zip",
  ".rar": "application/x-rar-compressed",
  // Add more extensions and MIME types as needed
};

const getMimeType = (extension: ValidFileExtensions): string => {
  return MIME_TYPES[extension] || "application/octet-stream";
};

export const getFilenameFromContentDisposition = (contentDisposition: string) => {
  // Regular expression pattern to match the filename
  const pattern = /filename="?(.+?)"?$/;

  // Search for the pattern in the content disposition string
  const match = contentDisposition.match(pattern);

  if (match) {
    // Return the captured filename
    return match[1];
  } else {
    // Return null if no filename is found
    return null;
  }
};

const createPseudoLinkAndDownload = (blobResponse: Blob, extention: ValidFileExtensions, fileName: string) => {
  const blob = new Blob([blobResponse], { type: getMimeType(extention) });
  const url = window.URL.createObjectURL(blob);

  const link = document.createElement("a");
  link.href = url;

  link.download = `${fileName}${extention}`;
  document.body.appendChild(link);
  link.click();

  window.URL.revokeObjectURL(url);
  document.body.removeChild(link);
};

const getFileDateStamp = () => new Date().toLocaleDateString().split("/").join("");

export { createPseudoLinkAndDownload, getMimeType, getFileDateStamp };
