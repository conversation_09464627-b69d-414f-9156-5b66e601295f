const serialize = (state: Record<string, never>) =>
  Object.keys(state)
    .map((key) => encodeURIComponent(key) + "=" + encodeURIComponent(state[key]))
    .join("&");
const deserialize = (query: string): unknown =>
  query.split("&").reduce((acc, current) => {
    const [key, value] = current.split("=").map((item) => decodeURIComponent(item));
    acc = {
      ...acc,
      [key]: value,
    };
    return acc;
  }, {});

function matchUrl(pattern: string, url: string) {
  // Remove the leading and trailing "/" from both pattern and url
  pattern = pattern.replace(/^\/|\/$/g, "");
  url = url.replace(/^\/|\/$/g, "");

  // Split the pattern and URL into arrays
  const patternParts = pattern.split("/");
  const urlParts = url.split("/");

  // If lengths are different, URLs don't match
  if (patternParts.length !== urlParts.length) {
    return false;
  }

  // Loop through each part and compare
  for (let i = 0; i < patternParts.length; i++) {
    // If current part has ":" in pattern, it's a wildcard, so continue
    if (patternParts[i].includes(":")) {
      continue;
    }
    // If parts don't match, return false
    if (patternParts[i] !== urlParts[i]) {
      return false;
    }
  }
  // All parts match
  return true;
}

export { serialize, deserialize, matchUrl };
