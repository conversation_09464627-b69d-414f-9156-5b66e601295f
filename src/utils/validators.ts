import { isAfter, isBefore, is<PERSON><PERSON>d, parse<PERSON><PERSON> } from "date-fns";
import { NUMBER } from "src/app/constants";
import { PhoneNumberType } from "src/modules/Employees/types/FormDataTypes";
import { convertHoursToMinutes, getMinutesDifference, getTimeDifference } from "./dateUtils";
/* eslint-disable @typescript-eslint/no-explicit-any */
import { ValidationError } from "./errors";

const EMAIL_REGEX = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
const PHONE_REGEX = /^((\+91)|(0091)|0?)[6789]{1}\d{9}$/;
const INTERNATIONAL_PHONE_REGEX = /^\+[1-9]\d{0,2}[-\s]?(?:\d{2,4}[-\s]?){1,4}\d{2,4}$/;
const NAME_REGEX = /^[a-zA-Z\s]+$/;
const NUMBER_REGEX = /^[0-9]+$/;
const PAN_REGEX = /^[A-Z]{5}[0-9]{4}[A-Z]$/;
const AADHAAR_REGEX = /^[2-9][0-9]{11}$/;
const ZIP_CODE_REGEX = /^[1-9][0-9]{5}$/;
const IFSC_REGEX = /^[A-Z]{4}0[A-Z0-9]{6}$/;
const SPECIAL_CHAR_REGEX = /^[a-zA-Z0-9 ._-]+$/;
const ACCOUNT_NO_REGEX = /^(\d{3,12})$/;
const TIME_REGEX = /^([01][0-9]|2[0-3]):([0-5][0-9])$/;
const WORKING_DAYS_REGEX = /^[1-7]$/;
const HTTP_URL_REGEX = /^(https?:\/\/)?([a-zA-Z0-9.-]+)(\.[a-zA-Z]{2,})(:[0-9]{1,5})?(\/\S*)?$/;

export type ValidatorReturnType = Error | null;

class Validator {
  validateEmail = (email: string): ValidatorReturnType => {
    if (!EMAIL_REGEX.test(email)) {
      return new ValidationError("email", "Wrong email format, please check");
    }
    return null;
  };

  validateInput = (value: string | number): ValidatorReturnType => {
    if (!value) {
      return new ValidationError("required", "Input should not be empty");
    }
    return null;
  };

  validateSelectDropdown = (value: []): ValidatorReturnType => {
    if (!value?.length) {
      return new ValidationError("required", "Input should not be empty");
    }
    return null;
  };

  validatePhone = (phoneNumber: PhoneNumberType): ValidatorReturnType => {
    if (!phoneNumber.countryCode) {
      return new ValidationError("required", "Country code is required");
    }
    if (!phoneNumber.number) {
      return new ValidationError("required", "Phone number is required");
    }
    if (!PHONE_REGEX.test(phoneNumber.number)) {
      return new ValidationError("phone", "Phone number is not valid");
    }
    return null;
  };

  validateInternationalPhone = (phoneNumber: string): ValidatorReturnType => {
    if (!INTERNATIONAL_PHONE_REGEX.test(phoneNumber)) {
      return new ValidationError("phone", "Phone number is not valid");
    }
    return null;
  };

  validateName = (value: string): ValidatorReturnType => {
    if (!NAME_REGEX.test(value)) {
      return new ValidationError("name", "Name should not contain any alphanumeric values");
    }
    return null;
  };

  validatePAN = (value: string): ValidatorReturnType => {
    if (!PAN_REGEX.test(value)) {
      return new ValidationError("pan", "PAN is not valid.");
    }
    return null;
  };

  validateAadhar = (value: string): ValidatorReturnType => {
    if (!AADHAAR_REGEX.test(value)) {
      return new ValidationError("aadhar", "Aadhar is not valid.");
    }
    return null;
  };

  validateBankAccounNumber = (value: string): ValidatorReturnType => {
    if (!ACCOUNT_NO_REGEX.test(value)) {
      return new ValidationError("bankAccountNumber", "Account Number is not valid.");
    }
    return null;
  };

  validateZipCode = (value: string): ValidatorReturnType => {
    if (!ZIP_CODE_REGEX.test(value)) {
      return new ValidationError("zipcode", "Zipcode should be a 6 digit number.");
    }
    return null;
  };

  validateIFSC = (value: string): ValidatorReturnType => {
    if (!IFSC_REGEX.test(value)) {
      return new ValidationError("ifsc", "Please provide valid IFSC Code");
    }
    return null;
  };

  shouldBeNumeric = (value: string): ValidatorReturnType => {
    if (!NUMBER_REGEX.test(value)) {
      return new ValidationError("numeric", "Should contain only numbers");
    }
    return null;
  };

  shouldNotContainSpecialCharacters = (value: string): ValidatorReturnType => {
    if (!SPECIAL_CHAR_REGEX.test(value)) {
      return new ValidationError(
        "name",
        "Should not contain special characters other than space, underscore or hyphen",
      );
    }
    return null;
  };
  //should be a custom validation function for employee config form.
  shouldBeOfMinLength = (value: string, formDetails: Record<string, any>): ValidatorReturnType => {
    const formDetail = formDetails?.[0] ? formDetails[0] : formDetails;
    const minLength = (formDetail?.prefix?.length || 0) + (formDetail?.sequence?.toString()?.length || 0);
    if (Number(value) < minLength) {
      return new ValidationError("minLength", `Minimum allowed value is ${minLength}. (Prefix + Sequence)`);
    }
    return null;
  };

  validateWorkingDays = (value: string): ValidatorReturnType => {
    if (!WORKING_DAYS_REGEX.test(value)) {
      return new ValidationError("numeric", "Invalid working days.");
    }
    return null;
  };

  validateTimeOnly = (value: string): ValidatorReturnType => {
    if (value === "" || !TIME_REGEX.test(value)) {
      return new ValidationError("time", "Please enter valid time (hint: 09:30)");
    }
    return null;
  };

  validateWorkEndTime = (value: string, formDetails: any): ValidatorReturnType => {
    const { workStartTime } = formDetails;
    if (getMinutesDifference(value, workStartTime) < 0) {
      return new ValidationError("time", "Work end time should be greater than work start time");
    }
    return null;
  };

  validateNightShift = (value: string, formDetails: any): ValidatorReturnType => {
    const { workStartTime } = formDetails;
    if (getMinutesDifference(value, workStartTime) < 0) {
      return new ValidationError("time", "+1 day (Night Shift)");
    }
    return null;
  };

  validateMinHalfDayTime = (value: string, formDetails: any): ValidatorReturnType => {
    const { workStartTime, workEndTime } = formDetails;
    const totalTime = getTimeDifference(workStartTime, workEndTime);
    const totalTimeInMin = convertHoursToMinutes(totalTime);
    const selectedTimeInMin = convertHoursToMinutes(value);

    if (selectedTimeInMin > totalTimeInMin) {
      return new ValidationError("time", "Please enter the valid half day hours");
    }
    return null;
  };

  validateMinFullDayTime = (value: string, formDetails: any): ValidatorReturnType => {
    const { workStartTime, workEndTime, minHoursHalfDay } = formDetails;
    const totalTime = getTimeDifference(workStartTime, workEndTime);
    const totalTimeInMin = convertHoursToMinutes(totalTime);
    const selectedTimeInMin = convertHoursToMinutes(value);
    const minHalfDayInMin = convertHoursToMinutes(minHoursHalfDay);

    if (selectedTimeInMin > totalTimeInMin || selectedTimeInMin < minHalfDayInMin) {
      return new ValidationError("time", "Please enter the valid full day hours");
    }
    return null;
  };

  validateWeekOff = (value: string, formDetails: any): ValidatorReturnType => {
    const { numberOfWorkingDays } = formDetails;

    if (typeof formDetails === "object" && Array.isArray(formDetails)) {
      return formDetails.reduce((_, curr) => {
        const { numberOfWorkingDays } = curr;
        if (NUMBER.SEVEN - Number(numberOfWorkingDays) !== value?.length) {
          return new ValidationError(
            "minLength",
            "Number of week off does not match the Number of working days condition ",
          );
        }
      }, null);
    }

    if (NUMBER.SEVEN - Number(numberOfWorkingDays) !== value?.length) {
      return new ValidationError(
        "minLength",
        "Number of week off does not match the Number of working days condition ",
      );
    }
    return null;
  };

  validateURL = (email: string): ValidatorReturnType => {
    if (!HTTP_URL_REGEX.test(email)) {
      return new ValidationError("url", "Wrong url format, please check");
    }
    return null;
  };

  validateDate = (date: string): ValidatorReturnType => {
    if (!date) {
      return new ValidationError("required", "Date should not be empty");
    }

    const parsedDate = date;

    if (!isValid(parsedDate)) {
      return new ValidationError("required", "Invalid date format");
    }

    // Define the acceptable date range
    const minDate = parseISO("2000-01-01");
    const maxDate = parseISO("2100-12-31");

    if (isBefore(parsedDate, minDate)) {
      return new ValidationError(
        "out_of_range",
        `Date must not be earlier than ${minDate.toISOString().split("T")[0]}`,
      );
    }

    if (isAfter(parsedDate, maxDate)) {
      return new ValidationError("out_of_range", `Date must not be later than ${maxDate.toISOString().split("T")[0]}`);
    }

    return null;
  };

  validateMultiSelect = (value: any): ValidatorReturnType => {
    if (value?.length < 1) {
      return new ValidationError("required", "Please select at least one option");
    }
    return null;
  };

  sequenceShouldBeLessThenEmployeeIdConfigLength = (
    value: string,
    formDetails: Record<string, any>,
  ): ValidatorReturnType => {
    const formDetail = formDetails?.[0] ? formDetails[0] : formDetails;
    if (formDetail?.idLength) {
      const userGivenIdLength = (formDetail?.prefix?.length || 0) + (value?.toString()?.length || 0);
      const actualIdLength = Number(formDetail?.idLength);
      const expectedLength = actualIdLength - (formDetail?.prefix?.toString()?.length || 0);
      if (userGivenIdLength > actualIdLength) {
        return new ValidationError("lengthExceeded", `Sequence length should be less than ${expectedLength}`);
      }
    }
    return null;
  };
  prefixShouldBeLessThenEmployeeIdConfigLength = (
    value: string,
    formDetails: Record<string, any>,
  ): ValidatorReturnType => {
    const formDetail = formDetails?.[0] ? formDetails[0] : formDetails;
    if (formDetail?.idLength) {
      const userGivenIdLength = (value?.length || 0) + (formDetail?.sequence?.toString()?.length || 0);
      const actualIdLength = Number(formDetail?.idLength);
      const expectedLength = actualIdLength - (formDetail?.sequence?.toString()?.length || 0);
      if (userGivenIdLength > actualIdLength) {
        return new ValidationError("lengthExceeded", `Prefix length should be less than ${expectedLength}`);
      }
    }
    return null;
  };

  shouldBePositive = (value: number): ValidatorReturnType => {
    if (value <= 0) {
      return new ValidationError("numeric", "Value should be positive");
    }
    return null;
  };

  validateDeprioritationReason = (value: string, formDetails: Record<string, string>[]): ValidatorReturnType => {
    const indexOfDeprioritisationStatusWithNoReason = formDetails?.findIndex(
      (formDetail) => formDetail?.status === "Deprioritised" && (value ? !value : !formDetail?.deprioritisation_reason),
    );
    if (indexOfDeprioritisationStatusWithNoReason !== -1) {
      return new ValidationError("required", "Deprioritisation reason is required");
    }
    return null;
  };

  validateEffectiveDate = (value: string, formDetails: Record<string, string>[]): ValidatorReturnType => {
    if (formDetails?.status === "Approved" && !value) {
      return new ValidationError("required", "Effective date is required");
    }
    return null;
  };
}

export default new Validator();
