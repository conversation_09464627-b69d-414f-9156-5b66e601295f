import { Node } from "@xyflow/react";
import { CustomNodeRA, MappedResource } from "../modules/PerformanceManagement/ResourceAllocationTypes";
import { CustomNode, Reportee } from "../modules/Profile/components/OrgChart/OrgChartFlow";

interface Position {
  x: number;
  y: number;
}

interface LayoutConfig {
  horizontalSpacing: number;
  verticalSpacing: number;
}

export const findExistingNodesAtLevel = (nodes: Node[], levelY: number, verticalSpacing: number): Node[] => {
  return nodes
    .filter((node) => Math.abs(node.position.y - levelY) < verticalSpacing / 2)
    .sort((a, b) => a.position.x - b.position.x);
};

export const calculateNodePositions = (
  clickedNode: CustomNode | CustomNodeRA,
  reportees: Reportee[] | MappedResource[],
  nodes: Node[],
  config: LayoutConfig,
): Position[] => {
  const { horizontalSpacing, verticalSpacing } = config;
  const levelY = clickedNode.position.y + verticalSpacing;
  const existingNodes = findExistingNodesAtLevel(nodes, levelY, verticalSpacing);

  // Check if we can render nodes symmetrically below the parent
  const totalWidth = (reportees.length - 1) * horizontalSpacing;
  const positions = reportees.map((_, index) => {
    const offset = index - (reportees.length - 1) / 2;
    return clickedNode.position.x + offset * horizontalSpacing;
  });

  const hasOverlaps = positions.some((x) =>
    existingNodes.some((node) => Math.abs(node.position.x - x) < horizontalSpacing),
  );

  if (!hasOverlaps) {
    return positions.map((x) => ({
      x,
      y: levelY,
    }));
  }

  // Split reportees into existing and new
  const existingReportees = reportees.filter((reportee) => nodes.some((node) => node.id === reportee.employee_code));
  const newReportees = reportees.filter((reportee) => !nodes.some((node) => node.id === reportee.employee_code));

  // Handle mixed case: some existing and some new reportees
  if (existingReportees.length > 0 && newReportees.length > 0) {
    const existingPositions = existingReportees
      .map((reportee) => {
        const node = nodes.find((n) => n.id === reportee.employee_code);
        return node?.position.x;
      })
      .filter((x): x is number => x !== undefined);

    const centerX = existingPositions.reduce((sum, x) => sum + x, 0) / existingPositions.length;
    const positions = new Array(reportees.length).fill(0);

    // Pre-fill existing positions
    reportees.forEach((reportee, index) => {
      const existingNode = nodes.find((node) => node.id === reportee.employee_code);
      if (existingNode) {
        positions[index] = existingNode.position.x;
      }
    });

    // Calculate new positions
    const leftCount = Math.floor(newReportees.length / 2);
    const rightCount = newReportees.length - leftCount;
    let leftX = centerX - horizontalSpacing;
    let rightX = centerX + horizontalSpacing;

    const checkAndAdjustPosition = (x: number, direction: "left" | "right"): number => {
      let adjustedX = x;
      while (existingNodes.some((node) => Math.abs(node.position.x - adjustedX) < horizontalSpacing)) {
        adjustedX = direction === "left" ? adjustedX - horizontalSpacing : adjustedX + horizontalSpacing;
      }
      return adjustedX;
    };

    let leftPlaced = 0;
    let rightPlaced = 0;

    reportees.forEach((reportee, index) => {
      if (!nodes.some((node) => node.id === reportee.employee_code)) {
        if (leftPlaced < leftCount) {
          leftX = checkAndAdjustPosition(leftX, "left");
          positions[index] = leftX;
          leftX -= horizontalSpacing;
          leftPlaced++;
        } else if (rightPlaced < rightCount) {
          rightX = checkAndAdjustPosition(rightX, "right");
          positions[index] = rightX;
          rightX += horizontalSpacing;
          rightPlaced++;
        }
      }
    });

    return reportees.map((_, index) => ({
      x: positions[index],
      y: levelY,
    }));
  }

  // Handle single reportee case
  if (reportees.length === 1) {
    const proposedX = clickedNode.position.x;
    const hasOverlap = existingNodes.some((node) => Math.abs(node.position.x - proposedX) < horizontalSpacing);

    if (!hasOverlap) {
      return [{ x: proposedX, y: levelY }];
    }
  }

  // Handle multiple reportees case
  const idealCenter = clickedNode.position.x;
  let startX = idealCenter - totalWidth / 2;

  // If no existing nodes, distribute evenly
  if (existingNodes.length === 0) {
    return reportees.map((_, index) => ({
      x: startX + index * horizontalSpacing,
      y: levelY,
    }));
  }

  // Find gaps between existing nodes
  const gaps = [];
  for (let i = 0; i < existingNodes.length - 1; i++) {
    const gap = {
      start: existingNodes[i].position.x + horizontalSpacing,
      end: existingNodes[i + 1].position.x - horizontalSpacing,
      width: existingNodes[i + 1].position.x - existingNodes[i].position.x - 2 * horizontalSpacing,
    };
    if (gap.width >= totalWidth) {
      gaps.push(gap);
    }
  }

  // Add left and right edge gaps
  gaps.push(
    {
      start: existingNodes[0].position.x - totalWidth - horizontalSpacing,
      end: existingNodes[0].position.x - horizontalSpacing,
      width: totalWidth,
    },
    {
      start: existingNodes[existingNodes.length - 1].position.x + horizontalSpacing,
      end: existingNodes[existingNodes.length - 1].position.x + totalWidth + horizontalSpacing,
      width: totalWidth,
    },
  );

  // Find best gap (closest to ideal center)
  const bestGap = gaps.reduce((best, gap) => {
    const gapCenter = (gap.start + gap.end) / 2;
    const currentDistance = Math.abs(gapCenter - idealCenter);
    const bestDistance = Math.abs((best.start + best.end) / 2 - idealCenter);
    return currentDistance < bestDistance ? gap : best;
  }, gaps[0]);

  // Calculate final positions within best gap
  const gapCenter = (bestGap.start + bestGap.end) / 2;
  startX = Math.max(bestGap.start, Math.min(bestGap.end - totalWidth, gapCenter - totalWidth / 2));

  return reportees.map((_, index) => ({
    x: startX + index * horizontalSpacing,
    y: levelY,
  }));
};
