import React from "react";
import { ToastOptions, toast } from "react-toastify";
import ToastComponent from "src/modules/Toast/components/ToastComponent";

const showToast = (title: string, options: ToastOptions) => {
  return toast(<ToastComponent toastId={options?.toastId} title={title} type={options.type ?? "info"} />, {
    ...options,
    position: "top-right",
    hideProgressBar: true,
    closeOnClick: false,
    pauseOnHover: true,
    draggable: false,
    autoClose: 3000,
    icon: false,
  });
};

export { showToast };
