import cookie, { <PERSON>ieAttributes } from "js-cookie";

const setCookie = (name: string, value: string, options?: CookieAttributes) => {
  cookie.set(name, value, options);
};

const getCookie = (name: string) => {
  return cookie.get(name);
};

const deleteCookie = (name: string, options?: <PERSON>ieAttributes) => {
  return cookie.remove(name, options);
};

export { setCookie, getCookie, deleteCookie };
