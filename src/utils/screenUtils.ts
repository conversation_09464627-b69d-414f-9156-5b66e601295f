import { AccessControl, SideBarMenu } from "src/configs/app.config";
import { store } from "src/store/store.config";

const findACLRecursively = (authorisedScreens: SideBarMenu[], featureKey: string): AccessControl => {
  const DEFAULT_STATE = { canRead: false, canWrite: false };

  for (const screen of authorisedScreens) {
    if (screen.key === featureKey) {
      return { ...DEFAULT_STATE, ...screen.acl };
    }

    if (screen.subRoutes && screen.subRoutes.length > 0) {
      const acl = findACLRecursively(screen.subRoutes, featureKey);
      if (acl.canRead || acl.canWrite) {
        return acl;
      }
    }
  }

  return DEFAULT_STATE;
};

// TODO: Make this recursive as to find out the acl from the authorised screens
const getACLFromFeaturekey = (featureKey: string): AccessControl => {
  return findACLRecursively(store.getState().userManagement.authorisedScreens, featureKey);
};

export { getACLFromFeaturekey };
