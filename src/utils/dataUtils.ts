/* eslint-disable @typescript-eslint/no-explicit-any */

import { BaseObject } from "src/app/global";

// Function to convert a string to camelCase
function toCamelCase(str: string): string {
  return str.replace(/_([a-z])/g, (_match, letter) => letter.toUpperCase());
}

// Recursive function to convert object keys to camelCase
function keysToCamelCase<T>(obj: T): T {
  if (Array.isArray(obj)) {
    return obj.map((item) => keysToCamelCase(item)) as any;
  } else if (obj !== null && typeof obj === "object") {
    return Object.keys(obj).reduce((result, key) => {
      const camelCaseKey = toCamelCase(key);
      result[camelCaseKey] = keysToCamelCase((obj as any)[key]);
      return result;
    }, {} as any) as T;
  }
  return obj;
}

const isEmpty = (value: any) => value == null || value === "" || value === 0;

function isAllCustomEmpty(obj: BaseObject): boolean {
  if (isEmpty(obj)) return true;
  return Object?.values(obj)?.every((value) => isEmpty(value));
}

function isSomeCustomEmpty(obj: BaseObject): boolean {
  if (isEmpty(obj)) return true;
  return Object?.values(obj)?.some((value) => isEmpty(value));
}

const truncatedString = (value: string, tuncateLength: number) => {
  if (value?.length > tuncateLength) {
    return value?.substring(0, tuncateLength) + "...";
  }
  return value;
};
const isSubset = <T extends string | unknown | number>(arrA: T[], arrB: T[]) => {
  const setB = new Set(arrB);

  // Check if every element in arrA exists in setB
  return arrA.every((element) => setB.has(element));
};

function deepEqualArrays(array1: unknown[], array2: unknown[]): boolean {
  // Check if both are arrays
  if (!Array.isArray(array1) || !Array.isArray(array2)) {
    return false;
  }

  // Check lengths first
  if (array1.length !== array2.length) {
    return false;
  }

  // Compare elements recursively
  for (let i = 0; i < array1.length; i++) {
    const element1 = array1[i];
    const element2 = array2[i];

    const areObjects =
      typeof element1 === "object" && typeof element2 === "object" && element1 !== null && element2 !== null;

    if (areObjects) {
      // Recursively compare if elements are objects (including arrays)
      if (Array.isArray(element1) && Array.isArray(element2)) {
        if (!deepEqualArrays(element1, element2)) {
          return false;
        }
      } else if (!deepEqualObjects(element1, element2)) {
        return false;
      }
    } else if (element1 !== element2) {
      // Direct comparison for primitives
      return false;
    }
  }

  return true;
}

function deepEqualObjects(obj1: Record<string, unknown>, obj2: Record<string, unknown>): boolean {
  // Handle primitives and null checks
  if (typeof obj1 !== "object" || typeof obj2 !== "object" || obj1 === null || obj2 === null) {
    return obj1 === obj2;
  }

  const keys1 = Object.keys(obj1);
  const keys2 = Object.keys(obj2);

  if (keys1.length !== keys2.length) {
    return false;
  }

  for (const key of keys1) {
    if (!keys2.includes(key) || !deepEqualArrays([obj1[key]], [obj2[key]])) {
      return false;
    }
  }

  return true;
}

export {
  keysToCamelCase,
  isAllCustomEmpty,
  isEmpty,
  truncatedString,
  isSubset,
  isSomeCustomEmpty,
  deepEqualArrays,
  deepEqualObjects,
};
