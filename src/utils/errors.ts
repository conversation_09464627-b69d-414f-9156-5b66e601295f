export type ValidationErrorTypes =
  | "email"
  | "phone"
  | "required"
  | "name"
  | "numeric"
  | "minLength"
  | "pan"
  | "bankAccountNumber"
  | "aadhar"
  | "zipcode"
  | "ifsc"
  | "time"
  | "url"
  | "out_of_range"
  | "lengthExceeded";

export class ValidationError extends Error {
  constructor(type: ValidationErrorTypes, message: string) {
    super();
    this.name = type;
    this.message = message;
    this.stack = "Input Error Validation";
  }
}
