import { PayloadAction, createSlice } from "@reduxjs/toolkit";
import { EmployeeDetails } from "src/services/api_definitions/profile";

type ProfileState = {
  employeeDetails: Record<string, string>;
  userDetails: EmployeeDetails | null;
};

const defaultState: ProfileState = {
  employeeDetails: {
    employee_id: "",
    first_name: "",
    last_name: "",
    date_of_birth: "",
    gender: "",
    email: "",
    phone: "",
    date_of_joining: "",
    nationality: "",
    pan: "",
    managerEmail: "",
    location: "",
    uan: "",
  },
  userDetails: null,
};

export const initialState: ProfileState = { ...defaultState };

const profileSlice = createSlice({
  name: "profile",
  initialState,
  reducers: {
    setFormDetails: (state: ProfileState, action: PayloadAction<{ name: string; value: string }>) => {
      const { payload } = action;
      state.employeeDetails = {
        ...state.employeeDetails,
        [payload.name]: payload.value,
      };
    },
    setUserDetails: (state, action: PayloadAction<EmployeeDetails | null>) => {
      state.userDetails = action.payload || null;
    },
    reset: (state) => {
      state.employeeDetails = defaultState.employeeDetails;
    },
  },
});

export const { setFormDetails, reset, setUserDetails } = profileSlice.actions;
export default profileSlice;
