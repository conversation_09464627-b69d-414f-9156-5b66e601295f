import { combineReducers } from "@reduxjs/toolkit";
import { default as appSlice } from "./app.slice";
import authSlice from "./auth.slice";
import { default as breadcrumbSlice } from "./breadcrumbs.slice";
import { default as calendarSlice } from "./calendar.slice";
import { default as profileSlice } from "./profile.slice";
import { default as userManagementSlice } from "./userManagement.slice";
// import TemplateSlice from './template.slice';

export const rootReducer = combineReducers({
  profile: profileSlice.reducer,
  userManagement: userManagementSlice.reducer,
  app: appSlice.reducer,
  auth: authSlice.reducer,
  calendar: calendarSlice.reducer,
  breadcrumbs: breadcrumbSlice.reducer,
  // template: TemplateSlice.reducer,
});
