import { PayloadAction, createSlice } from "@reduxjs/toolkit";
import { AccessControl, SubRoute, default as appConfig } from "src/configs/app.config";
import { AccessControlTypes, Screens } from "src/services/api_definitions/auth.service";
import { TenantDetailsModel } from "src/services/api_definitions/tenants";
import { OrganisationDetails, UserDetails, UserSettings } from "src/services/api_definitions/userManagement";
import { setCookie } from "src/utils/cookieUtils";

type DefaultUserManagementState = {
  authorisedScreens: typeof appConfig.sideBarMenus | [];
  selectedRole: string | null;
  userRoles: string[];
  userSettings: UserSettings;
  tenantDetails: TenantDetailsModel;
  userDetails: UserDetails;
  selectedOrganisation: string | null;
  selectedOrganisationDetails: OrganisationDetails;
};

const defaultState: DefaultUserManagementState = {
  authorisedScreens: [],
  userRoles: [],
  selectedRole: null,
  selectedOrganisation: null,
  userSettings: {} as UserSettings,
  tenantDetails: {} as TenantDetailsModel,
  userDetails: {} as UserDetails,
  selectedOrganisationDetails: {} as OrganisationDetails,
};

type ScreenAccess = {
  [screen: string]: AccessControl & Screens;
};

const parseACL = (aclList: Screens[]): ScreenAccess => {
  const readScreenKeys: AccessControlTypes[] = ["Read-Only", "Read-Write"];
  const writeScreenKeys: AccessControlTypes[] = ["Read-Write"];
  return aclList.reduce((acc, { screen, acl, ...otherProps }) => {
    const canRead = readScreenKeys.includes(acl);
    const canWrite = writeScreenKeys.includes(acl);
    acc[screen] = { ...otherProps, canRead, canWrite, screen, acl };
    return acc;
  }, {} as ScreenAccess);
};

const filterSubRoutes = (subRoutes: SubRoute[] | undefined, screenAccess: ScreenAccess): SubRoute[] | undefined => {
  if (!subRoutes) return undefined;
  return subRoutes
    .filter((subRoute) => screenAccess[subRoute.key]?.canRead)
    .map((subRoute) => {
      const accessControl = screenAccess[subRoute.key];
      return {
        ...subRoute,
        acl: accessControl,
        subRoutes: filterSubRoutes(subRoute.subRoutes, screenAccess),
      };
    });
};

const filterAppConfig = (config: typeof appConfig, screenAccess: ScreenAccess): typeof appConfig => {
  const sideBarMenus = config.sideBarMenus
    .filter((menu) => {
      const accessControl = screenAccess[menu.key];
      return menu.isInternal || accessControl?.canRead;
    })
    .map((menu) => {
      const accessControl = screenAccess[menu.key];
      return {
        ...menu,
        acl: menu.isInternal ? { canRead: true, canWrite: true } : accessControl,
        subRoutes: filterSubRoutes(menu.subRoutes, screenAccess),
        redirectURL: accessControl?.redirect_url,
      };
    });
  return { ...config, sideBarMenus };
};

const userManagement = createSlice({
  name: "userManagement",
  initialState: { ...defaultState },
  reducers: {
    setAuthorisedScreens: (state, action: PayloadAction<Screens[]>) => {
      state.authorisedScreens = filterAppConfig(appConfig, parseACL(action.payload)).sideBarMenus;
    },
    setSelectedRole: (state, action: PayloadAction<string>) => {
      state.selectedRole = action.payload;

      // need to reset the values as we need to refresh the screens
      state.authorisedScreens = [];
      setCookie("role", action.payload);
    },
    setUserRoles: (state, action: PayloadAction<string[]>) => {
      state.userRoles = action.payload;
    },
    setUserSettings: (state, action: PayloadAction<UserSettings>) => {
      state.userSettings = action.payload;
    },
    setTenantDetails: (state, action: PayloadAction<TenantDetailsModel>) => {
      state.tenantDetails = action.payload;
    },
    setUserDetails: (state, action: PayloadAction<UserDetails>) => {
      state.userDetails = action.payload;
    },
    setSelectedOrganisation: (state, action: PayloadAction<string>) => {
      state.selectedOrganisation = action.payload;
      setCookie("org", action.payload);
      const selectedOrganisationDetails = state.userDetails?.organisations.find(
        (organisation) => organisation.name === action.payload,
      );

      if (selectedOrganisationDetails) {
        state.selectedOrganisationDetails = { ...selectedOrganisationDetails };
      }
    },
  },
});

export const {
  setAuthorisedScreens,
  setSelectedRole,
  setUserRoles,
  setUserSettings,
  setTenantDetails,
  setUserDetails,
  setSelectedOrganisation,
} = userManagement.actions;

export default userManagement;
