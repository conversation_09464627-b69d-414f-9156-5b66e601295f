import { PayloadAction, createSlice } from "@reduxjs/toolkit";

// Set the initial state of slice
type TemplateState = {
  TemplatedValue: string | null;
};

const defaultState: TemplateState = {
  TemplatedValue: null,
};

const initialState: TemplateState = { ...defaultState };

// Create new Slice
const TemplateSlice = createSlice({
  name: "Template",
  initialState,
  reducers: {
    setTemplateValue: (state: TemplateState, action: PayloadAction<TemplateState>) => {
      const { payload } = action;
      state.TemplatedValue = payload.TemplatedValue;
    },
  },
});

// Export slice functions for use
export const { setTemplateValue } = TemplateSlice.actions;
// Export slice to store config
export default TemplateSlice;

// e.g. use case :
// function useTemplateHook(value: string | null, delay: number) {
//   const dispatch = useAppDispatch();
//   const templateState = useAppSelector((state) => state.template)
//   dispatch(setTemplateValue({ TemplateValue: newValue }));
// }
