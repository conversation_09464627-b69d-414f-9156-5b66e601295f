import { PayloadAction, createSlice } from "@reduxjs/toolkit";

const defaultState = {
  contentHeight: 0,
  isFullView: false,
};

const appSlice = createSlice({
  name: "app",
  initialState: { ...defaultState },
  reducers: {
    setContentHeight: (state, action: PayloadAction<number>) => {
      state.contentHeight = action.payload;
    },
    setFullviewMode: (state, action: PayloadAction<boolean>) => {
      state.isFullView = action.payload;
    },
  },
});

export const { setContentHeight, setFullviewMode } = appSlice.actions;
export default appSlice;
