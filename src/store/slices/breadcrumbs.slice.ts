import { PayloadAction, createSlice } from "@reduxjs/toolkit";
import { Breadcrumb } from "src/app/global";

interface DefaultBreadcrumbState {
  breadcrumbs: Breadcrumb[];
}

const defaultState: DefaultBreadcrumbState = {
  breadcrumbs: [],
};

const breadcrumbSlice = createSlice({
  name: "breadcrumbs",
  initialState: { ...defaultState },
  reducers: {
    addBreadcrumb: (prevState, action: PayloadAction<Breadcrumb>) => {
      const prevBreadcrumbs = [...prevState.breadcrumbs];
      const doesPayloadAlreadyExist = prevBreadcrumbs.some((eachCrumb) => eachCrumb.label === action.payload.label);

      if (!doesPayloadAlreadyExist) {
        prevBreadcrumbs.push(action.payload);
        prevState.breadcrumbs = prevBreadcrumbs;
      }
    },
    removeBreadcrumb: (prevState, action: PayloadAction<string>) => {
      prevState.breadcrumbs = prevState.breadcrumbs.filter((eachCrumb) => eachCrumb.path !== action.payload);
    },
    setBreadcrumbs: (prevState, action: PayloadAction<Breadcrumb[]>) => {
      prevState.breadcrumbs = action.payload;
    },
    updateActiveBreadcrumb: (prevState, action: PayloadAction<string>) => {
      prevState.breadcrumbs = prevState.breadcrumbs.map((crumb) => ({
        ...crumb,
        isActive: crumb.path === action.payload,
      }));
    },
  },
});

export const { addBreadcrumb, removeBreadcrumb, setBreadcrumbs, updateActiveBreadcrumb } = breadcrumbSlice.actions;
export default breadcrumbSlice;
