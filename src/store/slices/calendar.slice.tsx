import { PayloadAction, createSlice } from "@reduxjs/toolkit";
import { DefaultCalendarState, EventResponseProps } from "src/services/api_definitions/calendar";

const defaultState: DefaultCalendarState = {
  myCalendarEvents: [],
  weekOffs: [],
};

export const initialState: DefaultCalendarState = { ...defaultState };

const calendarSlice = createSlice({
  name: "calendar",
  initialState,
  reducers: {
    setMyCalendarEvents: (state, action: PayloadAction<EventResponseProps>) => {
      state.myCalendarEvents = action.payload.events || [];
      state.weekOffs = action.payload.week_offs;
    },
  },
});

export const { setMyCalendarEvents } = calendarSlice.actions;
export default calendarSlice;
