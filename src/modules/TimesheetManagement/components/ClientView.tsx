import { AddBox } from "@mui/icons-material";
import { Box, Checkbox, IconButton, Typography } from "@mui/material";
import { useForm } from "@tanstack/react-form";
import { useMutation } from "@tanstack/react-query";
import { format } from "date-fns";
import { MRT_ColumnDef, MRT_GlobalFilterTextField } from "material-react-table";
import React, { useMemo } from "react";
import { FormActions } from "src/modules/Common/CRUDTableV2";
import ContentHeader from "src/modules/Common/ContentHeader/ContentHeader";
import { EmployeeCellInfo } from "src/modules/Common/EmployeeViews/EmployeeCellInfo";
import Modal from "src/modules/Common/Modal/Modal";
import DataTable from "src/modules/Common/Table/DataTable";
import TableActions from "src/modules/Common/Table/TableActions";
import TimesheetTracking, { ClientActionStates, getTitle } from "src/pages/TimesheetManagement";
import { CreateClientRequest } from "src/services/api_definitions/timesheetTracking.service";
import timesheetTrackingService from "src/services/timesheetTracking.service";
import { formatDateToDayMonthYear } from "src/utils/dateUtils";
import { getStatusColors } from "src/utils/typographyUtils";
import { z } from "zod";
import AddEditClients from "./AddEditClients";

type ClientViewProps = {
  clients: TimesheetTracking[];
  isLoading: boolean;
  refetch: () => void;
  selectedRow: TimesheetTracking | null;
  setSelectedRow: React.Dispatch<React.SetStateAction<TimesheetTracking | null>>;
  modalStates: ClientActionStates | null;
  setModalStates: React.Dispatch<React.SetStateAction<ClientActionStates | null>>;
  setSelectedClient: React.Dispatch<React.SetStateAction<TimesheetTracking | null>>;
  setCurrentSelectedView: React.Dispatch<
    React.SetStateAction<ClientActionStates.VIEW_CLIENT | ClientActionStates.VIEW_PROJECT>
  >;
};

const clientSchema = z.object({
  code: z.string().min(1, "Code is required"),
  name: z.string().min(1, "Name is required"),
  contract_start_date: z.date().nullish(),
  contract_end_date: z.date().nullish(),
  internal: z.boolean(),
});

const ClientView: React.FC<ClientViewProps> = ({
  clients,
  isLoading,
  refetch,
  selectedRow,
  setSelectedRow,
  modalStates,
  setModalStates,
  setSelectedClient,
  setCurrentSelectedView,
}) => {
  const defaultClientValues = useMemo((): CreateClientRequest => {
    const row = selectedRow as TimesheetTracking;

    if (row) {
      return {
        code: row.code,
        name: row.name,
        contract_start_date: row.contract_start_date ? new Date(row.contract_start_date) : undefined,
        contract_end_date: row.contract_end_date ? new Date(row.contract_end_date) : undefined,
        internal: row.internal,
      };
    }
    return {
      code: "",
      name: "",
      contract_start_date: undefined,
      contract_end_date: undefined,
      internal: false,
    };
  }, [selectedRow as TimesheetTracking]);

  const onModalClose = () => {
    refetch();
    setSelectedRow(null);
    setModalStates(null);
    clientForm.reset(defaultClientValues);
  };

  const createClientMutation = useMutation({
    mutationFn: async (client: CreateClientRequest) => {
      return timesheetTrackingService.createClient(client);
    },
    onSuccess: () => {
      onModalClose();
    },
  });

  const updateClientMutation = useMutation({
    mutationFn: async (client: CreateClientRequest) => {
      return timesheetTrackingService.updateClient(client);
    },
    onSuccess: () => {
      onModalClose();
    },
  });

  const clientForm = useForm({
    defaultValues: defaultClientValues,
    validators: {
      onChange: clientSchema,
    },
    onSubmit: async ({ value }) => {
      if (modalStates === ClientActionStates.CREATE_CLIENT) {
        await createClientMutation.mutate({
          name: value.name,
          code: value.code,
          contract_start_date: value.contract_start_date
            ? (format(value.contract_start_date as Date, "yyyy-MM-dd") as unknown as Date)
            : undefined,
          contract_end_date: value.contract_end_date
            ? (format(value.contract_end_date, "yyyy-MM-dd") as unknown as Date)
            : undefined,
          internal: value.internal,
        });
      }
      if (modalStates === ClientActionStates.EDIT_CLIENT) {
        await updateClientMutation.mutate({
          name: value.name,
          code: value.code,
          contract_start_date: value.contract_start_date
            ? (format(value.contract_start_date as Date, "yyyy-MM-dd") as unknown as Date)
            : undefined,
          contract_end_date: value.contract_end_date
            ? (format(value.contract_end_date, "yyyy-MM-dd") as unknown as Date)
            : undefined,
          internal: value.internal,
        });
      }
    },
  });

  const clientColumns: MRT_ColumnDef<TimesheetTracking>[] = [
    {
      accessorKey: "name",
      header: "Client Name",
      size: 200,
      Cell: ({ row }) => <EmployeeCellInfo name={row.original.name} jobTitle={row.original.code} hideAvatar />,
      accessorFn: ({ name, code }) => `${name} ${code}`,
      filterFn: "includesString",
    },
    {
      accessorKey: "status",
      header: "Status",
      size: 50,
      Cell: ({ cell }) => (
        <Typography color={getStatusColors(cell.getValue<string>())}>{cell.getValue<string>()}</Typography>
      ),
    },
    {
      accessorKey: "contract_start_date",
      header: "Contract Start Date",
      size: 100,
      Cell: ({ cell }) => formatDateToDayMonthYear(cell.getValue<string>()),
    },
    {
      accessorKey: "contract_end_date",
      header: "Contract End Date",
      size: 100,
      Cell: ({ cell }) => formatDateToDayMonthYear(cell.getValue<string>()),
    },
    {
      accessorKey: "internal",
      header: "Internal",
      size: 100,
      Cell: ({ cell }) => <Checkbox checked={cell.getValue<boolean>()} disabled />,
    },
    {
      accessorKey: "created_by",
      header: "Created By",
      size: 150,
    },
  ];

  return (
    <Box display="flex" flexDirection="column" gap={2}>
      <ContentHeader title="Clients" subtitle="Manage clients" />
      <DataTable
        columns={clientColumns}
        data={clients || []}
        enableEditing
        state={{
          showSkeletons: isLoading,
        }}
        positionActionsColumn="last"
        enableTopToolbar
        renderRowActions={({ row }) => (
          <TableActions
            edit={{
              onClick: () => {
                setModalStates(ClientActionStates.EDIT_CLIENT);
                setSelectedRow(row.original);
              },
            }}
            remove={{ onClick: () => {}, hide: true }}
            view={{
              tooltip: "View Client Projects",
              onClick: () => {
                setSelectedClient(row.original);
                setCurrentSelectedView(ClientActionStates.VIEW_PROJECT);
              },
            }}
          />
        )}
        renderTopToolbar={({ table }) => (
          <Box display="flex" gap={2} p={2}>
            <IconButton onClick={() => setModalStates(ClientActionStates.CREATE_CLIENT)}>
              <AddBox color="primary" fontSize="medium" />
            </IconButton>
            <MRT_GlobalFilterTextField table={table} />
          </Box>
        )}
      />
      <Modal
        title={getTitle(modalStates)}
        isOpen={!!modalStates}
        onClose={onModalClose}
        actions={
          <clientForm.Subscribe
            selector={(state) => [state.canSubmit, state.isSubmitting, state.isPristine, state.isDirty]}
          >
            {([canSubmit, isSubmitting, isPristine, isDirty]) => {
              return (
                <FormActions
                  onCancelClick={onModalClose}
                  onSubmitClick={clientForm.handleSubmit}
                  disabled={
                    !canSubmit ||
                    isPristine ||
                    isSubmitting ||
                    (modalStates === ClientActionStates.EDIT_CLIENT ? !isDirty : undefined)
                  }
                />
              );
            }}
          </clientForm.Subscribe>
        }
      >
        {(modalStates === ClientActionStates.CREATE_CLIENT || modalStates === ClientActionStates.EDIT_CLIENT) && (
          <AddEditClients form={clientForm} isEdit={modalStates === ClientActionStates.EDIT_CLIENT} />
        )}
      </Modal>
    </Box>
  );
};

export default ClientView;
