import { AddBox, VisibilityOff, VisibilityOutlined } from "@mui/icons-material";
import { Box, Checkbox, IconButton } from "@mui/material";
import { useForm } from "@tanstack/react-form";
import { useMutation } from "@tanstack/react-query";
import { format, parse } from "date-fns";
import { MRT_ColumnDef, MRT_GlobalFilterTextField } from "material-react-table";
import React, { useMemo } from "react";
import { queryClient } from "src/app/App";
import { FormActions } from "src/modules/Common/CRUDTableV2/FormActions";
import ContentHeader from "src/modules/Common/ContentHeader/ContentHeader";
import { EmployeeCellInfo } from "src/modules/Common/EmployeeViews/EmployeeCellInfo";
import Modal from "src/modules/Common/Modal/Modal";
import DataTable from "src/modules/Common/Table/DataTable";
import TableActions from "src/modules/Common/Table/TableActions";
import DeleteConfirmationModal from "src/modules/Settings/components/Common/DeleteConfirmationModal";
import TimesheetTracking, { ClientActionStates, getTitle } from "src/pages/TimesheetManagement";
import { CreateProjectRequest, Project } from "src/services/api_definitions/timesheetTracking.service";
import timesheetTrackingService from "src/services/timesheetTracking.service";
import { formatDateToDayMonthYear } from "src/utils/dateUtils";
import { z } from "zod";
import AddEditProjects from "./AddEditProjects";
import AssigneesView from "./AssigneesView";

const projectSchema = z
  .object({
    project_name: z.string().min(1, "Project name is required"),
    project_code: z.string().min(1, "Project code is required"),
    start_date: z.date().nullable().optional(),
    end_date: z.date().nullable().optional(),
    internal: z.boolean().optional(),
    billable: z.boolean().optional(),
    billing_type: z.string().nullable().optional(),
    hourly_rate: z.number().optional(),
    currency_code: z.string().optional(),
    assignees: z
      .array(
        z.object({
          employee_search_code: z.string().optional(),
          employee_code: z.string().min(1, "Employee code is required"),
          hourly_rate: z.number().gte(0),
          currency_code: z.string().min(1, "Currency is required"),
          min_daily_hours_committed: z.date().optional(),
          assigned: z.boolean().default(true),
        }),
      )
      .optional(),
  })
  .superRefine((arg, ctx) => {
    if (!arg.internal) {
      if (!arg.start_date) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: "Start date is required",
          path: ["start_date"],
        });
      }

      if (!arg.end_date) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: "End date is required",
          path: ["end_date"],
        });
      }

      if (arg.start_date && arg.end_date && arg.end_date < arg.start_date) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: "End date should be greater than start date",
          path: ["end_date"],
        });
      }
    }

    if (arg.billable) {
      if (!arg.billing_type) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: "Billing type is required",
          path: ["billing_type"],
        });
      }

      if (arg.billing_type === "Project Level") {
        if (arg.hourly_rate === undefined || arg.hourly_rate === null) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: "Hourly rate is required",
            path: ["hourly_rate"],
          });
        }

        if (!arg.currency_code) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: "Currency is required",
            path: ["currency_code"],
          });
        }
      }

      if (arg.billing_type === "Resource Level") {
        if (arg.assignees?.length === 0) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: "At least one resource is required",
            path: ["assignees"],
          });
        }

        if (!arg.assignees) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: "At least one resource is required",
            path: ["assignees"],
          });
        }
      }
    }
  });

type ProjectForm = z.infer<typeof projectSchema>;

type ProjectViewProps = {
  modalStates: ClientActionStates | null;
  setModalStates: React.Dispatch<React.SetStateAction<ClientActionStates | null>>;
  selectedRow: Project | null;
  setSelectedRow: React.Dispatch<React.SetStateAction<Project | null>>;
  selectedClient: TimesheetTracking;
  setSelectedClient: React.Dispatch<React.SetStateAction<TimesheetTracking | null>>;
  setCurrentSelectedView: React.Dispatch<
    React.SetStateAction<ClientActionStates.VIEW_CLIENT | ClientActionStates.VIEW_PROJECT>
  >;
  refetch: () => void;
  isLoading: boolean;
};

const ProjectView: React.FC<ProjectViewProps> = ({
  modalStates,
  setModalStates,
  selectedRow,
  setSelectedRow,
  selectedClient,
  setSelectedClient,
  setCurrentSelectedView,
  refetch,
  isLoading,
}) => {
  const defaultProjectForm = useMemo((): ProjectForm => {
    const row = selectedRow as Project & {
      client_code: string;
      billing_type: string;
    };

    if (row && row.billing_type === "Project Level") {
      return {
        project_name: row.project_name,
        project_code: row.project_code,
        start_date: row.start_date ? new Date(row.start_date) : undefined,
        end_date: row.end_date ? new Date(row.end_date) : undefined,
        billable: row.billable,
        billing_type: row.billing_type,
        hourly_rate: row.hourly_rate,
        currency_code: row.currency_code,
        assignees: [],
        internal: row.internal,
      };
    }

    if (row) {
      return {
        project_name: row.project_name,
        project_code: row.project_code,
        start_date: row.start_date ? new Date(row.start_date) : undefined,
        end_date: row.end_date ? new Date(row.end_date) : undefined,
        billable: row.billable,
        billing_type: row.billing_type,
        hourly_rate: row.hourly_rate || undefined,
        currency_code: row.currency_code || undefined,
        assignees: row.assignees?.map((assignee) => ({
          ...assignee,
          min_daily_hours_committed: assignee.min_daily_hours_committed
            ? parse(assignee.min_daily_hours_committed, "HH:mm", new Date())
            : undefined,
          assigned: assignee.assigned !== false,
        })),
        internal: row.internal,
      };
    }

    return {
      project_name: "",
      project_code: "",
      start_date: undefined,
      end_date: undefined,
      billable: false,
      billing_type: "",
      internal: false,
      hourly_rate: 0,
      currency_code: undefined,
      assignees: [],
    };
  }, [selectedRow as Project]);

  const onModalClose = () => {
    refetch();
    setModalStates(null);
    setSelectedRow(null);
    queryClient.invalidateQueries(["get-clients"]);
    projectForm.reset();
  };

  const createProjectMutation = useMutation({
    mutationFn: async (project: CreateProjectRequest) => {
      return timesheetTrackingService.createProject(project);
    },
    onSuccess: () => {
      onModalClose();
    },
  });

  const updateProjectMutation = useMutation({
    mutationFn: async (project: CreateProjectRequest) => {
      return timesheetTrackingService.updateProject(project);
    },
    onSuccess: () => {
      onModalClose();
    },
  });

  const deleteProjectMutation = useMutation({
    mutationKey: ["delete-project"],
    mutationFn: async (projectId: string) => {
      return timesheetTrackingService.deleteProject(projectId);
    },
    onSuccess: () => {
      onModalClose();
    },
  });

  const projectForm = useForm({
    defaultValues: defaultProjectForm,
    validators: {
      onChange: projectSchema,
    },
    onSubmit: async ({ value }) => {
      const midnightValue = new Date();
      midnightValue.setHours(0, 0, 0, 0);
      const projectData: CreateProjectRequest = {
        project_name: value.project_name,
        project_code: value.project_code,
        start_date: value.start_date ? format(value.start_date, "yyyy-MM-dd") : undefined,
        end_date: value.end_date ? format(value.end_date, "yyyy-MM-dd") : undefined,
        client_code: selectedClient?.code,
        billable: value.billable,
        billing_type: value.billing_type,
      };

      if (value.billable) {
        if (value.billing_type === "Project Level") {
          projectData.hourly_rate = value.hourly_rate;
          projectData.currency_code = value.currency_code;
          projectData.assignees = undefined;
        } else if (value.billing_type === "Resource Level" && Array.isArray(value.assignees)) {
          projectData.hourly_rate = undefined;
          projectData.currency_code = undefined;
          projectData.assignees = value?.assignees?.map((resource) => ({
            employee_code: resource?.employee_search_code || resource.employee_code,
            hourly_rate: resource.hourly_rate,
            currency_code: resource.currency_code,
            min_daily_hours_committed: resource?.min_daily_hours_committed
              ? format(resource?.min_daily_hours_committed, "HH:mm")
              : undefined,
            assigned: resource.assigned,
          }));
        }
      }
      if (modalStates === ClientActionStates.CREATE_PROJECT) {
        await createProjectMutation.mutate(projectData);
      }
      if (modalStates === ClientActionStates.EDIT_PROJECT) {
        projectData.project_id = (selectedRow as Project)?.project_id;
        await updateProjectMutation.mutate(projectData);
      }
    },
  });

  const projectColumns: MRT_ColumnDef<Project>[] = [
    {
      accessorKey: "project_name",
      accessorFn: ({ project_name, project_code }) => `${project_name} ${project_code}`,
      header: "Project Name",
      size: 150,
      muiTableBodyCellProps: () => ({
        sx: {
          msTextOverflow: "ellipsis",
          textOverflow: "ellipsis",
        },
      }),
      filterFn: "includesString",
      Cell: ({ row }) => (
        <EmployeeCellInfo name={row.original.project_name} jobTitle={row.original.project_code} hideAvatar />
      ),
    },
    {
      accessorKey: "start_date",
      header: "Start Date",
      size: 150,
      Cell: ({ cell }) => formatDateToDayMonthYear(cell.getValue<string>()),
    },
    {
      accessorKey: "end_date",
      header: "End Date",
      size: 150,
      Cell: ({ cell }) => formatDateToDayMonthYear(cell.getValue<string>()),
    },
    {
      accessorKey: "billable",
      header: "Billable",
      size: 100,
      Cell: ({ cell }) => <Checkbox checked={cell.getValue<boolean>()} disabled />,
    },
    {
      accessorKey: "created_by",
      header: "Created By",
      size: 150,
    },
  ];

  const onViewAssigneeClick = (project: Project) => {
    setSelectedRow(project);
  };

  const getProjectFormActions = () => {
    if (modalStates === ClientActionStates.VIEW_ASSIGNEES) {
      return null;
    }
    return (
      <projectForm.Subscribe
        selector={(state) => [state.canSubmit, state.isSubmitting, state.isPristine, state.isDirty, state.errorMap]}
      >
        {([canSubmit, isSubmitting, isPristine, isDirty]) => {
          const isDisabledWhenEditingExistingFields = modalStates === ClientActionStates.EDIT_PROJECT && !isDirty;
          return (
            <FormActions
              onCancelClick={onModalClose}
              onSubmitClick={projectForm.handleSubmit}
              disabled={!canSubmit || isSubmitting || isPristine || isDisabledWhenEditingExistingFields}
            />
          );
        }}
      </projectForm.Subscribe>
    );
  };

  return (
    <Box display="flex" flexDirection="column" gap={2}>
      <ContentHeader
        showBackButton
        goBack={() => {
          setSelectedClient(null);
          setCurrentSelectedView(ClientActionStates.VIEW_CLIENT);
        }}
        title={`Projects for ${selectedClient?.name}`}
        subtitle={`Manage projects for ${selectedClient?.internal ? "Internal" : "External"} client ${selectedClient?.name} (${selectedClient?.code})`}
      />
      <DataTable
        enableTopToolbar
        columns={projectColumns}
        data={selectedClient?.projects || []}
        enableEditing
        positionActionsColumn="last"
        state={{
          showSkeletons: isLoading,
        }}
        renderRowActions={({ row: projectRow }) => {
          return (
            <TableActions
              edit={{
                onClick: () => {
                  setModalStates(ClientActionStates.EDIT_PROJECT);
                  setSelectedRow({
                    ...projectRow.original,
                    internal: selectedClient?.internal,
                  });
                },
              }}
              remove={{
                onClick: () => {
                  setModalStates(ClientActionStates.DELETE_PROJECT);
                  setSelectedRow(projectRow.original);
                },
              }}
              view={{
                tooltip: "View Assignees",
                disabled: projectRow.original.assignees?.length === 0,
                Icon: projectRow.original.assignees?.length === 0 ? VisibilityOff : VisibilityOutlined,
                onClick: () => {
                  setModalStates(ClientActionStates.VIEW_ASSIGNEES);
                  onViewAssigneeClick(projectRow.original);
                },
              }}
            />
          );
        }}
        renderTopToolbar={({ table }) => {
          return (
            <Box display="flex" gap={2} p={2}>
              <IconButton
                onClick={() => {
                  setModalStates(ClientActionStates.CREATE_PROJECT);
                  projectForm.setFieldValue("internal", selectedClient?.internal);
                }}
              >
                <AddBox color="primary" fontSize="medium" />
              </IconButton>
              <MRT_GlobalFilterTextField table={table} />
            </Box>
          );
        }}
      />
      <Modal
        showBackButton
        showDivider
        title={getTitle(modalStates)}
        isOpen={!!modalStates}
        onClose={onModalClose}
        actions={getProjectFormActions()}
      >
        {(modalStates === ClientActionStates.CREATE_PROJECT || modalStates === ClientActionStates.EDIT_PROJECT) && (
          <AddEditProjects form={projectForm} isEdit={modalStates === ClientActionStates.EDIT_PROJECT} />
        )}
        {modalStates === ClientActionStates.VIEW_ASSIGNEES && <AssigneesView selectedRow={selectedRow as Project} />}
      </Modal>
      <DeleteConfirmationModal
        isModalOpen={modalStates === ClientActionStates.DELETE_PROJECT}
        onCancel={onModalClose}
        onDelete={() => deleteProjectMutation.mutate((selectedRow as Project)?.project_id || "")}
        selectedRole={(selectedRow as Project)?.project_name || ""}
        title="Are you sure you want to delete this project?"
      />
    </Box>
  );
};

export default ProjectView;
