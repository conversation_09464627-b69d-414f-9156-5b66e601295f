import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Grid2, Switch } from "@mui/material";
import React from "react";
import CustomDateField from "src/modules/Common/FormInputs/CustomDateField";
import CustomTextField from "src/modules/Common/FormInputs/CustomTextField";

interface Props {
  form: any;
  isEdit: boolean;
}

const AddEditClients: React.FC<Props> = ({ form, isEdit }) => {
  return (
    <Grid2 container spacing={2}>
      <Grid2 size={6}>
        <form.Field name="code">
          {(field) => (
            <CustomTextField
              required
              size="small"
              fullWidth
              title="Client Code"
              disabled={isEdit}
              name={field.name}
              value={field.state.value}
              onChange={(e) => field.handleChange(e.target.value)}
            />
          )}
        </form.Field>
      </Grid2>
      <Grid2 size={6}>
        <form.Field name="name">
          {(field) => (
            <CustomTextField
              required
              size="small"
              fullWidth
              title="Client Name"
              name={field.name}
              value={field.state.value}
              onChange={(e) => field.handleChange(e.target.value)}
            />
          )}
        </form.Field>
      </Grid2>
      <Grid2 size={6}>
        <form.Field name="contract_start_date">
          {(field) => (
            <CustomDateField
              onChange={field.handleChange}
              value={field.state.value}
              slotProps={{
                textField: {
                  fullWidth: true,
                  size: "small",
                },
              }}
              title="Contract Start Date"
              name={field.name}
            />
          )}
        </form.Field>
      </Grid2>
      <Grid2 size={6}>
        <form.Field name="contract_end_date">
          {(field) => (
            <CustomDateField
              value={field.state.value}
              onChange={field.handleChange}
              slotProps={{
                textField: {
                  fullWidth: true,
                  size: "small",
                },
              }}
              title="Contract End Date"
              name={field.name}
            />
          )}
        </form.Field>
      </Grid2>
      <Grid2 size={12}>
        <form.Field name="internal">
          {(field) => (
            <FormControlLabel
              control={
                <Switch
                  disabled={isEdit}
                  title="Internal"
                  name={field.name}
                  checked={field.state.value}
                  onChange={(e) => field.handleChange(e.target.checked)}
                />
              }
              label="Internal"
            />
          )}
        </form.Field>
      </Grid2>
    </Grid2>
  );
};

export default AddEditClients;
