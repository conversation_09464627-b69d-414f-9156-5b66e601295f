import React from "react";
import { EmployeeCellInfo } from "src/modules/Common/EmployeeViews/EmployeeCellInfo";
import DataTable from "src/modules/Common/Table/DataTable";
import { formatCurrency } from "src/modules/Profile/components/EmployeeCompensation/EmployeeCompensation";
import { Project } from "src/services/api_definitions/timesheetTracking.service";
import { convertToHourMinute } from "src/utils/dateUtils";

type AssigneeViewProps = {
  selectedRow: Project;
};

const AssigneesView: React.FC<AssigneeViewProps> = ({ selectedRow }) => {
  return (
    <DataTable
      data={selectedRow?.assignees?.filter((a) => a.assigned) || []}
      columns={[
        {
          accessorKey: "employee_name",
          header: "Employee Name",
          Cell: ({ row }) => (
            <EmployeeCellInfo name={row.original.employee_name} jobTitle={row.original.employee_code} hideAvatar />
          ),
        },
        {
          accessorKey: "hourly_rate",
          header: "Hourly Rate",
          Cell: ({ cell }) => formatCurrency(cell.getValue<number>(), cell.row.original.currency_code),
        },
        {
          accessorKey: "min_daily_hours_committed",
          header: "Min. Daily Commitment",
          size: 200,
          Cell: ({ cell }) => (cell.getValue<string>() ? convertToHourMinute(cell.getValue<string>()) : "—"),
        },
      ]}
    />
  );
};

export default AssigneesView;
