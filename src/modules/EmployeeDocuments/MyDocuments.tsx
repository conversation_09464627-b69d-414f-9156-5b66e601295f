import { Box } from "@mui/material";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import React, { useCallback, useRef, useState } from "react";
import { BaseObject } from "src/app/global";
import { useAppSelector } from "src/customHooks/useAppSelector";
import { useFilePreview } from "src/customHooks/useFilePreview";
import documentsService from "src/services/documents.service";
import fileuploaderService from "src/services/fileuploader.service";
import { getCurrentTenantId } from "src/utils/authUtils";
import { isSomeCustomEmpty } from "src/utils/dataUtils";
import { CRUDTableWrapper, FormActions, TableRowActions } from "../Common/CRUDTableV2";
import Modal from "../Common/Modal/Modal";
import DocumentForm from "../Employees/components/DocumentUpload";
import { CustomInputElement } from "../Employees/types/FormDataTypes";

const containerStyles = { width: "140%", overflowY: "auto", flex: 1, padding: "0 20px" };
const columns = [{ accessorKey: "documentType", header: "Document Type", size: 360 }];

export const MyDocuments = () => {
  const tenantId = getCurrentTenantId();
  const { selectedRole } = useAppSelector((state) => state.userManagement);
  const [openAddModal, setOpenAddModal] = useState(false);
  const [disableSubmit, setDisableSubmit] = useState(true);

  const { handlePreviewClick, documentPreview } = useFilePreview();
  const formActionButton = useRef<CustomInputElement>(null);
  const queryClient = useQueryClient();

  const onFormComplete = async (form: any) => {
    const formKeys = Object.keys(form);
    const formData = formKeys.map((key) => form[key]);
    const dataWithoutEmptyValues = formData.filter((value) => value !== "");
    await fileuploaderService.saveDocument({ formData: dataWithoutEmptyValues });
    queryClient.invalidateQueries(["all-user-roles", selectedRole]);
    setOpenAddModal(false);
  };

  const onFormSubmit = () => {
    formActionButton.current?.next?.();
  };

  const onCancelClick = () => {
    setOpenAddModal(false);
  };

  const { data: allDocuments, isLoading: allDocumentsLoading } = useQuery(
    ["all-user-roles", selectedRole],
    async () => documentsService.getEmployeeDocuments(),
    {
      enabled: !!tenantId,
      retryOnMount: false,
      refetchOnWindowFocus: false,
      retry: false,
    },
  );

  const deafaultResponse =
    allDocuments?.map((document) => ({
      name: document.name,
      documentType: document?.document_type,
    })) || [];

  const defaultFormState: BaseObject[] = deafaultResponse.length ? deafaultResponse : [];

  const handleDownloadConfirmed = async (index: number) => {
    await fileuploaderService.downloadDocumentS3(allDocuments?.[index]?.s3_link as string);
  };

  const getRowActions = useCallback(
    ({ row }: { row: any }) => {
      const s3LinkForPreview = allDocuments?.[row.index]?.s3_link;
      return (
        <TableRowActions
          row={row}
          onDownloadClick={() => handleDownloadConfirmed(row.index)}
          onPreviewClick={() => handlePreviewClick(s3LinkForPreview || null)}
        />
      );
    },
    [allDocuments],
  );

  const allowedMyDocuments = ["PAN", "Aadhaar"]?.filter(
    (document) => !allDocuments?.map((doc) => doc.document_type).includes(document),
  );

  const onFormChange = (form: BaseObject) => {
    if (isSomeCustomEmpty(form)) {
      setDisableSubmit(true);
    } else {
      setDisableSubmit(false);
    }
  };

  return (
    <React.Fragment>
      <CRUDTableWrapper
        isLoading={allDocumentsLoading}
        tableColumns={columns}
        rowData={defaultFormState}
        tableHeaderTitle="My Documents"
        getRowActions={getRowActions}
        allowAdd={allowedMyDocuments.length > 0}
        addButtonText="Add Document"
        onAddButtonClick={() => {
          setOpenAddModal(true);
        }}
      />
      <Modal
        isOpen={openAddModal}
        onClose={() => setOpenAddModal(false)}
        title="Add Document"
        actions={<FormActions disabled={disableSubmit} onSubmitClick={onFormSubmit} onCancelClick={onCancelClick} />}
        maxWidth={"700px"}
      >
        <Box sx={containerStyles}>
          <DocumentForm
            formActionButton={formActionButton}
            isViewOnlyMode={false}
            onFormComplete={onFormComplete}
            myDocuments={true}
            allowedMyDocuments={allowedMyDocuments}
            onFormChange={onFormChange}
          />
        </Box>
      </Modal>
      {documentPreview}
    </React.Fragment>
  );
};
