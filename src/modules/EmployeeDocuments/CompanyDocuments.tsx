import { useQuery } from "@tanstack/react-query";
import React, { useEffect, useMemo, useState } from "react";
import { getCurrentTenantId } from "src/utils/authUtils";

import languageConfig from "src/configs/language/en.lang";
import { useAppSelector } from "src/customHooks/useAppSelector";
import { useFilePreview } from "src/customHooks/useFilePreview";
import { useForm } from "src/customHooks/useForm";
import documentsService from "src/services/documents.service";
import fileuploaderService from "src/services/fileuploader.service";
import roleManagementService from "src/services/roleManagement.service";
import validators from "src/utils/validators";
import {
  CRUDTableWrapper,
  DeleteModalFormWrapper,
  FormActions,
  ModalFormWrapper,
  TableRowActions,
} from "../Common/CRUDTableV2";
import { FormInputType } from "../Employees/types/FormDataTypes";

const { employeeDocuments: employeeDocumentsLang } = languageConfig;

interface FormDetail {
  name: string;
  roles: string[];
  status: string;
  policyFile: { s3_link: string; name: string };
}

const getRequestPayload = (formDetails: any) => {
  return {
    name: formDetails.name,
    roles: formDetails.roles,
    status: formDetails.status,
    s3_link: formDetails?.policyFile?.s3_link,
  };
};

const rowAdditionalData = {
  name: "",
  roles: [],
  status: "",
  policyFile: null,
  rolesString: "",
  hideEdit: false,
  hideDelete: false,
  hideDownload: false,
};

const formValidators = {
  name: [validators.validateInput],
  roles: [],
  policyFile: [validators.validateInput],
  status: [],
  rolesString: [],
  hideEdit: [],
  hideDelete: [],
  hideDownload: [],
};

export const uploadPolicyDocument = async (acceptedFiles: File[], documentType: string) => {
  const formData = new FormData();
  formData.append("file", acceptedFiles[0]);
  formData.append("key", documentType);
  const url = "/document/policy/upload";
  const result = await fileuploaderService.uploadFile(url, formData);
  if (result.message) {
    const newFileName = (result.message as string).split("/").pop();
    const document = {
      name: newFileName || "",
      s3_link: result.message,
      document_type: documentType,
    };
    return document;
  }
  return null;
};

export const CompanyDocuments = () => {
  const tenantId = getCurrentTenantId();
  const { selectedRole, authorisedScreens } = useAppSelector((state) => state.userManagement);
  const allowedRoles = authorisedScreens.filter((screen) => screen.key === "documents");
  const allowEditDocuments = allowedRoles?.[0]?.acl?.canWrite || false;
  const [selectedRow, setSelectedRow] = useState<number | null>(null);
  const [openDeleteModal, setOpenDeleteModal] = useState<boolean>(false);
  const [openEditModal, setOpenEditModal] = useState<boolean>(false);
  const [openAddModal, setOpenAddModal] = useState<boolean>(false);
  const { handlePreviewClick, documentPreview } = useFilePreview();

  const {
    data: allDocuments,
    isLoading: allDocumentsLoading,
    refetch: refetchAllDocuments,
  } = useQuery(["all-company-documents", selectedRole], async () => documentsService.getCompanyDocuments(), {
    enabled: !!tenantId,
    retryOnMount: false,
    refetchOnWindowFocus: false,
    retry: false,
  });

  const { data: allRoles } = useQuery(["user-roles"], roleManagementService.getUserRoles, {
    enabled: !!tenantId && allowEditDocuments,
    refetchOnMount: true,
    refetchOnWindowFocus: false,
  });
  const roleOptions = allRoles?.map((role) => ({ value: role?.name, label: role?.name })) || [];
  // const roleOptions = allRoles?.map((role) => ({ value: role, label: role })) || [];

  const deafaultResponse = useMemo(
    () =>
      allDocuments?.map((document) => ({
        name: document.name,
        status: document.status || "",
        policyFile: { s3_link: document.s3_link, name: document.name },
        roles: document?.roles || [],
        rolesString: document?.roles?.join(", ") || "",
        hideEdit: !document.owner || !allowEditDocuments,
        hideDelete: !document.owner || !allowEditDocuments,
        hideDownload: false,
      })) || [],
    [allDocuments],
  );
  const selectedRowData = useMemo(
    () => (selectedRow !== null ? deafaultResponse?.[selectedRow] : rowAdditionalData),
    [selectedRow],
  );
  const { formDetails, formErrors, setFormDetail, areFormDetailsValid } = useForm({
    initialState: selectedRowData,
    validations: formValidators,
  });

  const postFormSubmit = () => {
    refetchAllDocuments();
    setOpenAddModal(false);
    setOpenEditModal(false);
    setOpenDeleteModal(false);
  };

  const handleEditDetailsClick = async () => {
    const parsedData = getRequestPayload(formDetails);
    const requestObject = { ...parsedData, status: "Published" };
    await documentsService.updateCompanyDocument(requestObject);
    postFormSubmit();
  };

  const handleAddDetailsClick = async () => {
    const parsedData = getRequestPayload(formDetails as FormDetail);
    const requestObject = {
      ...parsedData,
      status: "Published",
    };
    await documentsService.createCompanyDocument(requestObject);
    postFormSubmit();
  };

  const handleEditDraftDetailsClick = async () => {
    const parsedData = getRequestPayload(formDetails as FormDetail);
    const requestObject = { ...parsedData, status: "Draft" };
    await documentsService.updateCompanyDocument(requestObject);
    postFormSubmit();
  };

  const handleAddDraftDetailsClick = async () => {
    const parsedData = getRequestPayload(formDetails as FormDetail);
    const requestObject = {
      ...parsedData,
      status: "Draft",
    };
    await documentsService.createCompanyDocument(requestObject);
    postFormSubmit();
  };

  const handleDeleteConfirmed = async () => {
    await documentsService.deleteCompanyDocument({ name: selectedRowData?.name });
    postFormSubmit();
  };

  const inputElements: FormInputType[] = [
    {
      name: "name",
      label: "Document Name",
      variant: "text",
      placeholder: "Document Name",
      isRequired: true,
      xs: 6,
    },
    {
      name: "roles",
      label: "Role",
      variant: "multi-select",
      placeholder: "Role",
      isRequired: true,
      xs: 6,
    },
    {
      name: "policyFile",
      label: employeeDocumentsLang.documentType,
      variant: "file",
      placeholder: employeeDocumentsLang.documentType,
      isRequired: true,
      xs: 12,
    },
  ];

  const handleDownloadConfirmed = async (row: any) => {
    await fileuploaderService.downloadDocumentS3(deafaultResponse?.[row?.id]?.policyFile?.s3_link || "");
  };

  useEffect(() => {
    if (!openAddModal && !openEditModal && !openDeleteModal) {
      setSelectedRow(null);
    }
  }, [openAddModal, openEditModal, openDeleteModal]);

  const columns = allowEditDocuments
    ? [
        { accessorKey: "name", header: "Document Name" },
        { accessorKey: "rolesString", header: "Roles" },
        { accessorKey: "status", header: "Status" },
      ]
    : [{ accessorKey: "name", header: "Document Name", size: 580 }];

  const selectOptions = { roles: roleOptions };

  const onFileChange = async (acceptedFiles: File[], name: string, index: number) => {
    const document = await uploadPolicyDocument(acceptedFiles, name);
    if (document) {
      setFormDetail("policyFile", document, index);
    }
  };

  const onChange = (name: string, value: unknown) => {
    if (name === "policyFile") {
      if (value) {
        const fileName = (formDetails as FormDetail)?.name;
        onFileChange(value as File[], fileName, 0);
      } else {
        setFormDetail(name, value);
      }
    } else {
      setFormDetail(name, value);
    }
  };

  const getFormActions = ({ row }: { row: any }) => {
    return (
      <TableRowActions
        row={row}
        onDeleteClick={() => {
          setOpenDeleteModal(true);
          setSelectedRow(row?.id);
        }}
        onEditClicked={() => {
          setOpenEditModal(true);
          setSelectedRow(row?.id);
        }}
        onDownloadClick={handleDownloadConfirmed}
        onPreviewClick={() => handlePreviewClick(row?.original?.policyFile?.s3_link)}
      />
    );
  };

  const disabledInputFields = {
    policyFile: !(formDetails as FormDetail)?.name,
    name: openEditModal,
  };
  const editFormActions = (
    <FormActions
      disabled={!areFormDetailsValid}
      onSubmitClick={handleEditDetailsClick}
      onCancelClick={handleEditDraftDetailsClick}
      submitButtonText="Publish"
      cancelButtonText="Save as Draft"
    />
  );
  const addFormActions = (
    <FormActions
      disabled={!areFormDetailsValid}
      onSubmitClick={handleAddDetailsClick}
      onCancelClick={handleAddDraftDetailsClick}
      submitButtonText="Publish"
      cancelButtonText="Save as Draft"
    />
  );

  return (
    <React.Fragment>
      <CRUDTableWrapper
        isLoading={allDocumentsLoading}
        rowData={deafaultResponse}
        tableColumns={columns}
        getRowActions={getFormActions}
        tableHeaderTitle="Company Documents"
        addButtonText="Add Document"
        onAddButtonClick={() => {
          setOpenAddModal(true);
        }}
        allowAdd={allowEditDocuments}
      />
      <DeleteModalFormWrapper
        deleteModalOpen={openDeleteModal}
        setDeleteModalOpen={setOpenDeleteModal}
        onDeleteSubmit={handleDeleteConfirmed}
        question={`Are you sure you want to delete this document ${selectedRowData?.name}?`}
        formTitle="Delete Document"
        nextButtonText="Delete"
      />
      <ModalFormWrapper
        modalConfig={{ isOpen: openEditModal, setIsOpen: setOpenEditModal, formTitle: "Edit Document" }}
        inputElements={inputElements}
        onChange={onChange}
        selectOptions={selectOptions}
        formValues={formDetails as Record<string, unknown>}
        formErrors={formErrors as Record<string, string>}
        disabledInputFields={disabledInputFields}
        isViewOnlyMode={false}
        onSubmitClick={handleEditDetailsClick}
        formActions={editFormActions}
      />
      <ModalFormWrapper
        modalConfig={{ isOpen: openAddModal, setIsOpen: setOpenAddModal, formTitle: "Add Document" }}
        inputElements={inputElements}
        onChange={onChange}
        selectOptions={selectOptions}
        formValues={formDetails as Record<string, unknown>}
        formErrors={formErrors as Record<string, string>}
        disabledInputFields={disabledInputFields}
        isViewOnlyMode={false}
        onSubmitClick={handleAddDetailsClick}
        formActions={addFormActions}
      />
      {documentPreview}
    </React.Fragment>
  );
};
