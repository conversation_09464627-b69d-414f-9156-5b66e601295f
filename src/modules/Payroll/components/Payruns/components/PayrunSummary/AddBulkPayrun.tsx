import { Close } from "@mui/icons-material";
import { Box, Button, Chip, Paper, Typography } from "@mui/material";
import { useStore } from "@tanstack/react-form";
import React from "react";
import { useAllCompensations } from "src/customHooks/useAllCompensations";
import { useAppForm } from "src/modules/Common/Form/effiFormContext";
import Modal from "src/modules/Common/Modal/Modal";
import { CompensationComponent, PayRunDetail } from "src/services/api_definitions/payroll.service";
import { z } from "zod";

interface AddBulkPayrunProps {
  isModalOpen: boolean;
  onClose: () => void;
  selectedRows: PayRunDetail[];
  allCompensations?: CompensationComponent[];
}

const bulkSupplementaryPaySchema = z.object({
  supplementaryPay: z.boolean(),
  supplementaryPayCalculationType: z.enum(["Earning", "Deduction"]),
  payType: z.string().optional().nullable(),
  calculationType: z.enum(["Flat", "Percentage"]),
  amount: z.number(),
  percentageOf: z.string().optional().nullable(),
});

const AddBulkPayrun: React.FC<AddBulkPayrunProps> = ({ isModalOpen, onClose, selectedRows }) => {
  if (!isModalOpen) return null;

  const { data: allCompensations, isLoading } = useAllCompensations({
    country: "India",
    active: true,
    employeeTypes: selectedRows?.map((eachRow) => eachRow.employee.employee_type).join(","),
  });

  const earnings = allCompensations
    ?.filter((eachCompensation) => eachCompensation?.component_type === "Earning" && eachCompensation?.active)
    ?.map((eachCompensation) => ({
      label: eachCompensation.name,
      value: eachCompensation.name,
    }));
  const deductions = allCompensations
    ?.filter((eachCompensation) => eachCompensation?.component_type === "Deduction" && eachCompensation?.active)
    ?.map((eachCompensation) => ({
      label: eachCompensation.name,
      value: eachCompensation.name,
    }));

  const form = useAppForm({
    defaultValues: {
      supplementaryPay: false,
      supplementaryPayCalculationType: "Earning",
      payType: "",
      calculationType: "Flat",
      amount: 0,
      percentageOf: null,
    },
    validators: {
      onSubmit: bulkSupplementaryPaySchema as any,
    },
    onSubmit: (props) => {
      console.log(props);
    },
  });
  const { supplementaryPayCalculationType, supplementaryPay, calculationType } = useStore(
    form.store,
    (state) => state.values,
  );

  const [selectedChips, setSelectedChips] = React.useState<any[]>([...selectedRows]);
  const onDelete = (ev: React.ChangeEvent, index: number) => {
    ev.preventDefault();
    ev.stopPropagation();
    setSelectedChips((prevChips) => {
      const elementToRemove = prevChips[index];
      const newChips = [...prevChips].filter((chip) => chip !== elementToRemove);
      return newChips;
    });
  };

  return (
    <Modal
      isOpen={isModalOpen}
      title="Add Bulk Payrun"
      onClose={onClose}
      showBackButton
      showDivider
      fullwidth
      actions={
        <Box display="flex" p={2} gap={1} justifyContent="flex-end">
          <Button variant="outlined" onClick={() => onClose()}>
            Cancel
          </Button>
          <Button variant="contained" type="submit" onClick={form.handleSubmit}>
            Save
          </Button>
        </Box>
      }
    >
      <Box display="flex" flexDirection="column" gap={1}>
        <Box width="100%" display="flex" gap={2}>
          {selectedChips?.map((eachChip, index) => (
            <Chip
              key={eachChip.id}
              deleteIcon={selectedRows?.length === 1 ? <></> : <Close />}
              onDelete={(ev) => onDelete(ev, index)}
              sx={{ background: "#E6F2F1", borderRadius: 2, minWidth: 120 }}
              label={eachChip.id}
            />
          ))}
        </Box>
        <form.AppField name="supplementaryPay">
          {(field: any) => <field.EffiSwitch label="Supplementary Pay" />}
        </form.AppField>
        <form.AppField name="supplementaryPayCalculationType">
          {(field: any) => (
            <field.EffiRadioGroup
              label="Supplementary Pay Calculation Type"
              layout="horizontal"
              disabled={!supplementaryPay}
              options={[
                { label: "Earning", value: "Earning" },
                { label: "Deduction", value: "Deduction" },
              ]}
            />
          )}
        </form.AppField>
        <form.AppField name="payType">
          {(field: any) => (
            <field.EffiSelect
              label={supplementaryPayCalculationType === "Earning" ? "Earning Type" : "Deduction Type"}
              disabled={isLoading || !supplementaryPay}
              options={supplementaryPayCalculationType === "Earning" ? earnings : deductions}
            />
          )}
        </form.AppField>
        <Box component={Paper} elevation={2} p={2} display="flex" flexDirection="column" gap={2}>
          <form.AppField name="calculationType">
            {(field: any) => (
              <field.EffiRadioGroup
                label="Calculation Type"
                layout="horizontal"
                disabled={!supplementaryPay}
                options={[
                  { label: "Flat Amount", value: "Flat" },
                  { label: "Percentage", value: "Percentage" },
                ]}
              />
            )}
          </form.AppField>
          <Box display="flex" gap={1}>
            <form.AppField name="amount">
              {(field: any) => {
                if (calculationType === "Percentage") {
                  return (
                    <field.EffiPercentageField label="" required placeholder="Enter percentage" sx={{ width: 100 }} />
                  );
                }
                return <field.EffiCurrency label="" required currency="INR" placeholder="Enter amount" />;
              }}
            </form.AppField>
            {calculationType === "Percentage" && <Typography alignSelf="center">of</Typography>}
            {calculationType === "Percentage" && (
              <form.AppField name="percentageOf">
                {(field: any) => (
                  <field.EffiSelect
                    label=""
                    required
                    options={supplementaryPayCalculationType === "Earning" ? earnings : deductions}
                    placeholder="Select component"
                    sx={{ width: 200 }}
                  />
                )}
              </form.AppField>
            )}
          </Box>
        </Box>
      </Box>
    </Modal>
  );
};

export default AddBulkPayrun;
