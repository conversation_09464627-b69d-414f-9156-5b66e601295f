import { Box } from "@mui/material";
import React from "react";
import EffiSummaryCard from "src/modules/Common/SummaryCard/SummaryCard";
import TrendsSummaryCard from "src/modules/Common/SummaryCard/TrendsSummaryCard";

export type TSummaryDetail<T> = {
  // recommended
  title: string;
  value: string | number | boolean | unknown | null;
  type: "trend" | "summary" | "base";
  format: "currency" | "number";
  key: keyof T;
  // optional
  trend?: string | number | null;
  trendLabel?: string;
  isPositive?: boolean;
  actions?: React.ReactNode;
  subBaseActions?: (value: string | number) => React.ReactNode;
  formatter?: (value: string | number) => string;
};

interface SummaryDetailsProps<T, X> {
  baseSummaries: TSummaryDetail<T>[];
  subBaseSummaries: TSummaryDetail<X>[];
  isMasked?: boolean;
}

const SummaryDetails = <T, X>({
  baseSummaries = [],
  subBaseSummaries = [],
  isMasked = false,
  ...otherProps
}: SummaryDetailsProps<T, X>) => {
  const colors = ["#CEF2E9", "#D7EDFF", "#DAD4FF", "#FFE2EB", "#FFE4C1"]; // Example palette

  return (
    <Box display="flex" flexDirection="column" gap={2}>
      <Box display="flex" flexDirection="row" flexGrow="grow" gap={2} width="100%" overflow="auto">
        {baseSummaries.map((item) => {
          if (item.type === "trend") {
            return (
              <TrendsSummaryCard
                key={item.title}
                title={item.title}
                value={item?.value}
                trend={item?.trend || null}
                isPositive={item.isPositive}
                trendLabel={item?.trendLabel || null}
                {...otherProps}
              />
            );
          }
          if (item.type === "summary") {
            return (
              <EffiSummaryCard
                key={item.title}
                title={item.title}
                value={item.value}
                actions={item.actions}
                {...otherProps}
              />
            );
          }

          if (item.type === "base") {
            return (
              <EffiSummaryCard
                cardProps={{
                  sx: { backgroundColor: colors[baseSummaries.indexOf(item) % colors.length] },
                }}
                isMasked={isMasked}
                key={item.title}
                title={item.title}
                value={item.value as string}
                subBaseActions={item?.subBaseActions}
                {...otherProps}
              />
            );
          }
          return null;
        })}
      </Box>
      <Box display="flex" flexDirection="row" flexGrow="grow" gap={2} width="100%" overflow="auto">
        {subBaseSummaries.map((item) => {
          if (item.type === "base") {
            return (
              <EffiSummaryCard
                cardProps={{
                  sx: {
                    backgroundColor: colors[subBaseSummaries.indexOf(item) % colors.length],
                  },
                }}
                isMasked={isMasked}
                key={item.title}
                title={item.title}
                value={item.value as string}
                subBaseActions={item?.subBaseActions}
                {...otherProps}
              />
            );
          }
          return null;
        })}
      </Box>
    </Box>
  );
};
export default SummaryDetails;
