import { CopyAll } from "@mui/icons-material";
import { Box, Chip, IconButton } from "@mui/material";
import React from "react";

const PayrunDetailSummary: React.FC = () => {
  return (
    <Box display="flex" flexDirection="row" gap={2}>
      <Chip
        icon={
          <IconButton
            onCopy={() => {
              navigator.clipboard.writeText("Payrun Name");
            }}
          >
            <CopyAll />
          </IconButton>
        }
        variant="filled"
        color="default"
        label="Payrun Name"
      />
    </Box>
  );
};

export default PayrunDetailSummary;
