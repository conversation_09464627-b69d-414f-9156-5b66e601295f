import { DownloadForOfflineOutlined } from "@mui/icons-material";
import { Box, IconButton } from "@mui/material";
import { useMutation, useQuery } from "@tanstack/react-query";
import React, { useEffect, useMemo } from "react";
import { queryClient } from "src/app/App";
import { FROM_LAST_MONTH } from "src/app/constants";
import { useAppDispatch } from "src/customHooks/useAppDispatch";
import SplitButton from "src/modules/Common/Buttons/SplitButton";
import ContentHeader from "src/modules/Common/ContentHeader/ContentHeader";
import { Aggregates } from "src/services/api_definitions/payroll.service";
import payrollService from "src/services/payroll.service";
import { setFullviewMode } from "src/store/slices/app.slice";
import { enrichBaseSummaries } from "../PayrunSummary";
import SummaryDetails, { TSummaryDetail } from "../PayrunSummary/SummaryDetails";
import AllExcludedPayrunEmployees from "./AllExcludedPayrunEmployees";

export const summaryConfig: TSummaryDetail<Partial<Aggregates>>[] = [
  {
    title: "Payroll Cost",
    value: "",
    trend: "10%",
    isPositive: true,
    trendLabel: FROM_LAST_MONTH,
    type: "base",
    key: "total_gross_pay",
    format: "currency",
  },
  {
    title: "Employees Net Pay",
    value: "",
    trend: "10%",
    isPositive: false,
    trendLabel: FROM_LAST_MONTH,
    type: "base",
    key: "total_net_pay",
    format: "currency",
  },
  {
    title: "Active Employees",
    value: "",
    type: "base",
    trendLabel: FROM_LAST_MONTH,
    key: "active_employees",
    format: "number",
  },
];

const baseConfig: TSummaryDetail<Partial<Aggregates>>[] = [
  {
    title: "Earnings",
    value: 0,
    type: "base",
    format: "currency",
    key: "total_earnings",
  },
  {
    title: "Statutory Deductions",
    value: 0,
    type: "base",
    key: "total_statutory_deductions",
    format: "currency",
  },
  {
    title: "Total TDS",
    value: 0,
    type: "base",
    key: "total_taxes",
    format: "currency",
  },
];

const AllExcludedPayruns: React.FC<{
  onClose: () => void;
}> = ({ onClose }) => {
  const [selectedRows, setSelectedRows] = React.useState<Record<number, boolean>>({});
  const dispatch = useAppDispatch();

  const {
    data: excludedEmployees,
    isLoading,
    isFetched,
    refetch,
  } = useQuery({
    queryKey: ["get-excluded-payments"],
    queryFn: async () => payrollService.getExcludedPayments(),
    refetchOnMount: true,
    refetchOnWindowFocus: false,
  });

  const updateOffcyclePayrun = useMutation({
    mutationKey: ["update-off-cycle-payrun"],
    mutationFn: async (payrunIds: string[]) => payrollService.updatePayrun("Off Cycle", payrunIds),
    onSuccess: () => {
      refetch();
      setSelectedRows({});
      queryClient.invalidateQueries({ queryKey: ["get-payruns"] });
      queryClient.invalidateQueries({ queryKey: ["get-adhoc-payruns"] });
    },
  });

  const selectedExcludedEmployees = useMemo(() => {
    return Object.keys(selectedRows)
      .map(Number)
      .map((key) => excludedEmployees?.employee_pay_runs?.[key as any]);
  }, [selectedRows, excludedEmployees]);

  useEffect(() => {
    dispatch(setFullviewMode(true));
  }, []);

  return (
    <Box display="flex" flexDirection="column" gap={2}>
      <ContentHeader title="Excluded Employees" showBackButton goBack={onClose} />
      <Box>
        <SummaryDetails
          baseSummaries={enrichBaseSummaries(excludedEmployees as any, summaryConfig)}
          subBaseSummaries={enrichBaseSummaries(excludedEmployees as any, baseConfig)}
        />
      </Box>
      <ContentHeader
        title="Employee Details"
        actions={
          <Box display="flex" alignItems="center" gap={1}>
            <IconButton color="primary" size="large">
              <DownloadForOfflineOutlined color="primary" fontSize="large" />
            </IconButton>
            <SplitButton
              disabled={!Object.values(selectedRows).some(Boolean)}
              options={[
                {
                  id: "add-offcycle-payrun",
                  title: "Off-Cycle Pay Run",
                  onClick: () =>
                    updateOffcyclePayrun.mutate(selectedExcludedEmployees.map((eachEmployee) => eachEmployee.id)),
                  disabled: !Object.values(selectedRows).some(Boolean),
                },
              ]}
            />
          </Box>
        }
      />
      <AllExcludedPayrunEmployees
        payRunDetails={excludedEmployees?.employee_pay_runs}
        isLoading={isLoading}
        isFetched={isFetched}
        selectedRows={selectedRows}
        setSelectedRows={setSelectedRows}
      />
    </Box>
  );
};

export default AllExcludedPayruns;
