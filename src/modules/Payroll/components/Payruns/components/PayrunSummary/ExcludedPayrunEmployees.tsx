import { InfoOutlined, PersonAdd } from "@mui/icons-material";
import { Box, Tooltip, Typography } from "@mui/material";
import { MRT_GlobalFilterTextField } from "material-react-table";
import React from "react";
import { EmployeeCellInfo } from "src/modules/Common/EmployeeViews/EmployeeCellInfo";
import DataTable from "src/modules/Common/Table/DataTable";
import TableActions from "src/modules/Common/Table/TableActions";
import { formatCurrency } from "src/modules/Profile/components/EmployeeCompensation/EmployeeCompensation";
import { PayrunEmployeeDetail } from "src/services/api_definitions/payroll.service";
import ReadOnlyEmployeePayrunView from "../Common/ReadOnlyEmployeePayrunView";

const ExcludedPayrunEmployees: React.FC<{
  payRunDetails: PayrunEmployeeDetail[] | null;
  isLoading: boolean;
  isFetched: boolean;
  onIncludeEmployee: (ids: string[]) => void;
  selectedRows: Record<number, boolean>;
  setSelectedRows: React.Dispatch<React.SetStateAction<Record<number, boolean>>>;
  hideActions?: boolean;
}> = ({ payRunDetails, isLoading, isFetched, onIncludeEmployee, selectedRows, setSelectedRows, hideActions }) => {
  const [employeeDetails, setEmployeeDetails] = React.useState<PayrunEmployeeDetail | null>(null);
  return (
    <>
      {employeeDetails && (
        <ReadOnlyEmployeePayrunView
          employeeToEdit={employeeDetails}
          onClose={() => setEmployeeDetails(null)}
          isOpen={!!employeeDetails}
        />
      )}
      <DataTable
        onRowSelectionChange={setSelectedRows}
        enableRowActions={!hideActions}
        positionActionsColumn="last"
        enableTopToolbar
        renderTopToolbar={({ table }) => (
          <Box p={2}>
            <MRT_GlobalFilterTextField table={table} />
          </Box>
        )}
        displayColumnDefOptions={{
          "mrt-row-actions": {
            size: 200,
            maxSize: 200,
            header: "",
            muiTableBodyCellProps: {
              align: "right",
            },
            enablePinning: true,
          },
        }}
        renderRowActions={({ row }) => (
          <TableActions
            key={row.id}
            edit={{
              onClick: () => {},
              hide: true,
            }}
            remove={{
              onClick: () => {},
              hide: true,
            }}
            view={{
              onClick: () => {},
              hide: true,
            }}
            renderCustomActions={() => [
              {
                title: "Include",
                Icon: PersonAdd,
                tooltip: "Include",
                onClick: () => onIncludeEmployee([row.original.id]),
              },
            ]}
          />
        )}
        data={payRunDetails || []}
        columns={[
          {
            accessorKey: "employee.display_name",
            header: "Employee",
            Cell: ({ row }) => (
              <Box display="flex" gap={1} alignItems="center">
                <EmployeeCellInfo
                  name={row.original?.employee?.display_name as string}
                  jobTitle={row.original.employee?.job_title}
                  displayPic={row.original.employee?.display_pic}
                  onLinkClick={() => setEmployeeDetails(row.original)}
                />
                {!!row.original?.exclusion_reason && (
                  <Tooltip title={row.original.exclusion_reason} placement="top" arrow>
                    <InfoOutlined fontSize="small" color="action" />
                  </Tooltip>
                )}
              </Box>
            ),
          },
          {
            accessorKey: "paid_days",
            header: "Paid Days",
          },
          {
            accessorKey: "gross_pay",
            header: "Gross Pay",
            Cell: ({ row }) => formatCurrency(row?.original?.gross_pay),
          },
          {
            accessorKey: "earnings",
            header: "Earnings",
            Cell: ({ row }) => formatCurrency(row?.original?.earnings),
          },
          {
            accessorKey: "deductions",
            header: "Deductions",
            Cell: ({ row }) => formatCurrency(row?.original?.deductions),
          },
          {
            accessorKey: "tax_amount",
            header: "TDS",
            Cell: ({ row }) => formatCurrency(row?.original?.tax_amount),
          },
          {
            accessorKey: "net_pay",
            header: "Net Amount",
            Cell: ({ row }) => formatCurrency(row?.original?.net_pay),
          },
        ]}
        enableRowSelection
        enableMultiRowSelection
        state={{
          showSkeletons: isLoading || !isFetched,
          rowSelection: selectedRows,
          columnPinning: {
            right: ["mrt-row-actions"],
          },
        }}
      />
    </>
  );
};

export default ExcludedPayrunEmployees;
