import { Box, Divider } from "@mui/material";
import React, { useEffect, useState } from "react";
import { useNavigate, useSearchParams } from "react-router-dom";
import { useAppSelector } from "src/customHooks/useAppSelector";
import ContentHeader from "src/modules/Common/ContentHeader/ContentHeader";
import TabsView from "src/modules/Common/CustomTabs/CustomTabs";
import AdHocPayRuns from "./AdHocPayRuns";
import PayrunHistory from "./PayrunHistory";
import PayrunSummary from "./PayrunSummary";

const tabs = [
  {
    label: "Regular Pay Runs",
    component: <PayrunSummary />,
    id: 0,
  },
  {
    label: "Ad Hoc Pay Runs",
    component: <AdHocPayRuns />,
    id: 1,
  },
  {
    label: "History",
    component: <PayrunHistory />,
    id: 2,
  },
];

const Payruns = () => {
  const { isFullView } = useAppSelector((state) => state.app);
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const tabParam = searchParams.get("tab");

  const [currentTabIndex, setCurrentTabIndex] = useState<number>(
    Number.isNaN(parseInt(tabParam || ""))
      ? 0
      : Math.min(Math.max(parseInt(tabParam as string, 10), 0), tabs.length - 1),
  );

  useEffect(() => {
    const tab = searchParams.get("tab");
    if (tab !== null) {
      const parsed = parseInt(tab, 10);
      if (!Number.isNaN(parsed)) {
        setCurrentTabIndex(Math.min(Math.max(parsed, 0), tabs.length - 1));
      }
    }
  }, [searchParams]);

  const handleTabChange = (index: number) => {
    setCurrentTabIndex(index);
    const params = new URLSearchParams(searchParams);
    params.set("tab", String(index));
    // preserve outer Payroll tabId (Pay Runs) and just update inner tab in current route
    navigate({ search: `?${params.toString()}` }, { replace: true });
  };

  return (
    <Box display="flex" flexDirection="column" gap={1}>
      {!isFullView && <ContentHeader title="Pay Runs" />}
      {!isFullView && <Divider orientation="horizontal" />}
      <TabsView
        tabs={tabs}
        hideTabBar={isFullView}
        currentTabIndex={currentTabIndex}
        handleTabChange={handleTabChange}
      />
    </Box>
  );
};

export default Payruns;
