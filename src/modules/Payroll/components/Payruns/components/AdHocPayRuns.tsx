import { Box, Button } from "@mui/material";
import { useQuery } from "@tanstack/react-query";
import React, { useState } from "react";
import { useAppSelector } from "src/customHooks/useAppSelector";
import ContentHeader from "src/modules/Common/ContentHeader/ContentHeader";
import NoDataWithAction from "src/modules/Common/NoDataWithAction/NoDataWithAction";
import payrollService from "src/services/payroll.service";
import PayScheduleModal from "./Common/PayScheduleModal";
import PayrunDetails from "./PayrunSummary/PayrunDetails";

const AdHocPayRuns = () => {
  const [isExcludedEmployeesView, setIsExcludedEmployeesView] = React.useState(false);
  const [isPayScheduleModalOpen, setIsPayScheduleModalOpen] = useState(false);
  const { isFullView } = useAppSelector((state) => state.app);

  const { data, refetch } = useQuery({
    queryKey: ["get-adhoc-payruns"],
    queryFn: async () => payrollService.getAdhocPayruns(),
    refetchOnMount: true,
    refetchOnWindowFocus: false,
  });

  // Handle both possible API response structures
  const payrunData = Array.isArray(data) ? data : data?.pay_runs || [];
  const hasPayruns = payrunData && payrunData.length > 0;

  // Debug: Log the data structure to understand what the API returns
  React.useEffect(() => {
    if (data) {
      console.log("AdHoc Payrun API Response:", data);
      console.log("Processed payrun data:", payrunData);
      if (payrunData.length > 0) {
        console.log("First payrun item:", payrunData[0]);
        console.log("Comments in first payrun:", payrunData[0]?.comments);
      }
    }
  }, [data, payrunData]);

  if (!hasPayruns) {
    return (
      <Box display="flex" flexDirection="column" gap={2}>
        <NoDataWithAction
          title="No ad hoc pay run data available. Use the button below to create one."
          actionText="Create"
          onActionClick={() => setIsPayScheduleModalOpen(true)}
        />
        <PayScheduleModal
          isOpen={isPayScheduleModalOpen}
          onClose={() => setIsPayScheduleModalOpen(false)}
          isAdHocPayrunFlow
        />
      </Box>
    );
  }

  return (
    <Box display="flex" flexDirection="column" gap={2}>
      {!isFullView && (
        <ContentHeader
          title=""
          actions={
            <Button variant="contained" color="primary" onClick={() => setIsPayScheduleModalOpen(true)}>
              Create
            </Button>
          }
        />
      )}
      <PayrunDetails
        payruns={payrunData}
        isAdHocPayrunFlow
        refetch={refetch}
        isExcludedEmployeesView={isExcludedEmployeesView}
        setIsExcludedEmployeesView={setIsExcludedEmployeesView}
        isMasked={false}
      />
      <PayScheduleModal
        isOpen={isPayScheduleModalOpen}
        onClose={() => setIsPayScheduleModalOpen(false)}
        isAdHocPayrunFlow
      />
    </Box>
  );
};

export default AdHocPayRuns;
