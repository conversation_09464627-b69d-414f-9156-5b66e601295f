import { Visibility, VisibilityOff } from "@mui/icons-material";
import { Box, Button, IconButton, Link, Tooltip, Typography } from "@mui/material";
import { useQuery } from "@tanstack/react-query";
import React, { useState } from "react";
import { useAppSelector } from "src/customHooks/useAppSelector";
import ContentHeader from "src/modules/Common/ContentHeader/ContentHeader";
import NoDataWithAction from "src/modules/Common/NoDataWithAction/NoDataWithAction";
import { formatCurrency } from "src/modules/Profile/components/EmployeeCompensation/EmployeeCompensation";
import { Aggregates, PayRunDetail, Payrun } from "src/services/api_definitions/payroll.service";
import payrollService from "src/services/payroll.service";
import PayScheduleModal from "./Common/PayScheduleModal";
import PayrunDetails from "./PayrunSummary/PayrunDetails";
import SummaryDetails, { TSummaryDetail } from "./PayrunSummary/SummaryDetails";

export const summaryDetailsConfig: TSummaryDetail<Partial<Aggregates>>[] = [
  {
    title: "Total Cost",
    value: 23123,
    type: "base",
    key: "total_gross_pay",
    format: "currency",
  },
  {
    title: "Total Net Pay",
    value: 213213,
    trend: "10%",
    key: "total_net_pay",
    type: "base",
    format: "currency",
  },
  {
    title: "Active Employees",
    value: 12312,
    type: "base",
    key: "active_employees",
    format: "number",
  },
];

export const baseConfig: (callback: () => void) => TSummaryDetail<Partial<Aggregates>>[] = (callback: () => void) => [
  {
    title: "Total Taxes",
    value: 25000,
    type: "base",
    format: "currency",
    key: "total_taxes",
  },
  {
    title: "Total Statutory Deductions",
    value: 36000,
    type: "base",
    key: "total_statutory_deductions",
    format: "currency",
  },
  {
    title: "Excluded Payments",
    value: 2511233,
    type: "base",
    key: "excluded_payments",
    subBaseActions: (_value: string | number) => (
      <Link sx={{ cursor: "pointer" }} underline="none" onClick={callback}>
        View Details
      </Link>
    ),
    format: "currency",
  },
];

export const enrichBaseSummaries = (data: Payrun | PayRunDetail, config: TSummaryDetail<Partial<Aggregates>>[]) => {
  return config.map((eachSummary) => ({
    ...eachSummary,
    value:
      eachSummary?.format === "number"
        ? (eachSummary?.formatter ? eachSummary?.formatter(data?.[eachSummary.key]) : data?.[eachSummary.key]) || 0
        : formatCurrency(Math.abs(data?.[eachSummary.key]), "INR") || 0,
  }));
};

const PayrunSummary = () => {
  const { isFullView } = useAppSelector((state) => state.app);
  const [isExcludedEmployeesView, setIsExcludedEmployeesView] = React.useState(false);
  const [masked, setMasked] = React.useState(true);
  const [isPayScheduleModalOpen, setIsPayScheduleModalOpen] = useState(false);

  const { data, refetch } = useQuery({
    queryKey: ["get-payruns"],
    queryFn: async () => payrollService.getPayruns(),
    refetchOnMount: true,
    refetchOnWindowFocus: false,
  });

  const hasPayruns = data && data.pay_runs && data.pay_runs.length > 0;

  if (!hasPayruns) {
    return (
      <Box display="flex" flexDirection="column" gap={2}>
        <NoDataWithAction
          title="Currently no pay run data is available. Click below to initiate the payroll cycle."
          actionText="Create"
          onActionClick={() => setIsPayScheduleModalOpen(true)}
        />
        <PayScheduleModal
          isOpen={isPayScheduleModalOpen}
          onClose={() => setIsPayScheduleModalOpen(false)}
          isAdHocPayrunFlow={false}
        />
      </Box>
    );
  }

  return (
    <Box display="flex" flexDirection="column" gap={2}>
      {!isFullView && (
        <ContentHeader
          title={
            <Box display="flex" gap={0.5} alignItems="center">
              <Typography variant="h5" color="textDarkText" fontWeight={600} fontSize={16}>
                Summary
              </Typography>
              <Tooltip title={masked ? "Unmask" : "Mask"}>
                <IconButton color="primary" onClick={() => setMasked((prev) => !prev)}>
                  {masked ? <VisibilityOff /> : <Visibility />}
                </IconButton>
              </Tooltip>
            </Box>
          }
          actions={
            <Button variant="contained" color="primary" onClick={() => setIsPayScheduleModalOpen(true)}>
              Create
            </Button>
          }
        />
      )}
      {!isFullView && (
        <SummaryDetails
          isMasked={masked}
          baseSummaries={enrichBaseSummaries(data, summaryDetailsConfig)}
          subBaseSummaries={enrichBaseSummaries(
            data,
            baseConfig(() => setIsExcludedEmployeesView(true)),
          )}
        />
      )}
      <PayrunDetails
        payruns={data?.pay_runs}
        refetch={refetch}
        isExcludedEmployeesView={isExcludedEmployeesView}
        setIsExcludedEmployeesView={setIsExcludedEmployeesView}
        isMasked={masked}
      />
      <PayScheduleModal
        isOpen={isPayScheduleModalOpen}
        onClose={() => setIsPayScheduleModalOpen(false)}
        isAdHocPayrunFlow={false}
      />
    </Box>
  );
};

export default PayrunSummary;
