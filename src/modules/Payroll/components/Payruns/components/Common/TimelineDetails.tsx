import { AccessTime, AccessTimeOutlined, CheckCircle, PersonOutline, RadioButtonUnchecked } from "@mui/icons-material";
import TimelineContent from "@mui/lab/TimelineContent";
import TimelineItem from "@mui/lab/TimelineItem";
import TimelineSeparator from "@mui/lab/TimelineSeparator";
import { Divider, Tooltip } from "@mui/material";
import Typography from "@mui/material/Typography";
import * as React from "react";
import ContentHeader from "src/modules/Common/ContentHeader/ContentHeader";
import { ProgressTimelineItem } from "src/services/api_definitions/payroll.service";
import { getIntlTimeToSpecifiedFormat } from "src/utils/dateUtils";
import {
  AdditionalCount,
  Card,
  Connector,
  colors,
  Dot,
  InfoLabel,
  InfoValue,
  Row,
  StatusPill,
  StepState,
  StyledTimeline,
} from "./TimelineDetails.styles";

export default function TimelineDetails({ payrun }: { payrun: { progress_timeline?: ProgressTimelineItem[] } }) {
  const items: ProgressTimelineItem[] = (payrun?.progress_timeline || []) as ProgressTimelineItem[];

  // Determine the first pending index to split done/pending/upcoming
  const firstPendingIndex = items.findIndex((it) => !it?.actioned_at);

  const getState = (idx: number) => {
    if (firstPendingIndex === -1) return "done";
    if (idx < firstPendingIndex) return "done";
    if (idx === firstPendingIndex) return "pending";
    return "upcoming";
  };

  const getStatusLabel = (state: string, status: string) => {
    if (state === "upcoming") return "Yet to Process";
    return status || (state === "pending" ? "Pending" : "");
  };

  const formatDateTime = (date: string | null) => {
    if (!date) return "--";
    return getIntlTimeToSpecifiedFormat(date, "dd MMM, hh:mm a").formattedDate;
  };

  const renderNameLabel = (item: ProgressTimelineItem): React.ReactNode => {
    if (item?.actioned_by) return item.actioned_by;
    const approvers = item?.approvers || [];
    if (approvers.length === 0) return "--";
    if (approvers.length <= 2) return approvers.join(", ");
    const visible = approvers.slice(0, 2).join(", ");
    const hiddenCount = approvers.length - 2;
    const fullList = approvers.join(", ");
    return (
      <Tooltip title={fullList} arrow>
        <span>
          {visible}, <AdditionalCount>{hiddenCount}+</AdditionalCount>
        </span>
      </Tooltip>
    );
  };

  return (
    <StyledTimeline>
      <ContentHeader title="Pay Run Time Line" subtitle="Track the progress and actions taken on this pay run" />
      <Divider orientation="horizontal" sx={{ margin: "8px 0px" }} />
      {items.map((item: ProgressTimelineItem, idx: number) => {
        const state = getState(idx) as StepState;
        const isDone = state === "done";
        const isPending = state === "pending";

        const iconColor = isDone ? colors.icon.done : isPending ? colors.icon.pending : colors.icon.upcoming;

        const nameLabel: React.ReactNode = renderNameLabel(item);
        const labelPrefix =
          !item.actioned_by && item.approvers?.length && state !== "done" ? "Approvers" : "Actioned By";

        return (
          <TimelineItem key={idx}>
            <TimelineSeparator>
              <Connector state={state} sx={{ visibility: idx === 0 ? "hidden" : "visible" }} />
              <Dot state={state}>
                {isDone ? (
                  <CheckCircle htmlColor={iconColor} fontSize={"small"} />
                ) : isPending ? (
                  <AccessTimeOutlined htmlColor={iconColor} fontSize={"small"} />
                ) : (
                  <RadioButtonUnchecked htmlColor={iconColor} fontSize={"small"} />
                )}
              </Dot>
              <Connector state={state} sx={{ visibility: idx === items.length - 1 ? "hidden" : "visible" }} />
            </TimelineSeparator>
            <TimelineContent sx={{ py: "12px", px: 2, width: "100%" }}>
              <Card sx={{ width: "100%" }}>
                <StatusPill state={state}>{getStatusLabel(state, item?.status)}</StatusPill>

                <Typography variant="subtitle1" fontWeight={600} sx={{ mb: 1 }}>
                  {item?.comments}
                </Typography>

                <Row>
                  <PersonOutline fontSize="small" />
                  <Typography variant="body2">
                    <InfoLabel>{labelPrefix}:</InfoLabel> <InfoValue>{nameLabel}</InfoValue>
                  </Typography>
                </Row>
                <Row sx={{ mt: 1 }}>
                  <AccessTime fontSize="small" />
                  <Typography variant="body2">{formatDateTime(item?.actioned_at)}</Typography>
                </Row>
              </Card>
            </TimelineContent>
          </TimelineItem>
        );
      })}
    </StyledTimeline>
  );
}
