import { Announcement } from "@mui/icons-material";
import { Box, Divider, Grid2, Tooltip, Typography } from "@mui/material";
import { useQuery } from "@tanstack/react-query";
import React, { useMemo } from "react";
import Modal from "src/modules/Common/Modal/Modal";
import { formatCurrency } from "src/modules/Profile/components/EmployeeCompensation/EmployeeCompensation";
import payrollService from "src/services/payroll.service";
import EmployeeCommonInfoDetailView from "./EmployeeCommonInfoDetailView";
import EmployeeComponentTypeContainer from "./EmployeeComponentTypeContainer";

const ReadOnlyComponentDetail: React.FC<{
  component: any;
  isAdHoc: boolean;
  typographyProps?: any;
  additionalNote?: string;
}> = ({ component, isAdHoc, typographyProps, additionalNote }) => {
  return (
    <Box display="flex" alignItems="center" justifyContent="space-between" gap={1}>
      <Box display="flex" gap={1}>
        <Typography color={isAdHoc ? "#007AFF" : "text.primary"} {...typographyProps} variant="body2">
          {component.name}
        </Typography>
        {additionalNote && (
          <Tooltip title={additionalNote} placement="top">
            <Announcement />
          </Tooltip>
        )}
      </Box>
      <Typography color={isAdHoc ? "#007AFF" : "text.primary"} {...typographyProps} variant="body2">
        {formatCurrency(component.amount, "INR")}
      </Typography>
    </Box>
  );
};

const ReadOnlyEmployeePayrunView: React.FC<{ employeeToEdit: any; onClose: () => void; isOpen: boolean }> = ({
  employeeToEdit,
  onClose,
  isOpen,
}) => {
  const { data: employeePayrunDetails } = useQuery(
    ["readonly-employee-payrun-details", employeeToEdit.id],
    async () => payrollService.getEmployeeDetailsPayrunId(employeeToEdit.id),
    {
      refetchOnMount: true,
      refetchOnWindowFocus: false,
    },
  );

  const groupedComponents = useMemo(() => {
    const groupedData = employeePayrunDetails?.components?.reduce(
      (acc, item) => {
        if (!acc[item.component_type]) {
          acc[item.component_type] = [];
        }
        acc[item.component_type].push(item);
        return acc;
      },
      {} as Record<string, any[]>,
    );
    return groupedData;
  }, [employeePayrunDetails]);

  return (
    <Modal isOpen={isOpen} onClose={onClose} title="Employee Details" showBackButton showDivider>
      <Box display="flex" flexDirection="column" gap={2}>
        <EmployeeCommonInfoDetailView employeePayrunDetails={employeePayrunDetails} />
        <Grid2 container overflow="auto" border="0.5px solid gray" borderRadius={2}>
          <Grid2 size={6} borderBottom="0.5px solid gray" borderRight="0.5px solid gray">
            <EmployeeComponentTypeContainer title="Earnings">
              <Box display="flex" gap={2} flexDirection="column">
                {[...(groupedComponents?.["Earning"] || []), ...(groupedComponents?.["Benefit"] || [])]?.map(
                  (eachComponent, index) => (
                    <Box display="flex" flexDirection="column" gap={1} key={eachComponent.name}>
                      <ReadOnlyComponentDetail
                        component={eachComponent}
                        isAdHoc={eachComponent?.source === "AD_HOC"}
                        additionalNote={eachComponent?.adjustment_note}
                      />
                      {index <
                        [...(groupedComponents?.["Earning"] || []), ...(groupedComponents?.["Benefit"] || [])]?.length -
                          1 && <Divider orientation="horizontal" />}
                    </Box>
                  ),
                )}
              </Box>
            </EmployeeComponentTypeContainer>
          </Grid2>
          <Grid2 size={6} borderBottom="0.5px solid gray" borderRight="0.5px solid gray">
            <EmployeeComponentTypeContainer title="Deductions">
              <Box display="flex" gap={2} flexDirection="column">
                {groupedComponents?.["Deduction"]?.map((eachComponent, index) => (
                  <Box display="flex" flexDirection="column" gap={1} key={eachComponent.name}>
                    <ReadOnlyComponentDetail
                      component={eachComponent}
                      isAdHoc={eachComponent?.source === "AD_HOC"}
                      additionalNote={eachComponent?.adjustment_note}
                    />
                    {index < groupedComponents?.["Deduction"]?.length - 1 && <Divider orientation="horizontal" />}
                  </Box>
                ))}
              </Box>
            </EmployeeComponentTypeContainer>
          </Grid2>
          <Grid2 container size={12}>
            <Grid2 size={6} p={2} borderTop="0.5px solid gray" borderRight="0.5px solid gray">
              <ReadOnlyComponentDetail
                component={{
                  name: "Total Earnings",
                  amount: (employeePayrunDetails?.earnings || 0) + (employeePayrunDetails?.benefits || 0),
                }}
                isAdHoc={false}
                typographyProps={{
                  sx: {
                    fontWeight: 600,
                    color: "#007F6F",
                  },
                }}
              />
            </Grid2>
            <Grid2 size={6} p={2} borderTop="0.5px solid gray" borderRight="0.5px solid gray">
              <ReadOnlyComponentDetail
                component={{ name: "Total Deductions", amount: employeePayrunDetails?.deductions }}
                isAdHoc={false}
                typographyProps={{
                  sx: {
                    fontWeight: 600,
                    color: "#DF2121",
                  },
                }}
              />
            </Grid2>
          </Grid2>
          <Grid2 container size={12}>
            <Grid2 size={6} borderTop="0.5px solid gray" borderRight="0.5px solid gray" />
            <Grid2 size={6} p={2} borderTop="0.5px solid gray" borderRight="0.5px solid gray">
              <ReadOnlyComponentDetail
                component={{ name: "Total Taxes", amount: employeePayrunDetails?.tax_amount }}
                isAdHoc={false}
                typographyProps={{
                  sx: {
                    fontWeight: 600,
                    color: "#DF2121",
                  },
                }}
              />
            </Grid2>
          </Grid2>
          <Grid2 size={12} p={2} borderTop="0.5px solid gray" borderRight="0.5px solid gray">
            <ReadOnlyComponentDetail
              component={{ name: "Net Pay", amount: employeePayrunDetails?.net_pay }}
              isAdHoc={false}
              typographyProps={{
                sx: {
                  fontWeight: 600,
                },
              }}
            />
          </Grid2>
        </Grid2>
      </Box>
    </Modal>
  );
};

export default ReadOnlyEmployeePayrunView;
