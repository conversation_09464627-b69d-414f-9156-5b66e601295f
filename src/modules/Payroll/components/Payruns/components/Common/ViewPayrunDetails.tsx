import { <PERSON>py<PERSON><PERSON>, DownloadOutlined, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ove, RefreshOutlined, TrendingUp } from "@mui/icons-material";
import { <PERSON><PERSON>, <PERSON>, Button, Chip, Divider, Drawer, Icon<PERSON><PERSON>on, Tooltip, Typography } from "@mui/material";
import { useStore } from "@tanstack/react-form";
import { useMutation } from "@tanstack/react-query";
import { differenceInDays, format } from "date-fns";
import React, { useContext, useMemo } from "react";
import { queryClient } from "src/app/App";
import { FROM_LAST_MONTH } from "src/app/constants";
import { ButtonOption } from "src/modules/Common/Buttons/SplitButton";
import ContentHeader from "src/modules/Common/ContentHeader/ContentHeader";
import TabsView from "src/modules/Common/CustomTabs/CustomTabs";
import { useAppForm } from "src/modules/Common/Form/effiFormContext";
import ConfirmationModal from "src/modules/Common/Modal/ConfirmationModal";
import { Aggregates, PayRunDetail } from "src/services/api_definitions/payroll.service";
import payrollService from "src/services/payroll.service";
import { getStatusColors } from "src/utils/typographyUtils";
import { enrichBaseSummaries } from "../PayrunSummary";
import AdHocPay from "../PayrunSummary/AdHocPay";
import { EditPayrunDetailModes, EditPayrunDetailProps } from "../PayrunSummary/EditPayrunDetail";
import ExcludedPayrunEmployees from "../PayrunSummary/ExcludedPayrunEmployees";
import IncludedPayrunEmployees from "../PayrunSummary/IncludedPayrunEmployees";
import { PayrunDetailsContext, PayrunDetailsViewModes } from "../PayrunSummary/PayrunDetails";
import SummaryDetails, { TSummaryDetail } from "../PayrunSummary/SummaryDetails";
import TimelineDetails from "./TimelineDetails";

export const summaryConfig: TSummaryDetail<Partial<Aggregates>>[] = [
  {
    title: "Payroll Cost",
    value: "",
    trend: "10%",
    isPositive: true,
    trendLabel: FROM_LAST_MONTH,
    type: "base",
    key: "total_gross_pay",
    format: "currency",
  },
  {
    title: "Employees Net Pay",
    value: "",
    trend: "10%",
    isPositive: false,
    trendLabel: FROM_LAST_MONTH,
    type: "base",
    key: "total_net_pay",
    format: "currency",
  },
  {
    title: "Active Employees",
    value: "",
    type: "base",
    trendLabel: FROM_LAST_MONTH,
    key: "active_employees",
    format: "number",
  },
  {
    title: "Pay Day",
    value: "",
    type: "base",
    trendLabel: "In a few days",
    key: "pay_date",
    format: "number",
    subBaseActions: (value: string | number) =>
      differenceInDays(new Date(value), new Date()) + 1 < 0 ? (
        ""
      ) : (
        <Typography color="primary">In {differenceInDays(new Date(value), new Date()) + 1} days</Typography>
      ),
    formatter: (value: string | number) => (value ? format(value, "MMM dd, yyyy") : ""),
  },
];

const baseConfig: TSummaryDetail<Partial<Aggregates>>[] = [
  {
    title: "Earnings",
    value: 0,
    type: "base",
    format: "currency",
    key: "total_earnings",
  },
  {
    title: "Statutory Deductions",
    value: 0,
    type: "base",
    key: "total_statutory_deductions",
    format: "currency",
  },
  {
    title: "Total TDS",
    value: 0,
    type: "base",
    key: "total_taxes",
    format: "currency",
  },
  {
    title: "Base Days",
    value: 0,
    type: "base",
    key: "base_days",
    format: "currency",
  },
];

const ViewPayrunDetails: React.FC<
  EditPayrunDetailProps & {
    setEmployeeToEdit: React.Dispatch<React.SetStateAction<PayRunDetail | null>>;
    setEditPayrunDetailMode: React.Dispatch<React.SetStateAction<EditPayrunDetailModes | null>>;
    payRunDetails: PayRunDetail | null;
    isLoading: boolean;
    isFetched: boolean;
    refetch: () => void;
  }
> = ({
  payrun,
  payRunDetails,
  isLoading,
  isFetched,
  refetch,
  setPayrunDetailsViewModes,
  setEmployeeToEdit,
  setEditPayrunDetailMode,
}) => {
  const [selectedRows, setSelectedRows] = React.useState<Record<number, boolean>>({});
  // Form for exclusion reason
  const excludeForm = useAppForm({
    defaultValues: {
      reason: "",
    },
  });
  const { reason } = useStore(excludeForm.store, (state) => state.values);
  const [excludeEmployeeIds, setExcludeEmployeeIds] = React.useState<string[] | null>(null);

  const [activeTab, setActiveTab] = React.useState(0);
  const [isAdHocPayModalOpen, setIsAdHocPayModalOpen] = React.useState(false);
  const { isAdHocPayrunFlow } = useContext(PayrunDetailsContext);
  const [timelineDetailsDrawerOpen, setTimelineDetailsDrawerOpen] = React.useState(false);

  const includedEmployeesInPayrun = useMemo(
    () => payRunDetails?.details?.filter((eachDetail) => eachDetail.included_in_pay_run) || [],
    [payRunDetails],
  );
  const excludedEmployeesInPayrun = useMemo(
    () => payRunDetails?.details?.filter((eachDetail) => !eachDetail.included_in_pay_run) || [],
    [payRunDetails],
  );

  const submitPayrunMutation = useMutation({
    mutationFn: () => payrollService.submitPayrun([payrun?.id || ""], payrun?.pay_run_type || ""),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["get-payruns"] });
      queryClient.invalidateQueries({ queryKey: ["get-adhoc-payruns"] });
      queryClient.invalidateQueries({ queryKey: ["get-payroll-by-id", payrun?.id] });
      setPayrunDetailsViewModes(PayrunDetailsViewModes.VIEW_ALL_PAYRUN_DETAILS);
    },
  });

  const exportEmployeeMutation = useMutation({
    mutationKey: ["export-employee-payrun-details"],
    mutationFn: async () =>
      payrollService.exportPayrunDetails(payrun?.pay_run_type || "", payrun?.id || "", {
        onDownloadProgress: (ev) => {
          console.log(ev);
        },
      }),
  });
  const includedEmployeesInSelectedRows = useMemo(() => {
    return Object.keys(selectedRows)
      .map(Number)
      .map((key) => includedEmployeesInPayrun?.[key] as any);
  }, [selectedRows, includedEmployeesInPayrun]);

  const excludedEmployeesInSelectedRows = useMemo(() => {
    return Object.keys(selectedRows)
      .map(Number)
      .map((key) => excludedEmployeesInPayrun?.[key] as any);
  }, [selectedRows, excludedEmployeesInPayrun]);

  const onExcludeEmployee = (ids: string[]) => {
    setExcludeEmployeeIds(ids);
  };

  const onIncludeEmployee = (ids: string[]) => {
    includeEmployeeMutation.mutate(ids);
  };

  const onRefreshEmployee = (ids: string[]) => {
    refreshEmployeePayrunDataMutation.mutate(ids);
  };

  // const onAdhocPay = () => {
  //   console.log({ includedEmployeesInSelectedRows });
  //   setIsAdHocPayModalOpen(true);
  // };

  const optionsMap: Record<number, ButtonOption[]> = useMemo(() => {
    return {
      0: [
        // {
        //   id: "ad-hoc-pay",
        //   title: "Ad hoc Pay",
        //   onClick: onAdhocPay,
        //   disabled: !Object.values(selectedRows).some(Boolean) || includedEmployeesInSelectedRows.length === 0,
        // },
        {
          id: "download-payrun-details",
          title: "Download Payrun Details",
          onClick: () => exportEmployeeMutation.mutate(),
          icon: <DownloadOutlined />,
        },
        {
          id: "exclude-employees",
          title: "Exclude Employees",
          onClick: () => onExcludeEmployee(includedEmployeesInSelectedRows.map((eachEmployee) => eachEmployee.id)),
          disabled: !Object.values(selectedRows).some(Boolean) || !includedEmployeesInSelectedRows.length,
          hidden: isAdHocPayrunFlow || payrun?.status === "Approved",
          icon: <PersonRemove />,
        },
        {
          id: "refresh-pay-run-data",
          disabled: !Object.values(selectedRows).some(Boolean) || !includedEmployeesInSelectedRows.length,
          title: "Refresh Pay Run Data",
          hidden: payrun?.status === "Approved",
          onClick: () => onRefreshEmployee(includedEmployeesInSelectedRows.map((eachEmployee) => eachEmployee.id)),
          icon: <RefreshOutlined />,
        },
      ].filter((eachOption) => !eachOption.hidden),
      1: [
        {
          id: "download-payrun-details",
          title: "Download Payrun Details",
          onClick: () => exportEmployeeMutation.mutate(),
          icon: <DownloadOutlined />,
        },
        {
          id: "include-employees",
          title: "Include Employees",
          onClick: () => onIncludeEmployee(excludedEmployeesInSelectedRows.map((eachEmployee) => eachEmployee.id)),
          disabled: !Object.values(selectedRows).some(Boolean) || !excludedEmployeesInSelectedRows.length,
          icon: <PersonAdd />,
          hidden: payrun?.status === "Approved",
        },
      ].filter((eachOption) => !eachOption.hidden),
    };
  }, [includedEmployeesInSelectedRows, excludedEmployeesInSelectedRows, isAdHocPayrunFlow]);

  const excludeEmployeeMutation = useMutation({
    mutationKey: ["exclude-employees-from-payrun"],
    mutationFn: async (payload: { employeePayrunIds: string[]; reason: string }) =>
      payrollService.excludeEmployeesFromPayrun(payload.employeePayrunIds, payload.reason),
    onSuccess: () => {
      refetch();
      setSelectedRows({});
      setExcludeEmployeeIds(null);
      queryClient.invalidateQueries({ queryKey: ["get-payruns"] });
      queryClient.invalidateQueries({ queryKey: ["get-adhoc-payruns"] });
    },
  });

  const includeEmployeeMutation = useMutation({
    mutationKey: ["include-employees-in-payrun"],
    mutationFn: async (employeePayrunIds: string[]) => payrollService.includeEmployeesInPayrun(employeePayrunIds),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["get-payruns"] });
      queryClient.invalidateQueries({ queryKey: ["get-adhoc-payruns"] });
      refetch();
      setSelectedRows({});
    },
  });

  const refreshEmployeePayrunDataMutation = useMutation({
    mutationKey: ["refresh-employee-payrun-data"],
    mutationFn: async (employeePayrunIds: string[]) => payrollService.refreshEmployeePayrunData(employeePayrunIds),
    onSuccess: () => {
      refetch();
      setSelectedRows({});
      queryClient.invalidateQueries({ queryKey: ["get-payruns"] });
      queryClient.invalidateQueries({ queryKey: ["get-adhoc-payruns"] });
    },
  });

  if (!payrun) {
    return null;
  }

  return (
    <Box display="flex" flexDirection="column" gap={1}>
      <ContentHeader
        title={
          <Box display="flex" gap={0.5} alignItems="center">
            <Typography variant="body1" fontWeight={600}>
              {isAdHocPayrunFlow
                ? `${payrun?.pay_run_type} Pay Run`
                : `${payrun?.pay_run_type} Pay Run (${payrun.period})`}
            </Typography>
            <Chip
              icon={
                <IconButton
                  onClick={() => {
                    navigator.clipboard.writeText(payrun.id);
                  }}
                >
                  <CopyAll />
                </IconButton>
              }
              variant="filled"
              color="default"
              label={payrun.id}
              sx={{ fontWeight: 600 }}
            />
            <Chip
              sx={{ color: getStatusColors(payrun.status) }}
              variant="outlined"
              label={payrun.status}
              size="small"
            />
          </Box>
        }
        showBackButton
        goBack={() => setPayrunDetailsViewModes(PayrunDetailsViewModes.VIEW_ALL_PAYRUN_DETAILS)}
        actions={
          <Box display="flex" gap={1}>
            <Button
              onClick={() => setTimelineDetailsDrawerOpen(true)}
              size="medium"
              startIcon={<TrendingUp color="primary" />}
              sx={{ bgcolor: "#E6F2F1", color: "primary.main", width: 130 }}
            >
              Timeline
            </Button>
            {!["Submitted", "Paid", "Approved"].includes(payrun.status) && (
              <Button
                onClick={() => submitPayrunMutation.mutate()}
                size="small"
                variant="contained"
                sx={{ width: 130 }}
                disabled={["Submitted", "Paid", "Approved"].includes(payrun.status)}
              >
                Submit
              </Button>
            )}
          </Box>
        }
      />
      <Divider orientation="horizontal" />
      <Box>
        <SummaryDetails
          baseSummaries={enrichBaseSummaries(
            payRunDetails as PayRunDetail,
            isAdHocPayrunFlow ? summaryConfig.filter((each) => each.key !== "pay_date") : summaryConfig,
          )}
          subBaseSummaries={enrichBaseSummaries(
            payRunDetails as PayRunDetail,
            isAdHocPayrunFlow ? baseConfig.filter((each) => each.key !== "base_days") : baseConfig,
          )}
        />
      </Box>
      {excludedEmployeesInPayrun?.length > 0 && (
        <Alert severity="warning">
          You have {excludedEmployeesInPayrun.length} employees excluded from the pay run. Employees excluded from the
          pay run will not be included in the pay run. You can include them back by selecting them and clicking on the
          Include Employees button.
        </Alert>
      )}
      {isAdHocPayrunFlow ? (
        <>
          <IncludedPayrunEmployees
            payRunDetails={includedEmployeesInPayrun || []}
            isLoading={isLoading}
            isFetched={isFetched}
            setEmployeeToEdit={setEmployeeToEdit}
            setEditPayrunDetailMode={setEditPayrunDetailMode}
            onExcludeEmployee={onExcludeEmployee}
            onRefreshEmployee={onRefreshEmployee}
            selectedRows={selectedRows}
            setSelectedRows={setSelectedRows}
            hideActions={["Submitted", "Paid", "Approved"].includes(payrun.status)}
          />
          {isAdHocPayModalOpen && (
            <AdHocPay
              isModalOpen={isAdHocPayModalOpen}
              onClose={() => setIsAdHocPayModalOpen(false)}
              selectedRows={includedEmployeesInSelectedRows}
            />
          )}
        </>
      ) : (
        <TabsView
          tabs={[
            {
              id: 0,
              label: "Included Employees",
              component: (
                <Box display="flex" flexDirection="column" gap={1}>
                  <ContentHeader
                    title="Employee Details"
                    actions={
                      <Box display="flex" alignItems="center" gap={1}>
                        {optionsMap[activeTab].map((eachOption) => (
                          <Box
                            key={eachOption.id}
                            sx={{ bgcolor: eachOption.disabled ? "#D2D2D2" : "#E6F2F1", borderRadius: 8 }}
                          >
                            <Tooltip title={eachOption.title}>
                              <IconButton
                                key={eachOption.id}
                                size="medium"
                                onClick={eachOption.onClick}
                                disabled={eachOption.disabled}
                              >
                                {eachOption.icon}
                              </IconButton>
                            </Tooltip>
                          </Box>
                        ))}
                      </Box>
                    }
                  />
                  <IncludedPayrunEmployees
                    payRunDetails={includedEmployeesInPayrun || []}
                    isLoading={isLoading}
                    isFetched={isFetched}
                    setEmployeeToEdit={setEmployeeToEdit}
                    setEditPayrunDetailMode={setEditPayrunDetailMode}
                    onExcludeEmployee={onExcludeEmployee}
                    onRefreshEmployee={onRefreshEmployee}
                    selectedRows={selectedRows}
                    setSelectedRows={setSelectedRows}
                    hideActions={["Submitted", "Paid", "Approved"].includes(payrun.status)}
                  />
                  {isAdHocPayModalOpen && (
                    <AdHocPay
                      isModalOpen={isAdHocPayModalOpen}
                      onClose={() => setIsAdHocPayModalOpen(false)}
                      selectedRows={includedEmployeesInSelectedRows}
                    />
                  )}
                </Box>
              ),
            },
            {
              id: 1,
              label: "Excluded Employees",
              component: (
                <Box display="flex" flexDirection="column" gap={1}>
                  <ContentHeader
                    title="Employee Details"
                    actions={
                      <Box display="flex" alignItems="center" gap={1}>
                        {optionsMap[activeTab].map((eachOption) => (
                          <Box
                            key={eachOption.id}
                            sx={{ bgcolor: eachOption.disabled ? "#D2D2D2" : "#E6F2F1", borderRadius: 8 }}
                          >
                            <Tooltip title={eachOption.title}>
                              <IconButton
                                key={eachOption.id}
                                size="medium"
                                onClick={eachOption.onClick}
                                disabled={eachOption.disabled}
                              >
                                {eachOption.icon}
                              </IconButton>
                            </Tooltip>
                          </Box>
                        ))}
                      </Box>
                    }
                  />
                  <ExcludedPayrunEmployees
                    payRunDetails={excludedEmployeesInPayrun || []}
                    isLoading={isLoading}
                    isFetched={isFetched}
                    onIncludeEmployee={onIncludeEmployee}
                    selectedRows={selectedRows}
                    setSelectedRows={setSelectedRows}
                    hideActions={["Submitted", "Paid", "Approved"].includes(payrun.status)}
                  />
                </Box>
              ),
            },
          ]}
          handleTabChange={(currentTabIndex: number) => {
            setActiveTab(currentTabIndex);
            setSelectedRows({});
          }}
        />
      )}
      {excludeEmployeeIds && (
        <ConfirmationModal
          isOpen={!!excludeEmployeeIds}
          title="Exclude Employees"
          onSubmit={() =>
            excludeEmployeeMutation.mutate({ employeePayrunIds: excludeEmployeeIds, reason: reason || "" })
          }
          onCancel={() => setExcludeEmployeeIds(null)}
          isSaveDisabled={!reason}
        >
          <Box display="flex" flexDirection="column" gap={2}>
            <Typography>Are you sure you want to exclude the selected employee(s)? Please provide a reason.</Typography>
            <excludeForm.AppField name="reason">
              {(field: { EffiTextField: React.FC<any> }) => <field.EffiTextField label="Reason" required />}
            </excludeForm.AppField>
          </Box>
        </ConfirmationModal>
      )}
      <Drawer
        anchor="right"
        open={timelineDetailsDrawerOpen}
        onClose={() => setTimelineDetailsDrawerOpen(false)}
        PaperProps={{
          sx: {
            width: 450,
          },
        }}
      >
        <TimelineDetails payrun={payRunDetails} />
      </Drawer>
    </Box>
  );
};

export default ViewPayrunDetails;
