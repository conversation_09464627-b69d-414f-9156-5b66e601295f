import { Grid2, Paper } from "@mui/material";
import React from "react";
import DetailListItem from "src/modules/PerformanceManagement/components/DetailListItem";
import { PayrunEmployeeDetail } from "src/services/api_definitions/payroll.service";

const employeeDetails = [
  {
    key: "employee.display_name",
    label: "Employee Name",
  },
  {
    key: "employee.cost_center",
    label: "Cost Center",
  },
  {
    key: "employee.employee_type",
    label: "Employee Type",
  },
  {
    key: "employee.work_role.band.name",
    label: "Band",
  },
  {
    key: "employee.work_role.level.name",
    label: "Level",
  },
  {
    key: "employee.work_role.grade.name",
    label: "Grade",
  },
  {
    key: "paid_days",
    label: "Paid Days",
  },
  {
    key: "employee.business_unit",
    label: "Business Unit",
  },
  {
    key: "employee.department",
    label: "Department",
  },
  {
    key: "employee.job_title",
    label: "Job Title",
  },
  {
    key: "lop_days",
    label: "LOP Days",
  },
  {
    key: "employee.employment_status",
    label: "Status",
  },
];

type EmployeeCommonInfoDetailViewProps = {
  employeePayrunDetails: PayrunEmployeeDetail;
};

const EmployeeCommonInfoDetailView: React.FC<EmployeeCommonInfoDetailViewProps> = ({ employeePayrunDetails }) => {
  const enrichedEmployeeDetails = employeeDetails
    .map(({ key, ...otherDetails }) => ({
      ...otherDetails,
      key,
      value: key.split(".").reduce((acc, key) => acc?.[key], employeePayrunDetails),
    }))
    .filter((eachDetail) => {
      const whitelistedLabels = ["lop_days", "employee.employment_status", "employee.employee_type", "paid_days"];
      if (whitelistedLabels.includes(eachDetail.key)) {
        return true;
      }
      return !!eachDetail.value;
    });
  return (
    <Grid2 container spacing={2} component={Paper} elevation={2} padding={2} sx={{ backgroundColor: "#F8F8F8" }}>
      {enrichedEmployeeDetails.map((eachDetail) => (
        <Grid2 key={eachDetail.label} size={3}>
          <DetailListItem key={eachDetail.label} title={eachDetail.label} value={eachDetail.value} />
        </Grid2>
      ))}
    </Grid2>
  );
};

export default EmployeeCommonInfoDetailView;
