import { AddCircleOutline } from "@mui/icons-material";
import { Box, IconButton, Tooltip, Typography } from "@mui/material";
import React from "react";

type TEmployeeComponentTypeContainer = React.PropsWithChildren & {
  title: string;
  onAddClick?: () => void;
  tooltip?: string;
};

const EmployeeComponentTypeContainer: React.FC<TEmployeeComponentTypeContainer> = ({
  title,
  onAddClick,
  tooltip = "Add",
  children,
}) => {
  return (
    <Box display="flex" flexDirection="column" gap={1}>
      <Box
        id="container"
        display="flex"
        justifyContent="space-between"
        alignItems="center"
        bgcolor={"#F8F8F8"}
        sx={{ padding: "10px 16px" }}
        borderRadius="4px 4px 0 0"
      >
        <Typography>{title}</Typography>
        {onAddClick && (
          <Tooltip title={tooltip}>
            <IconButton onClick={onAddClick} size="large" color="primary">
              <AddCircleOutline />
            </IconButton>
          </Tooltip>
        )}
      </Box>
      <Box p={2}>{children}</Box>
    </Box>
  );
};

export default EmployeeComponentTypeContainer;
