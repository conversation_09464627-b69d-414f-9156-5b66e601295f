import { Box, Button, Paper } from "@mui/material";
import { useStore } from "@tanstack/react-form";
import React, { useEffect } from "react";
import { useAllCompensations } from "src/customHooks/useAllCompensations";
import { useAppSelector } from "src/customHooks/useAppSelector";
import { useAppForm } from "src/modules/Common/Form/effiFormContext";
import Modal from "src/modules/Common/Modal/Modal";
import { CompensationComponent, PayrunEmployeeCompensationDetail } from "src/services/api_definitions/payroll.service";
import { z } from "zod";

interface AddEditEarningAndBenefitsProps {
  isModalOpen: boolean;
  onClose: () => void;
  selectedRow: PayrunEmployeeCompensationDetail;
  allCompensations?: CompensationComponent[];
  onSubmit: (values: any) => void;
  employeePayrunDetails: any;
}

const bulkSupplementaryPaySchema = z.object({
  component_type: z.enum(["Earning", "Benefit"]),
  pay_type: z.string().optional().nullable(),
  adjustment_note: z.string().optional().nullable(),
  source: z.enum(["AD_HOC"]),
  formula: z.object({
    calculation_type: z.enum(["Flat", "Percentage"]),
    value: z.number().gt(0),
    display_name: z.string().optional().nullable(),
  }),
});

const AddEditEarningAndBenefits: React.FC<AddEditEarningAndBenefitsProps> = ({
  isModalOpen,
  onClose,
  selectedRow,
  onSubmit,
  employeePayrunDetails,
}) => {
  if (!isModalOpen) return null;
  const isEdit = !!selectedRow;
  const { selectedOrganisationDetails } = useAppSelector((state) => state.userManagement);
  const { data: allCompensations, isLoading } = useAllCompensations({
    country: "India",
    active: true,
    employeeTypes: employeePayrunDetails?.employee?.employee_type,
  });
  const isAdHocComponent = (component: CompensationComponent) => {
    return !!(component?.attributes?.find((eachAttribute) => Reflect.has(eachAttribute, "ad_hoc")) as any)?.["ad_hoc"];
  };
  const adHocEarnings = allCompensations
    ?.filter(
      (eachCompensation) =>
        eachCompensation?.component_type === "Earning" &&
        eachCompensation?.active &&
        isAdHocComponent(eachCompensation) &&
        eachCompensation?.employee_types?.includes(employeePayrunDetails?.employee?.employee_type),
    )
    ?.map((eachCompensation) => ({
      label: eachCompensation.name,
      value: eachCompensation.name,
    }));
  const earnings = allCompensations
    ?.filter(
      (eachCompensation) =>
        eachCompensation?.component_type === "Earning" &&
        eachCompensation?.active &&
        !isAdHocComponent(eachCompensation) &&
        eachCompensation?.employee_types?.includes(employeePayrunDetails?.employee?.employee_type),
    )
    ?.map((eachCompensation) => ({
      label: eachCompensation.name,
      value: eachCompensation.name,
    }));
  const adHocBenefits = allCompensations
    ?.filter(
      (eachCompensation) =>
        eachCompensation?.component_type === "Benefit" &&
        eachCompensation?.active &&
        isAdHocComponent(eachCompensation) &&
        eachCompensation?.employee_types?.includes(employeePayrunDetails?.employee?.employee_type),
    )
    ?.map((eachCompensation) => ({
      label: eachCompensation.name,
      value: eachCompensation.name,
    }));

  const benefits = allCompensations
    ?.filter(
      (eachCompensation) =>
        eachCompensation?.component_type === "Benefit" &&
        eachCompensation?.active &&
        !isAdHocComponent(eachCompensation) &&
        eachCompensation?.employee_types?.includes(employeePayrunDetails?.employee?.employee_type),
    )
    ?.map((eachCompensation) => ({
      label: eachCompensation.name,
      value: eachCompensation.name,
    }));

  useEffect(() => {
    if (selectedOrganisationDetails?.compensation_basis === "gross") {
      earnings?.push({ label: "Gross", value: "Gross" });
      benefits?.push({ label: "Gross", value: "Gross" });
    }
    if (selectedOrganisationDetails?.compensation_basis === "ctc") {
      earnings?.push({ label: "CTC", value: "CTC" });
      benefits?.push({ label: "CTC", value: "CTC" });
    }
  }, [selectedRow, selectedOrganisationDetails, allCompensations]);

  const form = useAppForm({
    defaultValues: {
      ...selectedRow,
      component_type: selectedRow?.component_type || "Earning",
      pay_type:
        allCompensations?.find((eachCompensation) => eachCompensation?.id === selectedRow?.compensation_component_id)
          ?.name || "",
      formula: selectedRow?.formula || {
        calculation_type: "Flat",
        value: 0,
        display_name: null,
      },
      adjustment_note: selectedRow?.adjustment_note || "",
      source: selectedRow?.source || "AD_HOC",
    },
    validators: {
      onSubmit: bulkSupplementaryPaySchema as any,
    },
    onSubmit: ({ value }) => {
      onSubmit(value);
    },
  });
  const { component_type: componentType, formula } = useStore(form.store, (state) => state.values);

  return (
    <Modal
      isOpen={isModalOpen}
      title={`${isEdit ? "Edit" : "Add"} ${componentType}`}
      onClose={onClose}
      showBackButton
      showDivider
      fullwidth
      actions={
        <Box display="flex" p={2} gap={1} justifyContent="flex-end">
          <Button variant="outlined" onClick={() => onClose()}>
            Cancel
          </Button>
          <form.Subscribe
            selector={(state) => [
              state.canSubmit,
              state.isSubmitting,
              state.isPristine,
              state.values.formula,
              state.values.pay_type,
            ]}
          >
            {([canSubmit, isSubmitting, isPristine, formula, pay_type]) => {
              return (
                <Button
                  variant="contained"
                  type="submit"
                  disabled={!canSubmit || isSubmitting || !formula?.value || !pay_type}
                  onClick={form.handleSubmit}
                >
                  Save
                </Button>
              );
            }}
          </form.Subscribe>
        </Box>
      }
    >
      <Box display="flex" flexDirection="column" gap={1}>
        <form.AppField name="component_type">
          {(field: any) => (
            <field.EffiRadioGroup
              label="Component Type"
              layout="horizontal"
              disabled={isEdit}
              options={[
                { label: "Earning", value: "Earning" },
                { label: "Benefit", value: "Benefit" },
              ]}
            />
          )}
        </form.AppField>
        <form.AppField
          name="pay_type"
          listeners={{
            onChange: ({ value }: { value: string }) => {
              form.setFieldValue(
                "formula",
                allCompensations.find((eachCompensation) => eachCompensation?.name === value)?.formula,
              );
              form.setFieldValue(
                "compensation_component_id",
                allCompensations.find((eachCompensation) => eachCompensation?.name === value)?.id,
              );
            },
          }}
        >
          {(field: any) => (
            <field.EffiSelect
              label={componentType === "Earning" ? "Earning Type" : "Benefit Type"}
              disabled={isLoading || isEdit}
              options={componentType === "Earning" ? adHocEarnings : adHocBenefits}
            />
          )}
        </form.AppField>
        <Box component={Paper} elevation={2} p={2} display="flex" flexDirection="column" gap={2}>
          <form.AppField name="formula.calculation_type">
            {(field: any) => (
              <field.EffiRadioGroup
                label="Calculation Type"
                layout="horizontal"
                disabled
                options={[
                  { label: "Flat Amount", value: "Flat" },
                  { label: "Percentage", value: "Percentage" },
                ]}
              />
            )}
          </form.AppField>
          <Box display="flex" gap={1}>
            <form.AppField name="formula.value">
              {(field: any) => {
                if (formula?.calculation_type === "Percentage") {
                  return (
                    <field.EffiPercentageField
                      label=""
                      required
                      placeholder="Enter percentage"
                      endHelperText={`of ${formula?.display_name}`}
                    />
                  );
                }
                return <field.EffiCurrency label="" required currency="INR" placeholder="Enter amount" />;
              }}
            </form.AppField>
          </Box>
        </Box>
        <form.AppField name="adjustment_note">
          {(field: any) => <field.EffiTextField label="Adjustment Note" />}
        </form.AppField>
      </Box>
    </Modal>
  );
};

export default AddEditEarningAndBenefits;
