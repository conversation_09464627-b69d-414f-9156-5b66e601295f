import { Box, Button, Chip, Grid2 } from "@mui/material";
import { useStore } from "@tanstack/react-form";
import React from "react";
import { useMasterData } from "src/customHooks/useMasterData";
import EffiDynamicForm from "src/modules/Common/Form/components/EffiDynamicForm";
import { useAppForm } from "src/modules/Common/Form/effiFormContext";
import Modal from "src/modules/Common/Modal/Modal";
import { z } from "zod";

interface MarkAsPaidModalProps {
  onClose: () => void;
  onMarkAsPaid: (data: any) => void;
  isOpen: boolean;
  payrun?: any;
  selectedPayruns?: any[];
}

const markAsPaidFormSchema = z.object({
  paymentType: z.string().nonempty("Payment type is required"),
  paymentDate: z.string().nonempty("Payment date is required"),
  payruns: z.array(z.string()).nonempty("At least one payrun must be selected"),
});

const MarkAsPaidModal: React.FC<MarkAsPaidModalProps> = ({
  onClose,
  onMarkAsPaid,
  isOpen,
  payrun,
  selectedPayruns,
}) => {
  console.log({ selectedPayruns });

  const { data: paymentModes } = useMasterData("PaymentMode");
  const paymentModeOptions = paymentModes?.map((mode) => ({ label: mode, value: mode })) || [];

  const form = useAppForm({
    defaultValues: {
      payruns: selectedPayruns || [],
      paymentType: "",
      paymentDate: "",
    },
    validators: {
      onSubmit: markAsPaidFormSchema,
    },
    onSubmit: onMarkAsPaid,
  });

  const { payruns } = useStore(form.store, (state) => state.values);

  return (
    <Modal
      isOpen={true}
      onClose={onClose}
      title="Mark as Paid"
      actions={
        <Box display="flex" justifyContent="flex-end" alignItems="center" gap={1} p={2}>
          <Button variant="outlined" onClick={onClose}>
            Cancel
          </Button>
          <Button variant="contained" onClick={form.handleSubmit}>
            Submit
          </Button>
        </Box>
      }
    >
      <Box>
        {payruns?.map((payrunId: string) => (
          <Chip color="primary" size="small" key={payrunId} label={payrunId} sx={{ margin: "0.5rem" }} />
        ))}
        <EffiDynamicForm
          form={form}
          inputFields={[
            {
              fieldProps: { name: "paymentType" },
              formProps: {
                type: "select",
                label: "Payment Type",
                required: true,
                options: paymentModeOptions,
              },
            },
            {
              fieldProps: { name: "paymentDate" },
              formProps: {
                type: "date",
                label: "Payment Date",
                required: true,
                maxDate: new Date(),
              },
            },
          ]}
        />
      </Box>
    </Modal>
  );
};
export default MarkAsPaidModal;
