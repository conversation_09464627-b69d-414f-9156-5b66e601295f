import { Announcement } from "@mui/icons-material";
import { <PERSON>, Divider, Grid2, <PERSON><PERSON><PERSON>, Typography } from "@mui/material";
import { useStore } from "@tanstack/react-form";
import { useQuery } from "@tanstack/react-query";
import React from "react";
import { useAllCompensations } from "src/customHooks/useAllCompensations";
import ContentHeader from "src/modules/Common/ContentHeader/ContentHeader";
import { useAppForm } from "src/modules/Common/Form/effiFormContext";
import TableActions from "src/modules/Common/Table/TableActions";
import { CompensationComponent } from "src/modules/Employees/Compensation/Compensation";
import { formatCurrency } from "src/modules/Profile/components/EmployeeCompensation/EmployeeCompensation";
import { PayRunDetail, PayrunEmployeeCompensationDetail } from "src/services/api_definitions/payroll.service";
import payrollService from "src/services/payroll.service";
import AddEditDeductions from "./AddEditDeductions";
import AddEditEarningAndBenefits from "./AddEditEarningAndBenefits";
import EmployeeCommonInfoDetailView from "./EmployeeCommonInfoDetailView";
import EmployeeComponentTypeContainer from "./EmployeeComponentTypeContainer";

type Props = {
  components: CompensationComponent[];
  employeeToEdit: PayRunDetail;
  onSubmit: (values: any, callback: () => void) => void;
};

interface DetailListItemProps {
  isEdit?: boolean;
  additionalNote?: string;
  title: string;
  value: string | number | null;
  actionNode?: React.ReactNode;
  typographyProps?: Record<string, unknown>;
}

const ComponentDetailListItem: React.FC<DetailListItemProps> = ({
  isEdit = false,
  additionalNote = null,
  title,
  value,
  actionNode = null,
  typographyProps = {},
}) => {
  return (
    <Box width="100%" display="flex" justifyContent="space-between" alignItems="center">
      <Box display="flex" gap={1}>
        <Typography sx={{ color: isEdit ? "#007AFF" : "#000" }} {...typographyProps}>
          {title}
        </Typography>
        {additionalNote && (
          <Tooltip title={additionalNote} placement="top">
            <Announcement />
          </Tooltip>
        )}
      </Box>
      <Box display="flex" alignItems="center" gap={1}>
        <Typography sx={{ color: isEdit ? "#007AFF" : "#000" }} {...typographyProps}>
          {formatCurrency(value, "INR")}
        </Typography>
        {isEdit && actionNode}
      </Box>
    </Box>
  );
};

const EmployeeDetailWithCompensationComponents: React.FC<Props> = ({ employeeToEdit, onSubmit }) => {
  const [modalType, setModalType] = React.useState<"earnings" | "deductions" | null>(null);
  const [componentToEdit, setComponentToEdit] = React.useState<PayRunDetail | null>(null);
  const { data: allCompensations } = useAllCompensations({
    country: "India",
    active: true,
    employeeTypes: employeeToEdit?.employee?.employee_type,
  });

  const { data: employeePayrunDetails, refetch } = useQuery(
    ["get-employee-by-payrun-id", employeeToEdit.id],
    async () => payrollService.getEmployeeDetailsPayrunId(employeeToEdit.id),
    {
      refetchOnMount: true,
      refetchOnWindowFocus: false,
    },
  );

  const appForm = useAppForm({
    defaultValues: {
      earnings: employeePayrunDetails?.earnings,
      deductions: employeePayrunDetails?.deductions,
      benefits: employeePayrunDetails?.benefits,
      tax_amount: employeePayrunDetails?.tax_amount,
      gross_pay: employeePayrunDetails?.gross_pay,
      net_pay: employeePayrunDetails?.net_pay,
      paid_days: employeePayrunDetails?.paid_days,
      components: employeePayrunDetails?.components,
    },
  });
  const components = useStore(appForm.store, (state) => state.values.components);

  const getComponentsGroup = (eachComponentType: string) => {
    return components?.filter((eachComponent) => eachComponent.component_type === eachComponentType) || [];
  };

  const [earnings, deductions] = [
    [...getComponentsGroup("Earning"), ...getComponentsGroup("Benefit")]?.sort((a, b) =>
      a.source === "AD_HOC" ? 1 : -1,
    ),
    getComponentsGroup("Deduction")?.sort((a, b) => (a.source === "AD_HOC" ? 1 : -1)),
  ];

  const onAddClick = (componentType: "earnings" | "deductions", rowToEdit: PayRunDetail | null) => {
    setModalType(componentType);
    if (rowToEdit) {
      setComponentToEdit(rowToEdit);
    }
  };

  const getCompensationComponent = (componentToFind: PayrunEmployeeCompensationDetail) =>
    allCompensations?.find((eachComponent) => eachComponent.id === componentToFind?.compensation_component_id);

  const onAddEditSubmit = (values: any) => {
    const oldAdHocCompensations = components?.filter((eachComponent) => eachComponent.source === "AD_HOC") || [];
    const isValuePresentInCompensation = !!oldAdHocCompensations?.find(
      (eachComponent) => eachComponent.compensation_component_id === values.compensation_component_id,
    );

    if (isValuePresentInCompensation) {
      const newComponents = [...oldAdHocCompensations].map((eachComponent) => {
        if (eachComponent.compensation_component_id === values.compensation_component_id) {
          return values;
        }
        return eachComponent;
      });

      const request = {
        employee_pay_run_id: employeeToEdit.id,
        components: newComponents.map((eachComponent) => ({
          ...eachComponent,
          compensation_component_id: getCompensationComponent(eachComponent)?.id,
          adjustment_note: eachComponent.adjustment_note,
          formula: {
            ...getCompensationComponent(eachComponent)?.formula,
            value: eachComponent?.formula?.value,
          },
        })),
      };
      onSubmit(request, refetch);
      setComponentToEdit(null);
      setModalType(null);
      refetch();
    } else {
      const request = {
        employee_pay_run_id: employeeToEdit.id,
        components: [...oldAdHocCompensations, values]?.map((eachComponent) => ({
          ...eachComponent,
          compensation_component_id: getCompensationComponent(eachComponent)?.id,
          adjustment_note: eachComponent.adjustment_note,
          formula: {
            ...getCompensationComponent(eachComponent)?.formula,
            value: eachComponent?.formula?.value,
          },
        })),
      };
      onSubmit(request, refetch);
      setModalType(null);
      setComponentToEdit(null);
      refetch();
    }
  };

  const onRemoveClick = (componentToRemove: PayrunEmployeeCompensationDetail) => {
    const newComponents = employeePayrunDetails?.components
      ?.filter((eachPayrun) => eachPayrun.source === "AD_HOC")
      ?.filter(
        (eachComponent) => eachComponent.compensation_component_id !== componentToRemove.compensation_component_id,
      );
    onSubmit(
      {
        employee_pay_run_id: employeeToEdit.id,
        components: newComponents,
      },
      refetch,
    );
  };

  return (
    <>
      <ContentHeader title="Employee Details" />
      <EmployeeCommonInfoDetailView employeePayrunDetails={employeePayrunDetails} />
      <ContentHeader title="Salary Breakdown" />
      <Grid2 container overflow="auto" border="0.5px solid gray" borderRadius={2}>
        <Grid2 size={6} borderBottom="0.5px solid gray" borderRight="0.5px solid gray">
          <EmployeeComponentTypeContainer title="Earnings" onAddClick={() => onAddClick("earnings", null)}>
            <Box display="flex" gap={2} flexDirection="column">
              {earnings?.map((eachComponent, index) => (
                <Box display="flex" flexDirection="column" gap={1} key={eachComponent.name}>
                  <ComponentDetailListItem
                    key={eachComponent.name}
                    title={eachComponent.name}
                    value={eachComponent.amount}
                    isEdit={eachComponent?.source === "AD_HOC"}
                    additionalNote={eachComponent?.adjustment_note}
                    actionNode={
                      <TableActions
                        edit={{
                          onClick: () => onAddClick("earnings", eachComponent),
                          hide: false,
                        }}
                        remove={{
                          onClick: () => onRemoveClick(eachComponent),
                          hide: false,
                        }}
                        view={{
                          onClick: () => {},
                          hide: true,
                        }}
                      />
                    }
                  />
                  {index < earnings?.length - 1 && <Divider orientation="horizontal" />}
                </Box>
              ))}
            </Box>
          </EmployeeComponentTypeContainer>
        </Grid2>
        <Grid2 size={6} borderBottom="0.5px solid gray" borderRight="0.5px solid gray">
          <EmployeeComponentTypeContainer title="Deductions" onAddClick={() => onAddClick("deductions", null)}>
            <Box display="flex" gap={2} flexDirection="column">
              {deductions?.map((eachComponent, index) => (
                <Box display="flex" flexDirection="column" gap={1} key={eachComponent.name}>
                  <ComponentDetailListItem
                    key={eachComponent.name}
                    title={eachComponent.name}
                    value={eachComponent.amount}
                    isEdit={eachComponent?.source === "AD_HOC"}
                    additionalNote={eachComponent?.adjustment_note}
                    actionNode={
                      <TableActions
                        edit={{
                          onClick: () => onAddClick("deductions", eachComponent),
                          hide: false,
                        }}
                        remove={{
                          onClick: () => onRemoveClick(eachComponent),
                          hide: false,
                        }}
                        view={{
                          onClick: () => {},
                          hide: true,
                        }}
                      />
                    }
                  />
                  {index < deductions?.length - 1 && <Divider orientation="horizontal" />}{" "}
                </Box>
              ))}
            </Box>
          </EmployeeComponentTypeContainer>
        </Grid2>
        <Grid2 container size={12}>
          <Grid2 size={6} p={2} borderTop="0.5px solid gray" borderRight="0.5px solid gray">
            <ComponentDetailListItem
              typographyProps={{
                sx: {
                  fontWeight: 600,
                  color: "#007F6F",
                },
              }}
              title="Total Earnings"
              value={(employeePayrunDetails?.earnings || 0) + (employeePayrunDetails?.benefits || 0) || 0}
            />
          </Grid2>
          <Grid2 size={6} p={2} borderTop="0.5px solid gray" borderRight="0.5px solid gray">
            <ComponentDetailListItem
              typographyProps={{
                sx: {
                  fontWeight: 600,
                  color: "#DF2121",
                },
              }}
              title="Total Deductions"
              value={employeePayrunDetails?.deductions}
            />
          </Grid2>
        </Grid2>
        <Grid2 container size={12}>
          <Grid2 size={6} borderTop="0.5px solid gray" borderRight="0.5px solid gray" />
          <Grid2 size={6} p={2} borderTop="0.5px solid gray" borderRight="0.5px solid gray">
            <ComponentDetailListItem
              typographyProps={{
                sx: {
                  fontWeight: 600,
                  color: "#DF2121",
                },
              }}
              title="Total Taxes"
              value={employeePayrunDetails?.tax_amount}
            />
          </Grid2>
        </Grid2>
        <Grid2 size={12} p={2} borderTop="0.5px solid gray" borderRight="0.5px solid gray">
          <ComponentDetailListItem
            typographyProps={{
              sx: {
                fontWeight: 600,
              },
            }}
            title="Net Pay"
            value={employeePayrunDetails?.net_pay}
          />
        </Grid2>
      </Grid2>
      {modalType === "earnings" ? (
        <AddEditEarningAndBenefits
          isModalOpen={!!modalType}
          allCompensations={components}
          selectedRow={componentToEdit}
          employeePayrunDetails={employeePayrunDetails}
          onSubmit={onAddEditSubmit}
          onClose={() => {
            setModalType(null);
            setComponentToEdit(null);
          }}
        />
      ) : modalType === "deductions" ? (
        <AddEditDeductions
          isModalOpen={!!modalType}
          allCompensations={components}
          selectedRow={componentToEdit}
          employeePayrunDetails={employeePayrunDetails}
          onSubmit={onAddEditSubmit}
          onClose={() => {
            setModalType(null);
            setComponentToEdit(null);
          }}
        />
      ) : null}
    </>
  );
};
export default EmployeeDetailWithCompensationComponents;
