import { PlayArrow } from "@mui/icons-material";
import {
  Box,
  <PERSON>ton,
  Card,
  CardContent,
  Checkbox,
  CircularProgress,
  IconButton,
  Tooltip,
  Typography,
} from "@mui/material";
import { useMutation, useQuery } from "@tanstack/react-query";
import React, { useState } from "react";
import { queryClient } from "src/app/App";
import { EmployeeCellInfo } from "src/modules/Common/EmployeeViews/EmployeeCellInfo";
import Modal from "src/modules/Common/Modal/Modal";
import { formatCurrency } from "src/modules/Profile/components/EmployeeCompensation/EmployeeCompensation";
import { PayRunData } from "src/services/api_definitions/payroll.service";
import payrollService from "src/services/payroll.service";

interface PayScheduleModalProps {
  isOpen: boolean;
  onClose: () => void;
  isAdHocPayrunFlow?: boolean;
}

const PayScheduleModal: React.FC<PayScheduleModalProps> = ({ isOpen, onClose, isAdHocPayrunFlow = false }) => {
  const [selectedEmployees, setSelectedEmployees] = useState<Set<string>>(new Set());

  const { data: payScheduleData = [], isLoading } = useQuery({
    queryKey: ["payScheduleData"],
    queryFn: () => payrollService.getPayScheduleData(),
    enabled: isOpen && !isAdHocPayrunFlow, // Only fetch for regular payruns
    refetchOnWindowFocus: false,
  });

  // Fetch excluded payments for adhoc payrun flow
  const { data: excludedEmployees, isLoading: isLoadingExcluded } = useQuery({
    queryKey: ["get-excluded-payments"],
    queryFn: async () => payrollService.getExcludedPayments(),
    enabled: isOpen && isAdHocPayrunFlow, // Only fetch for adhoc payruns
    refetchOnWindowFocus: false,
  });

  const createPayRunMutation = useMutation({
    mutationFn: async (name: string) => payrollService.createPayRun(name),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["get-payruns"] });
      queryClient.invalidateQueries({ queryKey: ["get-adhoc-payruns"] });
      onClose();
    },
  });

  const createAdhocPayrunMutation = useMutation({
    mutationFn: async () => {
      // Use selected employee IDs directly
      const employeeIds = Array.from(selectedEmployees);
      console.log("Creating adhoc payrun with employee IDs:", employeeIds);
      return payrollService.updatePayrun("Off Cycle", employeeIds);
    },
    onSuccess: (data) => {
      console.log("Adhoc payrun created successfully:", data);
      queryClient.invalidateQueries({ queryKey: ["get-payruns"] });
      queryClient.invalidateQueries({ queryKey: ["get-adhoc-payruns"] });
      queryClient.invalidateQueries({ queryKey: ["get-excluded-payments"] });
      setSelectedEmployees(new Set());
      onClose();
    },
    onError: (error) => {
      console.error("Error creating adhoc payrun:", error);
    },
  });

  const handleRegularPayrunCreate = (schedule: PayRunData) => {
    createPayRunMutation.mutate(schedule.name);
  };

  const handleAdhocFormSubmit = () => {
    createAdhocPayrunMutation.mutate();
  };

  const handleEmployeeSelect = (employeeId: string) => {
    const newSelected = new Set(selectedEmployees);
    if (newSelected.has(employeeId)) {
      newSelected.delete(employeeId);
    } else {
      newSelected.add(employeeId);
    }
    setSelectedEmployees(newSelected);
  };

  if ((isLoading && !isAdHocPayrunFlow) || (isLoadingExcluded && isAdHocPayrunFlow)) {
    return (
      <Modal isOpen={isOpen} onClose={onClose} title={`Loading ${isAdHocPayrunFlow ? "Offcycle" : "Regular"} Payruns`}>
        <Box display="flex" justifyContent="center" alignItems="center" height={200}>
          <CircularProgress />
        </Box>
      </Modal>
    );
  }

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={`Create ${isAdHocPayrunFlow ? "Offcycle" : ""} Pay Run`}
      actions={
        isAdHocPayrunFlow ? (
          <Box display="flex" justifyContent="flex-end" alignItems="center" gap={1} p={2}>
            <Button variant="outlined" onClick={onClose}>
              Cancel
            </Button>
            <Button
              variant="contained"
              onClick={handleAdhocFormSubmit}
              disabled={createAdhocPayrunMutation.isLoading || selectedEmployees.size === 0}
            >
              {createAdhocPayrunMutation.isLoading ? "Creating..." : "Create"}
            </Button>
          </Box>
        ) : null
      }
    >
      <Box>
        {isAdHocPayrunFlow ? (
          <Box display="flex" flexDirection="column" gap={2}>
            {!excludedEmployees?.employee_pay_runs || excludedEmployees.employee_pay_runs.length === 0 ? (
              <Typography variant="body2" color="textSecondary" textAlign="center" py={4}>
                No excluded employees found. All employees are included in their respective pay runs.
              </Typography>
            ) : (
              excludedEmployees.employee_pay_runs.map((employee) => (
                <Card
                  key={employee.id}
                  sx={{
                    cursor: "pointer",
                    border: selectedEmployees.has(employee.id) ? "2px solid" : "1px solid",
                    borderColor: selectedEmployees.has(employee.id) ? "primary.main" : "divider",
                    "&:hover": {
                      boxShadow: 2,
                    },
                  }}
                  onClick={() => handleEmployeeSelect(employee.id)}
                >
                  <CardContent>
                    <Box display="flex" alignItems="center" gap={2}>
                      <Checkbox
                        checked={selectedEmployees.has(employee.id)}
                        onChange={() => handleEmployeeSelect(employee.id)}
                        onClick={(e) => e.stopPropagation()}
                      />
                      <Box flex={1}>
                        <EmployeeCellInfo
                          name={employee.employee?.display_name as string}
                          jobTitle={employee.employee?.job_title}
                          displayPic={employee.employee?.display_pic}
                        />
                      </Box>
                      <Box display="flex" gap={4}>
                        <Box textAlign="center">
                          <Typography variant="body2" color="text.secondary">
                            Pay Period
                          </Typography>
                          <Typography variant="subtitle2">{employee.period}</Typography>
                        </Box>
                        <Box textAlign="center">
                          <Typography variant="body2" color="text.secondary">
                            Gross Pay
                          </Typography>
                          <Typography variant="subtitle2">{formatCurrency(employee.gross_pay)}</Typography>
                        </Box>
                        <Box textAlign="center">
                          <Typography variant="body2" color="text.secondary">
                            Net Pay
                          </Typography>
                          <Typography variant="subtitle2">{formatCurrency(employee.net_pay)}</Typography>
                        </Box>
                      </Box>
                    </Box>
                  </CardContent>
                </Card>
              ))
            )}
          </Box>
        ) : (
          <Box display="flex" flexDirection="column" gap={2}>
            {payScheduleData.filter((schedule) => !schedule.has_active_pay_run).length === 0 ? (
              <Typography variant="body2" color="textSecondary" textAlign="center" py={4}>
                {payScheduleData.length === 0
                  ? "No pay runs found. Please create a pay run first."
                  : "All pay schedules have active pay runs. Complete existing pay runs before creating new ones."}
              </Typography>
            ) : (
              payScheduleData
                .filter((schedule) => !schedule.has_active_pay_run)
                .map((schedule) => (
                  <Card key={schedule.name} variant="outlined" sx={{ border: "1px solid #e0e0e0" }}>
                    <CardContent sx={{ p: 3 }}>
                      <Box display="flex" justifyContent="space-between" alignItems="flex-start">
                        <Box flex={1}>
                          <Box display="flex" alignItems="center" gap={1} mb={1}>
                            <Typography variant="h6" fontWeight={600}>
                              {schedule.name}
                            </Typography>
                          </Box>

                          <Box display="flex" flexDirection="column" gap={0.5} mb={2}>
                            <Typography variant="body2" color="textSecondary">
                              <strong>Employee Types:</strong> {schedule.employee_types.join(", ")}
                            </Typography>
                            <Typography variant="body2" color="textSecondary">
                              <strong>Cost Centers:</strong> {schedule.cost_centers.join(", ")}
                            </Typography>
                            <Typography variant="body2" color="textSecondary">
                              <strong>Location:</strong> {schedule.organisation_address}
                            </Typography>
                          </Box>
                        </Box>

                        <Tooltip title="Create pay run for this schedule">
                          <IconButton
                            color="primary"
                            size="large"
                            onClick={() => handleRegularPayrunCreate(schedule)}
                            disabled={createPayRunMutation.isLoading}
                            sx={{
                              backgroundColor: "#f5f5f5",
                              "&:hover": { backgroundColor: "#e0e0e0" },
                              ml: 2,
                              alignSelf: "center",
                            }}
                          >
                            <PlayArrow fontSize="large" />
                          </IconButton>
                        </Tooltip>
                      </Box>
                    </CardContent>
                  </Card>
                ))
            )}
          </Box>
        )}
      </Box>
    </Modal>
  );
};

export default PayScheduleModal;
