import Timeline from "@mui/lab/Timeline";
import TimelineConnector from "@mui/lab/TimelineConnector";
import TimelineContent from "@mui/lab/TimelineContent";
import TimelineDot from "@mui/lab/TimelineDot";
import { timelineItemClasses } from "@mui/lab/TimelineItem";
import { Box, Typography } from "@mui/material";
import { styled } from "@mui/material/styles";

export type StepState = "done" | "pending" | "upcoming";

export const StyledTimeline = styled(Timeline)(({ theme }) => ({
  [`& .${timelineItemClasses.root}:before`]: { flex: 0, padding: 0 },
  marginTop: theme.spacing(1),
}));

export const Card = styled(Box)(() => ({
  border: "1px solid #E0E0E0",
  borderRadius: 16,
  padding: 16,
  width: "fit-content",
  minWidth: 260,
  boxShadow: "0 1px 2px rgba(0,0,0,0.04)",
}));

export const StatusPill = styled(Box, {
  shouldForwardProp: (prop) => prop !== "state",
})<{ state: StepState }>(({ state }) => ({
  display: "inline-block",
  padding: "6px 12px",
  borderRadius: 10,
  marginBottom: 8,
  fontWeight: 600,
  fontSize: 12,
  backgroundColor: state === "done" ? "#E8FFF5" : state === "pending" ? "#FFF6DA" : "#F1F3F5",
  color: state === "done" ? "#2F9E69" : state === "pending" ? "#926E00" : "#6B7280",
}));

export const Row = styled(Box)(() => ({
  display: "flex",
  alignItems: "center",
  gap: 12,
  color: "#637381",
}));

export const Title = styled(Typography)(() => ({
  marginBottom: 8,
  fontWeight: 600,
}));

export const Content = styled(TimelineContent)(() => ({
  padding: "12px 16px",
}));

export const Connector = styled(TimelineConnector, {
  shouldForwardProp: (prop) => prop !== "state",
})<{ state: StepState }>(({ state }) => ({
  backgroundColor: state === "done" ? "#3BB77E" : state === "pending" ? "#DDB651" : "#C8CDD2",
  width: 2,
}));

export const Dot = styled(TimelineDot, {
  shouldForwardProp: (prop) => prop !== "state",
})<{ state: StepState }>(({ state }) => ({
  backgroundColor: state === "done" ? "#E8FFF5" : state === "pending" ? "#FFF6DA" : "#ECEFF1",
  boxShadow: "none",
  padding: 4,
}));

export const colors = {
  icon: { done: "#2F9E69", pending: "#D39B00", upcoming: "#9E9E9E" },
};

export const AdditionalCount = styled("span")(({ theme }) => ({
  color: theme.palette.primary.main,
  fontWeight: 700,
  backgroundColor: theme.palette.primary.main + "14", // subtle tint
  borderRadius: 999,
  padding: "0px 6px",
  display: "inline-block",
  lineHeight: 1.4,
}));

export const InfoLabel = styled("span")(({ theme }) => ({
  color: theme.palette.text.secondary,
  fontWeight: 600,
  marginRight: 4,
}));

export const InfoValue = styled("span")(({ theme }) => ({
  color: theme.palette.text.primary,
  fontWeight: 600,
}));
