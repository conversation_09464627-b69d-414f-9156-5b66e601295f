import { Typography } from "@mui/material";
import React from "react";
import { StatutoryComponent } from "src/services/api_definitions/payroll.service";

export const SAMPLE_EPF_CALCULATION_DATA = [
  {
    salary_component: "Basic",
    package1: "₹ 40,000",
    package2: "₹ 25,000",
    package3: "₹ 12,000",
  },
  {
    salary_component: "Transport Allowance",
    package1: "₹ 5,000",
    package2: "₹ 2,000",
    package3: "₹ 1,000",
  },
  {
    salary_component: "Telephone Allowance",
    package1: "₹ 2,000",
    package2: "₹ 1,500",
    package3: "₹ 1,000",
  },
  {
    salary_component: "Restrict Contribution to ₹15,000 of PF Wage (Calculation: 12% of 15,000)",
    package1: "₹ 1,800",
    package2: "₹ 1,800",
    package3: "₹ 1,680",
  },
  {
    salary_component: (
      <Typography sx={{ fontSize: 14, fontWeight: 400, color: "#000000" }}>
        12% of Actual PF Wage
        <br /> (Calculation: 12% of Basic)
      </Typography>
    ),
    package1: "₹ 4,800",
    package2: "₹ 3,000",
    package3: "₹ 1,680",
  },
];

export const SAMPLE_EPF_CALCULATION_DATA_COLUMNS = [
  {
    header: "Salary Component",
    accessorKey: "salary_component",
    size: 300,
  },
  {
    header: "Package 1",
    accessorKey: "package1",
    size: 100,
  },
  {
    header: "Package 2",
    accessorKey: "package2",
    size: 100,
  },
  {
    header: "Package 3",
    accessorKey: "package3",
    size: 100,
  },
];

export const EPF_CONFIGURED_DETAILS = (statutoryComponent: StatutoryComponent) => {
  return [
    {
      label: "EPF Number",
      value: statutoryComponent?.registration_number,
    },
    {
      label: "EPF Registration Date",
      value: statutoryComponent?.registration_date,
    },
    {
      label: "Signatory Name",
      value: statutoryComponent?.signatory_name,
    },
    {
      label: "Allow Employee level Override",
      value: statutoryComponent?.can_employee_opt_out ? "Yes" : "No",
    },
    {
      label: "Pro-rate Restricted PF Wage",
      value: statutoryComponent?.attendance_based ? "Yes" : "No",
    },
    {
      label: "Deduction Cycle",
      value: statutoryComponent?.periodicity,
    },
  ];
};

export const getTaxNumberHeader = (taxType: string) => {
  if (taxType === "Professional Tax") {
    return "PT";
  } else if (taxType === "LWF") {
    return "LWF";
  } else if (taxType === "ESI") {
    return "ESI";
  } else if (taxType === "EPF") {
    return "EPF";
  }
  return "";
};

export const replaceEmptyString = (value: string) => {
  return value ? (
    <Typography fontSize={14} fontWeight={500}>
      {value}
    </Typography>
  ) : (
    <Typography fontSize={14} fontWeight={500}>
      --
    </Typography>
  );
};
