import { Box } from "@mui/material";
import { useQuery } from "@tanstack/react-query";
import React from "react";
import ContentHeader from "src/modules/Common/ContentHeader/ContentHeader";
import TabsView from "src/modules/Common/CustomTabs/CustomTabs";
import { StatutoryComponent } from "src/services/api_definitions/payroll.service";
import payrollService from "src/services/payroll.service";
import AddEditComponent from "./AddEditComponent";
import StatutoryTable from "./StatutoryTable";

const tabs = [
  {
    id: 0,
    label: "Employee Provident Fund",
    key: "EPF",
  },
  {
    id: 1,
    label: "Employee State Insurance",
    key: "ESI",
  },
  {
    id: 2,
    label: "Professional Tax",
    key: "Professional Tax",
  },
  {
    id: 3,
    label: "Labour Welfare Fund",
    key: "LWF",
  },
];

const CompensationComponents = () => {
  const [activeTab, setActiveTab] = React.useState(0);
  const [selectedRow, setSelectedRow] = React.useState<StatutoryComponent | null>(null);
  const {
    data: statutoryComponents,
    // refetch,
    // isLoading,
  } = useQuery(
    ["get-statutory-components", activeTab, selectedRow],
    async () => {
      const statutoryComponents = await payrollService.getStatutoryComponents(tabs[activeTab || 0].key);
      return statutoryComponents;
    },
    {
      enabled: activeTab !== undefined && !selectedRow,
      refetchOnWindowFocus: false,
    },
  );

  const handleChange = (value: number) => {
    setActiveTab(value);
  };

  if (selectedRow) {
    return (
      <AddEditComponent
        selectedRow={selectedRow}
        setSelectedRow={setSelectedRow}
        tax_type={tabs[activeTab || 0].key}
        taxLabel={tabs[activeTab || 0].label}
      />
    );
  }

  return (
    <Box display="flex" flexDirection="column" gap={2}>
      <ContentHeader title="Statutory Components" />
      <TabsView
        handleTabChange={handleChange}
        currentTabIndex={activeTab}
        tabs={tabs.map((tab) => ({
          ...tab,
          component: (
            <StatutoryTable
              setSelectedRow={setSelectedRow}
              taxType={tab.key}
              statutoryComponents={statutoryComponents as StatutoryComponent[]}
            />
          ),
        }))}
      />
    </Box>
  );
};

export default CompensationComponents;
