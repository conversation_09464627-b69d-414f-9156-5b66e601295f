import { Edit, Visibility, Warning } from "@mui/icons-material";
import { Box, Button, Tooltip, Typography } from "@mui/material";
import { MRT_Cell, MRT_RowData } from "material-react-table";
import React, { useState } from "react";
import Modal from "src/modules/Common/Modal/Modal";
import DataTable from "src/modules/Common/Table/DataTable";
import { StatutoryComponent } from "src/services/api_definitions/payroll.service";
import {
  EPF_CONFIGURED_DETAILS,
  getTaxNumberHeader,
  replaceEmptyString,
  SAMPLE_EPF_CALCULATION_DATA,
  SAMPLE_EPF_CALCULATION_DATA_COLUMNS,
} from "./config";

const StatutoryTable = ({
  taxType,
  statutoryComponents,
  setSelectedRow,
}: {
  taxType: string;
  statutoryComponents: StatutoryComponent[];
  setSelectedRow: (row: StatutoryComponent) => void;
}) => {
  const columns = [
    {
      header: "Organisation Location",
      accessorKey: "region",
      size: 200,
      Cell: ({ row }: { row: MRT_RowData }) => replaceEmptyString(row?.original?.region),
    },
    {
      header: "Deduction Cycle",
      accessorKey: "periodicity",
      Cell: ({ row }: { row: MRT_RowData }) => replaceEmptyString(row?.original?.periodicity),
    },
    {
      header: "Signatory Name",
      accessorKey: "signatory_name",
      Cell: ({ row }: { row: MRT_RowData }) => replaceEmptyString(row?.original?.signatory_name),
    },
    {
      header: `${getTaxNumberHeader(taxType)} Number`,
      accessorKey: "registration_number",
      Cell: ({ row }: { row: MRT_RowData }) => replaceEmptyString(row?.original?.registration_number),
    },
    {
      header: "Action",
      accessorKey: "action",
      Cell: ({ cell }: { cell: MRT_Cell<StatutoryComponent, any> }) => {
        return (
          <Box
            sx={{ display: "flex", flexDirection: "row", alignItems: "center", justifyContent: "flex-start", gap: 1 }}
          >
            <Button
              variant="text"
              color="primary"
              size="small"
              onClick={() => {
                setSelectedRow(cell.row.original);
              }}
            >
              {cell.row.original.registration_number ? "Edit" : "Setup"}
            </Button>
            {cell.row.original.warning_message && (
              <Tooltip title={cell.row.original.warning_message}>
                <Warning sx={{ color: "#FFBF00", cursor: "pointer", fontSize: "large" }} />
              </Tooltip>
            )}
          </Box>
        );
      },
      // size: 50,
    },
  ];

  const [openPreviewEPFCalculation, setOpenPreviewEPFCalculation] = useState(false);

  const PreviewEPFCalculation = () => {
    return (
      <Modal
        title={"Sample EPF Calculation"}
        subtitle={""}
        isOpen={openPreviewEPFCalculation}
        onClose={() => setOpenPreviewEPFCalculation(false)}
        PaperProps={{ sx: { borderRadius: "20px" } }}
      >
        <Box>
          <DataTable
            muiTableContainerProps={{
              sx: {
                maxHeight: "800px",
              },
            }}
            muiTableBodyRowProps={({ row, table }) => {
              const totalRows = table.getRowModel().rows.length;
              const isBottomTwo = row.index >= totalRows - 2;
              return {
                sx: {
                  height: "80px", // fixed height for every row
                  ...(isBottomTwo && { backgroundColor: "#E6F2F1" }), // color bottom two rows
                },
              };
            }}
            data={SAMPLE_EPF_CALCULATION_DATA}
            columns={SAMPLE_EPF_CALCULATION_DATA_COLUMNS}
          />
        </Box>
      </Modal>
    );
  };
  const epfConfiguredDetails = EPF_CONFIGURED_DETAILS(statutoryComponents?.[0]);

  if (taxType === "EPF" && !statutoryComponents?.[0]?.registration_number) {
    return (
      <Box
        sx={{
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
          justifyContent: "center",
          height: "50vh",
          width: "100%",
          gap: 2,
        }}
      >
        <Button
          variant="contained"
          color="primary"
          size="small"
          onClick={() => {
            setSelectedRow({} as StatutoryComponent);
          }}
        >
          Enable EPF
        </Button>
        {statutoryComponents?.[0]?.warning_message && (
          <Box sx={{ display: "flex", flexDirection: "row", alignItems: "flex-start", justifyContent: "flex-start" }}>
            <Warning sx={{ color: "#FFBF00", cursor: "pointer" }} fontSize="small" />
            <Typography sx={{ fontSize: 14, fontWeight: 400 }} align="center">
              {statutoryComponents?.[0]?.warning_message}
            </Typography>
          </Box>
        )}
      </Box>
    );
  } else if (taxType === "EPF" && statutoryComponents?.[0]?.registration_number) {
    return (
      <Box sx={{ p: 2, pt: 0 }}>
        <Box sx={{ display: "flex", flexDirection: "row", alignItems: "center", justifyContent: "flex-start", gap: 2 }}>
          <Typography sx={{ fontSize: 16, fontWeight: 600, color: "#000000" }} mt={2} mb={2}>
            Employees' Provident Fund
          </Typography>
          <Edit
            onClick={() => {
              setSelectedRow(statutoryComponents[0]);
            }}
            sx={{ color: "#667085", cursor: "pointer" }}
          />
          <Box
            onClick={() => {
              setOpenPreviewEPFCalculation(true);
            }}
            sx={{
              display: "flex",
              flexDirection: "row",
              alignItems: "center",
              justifyContent: "flex-start",
              gap: 1,
              cursor: "pointer",
              marginLeft: "auto",
            }}
          >
            <Visibility sx={{ color: "#007F6F", cursor: "pointer" }} />
            <Typography sx={{ fontSize: 14, fontWeight: 400, color: "#007F6F" }}>Preview EPF Calculation</Typography>
          </Box>
        </Box>
        <Box sx={{ border: "1px solid #E0E0E0", borderRadius: 1 }}>
          {epfConfiguredDetails.map((item: any) => (
            <Box
              key={item?.label}
              sx={{
                display: "flex",
                flexDirection: "row",
                alignItems: "center",
                p: 2,
                justifyContent: "space-between",
                borderBottom: "1px solid #E0E0E0",
              }}
            >
              <Typography sx={{ fontSize: 14, fontWeight: 400, color: "#42526B" }}>{item?.label}</Typography>
              <Box display="flex" flexDirection="row" alignItems="center" gap={1} width="50%">
                <Typography sx={{ fontSize: 14, fontWeight: 400, color: "#000000" }}>{item?.value}</Typography>
              </Box>
            </Box>
          ))}
        </Box>
        <PreviewEPFCalculation />
      </Box>
    );
  }
  return <DataTable data={statutoryComponents} columns={columns} />;
};

export default StatutoryTable;
