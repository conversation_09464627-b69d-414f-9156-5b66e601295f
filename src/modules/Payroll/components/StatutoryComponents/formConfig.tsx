import { Info } from "@mui/icons-material";
import { Box, Typography } from "@mui/material";
import React from "react";
import { StatutoryComponent } from "src/services/api_definitions/payroll.service";
import { z } from "zod";

const regionForm = [
  {
    fieldProps: {
      name: "region",
    },
    formProps: {
      label: "State",
      type: "text",
      required: true,
      disabled: true,
    },
    containerProps: {
      size: 12,
    },
  },
];

const getTaxNumberHeader = (taxType: string) => {
  if (taxType === "Professional Tax") {
    return "PT";
  } else if (taxType === "Labour Welfare Fund") {
    return "LWF";
  } else if (taxType === "Employee State Insurance") {
    return "ESI";
  } else if (taxType === "Employee Provident Fund") {
    return "EPF";
  }
  return "";
};

const inputFields = (selectedRow: StatutoryComponent, taxLabel: string) => [
  {
    fieldProps: {
      name: "registration_number",
    },
    formProps: {
      label: `${getTaxNumberHeader(taxLabel)} Number`,
      type: "text",
      required: true,
      disabled: !!selectedRow?.registration_number,
    },
    containerProps: {
      size: 6,
    },
  },
  {
    fieldProps: {
      name: "registration_date",
    },
    formProps: {
      label: `${getTaxNumberHeader(taxLabel)} Registration Date`,
      type: "date",
      required: true,
      disabled: !!selectedRow?.registration_date,
    },
    containerProps: {
      size: 6,
    },
  },
];

const employeeSwitchForm = [
  {
    fieldProps: {
      name: "is_employee",
    },
    formProps: {
      label: "",
      type: "radio",
      required: true,
      options: [
        {
          value: true,
          label: "Employee",
        },
        {
          value: false,
          label: "Non-Employee",
        },
      ],
    },
    containerProps: {
      size: 12,
    },
  },
];

const employeeTaxDeductorDetailsForm = (setSearchResponse: any) => [
  {
    fieldProps: {
      name: "signatory_name",
      listeners: {
        onChange: (value: any) => {
          setSearchResponse(value?.value?.originalRow);
        },
      },
      getData: "value",
    },
    formProps: {
      label: "Name",
      type: "auto-complete",
      required: true,
      size: "small" as any,
    },
    containerProps: {
      size: 6,
    },
  },
  {
    fieldProps: {
      name: "signatory_designation",
    },
    formProps: {
      label: "Designation",
      type: "text",
      required: false,
    },
    containerProps: {
      size: 6,
    },
  },
  {
    fieldProps: {
      name: "signatory_phone",
    },
    formProps: {
      label: "Phone Number",
      type: "phone",
      required: false,
    },
    containerProps: {
      size: 6,
    },
  },
  {
    fieldProps: {
      name: "signatory_email",
    },
    formProps: {
      label: "Email",
      type: "text",
      required: true,
      disabled: true,
    },
    containerProps: {
      size: 6,
    },
  },
];

const nonEmployeeTaxDeductorDetailsForm = [
  {
    fieldProps: {
      name: "signatory_display_name",
    },
    formProps: {
      label: "Name",
      type: "text",
      required: true,
    },
    containerProps: {
      size: 6,
    },
  },
  {
    fieldProps: {
      name: "signatory_designation",
    },
    formProps: {
      label: "Designation",
      type: "text",
      required: true,
    },
    containerProps: {
      size: 6,
    },
  },
  {
    fieldProps: {
      name: "signatory_phone",
    },
    formProps: {
      label: "Phone Number",
      type: "phone",
      required: false,
    },
    containerProps: {
      size: 6,
    },
  },
  {
    fieldProps: {
      name: "signatory_email",
    },
    formProps: {
      label: "Email",
      type: "text",
      required: false,
    },
    containerProps: {
      size: 6,
    },
  },
];

const getSubLabel = (value: string) => {
  return EPF_CALCULATION_METHODS.find((item) => item.value === value)?.subLabel;
};

const EPF_CALCULATION_METHODS = [
  {
    label: "Restricted Basic (Capped at ₹15,000)",
    value: "RESTRICTED_BASIC",
    subLabel: "PF is calculated on a capped basic salary of ₹15,000, regardless of the employee’s actual basic salary.",
  },
  {
    label: "Actual Basic Salary",
    value: "ACTUAL_BASIC",
    subLabel: "PF is calculated on the full basic salary of the employee with no cap.",
  },
  {
    label: "Allow both options (configure per employee)",
    value: "ALLOW_BOTH_OPTIONS",
    subLabel:
      "Let employees be assigned either calculation method individually. Admin can choose which method applies to each employee during onboarding or salary setup.",
  },
];

const getLabelFromValue = (value: string) => {
  return EPF_CALCULATION_METHODS.find((item) => item.value === value)?.label;
};

const employeeCalculationTypeForm = () => [
  {
    fieldProps: {
      name: "calculation_method_employee",
    },
    formProps: {
      label: "",
      layout: "vertical",
      type: "radio-group",
      required: true,
      options: EPF_CALCULATION_METHODS,
    },
    containerProps: {
      size: 12,
    },
  },
];

const employerCalculationTypeForm = () => [
  {
    fieldProps: {
      name: "calculation_method_employer",
    },
    formProps: {
      label: "",
      layout: "vertical",
      type: "radio-group",
      required: true,
      options: [
        {
          label: "Restricted Basic (Capped at ₹15,000)",
          value: "RESTRICTED_BASIC",
          subLabel:
            "PF is calculated on a capped basic salary of ₹15,000, regardless of the employee’s actual basic salary.",
        },
        {
          label: "Actual Basic Salary",
          value: "ACTUAL_BASIC",
          subLabel: "PF is calculated on the full basic salary of the employee with no cap.",
        },
      ],
    },
    containerProps: {
      size: 12,
    },
  },
];

const formSchema = z
  .object({
    // region: z.string().nonempty({
    //   message: "Region is required",
    // }),
    registration_number: z.string().nonempty({
      message: "Registration Number is required",
    }),
    registration_date: z.string().nonempty({
      message: "Registration Date is required",
    }),
    signatory_name: z
      .object({
        searchResult: z.string().optional(),
      })
      .optional()
      .or(z.string().optional()),

    signatory_designation: z.string().optional().nullable(),
    signatory_email: z.string(),
    signatory_display_name: z.string().optional().nullable(),
    is_employee: z.boolean(),
    calculation_method_employee: z.string(),
    calculation_method_employer: z.string(),
    attendance_based: z.boolean(),
    can_employee_opt_out: z.boolean(),
  })
  .refine(
    (data) => {
      const isEmployee = !!data?.is_employee;
      return isEmployee ? !!data?.signatory_name : !!data?.signatory_display_name && !!data?.signatory_designation;
    },
    {
      message: "Name is required",
      path: [""],
    },
  );

const getTaxTypeDescription = (period: string, tax_type: string) => {
  if (tax_type === "Professional Tax") {
    return `The Professional Tax contributions will be deducted on a ${period?.toLowerCase() || "monthly"} basis, in accordance with the standard payroll cycle`;
  }
  if (tax_type === "ESI") {
    return `The Employees' State Insurance contributions will be deducted on a ${period?.toLowerCase() || "monthly"} basis, in accordance with the standard payroll cycle`;
  }
  if (tax_type === "EPF") {
    return `The Employees' Provident Fund contributions will be deducted on a ${period?.toLowerCase() || "monthly"} basis, in accordance with the standard payroll cycle`;
  }
  return "";
};

const getESIDescription = () => {
  return `ESI contributions will continue to be paid for employees even during periods of Loss of Pay (LOP), as required under statutory guidelines.`;
};

const InfoText = ({ text }: { text: string }) => {
  return (
    <Box>
      <Box sx={{ display: "flex", flexDirection: "row", alignItems: "center", gap: 1 }} mt={2} mb={2}>
        {text && <Info sx={{ fontSize: 20, fontWeight: 600, color: "#667085" }} />}
        <Typography sx={{ fontSize: 14, fontWeight: 400, color: "#667085", marginLeft: "2px" }}>{text}</Typography>
      </Box>
    </Box>
  );
};

export {
  regionForm,
  inputFields,
  employeeSwitchForm,
  employeeTaxDeductorDetailsForm,
  nonEmployeeTaxDeductorDetailsForm,
  employeeCalculationTypeForm,
  employerCalculationTypeForm,
  formSchema,
  getLabelFromValue,
  InfoText,
  getESIDescription,
  getTaxTypeDescription,
  getSubLabel,
};
