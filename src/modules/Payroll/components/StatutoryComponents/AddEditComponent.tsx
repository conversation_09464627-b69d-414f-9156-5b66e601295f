import { InfoOutlined } from "@mui/icons-material";
import { Box, Button, Tooltip, Typography } from "@mui/material";
import { useStore } from "@tanstack/react-form";
import { useMutation } from "@tanstack/react-query";
import React, { useEffect } from "react";
import { useAppDispatch } from "src/customHooks/useAppDispatch";
import ContentHeader from "src/modules/Common/ContentHeader/ContentHeader";
import EffiDynamicForm from "src/modules/Common/Form/components/EffiDynamicForm";
import { useAppForm } from "src/modules/Common/Form/effiFormContext";
import DataTable from "src/modules/Common/Table/DataTable";
import { StatutoryComponent, StatutoryComponentRequest } from "src/services/api_definitions/payroll.service";
import payrollService from "src/services/payroll.service";
import { setFullviewMode } from "src/store/slices/app.slice";
import {
  employeeCalculationTypeForm,
  employeeSwitchForm,
  employeeTaxDeductorDetailsForm,
  employerCalculationTypeForm,
  formSchema,
  getESIDescription,
  getLabelFromValue,
  getSubLabel,
  getTaxTypeDescription,
  InfoText,
  inputFields,
  nonEmployeeTaxDeductorDetailsForm,
  regionForm,
} from "./formConfig";
import professionalTaxData from "./professional_tax_data";

const ATTENDANCE_DEPENDENT_INFO =
  "PF wage will be calculated using the earned salary for that specific month (after LOP deductions), rather than using the fixed monthly salary.";
const PF_OPT_OUT_INFO =
  "If enabled, the employee can opt out of PF. Form 11 will be auto-shared to collect and verify previous PF details for approval.";

const AddEditComponent = ({
  selectedRow,
  setSelectedRow,
  tax_type,
  taxLabel,
}: {
  selectedRow: StatutoryComponent;
  setSelectedRow: (row: StatutoryComponent | null) => void;
  tax_type: string;
  taxLabel: string;
}) => {
  const dispatch = useAppDispatch();

  useEffect(() => {
    dispatch(setFullviewMode(true));
    return () => {
      dispatch(setFullviewMode(false));
    };
  }, []);

  const createComponent = useMutation({
    mutationKey: ["create-component"],
    mutationFn: async (payload: StatutoryComponentRequest) => payrollService.createStatutoryComponent(payload),
    onSuccess: () => {
      goBack();
    },
    onError: (error) => {
      return error;
    },
  });

  const updateComponent = useMutation({
    mutationKey: ["update-component"],
    mutationFn: async (payload: StatutoryComponentRequest) => payrollService.updateStatutoryComponent(payload),
    onSuccess: () => {
      goBack();
    },
    onError: (error) => {
      return error;
    },
  });

  const goBack = () => {
    setSelectedRow(null);
  };

  const getInitialValues = () => {
    const isEmployee = !!selectedRow?.is_employee;
    const initialValues = {
      ...selectedRow,
      signatory_phone: {
        countryCode: selectedRow?.signatory_phone?.country_code || "",
        phone: selectedRow?.signatory_phone?.number || "",
      },
      signatory_name: isEmployee ? { searchResult: selectedRow?.signatory_name } : "",
      signatory_display_name: isEmployee ? "" : selectedRow?.signatory_name,
      signatory_designation: selectedRow?.signatory_designation || "",
      signatory_email: selectedRow?.signatory_email || "",
      is_employee: !!selectedRow?.is_employee,
      registration_date: selectedRow?.registration_date || "",
      registration_number: selectedRow?.registration_number || "",
      calculation_method_employee: selectedRow?.calculation_method_employee || "RESTRICTED_BASIC",
      calculation_method_employer: selectedRow?.calculation_method_employer || "RESTRICTED_BASIC",
      attendance_based: selectedRow?.attendance_based || false,
      can_employee_opt_out: selectedRow?.can_employee_opt_out || false,
    };
    return initialValues;
  };

  const form = useAppForm({
    defaultValues: getInitialValues(),
    validators: {
      onChange: formSchema as any,
      onSubmit: formSchema as any,
    },
    onSubmit: ({ value }) => {
      const isEmployee = !!value?.is_employee;
      const payload = {
        ...value,
        tax_type: tax_type,
        signatory_phone: value?.signatory_phone?.phone
          ? value?.signatory_phone?.countryCode + value?.signatory_phone?.phone
          : null,
        signatory_name: isEmployee ? (value?.signatory_name as any)?.searchResult : value?.signatory_display_name,
        signatory_display_name: undefined,
        signatory_email: !value?.signatory_email ? null : value?.signatory_email,
      };
      if (tax_type === "EPF" && !selectedRow?.registration_number) {
        createComponent.mutate(payload);
      } else {
        updateComponent.mutate(payload);
      }
    },
  });

  const { is_employee } = useStore(form.store, (state: any) => state.values);

  const setSearchResponse = (originalRow: any) => {
    if (originalRow) {
      form.setFieldValue("signatory_email", originalRow?.email);
    }
  };

  const isFilled = !!selectedRow?.registration_number;

  const epfConfiguredDetails = [
    {
      label: "PF Calculation Method (Employee)",
      value: getLabelFromValue(selectedRow?.calculation_method_employee),
      infoText: getSubLabel(selectedRow?.calculation_method_employee),
    },
    {
      label: "PF Calculation Method (Employer)",
      value: getLabelFromValue(selectedRow?.calculation_method_employer),
      infoText: getSubLabel(selectedRow?.calculation_method_employer),
    },
    {
      label: "Attendance Dependent",
      value: selectedRow?.attendance_based ? "Yes" : "No",
      // infoText: ATTENDANCE_DEPENDENT_INFO,
    },
    {
      label: "Enable PF opt-out for eligible employees",
      value: selectedRow?.can_employee_opt_out ? "Yes" : "No",
      // infoText: PF_OPT_OUT_INFO,
    },
  ];

  const ConfigValueRenderer = ({ title, value, subtitle }: { title: string; value: any; subtitle: string }) => {
    return (
      <Box>
        <Typography sx={{ fontSize: 16, fontWeight: 600, color: "#000000" }} mt={2} mb={2}>
          {title}
        </Typography>
        <Box sx={{ backgroundColor: "#E6F2F1", borderRadius: 3, pl: 2, pr: 2, pt: 1, pb: 1, mt: 2 }}>
          <Typography sx={{ fontSize: 16, fontWeight: 600, color: "#000000" }} mt={2} mb={2}>
            {subtitle}
          </Typography>
          <Box sx={{ borderTop: "1px solid #E0E0E0", pt: 2 }}>
            {value.map((item: any) => (
              <Box
                key={item?.label}
                sx={{
                  display: "flex",
                  flexDirection: "row",
                  alignItems: "center",
                  gap: 1,
                  mb: 3,
                  justifyContent: "space-between",
                }}
              >
                <Typography sx={{ fontSize: 14, fontWeight: 400, color: "#000000" }}>{item?.label}</Typography>
                <Box display="flex" flexDirection="row" alignItems="center" gap={1}>
                  <Typography sx={{ fontSize: 14, fontWeight: 400, color: "#000000" }}>{item?.value}</Typography>
                  {item?.infoText && (
                    <Tooltip title={item?.infoText} arrow>
                      <InfoOutlined fontSize="small" sx={{ ml: 0.5, color: "gray" }} />
                    </Tooltip>
                  )}
                </Box>
              </Box>
            ))}
          </Box>
        </Box>
      </Box>
    );
  };

  const getPFContributionOptions = () => {
    if (isFilled) {
      return <ConfigValueRenderer title="Configured Details" subtitle="Preferences" value={epfConfiguredDetails} />;
    }
    return (
      <Box>
        <Typography sx={{ fontSize: 16, fontWeight: 600, color: "#000000" }} mt={2}>
          PF Calculation Method
        </Typography>
        <Box
          sx={{
            display: "flex",
            flexDirection: "row",
            alignItems: "stretch",
            gap: 2,
            justifyContent: "space-between",
          }}
          mt={2}
          mb={2}
        >
          <Box border="1px solid #E0E0E0" borderRadius={1} p={2} width="50%">
            <Typography sx={{ fontSize: 14, fontWeight: 600, color: "#000000" }} mb={1}>
              Employee Contribution Rate
            </Typography>
            <EffiDynamicForm form={form} inputFields={employeeCalculationTypeForm()} />
          </Box>
          <Box border="1px solid #E0E0E0" borderRadius={1} p={2} width="50%">
            <Typography sx={{ fontSize: 14, fontWeight: 600, color: "#000000" }} mb={1}>
              Employer Contribution Rate
            </Typography>
            <EffiDynamicForm form={form} inputFields={employerCalculationTypeForm()} />
          </Box>
        </Box>
        <Box>
          <Typography sx={{ fontSize: 16, fontWeight: 600, color: "#000000" }} mb={2}>
            Configurations
          </Typography>
          <Box border="1px solid #E0E0E0" borderRadius={1} p={2} width="100%">
            <form.AppField name="attendance_based">
              {(field: any) => (
                <field.EffiCheckbox
                  label="Attendance Dependent"
                  infoText={ATTENDANCE_DEPENDENT_INFO}
                  disabled={isFilled}
                />
              )}
            </form.AppField>
            <form.AppField name="can_employee_opt_out">
              {(field: any) => (
                <field.EffiCheckbox
                  label="Enable PF opt-out for eligible employees"
                  infoText={PF_OPT_OUT_INFO}
                  disabled={isFilled}
                />
              )}
            </form.AppField>
          </Box>
        </Box>
      </Box>
    );
  };

  return (
    <Box>
      <Box mb={2}>
        <ContentHeader title={taxLabel} showBackButton goBack={goBack} />
      </Box>
      {tax_type !== "EPF" && (
        <Box mb={2}>
          <EffiDynamicForm form={form} inputFields={regionForm} />
        </Box>
      )}
      <EffiDynamicForm form={form} inputFields={inputFields(selectedRow, taxLabel)} />
      <InfoText text={getTaxTypeDescription(selectedRow?.periodicity, tax_type)} />
      <Typography sx={{ fontSize: 16, fontWeight: 600, color: "#000000" }}>Signatory Details</Typography>

      <EffiDynamicForm form={form} inputFields={employeeSwitchForm} />
      {is_employee && <EffiDynamicForm form={form} inputFields={employeeTaxDeductorDetailsForm(setSearchResponse)} />}
      {!is_employee && <EffiDynamicForm form={form} inputFields={nonEmployeeTaxDeductorDetailsForm} />}
      {tax_type === "EPF" && getPFContributionOptions()}
      {tax_type === "ESI" && <InfoText text={getESIDescription()} />}
      {tax_type === "ESI" && (
        <Box width="50%">
          <ConfigValueRenderer
            title="EPF Calculations"
            subtitle="Contributors"
            value={[
              { label: "Employee's Contribution", value: "0.75% of Gross Pay" },
              { label: "Employer's Contribution", value: "3.25% of Gross Pay" },
            ]}
          />
        </Box>
      )}

      {tax_type === "Professional Tax" && (
        <Box>
          <Box width="50%">
            <Box>
              <Typography sx={{ fontSize: 16, fontWeight: 600, color: "#000000", mb: 2, mt: 2 }}>
                Tax Slab Details
              </Typography>
            </Box>
            <DataTable
              muiTableContainerProps={{
                sx: {
                  maxHeight: "400px",
                },
              }}
              data={professionalTaxData[selectedRow?.region as keyof typeof professionalTaxData]?.slab}
              columns={[
                {
                  header: "Employee Gross",
                  accessorKey: "monthly_salary_range",
                  size: 200,
                },
                {
                  header: "PT / Month",
                  accessorKey: "tax_per_month",
                  size: 100,
                },
              ]}
            />
          </Box>
          <InfoText
            text={professionalTaxData[selectedRow?.region as keyof typeof professionalTaxData]?.additional_info}
          />
        </Box>
      )}

      <form.Subscribe
        selector={(state) => [state.canSubmit, state.isSubmitting, state.isPristine, state.isDefaultValue]}
      >
        {([canSubmit, isSubmitting, isPristine, isDefaultValue]) => {
          return (
            <Box sx={{ display: "flex", justifyContent: "flex-end", mt: 2 }}>
              <Button
                variant="contained"
                disabled={
                  !canSubmit || (isSubmitting as boolean) || (isPristine as boolean) || (isDefaultValue as boolean)
                }
                onClick={form.handleSubmit}
              >
                Save
              </Button>
            </Box>
          );
        }}
      </form.Subscribe>
    </Box>
  );
};

export default AddEditComponent;
