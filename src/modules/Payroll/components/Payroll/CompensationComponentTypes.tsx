import { Add, ExpandMore } from "@mui/icons-material";
import { Accordion, AccordionDetails, AccordionSummary, IconButton, Paper, Typography } from "@mui/material";
import { Box } from "@mui/system";
import React from "react";
import { useContentHeight } from "src/customHooks/useContentHeight";
import { useMasterData } from "src/customHooks/useMasterData";
import { CompensationComponent } from "src/modules/Employees/Compensation/Compensation";
import { PayrollComponentV2 } from "../../../../services/api_definitions/payroll.service";
import ContentHeader from "../../../Common/ContentHeader/ContentHeader";

type Props = {
  allComponents?: CompensationComponent[];
  components: CompensationComponent[];
  addComponentType: (fieldName: "components", component: PayrollComponentV2) => void;
};

const translations = {
  componentLibrary: "Component Library",
  buildYourPayTemplates: "Build your pay templates using active components below.",
  noComponents: "No components to display",
};

const CompensationComponentTypes: React.FC<Props> = ({ allComponents, addComponentType, components }) => {
  const height = useContentHeight();
  const { data: componentTypes = [], isLoading: isComponentTypeLoading } = useMasterData("CompensationComponentType");

  const onAddComponentClick = (component: PayrollComponentV2) => {
    addComponentType("components", { ...component, include_in_ctc: true } as any);
  };

  const getComponentsToDisplay = (componentType: string) => {
    return allComponents?.filter(
      (eachComponent) =>
        eachComponent?.component_type === componentType &&
        !components?.find((_eachComponent) => _eachComponent?.name === eachComponent?.name),
    );
  };

  return (
    <Paper elevation={2} sx={{ height: height - 120, maxHeight: height, overflow: "auto", padding: 2 }}>
      <ContentHeader title={translations.componentLibrary} subtitle={translations.buildYourPayTemplates} />
      {componentTypes &&
        !isComponentTypeLoading &&
        componentTypes.map((eachComponentType: any) => (
          <Accordion key={eachComponentType} sx={{ marginTop: "10px" }}>
            <AccordionSummary expandIcon={<ExpandMore />}>
              <Typography>{eachComponentType}</Typography>
            </AccordionSummary>
            <AccordionDetails>
              {!getComponentsToDisplay(eachComponentType)?.length && (
                <Typography variant="body2">{translations.noComponents}</Typography>
              )}
              {getComponentsToDisplay(eachComponentType)?.map((eachComponent: any) => (
                <Box component={Paper} key={eachComponent.id} elevation={2} gap={1} margin="8px 0px" padding="4px 8px">
                  <Box display="flex" alignItems="center" justifyContent="space-between">
                    <Typography variant="body2">{eachComponent.name}</Typography>
                    <IconButton color="primary" onClick={() => onAddComponentClick(eachComponent)}>
                      <Add />
                    </IconButton>
                  </Box>
                </Box>
              ))}
            </AccordionDetails>
          </Accordion>
        ))}
    </Paper>
  );
};
export default CompensationComponentTypes;
