import { InfoOutlined } from "@mui/icons-material";
import { <PERSON><PERSON>, <PERSON>, But<PERSON>, Container, Divider, <PERSON>, Switch } from "@mui/material";
import { useStore } from "@tanstack/react-form";
import { useMutation, useQuery } from "@tanstack/react-query";
import { MRT_ColumnDef, MRT_Row } from "material-react-table";
import React, { useCallback, useState } from "react";
import ContentHeader from "src/modules/Common/ContentHeader/ContentHeader";
import { useAppForm } from "src/modules/Common/Form/effiFormContext";
import Modal from "src/modules/Common/Modal/Modal";
import DataTable from "src/modules/Common/Table/DataTable";
import TableActions from "src/modules/Common/Table/TableActions";
import DeleteConfirmationModal from "src/modules/Settings/components/Common/DeleteConfirmationModal";
import { PayrollTemplateV2 } from "src/services/api_definitions/payroll.service";
import payrollService from "src/services/payroll.service";
import { useMasterData } from "../../../../customHooks/useMasterData";
import { PayrollTemplateProps, PayrollViewModes } from "./Payroll";
import PreviewTemplate from "./PreviewTemplate";

const AllTemplates: React.FC<PayrollTemplateProps> = ({
  setCurrentSelectedMode,
  setSelectedRow,
  selectedRow,
  setPreselectedEmployeeType,
}) => {
  const [templateToDelete, setTemplateToDelete] = useState<string | null>(null);
  const [isPreviewMode, setIsPreviewMode] = useState<boolean>(false);
  const [jobTitlesToShow, setJobTitlesToShow] = useState<string[]>([]);
  const [softDeleteConfirmation, setSoftDeleteConfirmation] = useState<string | null>(null);
  const { data, isLoading, isFetching, refetch } = useQuery(
    ["payroll-templates"],
    async () => payrollService.getAllTemplatesV2(),
    {
      refetchOnWindowFocus: false,
      refetchOnMount: true,
    },
  );
  const deleteMutation = useMutation({
    mutationFn: async (payload: string) => payrollService.deleteTemplate(payload),
    onSuccess: async () => {
      setTemplateToDelete(null);
      await refetch();
    },
  });

  const softDeleteMutation = useMutation({
    mutationFn: async (payload: { mode: "activate" | "deactivate"; name: string }) =>
      payrollService.softDelete(payload.mode, payload.name),
    onSuccess: async () => {
      await refetch();
    },
  });

  const globalFilterFn = useCallback((row: MRT_Row<PayrollTemplateV2>, columnId: string, filterValue: string) => {
    const value = row.getValue(columnId);
    const doesContainKeyword = String(value).toLowerCase().includes(filterValue.toLowerCase());
    if (Array.isArray(row.original["job_titles"])) {
      const isMatch = row.original.job_titles.some((item) => item.toLowerCase().includes(filterValue.toLowerCase()));

      return isMatch || doesContainKeyword;
    }
    return doesContainKeyword;
  }, []);

  const onDeactivate = async (name: string) => {
    softDeleteMutation.mutate({ mode: "deactivate", name });
    setSoftDeleteConfirmation(null);
  };

  const onCheck = async (checked: boolean, name: string) => {
    if (checked) {
      softDeleteMutation.mutate({ mode: "activate", name });
      return;
    }
    setSoftDeleteConfirmation(name);
  };

  const columns = (): MRT_ColumnDef<PayrollTemplateV2, any>[] => {
    return [
      {
        accessorKey: "name",
        header: "Template Name",
        minSize: 250,
        maxSize: 450,
        Cell: ({ row }) => (
          <Link
            component="button"
            sx={{ cursor: "pointer" }}
            underline="hover"
            fontWeight="bold"
            onClick={() => onView(row.original)}
          >
            {row.original.name}
          </Link>
        ),
        muiTableBodyCellProps: {
          align: "left",
        },
      },
      {
        accessorKey: "description",
        header: "Description",
        minSize: 250,
        maxSize: 450,
      },
      {
        accessorKey: "country",
        header: "Country",
        minSize: 100,
        maxSize: 150,
      },
      {
        accessorKey: "job_titles",
        header: "Applicable Job Roles",
        size: 200,
        accessorFn: (row) => {
          const jobTitles = row.job_titles as string[];
          return (
            <Link
              component="button"
              sx={{ cursor: "pointer" }}
              underline="hover"
              fontWeight="bold"
              onClick={() => setJobTitlesToShow(jobTitles)}
            >
              {jobTitles?.length} Job Titles
            </Link>
          );
        },
      },
    ];
  };

  const onEdit = (row: PayrollTemplateV2) => {
    if (setSelectedRow) {
      setSelectedRow(row);
    }
    setCurrentSelectedMode(PayrollViewModes.EDIT);
  };
  const onDelete = (row: PayrollTemplateV2) => {
    setTemplateToDelete(row.name);
  };

  const onDeleteConfirmation = () => {
    deleteMutation.mutate(templateToDelete || "");
  };
  const onView = (row: PayrollTemplateV2) => {
    if (setSelectedRow) {
      setSelectedRow(row);
    }
    setIsPreviewMode(true);
  };

  const { data: employeeTypes } = useMasterData("EmployeeType", { refetchOnMount: true });
  const [employeeTypeSelectorOpen, setEmployeeTypeSelectorOpen] = useState<boolean>(false);
  const selectorForm = useAppForm({ defaultValues: { employee_type: "" } });
  const selectedEmployeeType = useStore(selectorForm.store, (state) => state.values.employee_type);

  const onAddTemplateClick = () => {
    selectorForm.setFieldValue("employee_type", "");
    setEmployeeTypeSelectorOpen(true);
  };

  const onConfirmEmployeeType = () => {
    if (!selectedEmployeeType) return;
    if (setPreselectedEmployeeType) {
      setPreselectedEmployeeType(selectedEmployeeType);
    }
    setEmployeeTypeSelectorOpen(false);
    setCurrentSelectedMode(PayrollViewModes.ADD);
  };

  return (
    <Container maxWidth="xl" disableGutters>
      <ContentHeader
        title="Compensation"
        subtitle="Configure Compensation Templates"
        buttonTitle="Add Template"
        primaryAction={onAddTemplateClick}
      />
      <Divider sx={{ margin: "10px 0px" }} />
      <DataTable
        layoutMode="grid"
        data={data || []}
        initialState={{
          columnPinning: {
            right: ["mrt-row-actions"],
          },
        }}
        state={{
          showSkeletons: isLoading && isFetching,
          columnPinning: {
            right: ["mrt-row-actions"],
          },
        }}
        displayColumnDefOptions={{
          "mrt-row-actions": {
            header: "",
            size: 190,
            enablePinning: true,
          },
        }}
        columns={columns()}
        enableRowActions
        enableColumnFilters
        enableGlobalFilter
        enableTopToolbar
        positionActionsColumn="last"
        filterFns={{
          filterPayrollTemplates: (row, columnId, filterValue) =>
            globalFilterFn(row as MRT_Row<PayrollTemplateV2>, columnId, filterValue),
        }}
        globalFilterFn="filterPayrollTemplates"
        renderRowActions={({ row }) => (
          <Box display="flex" gap={1}>
            <Switch
              value={row.original.active}
              checked={row.original.active}
              onChange={(_ev, checked) => onCheck(checked, row?.original?.name)}
            />
            <TableActions
              edit={{ onClick: () => onEdit(row.original), color: "primary" }}
              remove={{ onClick: () => onDelete(row.original), color: "error" }}
              view={{ onClick: () => onView(row.original), color: "primary", hide: true }}
            />
          </Box>
        )}
      />
      <DeleteConfirmationModal
        isModalOpen={!!templateToDelete}
        title="Are you sure you want to delete this template?"
        selectedRole={templateToDelete || ""}
        onCancel={() => setTemplateToDelete(null)}
        onDelete={onDeleteConfirmation}
      />
      <Modal
        showBackButton
        title="Preview Template"
        isOpen={isPreviewMode && !!selectedRow}
        onClose={() => {
          setIsPreviewMode(false);
          if (setSelectedRow) {
            setSelectedRow(null);
          }
        }}
      >
        <PreviewTemplate
          componentsToPreview={
            selectedRow?.components?.map((eachComponent) => ({
              ...eachComponent?.compensation_component,
              amount: eachComponent?.amount,
            })) || []
          }
        />
      </Modal>
      <Modal
        showBackButton
        title="Job Titles"
        subtitle="Applicable Job Titles"
        isOpen={jobTitlesToShow.length > 0}
        onClose={() => {
          setJobTitlesToShow([]);
        }}
      >
        <DataTable
          data={jobTitlesToShow?.map((eachJobTitle) => ({ name: eachJobTitle })) || []}
          columns={[
            {
              accessorKey: "name",
              header: "Job Title",
            },
          ]}
        />
      </Modal>
      <Modal
        showBackButton
        title="Create Compensation Template"
        subtitle=""
        isOpen={employeeTypeSelectorOpen}
        onClose={() => setEmployeeTypeSelectorOpen(false)}
        actions={
          <Box display="flex" justifyContent="flex-end" gap={2} padding={2}>
            <Button onClick={() => setEmployeeTypeSelectorOpen(false)} variant="outlined">
              Cancel
            </Button>
            <Button onClick={onConfirmEmployeeType} variant="contained" disabled={!selectedEmployeeType}>
              Next
            </Button>
          </Box>
        }
      >
        <Box display="flex" flexDirection="column" gap={2} minWidth={400}>
          <selectorForm.AppField name="employee_type">
            {(field: any) => (
              <field.EffiSelect
                label="Applicable Employee Type"
                placeholder="Select Employee Type"
                required
                options={(employeeTypes || []).map((type) => ({ label: String(type), value: String(type) }))}
                size="small"
              />
            )}
          </selectorForm.AppField>
          <Alert color="success" icon={<InfoOutlined />}>
            Earnings, Deductions and Benefits will be automatically tailored based on the employee type selected for
            this template.
          </Alert>
        </Box>
      </Modal>
      <DeleteConfirmationModal
        isModalOpen={!!softDeleteConfirmation}
        title="Are you sure you want to deactivate this template?"
        selectedRole={softDeleteConfirmation || ""}
        onCancel={() => setSoftDeleteConfirmation(null)}
        onDelete={() => onDeactivate(softDeleteConfirmation || "")}
        saveButtonTitle="Deactivate"
        suffix="deactivated"
      />
    </Container>
  );
};

export default AllTemplates;
