import { Copy<PERSON>ll, Delete, Edit, PlayCircle, PlayCircleOutline } from "@mui/icons-material";
import { Box, Button, Switch, Tooltip } from "@mui/material";
import { MRT_Cell } from "material-react-table";
import React from "react";
import DataTable from "src/modules/Common/Table/DataTable";
import { PayRunData } from "src/services/api_definitions/payroll.service";

const getPayDayRuleEnum = (payDayRule: string) => {
  switch (payDayRule) {
    case "First working day of the next month":
      return "First Working Day";
    case "Last working day of the month":
      return "Last Working Day";
    default:
      return payDayRule;
  }
};

const columns = (
  setSelectedRow: (row: PayRunData) => void,
  createPayRun: (name: string) => void,
  copyPayRun: (row: PayRunData) => void,
  activePayRun: ({ name, isActive }: { name: string; isActive: boolean }) => void,
  deletePaySchedule: (row: PayRunData) => void,
) => [
  {
    header: "Organisation Location",
    accessorKey: "organisation_address",
  },
  {
    header: "Pay Schedule Name",
    accessorKey: "name",
  },
  {
    header: "Pay Frequency",
    accessorKey: "frequency",
  },
  {
    header: "Pay Date",
    accessorKey: "pay_day",
    Cell: ({ cell }: { cell: MRT_Cell<PayRunData, any> }) => {
      return <Box>{cell?.row.original.pay_day || getPayDayRuleEnum(cell?.row.original.pay_day_rule)}</Box>;
    },
  },
  {
    header: "Action",
    accessorKey: "action",
    Cell: ({ cell }: { cell: MRT_Cell<PayRunData, any> }) => {
      return (
        <Box
          sx={{ display: "flex", flexDirection: "row", alignItems: "center", justifyContent: "flex-start", gap: "4px" }}
        >
          <Tooltip title="Copy Pay Schedule">
            <Button
              variant="text"
              color="primary"
              size="small"
              onClick={() => {
                copyPayRun(cell.row.original);
              }}
              sx={{ minWidth: "45px" }}
            >
              <CopyAll />
            </Button>
          </Tooltip>
          {!cell.row.original.has_active_pay_run && (
            <Tooltip title={cell.row.original.active ? "Deactivate Pay Schedule" : "Activate Pay Schedule"}>
              <Switch
                checked={cell.row.original.active}
                onChange={(e) => {
                  activePayRun({ name: cell.row.original?.name, isActive: e.target.checked });
                }}
              />
            </Tooltip>
          )}
          {!cell.row.original.has_active_pay_run && (
            <Tooltip title="Edit Pay Schedule">
              <Button
                variant="text"
                color="primary"
                size="small"
                onClick={() => {
                  setSelectedRow(cell.row.original);
                }}
                disabled={!!cell.row.original.has_active_pay_run}
                sx={{ minWidth: "45px" }}
              >
                <Edit />
              </Button>
            </Tooltip>
          )}
          {cell.row.original.active && !cell.row.original.has_active_pay_run && (
            <Tooltip title="Create Pay Run">
              <Button
                variant="text"
                color="primary"
                size="small"
                onClick={() => {
                  createPayRun(cell.row.original?.name);
                }}
                sx={{ minWidth: "45px" }}
              >
                <PlayCircleOutline />
              </Button>
            </Tooltip>
          )}
          {!cell.row.original.has_pay_run && (
            <Tooltip title="Delete Pay Run">
              <Button
                variant="text"
                color="primary"
                size="small"
                onClick={() => {
                  deletePaySchedule(cell.row.original);
                }}
                sx={{ minWidth: "45px" }}
              >
                <Delete />
              </Button>
            </Tooltip>
          )}
        </Box>
      );
    },
  },
];

const PayScheduleTable = ({
  payScheduleData,
  setSelectedRow,
  createPayRun,
  copyPayRun,
  activePayRun,
  deletePaySchedule,
}: {
  payScheduleData: PayRunData[];
  setSelectedRow: (row: PayRunData) => void;
  createPayRun: (name: string) => void;
  copyPayRun: (row: PayRunData) => void;
  activePayRun: ({ name, isActive }: { name: string; isActive: boolean }) => void;
  deletePaySchedule: (row: PayRunData) => void;
}) => {
  return (
    <DataTable
      columns={columns(setSelectedRow, createPayRun, copyPayRun, activePayRun, deletePaySchedule)}
      data={payScheduleData}
    />
  );
};

export default PayScheduleTable;
