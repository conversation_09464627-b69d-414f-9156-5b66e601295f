import { Box, Button, Typography } from "@mui/material";
import { useStore } from "@tanstack/react-form";
import { useMutation, useQuery } from "@tanstack/react-query";
import { format, startOfMonth } from "date-fns";
import React, { useEffect } from "react";
import { useAppDispatch } from "src/customHooks/useAppDispatch";
import { useAppSelector } from "src/customHooks/useAppSelector";
import ContentHeader from "src/modules/Common/ContentHeader/ContentHeader";
import EffiDynamicForm from "src/modules/Common/Form/components/EffiDynamicForm";
import { useAppForm } from "src/modules/Common/Form/effiFormContext";
import { getEnumValues } from "src/modules/Employees/utils/utils";
import { PayRunData } from "src/services/api_definitions/payroll.service";
import payrollService from "src/services/payroll.service";
import tenantsService from "src/services/tenants.service";
import { setFullviewMode } from "src/store/slices/app.slice";
import { getFinancialYearStartDate } from "src/utils/dateUtils";
import { z } from "zod";

const AddEditComponent = ({
  selectedRow,
  setSelectedRow,
  // payScheduleData,
}: {
  selectedRow: PayRunData;
  setSelectedRow: (row: PayRunData | null) => void;
  // payScheduleData: PayRunData[];
}) => {
  const dispatch = useAppDispatch();
  const { tenantDetails } = useAppSelector((state) => state.userManagement);

  useEffect(() => {
    dispatch(setFullviewMode(true));
    return () => {
      dispatch(setFullviewMode(false));
    };
  }, []);

  const { data: costCenters } = useQuery(
    ["cost-center-details", tenantDetails.tenant_id],
    async () => tenantsService.getCostCenterDetails(tenantDetails.tenant_id),
    {
      enabled: !!tenantDetails.tenant_id,
      retryOnMount: false,
      refetchOnWindowFocus: false,
    },
  );

  const employeeTypeOptions = getEnumValues("EmployeeType");
  const { selectedOrganisationDetails } = useAppSelector((state) => state.userManagement);
  const addressOptions = selectedOrganisationDetails?.addresses.map((item) => ({
    label: item.display_address,
    value: item.display_address,
  }));

  const costCenterOptions = costCenters?.map((item) => ({
    label: item.code,
    value: item.code,
  }));

  const attendanceLockDayOptions = Array.from({ length: 22 }, (_, i) => ({
    label: i + 1,
    value: i + 1,
  }));

  const isEdit = !!selectedRow?.name;

  const formValidator = z
    .object({
      name: z.string().nonempty({
        message: "Pay Schedule Name is required",
      }),
      frequency: z.string().nonempty({
        message: "Pay Frequency is required",
      }),
      pay_day_rule: z.string().nonempty({
        message: "Pay Day Configuration is required",
      }),
      first_pay_date: z.string().nonempty({
        message: "First Pay Date is required",
      }),
      grace_period_days: z.number().min(1, {
        message: "Grace Period must be greater than 0",
      }),
      attendance_lock_day: z.number().min(1, {
        message: "Attendance Lock Day must be greater than 0",
      }),
      employee_types: z.array(z.string()).nonempty({
        message: "At least one employee type is required",
      }),
      cost_centers: z.array(z.string()).nonempty({
        message: "At least one cost center is required",
      }),
      organisation_address: z.string().nonempty({
        message: "Organisation Address is required",
      }),
      pay_day: z.number().optional().nullable(),
      approval_required_levels: z.number().min(0).max(3),
      approval_matrix_level_1: z.array(z.string().email()).optional(),
      approval_matrix_level_2: z.array(z.string().email()).optional(),
      approval_matrix_level_3: z.array(z.string().email()).optional(),
      // custom_pay_day: z.number().min(1, {
      //   message: "Custom Pay Day must be greater than 0",
      // }),
    })
    .refine(
      (data) => {
        const isCustomPayDay = data?.pay_day_rule === "Custom";
        return isCustomPayDay ? !!data?.pay_day : true;
      },
      {
        message: "Custom Pay Day is required",
        path: [""],
      },
    );

  const createPaySchedule = useMutation({
    mutationKey: ["create-pay-schedule"],
    mutationFn: async (payload: PayRunData) => {
      const resp = await payrollService.createPaySchedule(payload);
      return resp;
    },
    onSuccess: () => {
      goBack();
    },
    onError: (error) => {
      return error;
    },
  });

  const updatePaySchedule = useMutation({
    mutationKey: ["update-pay-schedule"],
    mutationFn: async (payload: PayRunData) => {
      const resp = await payrollService.updatePaySchedule(payload);
      return resp;
    },
    onSuccess: () => {
      goBack();
    },
    onError: (error) => {
      return error;
    },
  });

  // Helper function to get approval matrix level values
  const getApprovalLevelDefaults = () => {
    const approvalMatrix = (selectedRow as any)?.approval_matrix || [];
    const defaults: any = {};

    for (let level = 1; level <= 3; level++) {
      const levelData = approvalMatrix.find((item: any) => item.approval_level === level);
      defaults[`approval_matrix_level_${level}`] = levelData?.approver_emails || [];
    }

    return defaults;
  };

  const form = useAppForm({
    defaultValues: {
      name: selectedRow?.name || "",
      frequency: selectedRow?.frequency || "Monthly",
      pay_day: selectedRow?.pay_day || 0,
      grace_period_days: selectedRow?.grace_period_days || 0,
      attendance_lock_day: selectedRow?.attendance_lock_day || 0,
      employee_types: selectedRow?.employee_types || [],
      cost_centers: selectedRow?.cost_centers || [],
      organisation_address: selectedRow?.organisation_address || "",
      pay_day_rule: selectedRow?.pay_day_rule || "Last working day of the month",
      first_pay_date: selectedRow?.first_pay_date || "",
      approval_required_levels: (selectedRow as any)?.approval_required_levels || 0,
      ...getApprovalLevelDefaults(),
    },
    onSubmit: (values) => {
      const firstPayDate = startOfMonth(values?.value?.first_pay_date);

      // Build approval matrix from individual level fields
      const approval_matrix = [];
      for (let level = 1; level <= values?.value?.approval_required_levels; level++) {
        const approverEmails = values?.value?.[`approval_matrix_level_${level}`] || [];
        if (approverEmails.length > 0) {
          approval_matrix.push({
            approval_level: level,
            approver_emails: approverEmails,
          });
        }
      }

      const payloadValues = {
        ...values?.value,
        pay_day: values?.value?.pay_day_rule === "Custom" ? values?.value?.pay_day : null,
        first_pay_date: format(firstPayDate, "yyyy-MM-dd"),
        approval_matrix,
        // Remove individual level fields from payload
        approval_matrix_level_1: undefined,
        approval_matrix_level_2: undefined,
        approval_matrix_level_3: undefined,
      };

      if (selectedRow?.name) {
        payloadValues.new_name = values?.value?.name;
        payloadValues.name = selectedRow?.name;
        updatePaySchedule.mutate(payloadValues as any);
      } else {
        createPaySchedule.mutate(payloadValues as any);
      }
    },
    validators: {
      onChange: formValidator as any,
      onSubmit: formValidator as any,
    },
  });

  const { pay_day_rule, attendance_lock_day, grace_period_days, approval_required_levels } = useStore(
    form.store,
    (state: any) => state.values,
  );
  //26 is the max grace period (due to 28 days in february)
  const gracePeriodOptions = Array.from({ length: 26 - attendance_lock_day }, (_, i) => ({
    label: i + 1,
    value: i + 1,
  }));

  const inputFields = [
    {
      fieldProps: {
        name: "organisation_address",
      },
      formProps: {
        label: "Branch",
        type: "select",
        required: true,
        options: addressOptions,
        disabled: isEdit,
      },
      containerProps: {
        size: 6,
      },
    },
    {
      fieldProps: {
        name: "name",
      },
      formProps: {
        label: "Pay Schedule Name",
        type: "text",
        required: true,
      },
      containerProps: {
        size: 6,
      },
    },
    {
      fieldProps: {
        name: "cost_centers",
      },
      formProps: {
        label: "Cost Center",
        type: "multi-select",
        required: true,
        options: costCenterOptions,
        // disabled: isEdit,
      },
      containerProps: {
        size: 6,
      },
    },
    {
      fieldProps: {
        name: "employee_types",
      },
      formProps: {
        label: "Applicable Employee Types",
        type: "multi-select",
        required: true,
        options: employeeTypeOptions.data?.map((item) => ({
          label: item,
          value: item,
        })),
      },
      containerProps: {
        size: 6,
      },
    },
    {
      fieldProps: {
        name: "attendance_lock_day",
        listeners: {
          onChange: (value: any) => {
            if (value?.value + grace_period_days <= 26) return;
            form.setFieldValue("grace_period_days", 26 - value?.value);
            form.validate("change");
          },
        },
      },
      formProps: {
        label: "Attendance Lock Day",
        type: "select",
        required: true,
        options: attendanceLockDayOptions,
      },
      containerProps: {
        size: 3,
      },
    },
    {
      fieldProps: {
        name: "grace_period_days",
      },
      formProps: {
        label: "Grace Period(In Days)",
        type: "select",
        required: true,
        options: gracePeriodOptions,
      },
      containerProps: {
        size: 3,
      },
    },
    {
      fieldProps: {
        name: "frequency",
      },
      formProps: {
        label: "Pay Frequency",
        type: "select",
        required: true,
        options: [
          {
            label: "Monthly",
            value: "Monthly",
          },
        ],
        disabled: isEdit && selectedRow?.has_pay_run,
      },
      containerProps: {
        size: 3,
      },
    },
    {
      fieldProps: {
        name: "first_pay_date",
      },
      formProps: {
        label: "First Pay Month",
        type: "date",
        required: true,
        disabled: isEdit && selectedRow?.has_pay_run,
        format: "MMM yyyy",
        views: ["month", "year"],
        minDate: getFinancialYearStartDate(),
        slotProps: {
          textField: {
            id: "first_pay_date",
            size: "small",
            fullWidth: true,
            placeholder: "First Pay Month",
          },
        },
      },
      containerProps: {
        size: 3,
      },
    },
  ];

  const approverConfigInputProps = [
    {
      fieldProps: {
        name: "approval_required_levels",
      },
      formProps: {
        label: "Approval Levels",
        type: "select",
        required: true,
        options: [
          { label: "0 - No Approval Required", value: 0 },
          { label: "1 - Single Level Approval", value: 1 },
          { label: "2 - Two Level Approval", value: 2 },
          { label: "3 - Three Level Approval", value: 3 },
        ],
      },
      containerProps: {
        size: 12,
      },
    },
  ];

  const payDayInputProps = [
    {
      fieldProps: {
        name: "pay_day_rule",
      },
      formProps: {
        label: "",
        type: "radio-group",
        options: [
          {
            label: "Last working day of the month",
            value: "Last working day of the month",
          },
          {
            label: "First working day of the next month",
            value: "First working day of the next month",
          },
          {
            label: "Custom",
            value: "Custom",
            subLabel:
              "If the scheduled payday falls on a non-working day or holiday, salaries will be processed on the preceding working day.",
          },
        ],
        required: true,
      },
      containerProps: {
        size: 12,
      },
    },
  ];

  const customPayDayOptions = [
    {
      fieldProps: {
        name: "pay_day",
      },
      formProps: {
        label: "",
        type: "select",
        required: true,
        options: Array.from({ length: 28 }, (_, i) => ({
          label: i + 1,
          value: i + 1,
        })),
        // size: "small",
      },
      containerProps: {
        size: 12,
      },
    },
  ];

  const goBack = () => {
    setSelectedRow(null);
  };

  // Helper function to get approval level input fields
  const getApprovalLevelInputFields = () => {
    if (approval_required_levels === 0) return [];

    const levels = Array.from({ length: approval_required_levels }, (_, i) => i + 1);

    return levels.map((level) => ({
      fieldProps: {
        name: `approval_matrix_level_${level}`,
        validators: {
          onChange: ({ value }: { value: string[] }) => {
            if (!value || value.length === 0) {
              return `Level ${level} must have at least one approver`;
            }
            return undefined;
          },
        },
      },
      formProps: {
        type: "employee-multi-select",
        label: `Level ${level} Approvers`,
        required: true,
        placeholder: "Search and select employees",
      },
      containerProps: {
        size: 12,
      },
    }));
  };

  return (
    <Box display="flex" flexDirection="column" gap={1}>
      <ContentHeader title="Pay Schedule" showBackButton goBack={goBack} />
      <EffiDynamicForm form={form} inputFields={inputFields} />
      <Box display="flex" flexDirection="column" gap={1} margin={"16px 0px"}>
        <Typography fontSize={16} fontWeight={600}>
          Approval Configuration
        </Typography>
        <EffiDynamicForm form={form} inputFields={approverConfigInputProps} />
        {approval_required_levels > 0 && <EffiDynamicForm form={form} inputFields={getApprovalLevelInputFields()} />}
      </Box>
      <Box>
        <Typography fontSize={16} fontWeight={600}>
          Pay Day Configuration
        </Typography>
        <EffiDynamicForm form={form} inputFields={payDayInputProps} />
        {pay_day_rule === "Custom" && (
          <Box display="flex" alignItems="center" gap={1} fontSize={14}>
            Day{" "}
            <Box sx={{ width: 80 }}>
              <EffiDynamicForm form={form} inputFields={customPayDayOptions} />
            </Box>{" "}
            of the month
          </Box>
        )}
      </Box>
      <form.Subscribe
        selector={(state) => [state.canSubmit, state.isSubmitting, state.isPristine, state.isDefaultValue]}
      >
        {([canSubmit, isSubmitting, isPristine, isDefaultValue]) => {
          return (
            <Box sx={{ display: "flex", justifyContent: "flex-end", mt: 2 }}>
              <Button
                variant="contained"
                disabled={
                  !canSubmit || (isSubmitting as boolean) || (isPristine as boolean) || (isDefaultValue as boolean)
                }
                onClick={form.handleSubmit}
              >
                Save
              </Button>
            </Box>
          );
        }}
      </form.Subscribe>
    </Box>
  );
};

export default AddEditComponent;
