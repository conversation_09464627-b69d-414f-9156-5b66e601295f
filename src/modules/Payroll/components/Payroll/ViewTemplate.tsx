import { Box, Divider } from "@mui/material";
import React, { useEffect } from "react";
import { useAppDispatch } from "src/customHooks/useAppDispatch";
import ContentHeader from "src/modules/Common/ContentHeader/ContentHeader";
import { setFullviewMode } from "src/store/slices/app.slice";
import { PayrollTemplateProps, PayrollViewModes } from "./Payroll";
import ReadonlyPayrollHierarchy from "./ReadonlyPayrollHierarchy";

const ViewTemplate: React.FC<PayrollTemplateProps> = ({ selectedRow, setCurrentSelectedMode }) => {
  const dispatch = useAppDispatch();
  useEffect(() => {
    dispatch(setFullviewMode(true));
  }, []);

  const goBack = () => {
    setCurrentSelectedMode(PayrollViewModes.VIEW_ALL);
    dispatch(setFullviewMode(false));
  };

  return (
    <Box display="flex" flexDirection="column" gap={3}>
      <ContentHeader showBackButton goBack={goBack} title={selectedRow?.name} subtitle="Viewing configured template" />
      <Divider />
      <ReadonlyPayrollHierarchy selectedRow={selectedRow} />
    </Box>
  );
};

export default ViewTemplate;
