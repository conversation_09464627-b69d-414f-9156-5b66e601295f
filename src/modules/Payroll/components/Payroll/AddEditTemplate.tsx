import { Box, Button, Grid2 } from "@mui/material";
import { useStore } from "@tanstack/react-form";
import { useMutation } from "@tanstack/react-query";
import React, { useEffect, useMemo } from "react";
import { useAllCompensations } from "src/customHooks/useAllCompensations";
import { useAppDispatch } from "src/customHooks/useAppDispatch";
import ContentHeader from "src/modules/Common/ContentHeader/ContentHeader";
import Modal from "src/modules/Common/Modal/Modal";
import { setFullviewMode } from "src/store/slices/app.slice";
import { z } from "zod";
import { useContentHeight } from "../../../../customHooks/useContentHeight";
import { PayrollComponentV2, PayrollTemplateV2 } from "../../../../services/api_definitions/payroll.service";
import payrollService from "../../../../services/payroll.service";
import { useAppForm } from "../../../Common/Form/effiFormContext";
import CompensationComponentTypes from "./CompensationComponentTypes";
import DynamicPayrollInputs from "./DynamicPayrollInputs";
import FixedPayrollInputs from "./FixedPayrollInputs";
import { PayrollTemplateProps, PayrollViewModes } from "./Payroll";
import PreviewTemplate from "./PreviewTemplate";

type Props = {
  isEdit: boolean;
};

export type DefaultFixedFormState = {
  name: string;
  country: string;
  jobTitles: string[];
};

const formSchema = z.object({
  id: z.string().nullish(),
  name: z.string().nonempty({
    message: "Name is required",
  }),
  description: z.string(),
  country: z.string().nonempty({
    message: "Country is required",
  }),
  job_titles: z.array(z.string()).nonempty({
    message: "Job Titles are required",
  }),
  employee_type: z.string().nonempty({
    message: "Employee Type is required",
  }),
  aggregates: z.object({
    type: z.enum(["CTC", "GROSS"]),
    value: z.number(),
  }),
  components: z.array(
    z.object({
      id: z.string().nullish(),
      formula: z.object({
        value: z
          .number({
            message: "",
          })
          .or(
            z.string({
              message: "",
            }),
          )
          .refine((value) => {
            if (typeof value === "string" && value !== "System Defined") {
              return Number(value) > 0;
            }
            if (value === "System Defined") {
              return true;
            }
            return Number(value) > 0;
          }),
      }),
    }),
  ),
});

export const getTemplateDefaultFormState = (selectedRow?: PayrollTemplateV2 | null): DefaultFixedFormState | any => {
  return {
    name: selectedRow?.name || "",
    description: selectedRow?.description || "",
    country: selectedRow?.country || "India",
    job_titles: selectedRow?.job_titles || "",
    employee_type: selectedRow?.employee_type || "",
    aggregates: {
      type: "CTC",
      value: 0,
    },
    components:
      selectedRow?.components?.map((eachComponent: PayrollComponentV2) => ({
        ...eachComponent?.compensation_component,
        name: eachComponent?.compensation_component?.name,
        formula: {
          ...eachComponent?.compensation_component?.formula,
        },
      })) || [],
  };
};

const AddEditTemplate: React.FC<PayrollTemplateProps & Props> = ({
  isEdit = false,
  selectedRow,
  setCurrentSelectedMode,
  setSelectedRow,
  preselectedEmployeeType,
  setPreselectedEmployeeType,
}) => {
  const height = useContentHeight();
  const dispatch = useAppDispatch();
  const [isPreviewMode, setIsPreviewMode] = React.useState(false);

  const form = useAppForm({
    defaultValues: getTemplateDefaultFormState(selectedRow),
    validators: {
      onSubmit: formSchema,
    },
    onSubmit: (params: { value: any }) => {
      const formValues = params.value as any;
      const requestStructure: Partial<PayrollTemplateV2> = {
        name: formValues.name,
        description: formValues.description,
        country: formValues.country,
        job_titles: formValues.job_titles,
        employee_type: formValues.employee_type,
        components: (formValues.components as any[]).map((eachComponent: any, idx: number) => {
          return {
            ...eachComponent,
            include_in_ctc: isEdit
              ? eachComponent?.include_in_ctc
              : eachComponent?.component_type === "Benefit"
                ? eachComponent?.include_in_ctc
                : true,
            sort_order: idx,
            formula: {
              ...eachComponent?.formula,
              value: eachComponent?.formula?.value || 0,
            },
          };
        }),
      };
      if (isEdit) {
        updateTemplate.mutate(requestStructure as PayrollTemplateV2);
        return;
      }
      createTemplate.mutate(requestStructure as PayrollTemplateV2);
    },
  });

  // Prefill employee type from preselection if provided
  useEffect(() => {
    if (preselectedEmployeeType) {
      form.setFieldValue("employee_type", preselectedEmployeeType);
    }
  }, [preselectedEmployeeType]);
  const country = useStore(form.store, (state) => state.values.country);
  const components = useStore(form.store, (state) => state.values.components);

  const addComponentType = (_fieldName: "components", component: PayrollComponentV2) => {
    form.pushFieldValue("components", component as any);
  };
  const employeeType = useStore(form.store, (state) => state.values.employee_type);

  const { data: allCompensationComponents } = useAllCompensations({
    country,
    statutory: true,
    active: true,
    employeeTypes: employeeType || preselectedEmployeeType,
  });

  const allComponents = useMemo(() => {
    if (!isEdit) {
      allCompensationComponents
        ?.filter((eachComponent) => eachComponent?.mandatory)
        ?.forEach((eachComponent) => {
          addComponentType("components", { ...eachComponent, include_in_ctc: true } as any);
        });
      return allCompensationComponents;
    }
    return allCompensationComponents;
  }, [allCompensationComponents]);

  const createTemplate = useMutation({
    mutationKey: ["create-template"],
    mutationFn: async (payload: PayrollTemplateV2) => payrollService.createTemplate(payload),
    onSuccess: () => {
      goBack();
    },
    onError: (error) => {
      return error;
    },
  });

  const updateTemplate = useMutation({
    mutationKey: ["update-template"],
    mutationFn: async (payload: PayrollTemplateV2) => payrollService.updateTemplate(payload),
    onSuccess: () => {
      goBack();
    },
    onError: (error) => {
      return error;
    },
  });

  const goBack = () => {
    setCurrentSelectedMode(PayrollViewModes.VIEW_ALL);
    dispatch(setFullviewMode(false));
    if (setSelectedRow) {
      setSelectedRow(null);
    }
    if (setPreselectedEmployeeType) {
      setPreselectedEmployeeType(null);
    }
  };

  useEffect(() => {
    dispatch(setFullviewMode(true));
  }, []);

  return (
    <Box>
      <ContentHeader title={isEdit ? "Edit Template" : "Add Template"} showBackButton goBack={goBack} />
      <Grid2 container spacing={2}>
        <Grid2 size={3}>
          <CompensationComponentTypes
            allComponents={allComponents}
            addComponentType={addComponentType}
            components={components}
          />
        </Grid2>
        <Grid2 size={9}>
          <Box display="flex" flexDirection="column" gap={1}>
            <ContentHeader
              title="Details"
              actions={
                <Box display="flex" alignItems="center" gap={1}>
                  <Button variant="outlined" onClick={() => setIsPreviewMode(!isPreviewMode)}>
                    Preview
                  </Button>
                  <form.Subscribe selector={(state) => [state.canSubmit, state.isSubmitting, state.isPristine]}>
                    {([canSubmit, isSubmitting, isPristine]) => {
                      return (
                        <Box>
                          <Button
                            sx={{ align: "right" }}
                            variant="contained"
                            disabled={!canSubmit || (isSubmitting as boolean) || (isPristine as boolean)}
                            onClick={form.handleSubmit}
                          >
                            Save
                          </Button>
                        </Box>
                      );
                    }}
                  </form.Subscribe>
                </Box>
              }
            />
            <Box sx={{ maxHeight: height - 200, overflowY: "auto" }} display="flex" flexDirection="column" gap={2}>
              <FixedPayrollInputs form={form} isEdit={isEdit} />
              <DynamicPayrollInputs form={form} components={components} />
            </Box>
          </Box>
        </Grid2>
      </Grid2>
      <Modal
        isOpen={isPreviewMode}
        onClose={() => setIsPreviewMode(false)}
        actions={
          <Box display="flex" justifyContent="flex-end" gap={2} padding={2}>
            <Button onClick={() => setIsPreviewMode(false)} variant="outlined">
              Cancel
            </Button>
            <Button onClick={form.handleSubmit} variant="contained">
              Save
            </Button>
          </Box>
        }
      >
        <PreviewTemplate componentsToPreview={components} />
      </Modal>
    </Box>
  );
};

export default AddEditTemplate;
