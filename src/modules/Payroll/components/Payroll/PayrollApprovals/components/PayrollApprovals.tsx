import React from "react";
import TabsView from "src/modules/Common/CustomTabs/CustomTabs";
import ActivePayRuns from "./ActivePayRuns";
import AdHocApprovals from "./AdHocApprovals";

const tabs = [
  {
    id: 0,
    label: "Regular Pay Runs",
    component: <ActivePayRuns />,
  },
  {
    id: 1,
    label: "Ad Hoc Pay Runs",
    component: <AdHocApprovals />,
  },
];

const PayrollApprovals = () => {
  return <TabsView tabs={tabs} />;
};

export default PayrollApprovals;
