import { Block, Check, Close } from "@mui/icons-material";
import { Box, IconButton, Stack, Tooltip, Typography } from "@mui/material";
import { useStore } from "@tanstack/react-form";
import { useMutation, useQuery } from "@tanstack/react-query";
import React, { useCallback, useMemo } from "react";
import { queryClient } from "src/app/App";

import ContentHeader from "src/modules/Common/ContentHeader/ContentHeader";
import { useAppForm } from "src/modules/Common/Form/effiFormContext";
import ConfirmationModal from "src/modules/Common/Modal/ConfirmationModal";
import DataTable from "src/modules/Common/Table/DataTable";
import { formatCurrency } from "src/modules/Profile/components/EmployeeCompensation/EmployeeCompensation";
import payrollService from "src/services/payroll.service";
import { getIntlTimeToSpecifiedFormat } from "src/utils/dateUtils";

const AdHocApprovals = () => {
  const [selectedRows, setSelectedRows] = React.useState({});
  const [confirmationModalType, setConfirmationModalType] = React.useState<"approve" | "reject" | "skip" | null>(null);
  const [selectedRowId, setSelectedRowId] = React.useState<string | null>(null);
  const appForm = useAppForm({
    defaultValues: {
      reason: "",
    },
    validators: {
      onSubmit: ({ value }) => {
        if ((confirmationModalType === "reject" || confirmationModalType === "skip") && !value?.reason) {
          return "Reason is required";
        }
        return null;
      },
    },
  });
  const { reason } = useStore(appForm.store, (state) => state.values);
  const { data, isLoading, isFetching } = useQuery(
    ["get-adhoc-payrun-approvals"],
    async () => {
      return payrollService.getAdHocPayrunApprovals();
    },
    {
      refetchOnWindowFocus: false,
      refetchOnMount: true,
    },
  );

  const selectedRowsInApprovals = useMemo(() => {
    return Object.keys(selectedRows)
      .map(Number)
      .map((index) => data?.[index]);
  }, [selectedRows, data]);

  const approvePayRunMutation = useMutation({
    mutationKey: ["approve-payrun"],
    mutationFn: async (payload: { payrunIds: string[]; payrunType: string }) =>
      payrollService.approvePayrun(payload.payrunIds, reason, payload.payrunType),
    onSuccess: () => {
      queryClient.invalidateQueries(["get-adhoc-payrun-approvals"]);
      setSelectedRowId(null);
      setConfirmationModalType(null);
      appForm.reset();
    },
  });

  const rejectPayRunMutation = useMutation({
    mutationKey: ["reject-payrun"],
    mutationFn: async (payload: { payrunIds: string[]; reason: string; payrunType: string }) =>
      payrollService.rejectPayrun(payload.payrunIds, payload.reason, payload.payrunType),
    onSuccess: () => {
      queryClient.invalidateQueries(["get-adhoc-payrun-approvals"]);
      setSelectedRowId(null);
      setConfirmationModalType(null);
      appForm.reset();
    },
  });

  const skipPayrunMutation = useMutation({
    mutationFn: (payload: { payrunIds: string[]; reason: string; payrunType: string }) =>
      payrollService.skipPayRun(payload.payrunIds, payload.reason, payload.payrunType),
    onSuccess: () => {
      queryClient.invalidateQueries(["get-adhoc-payrun-approvals"]);
      setSelectedRowId(null);
      setConfirmationModalType(null);
      appForm.reset();
    },
  });

  const onApprovePayrun = useCallback(async (id?: string) => {
    if (id) {
      setSelectedRowId(id);
    }
    setConfirmationModalType("approve");
  }, []);
  const onRejectPayrun = useCallback(async (id?: string) => {
    if (id) {
      setSelectedRowId(id);
    }
    setConfirmationModalType("reject");
  }, []);

  const onSkipPayrun = useCallback(async (id?: string) => {
    if (id) {
      setSelectedRowId(id);
    }
    setConfirmationModalType("skip");
  }, []);

  const onBulkApprovePayrun = useCallback(async () => {
    if (selectedRowId) {
      // Individual row action
      const selectedRow = data?.find((row) => row.pay_run_id === selectedRowId);
      approvePayRunMutation.mutate({
        payrunIds: [selectedRowId],
        payrunType: selectedRow?.pay_run_type || "Ad Hoc",
      });
    } else {
      // Bulk action
      approvePayRunMutation.mutate({
        payrunIds: selectedRowsInApprovals.map((eachRow) => eachRow?.pay_run_id) as string[],
        payrunType: selectedRowsInApprovals?.[0]?.pay_run_type || "Ad Hoc",
      });
    }
    setConfirmationModalType(null);
  }, [selectedRowId, selectedRowsInApprovals, reason, data]);

  const onBulkRejectPayrun = useCallback(async () => {
    if (selectedRowId) {
      // Individual row action
      const selectedRow = data?.find((row) => row.pay_run_id === selectedRowId);
      rejectPayRunMutation.mutate({
        payrunIds: [selectedRowId],
        reason,
        payrunType: selectedRow?.pay_run_type || "Ad Hoc",
      });
    } else {
      // Bulk action
      rejectPayRunMutation.mutate({
        payrunIds: selectedRowsInApprovals.map((eachRow) => eachRow?.pay_run_id) as string[],
        reason,
        payrunType: selectedRowsInApprovals?.[0]?.pay_run_type || "Ad Hoc",
      });
    }
    setConfirmationModalType(null);
  }, [selectedRowId, selectedRowsInApprovals, reason, data]);

  const onBulkSkipPayrun = useCallback(async () => {
    if (selectedRowId) {
      // Individual row action
      const selectedRow = data?.find((row) => row.pay_run_id === selectedRowId);
      skipPayrunMutation.mutate({
        payrunIds: [selectedRowId],
        reason,
        payrunType: selectedRow?.pay_run_type || "Ad Hoc",
      });
    } else {
      // Bulk action
      skipPayrunMutation.mutate({
        payrunIds: selectedRowsInApprovals.map((eachRow) => eachRow?.pay_run_id) as string[],
        reason,
        payrunType: selectedRowsInApprovals?.[0]?.pay_run_type || "Ad Hoc",
      });
    }
    setConfirmationModalType(null);
  }, [selectedRowId, selectedRowsInApprovals, reason, data]);

  const bulkActionMap = {
    approve: {
      title: "Approve Pay Run",
      action: onBulkApprovePayrun,
      confirmation: "Are you sure you want to approve this pay run?",
    },
    reject: {
      title: "Send Back Pay Run",
      action: onBulkRejectPayrun,
      confirmation: "Are you sure you want to send back this pay run? if yes, please provide a reason.",
    },
    skip: {
      title: "Skip Pay Run",
      action: onBulkSkipPayrun,
      confirmation: "Are you sure you want to skip this pay run?",
    },
  };

  return (
    <Box display={"flex"} flexDirection="column" gap={2}>
      <ContentHeader
        title="Pay Run Details"
        actions={
          <Stack direction="row" gap={1}>
            <Box sx={{ bgcolor: !Object.values(selectedRows).some(Boolean) ? "#D2D2D2" : "#E6F2F1", borderRadius: 8 }}>
              <Tooltip title="Approve Pay Run">
                <IconButton
                  size="medium"
                  onClick={() => onApprovePayrun()}
                  disabled={!Object.values(selectedRows).some(Boolean)}
                >
                  <Check />
                </IconButton>
              </Tooltip>
            </Box>
            <Box sx={{ bgcolor: !Object.values(selectedRows).some(Boolean) ? "#D2D2D2" : "#E6F2F1", borderRadius: 8 }}>
              <Tooltip title="Send Back">
                <IconButton
                  size="medium"
                  onClick={() => onRejectPayrun()}
                  disabled={!Object.values(selectedRows).some(Boolean)}
                >
                  <Close />
                </IconButton>
              </Tooltip>
            </Box>
            <Box sx={{ bgcolor: !Object.values(selectedRows).some(Boolean) ? "#D2D2D2" : "#E6F2F1", borderRadius: 8 }}>
              <Tooltip title="Skip Pay Run">
                <IconButton
                  size="medium"
                  onClick={() => onSkipPayrun()}
                  disabled={!Object.values(selectedRows).some(Boolean)}
                >
                  <Block />
                </IconButton>
              </Tooltip>
            </Box>
          </Stack>
        }
      />
      <DataTable
        data={data || []}
        state={{
          showSkeletons: isLoading || isFetching,
          rowSelection: selectedRows,
          columnPinning: {
            right: ["mrt-row-actions"],
          },
        }}
        displayColumnDefOptions={{
          "mrt-row-actions": {
            size: 50,
            maxSize: 50,
            header: "",
            muiTableBodyCellProps: {
              align: "right",
            },
            enablePinning: true,
          },
        }}
        columns={[
          {
            accessorKey: "pay_run_id",
            header: "Pay Run ID",
            size: 300,
          },
          {
            accessorKey: "pay_run_type",
            header: "Pay Run Type",
            size: 200,
          },
          {
            accessorKey: "active_employees",
            header: "No of Employees",
            size: 100,
          },
          {
            accessorKey: "total_gross_pay",
            header: "Gross Pay",
            size: 100,
            Cell: ({ cell }) => formatCurrency(cell.getValue<number>(), "INR"),
          },
          {
            accessorKey: "submission_date",
            header: "Submitted At",
            Cell: ({ cell }) =>
              getIntlTimeToSpecifiedFormat(cell.getValue<string>(), "dd MMM yyyy, HH:MM").formattedDate,
          },
        ]}
        enableBatchRowSelection
        enableRowSelection
        enableRowActions
        positionActionsColumn="last"
        onRowSelectionChange={setSelectedRows}
        renderRowActions={({ row }) => (
          <Box display="flex" flexDirection="row" gap={1}>
            <Tooltip title="Approve Pay Run">
              <IconButton color="primary" size="small" onClick={() => onApprovePayrun(row.original.pay_run_id)}>
                <Check />
              </IconButton>
            </Tooltip>
            <Tooltip title="Send Back Pay Run">
              <IconButton color="error" size="small" onClick={() => onRejectPayrun(row.original.pay_run_id)}>
                <Close />
              </IconButton>
            </Tooltip>
            <Tooltip title="Skip Pay Run">
              <IconButton color="primary" size="small" onClick={() => onSkipPayrun(row?.original?.pay_run_id)}>
                <Block />
              </IconButton>
            </Tooltip>
          </Box>
        )}
      />
      {confirmationModalType && (
        <ConfirmationModal
          isOpen={confirmationModalType !== null}
          title={bulkActionMap[confirmationModalType]?.title}
          onSubmit={() => bulkActionMap[confirmationModalType].action()}
          onCancel={() => {
            setConfirmationModalType(null);
            setSelectedRowId(null);
            appForm.reset();
          }}
          isSaveDisabled={(confirmationModalType === "reject" || confirmationModalType === "skip") && !reason}
        >
          <Box display="flex" flexDirection="column" gap={2}>
            <Typography>{bulkActionMap[confirmationModalType]?.confirmation}</Typography>
            <appForm.AppField name="reason">
              {(field: any) => (
                <field.EffiTextField
                  label="Reason"
                  required={confirmationModalType === "reject" || confirmationModalType === "skip"}
                />
              )}
            </appForm.AppField>
          </Box>
        </ConfirmationModal>
      )}
    </Box>
  );
};

export default AdHocApprovals;
