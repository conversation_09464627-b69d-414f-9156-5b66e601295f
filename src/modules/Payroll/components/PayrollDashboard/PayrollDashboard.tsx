import { Box, Button } from "@mui/material";
import React, { useState } from "react";
import ContentHeader from "src/modules/Common/ContentHeader/ContentHeader";
import LazyModalController from "src/modules/Employees/components/Modals/ModalController";

const PayrollDashboard = () => {
  const [selectedModalId, setSelectedModalId] = useState<string | null>(null);
  return (
    <Box display="flex" flexDirection="column" gap={2}>
      <ContentHeader
        title={"Dashboard"}
        subtitle={"Import and Export Payroll Details"}
        // buttonTitle={"Add Employee"}
        secondaryButtonTitle={"Import"}
        // primaryAction={() => setSelectedModalId("add")}
        secondaryAction={() => setSelectedModalId("import")}
        actions={
          <Button onClick={() => setSelectedModalId("export")} variant="outlined">
            Export
          </Button>
        }
      />
      <LazyModalController
        isPayrollView={true}
        open={!!selectedModalId}
        modalId={selectedModalId}
        handleClose={() => setSelectedModalId(null)}
      />
    </Box>
  );
};

export default PayrollDashboard;
