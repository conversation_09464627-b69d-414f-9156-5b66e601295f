import { FileDownload } from "@mui/icons-material";
import { Box } from "@mui/material";
import { useMutation } from "@tanstack/react-query";
import React, { useCallback } from "react";
import ButtonWithLoading from "src/modules/Common/ButtonWithLoading/ButtonWithLoading";
import EffiMonthDatePicker from "src/modules/Common/FormInputs/EffiMonthDatePicker";
import timesheetsService from "src/services/timesheets.service";

interface TimeSheetHeaderProps {
  selectedDate: string;
  setSelectedDate: (selectedDate: string) => void;
}

const styles = {
  customDimentions: { width: 153, height: 42 },
};

const Header: React.FC<TimeSheetHeaderProps> = ({ selectedDate, setSelectedDate }) => {
  const exportTimesheetsMutation = useMutation({
    mutationKey: ["export-timesheets"],
    mutationFn: async () => timesheetsService.exportTimesheets(selectedDate),
  });

  const onExportFiles = useCallback(async () => {
    if (selectedDate) {
      exportTimesheetsMutation.mutate();
    }
  }, [selectedDate]);

  return (
    <Box display="flex" alignItems="center" justifyContent="flex-end" width="100%">
      <Box display="flex" gap={2}>
        <EffiMonthDatePicker
          onChange={(date) => setSelectedDate(date as string)}
          value={selectedDate}
          slotProps={{
            textField: {
              size: "small",
              sx: styles.customDimentions,
            },
          }}
        />
        <ButtonWithLoading
          sx={styles.customDimentions}
          onClick={onExportFiles}
          variant="outlined"
          endIcon={<FileDownload />}
          isLoading={exportTimesheetsMutation.isLoading}
        >
          Export
        </ButtonWithLoading>
      </Box>
    </Box>
  );
};

export default Header;
