import { BarChartRounded, Timeline } from "@mui/icons-material";
import { Box, IconButton, Tooltip, Typography } from "@mui/material";
import { useQuery } from "@tanstack/react-query";
import { format } from "date-fns";
import { MRT_ColumnDef, MRT_GlobalFilterTextField, MRT_ToggleFiltersButton } from "material-react-table";
import React, { useMemo } from "react";
import useToggle from "src/customHooks/useToggle";
import { EmployeeCellInfo } from "src/modules/Common/EmployeeViews/EmployeeCellInfo";
import Modal from "src/modules/Common/Modal/Modal";
import DataTable from "src/modules/Common/Table/DataTable";
import AttendanceTrendModal from "src/modules/Dashboard/component/QuickViews/AttendanceTrendModal";
import ActivityLogDetailsView from "src/modules/EmployeeAttendance/components/components/ActivityLogDetailsView";
import { ActivityLogDetails, TimeSheetStatus } from "src/services/api_definitions/employeeAttendance.service";
import { Attendance, TimeSheet } from "src/services/api_definitions/timesheets.service";
import timesheetsService from "src/services/timesheets.service";
import { convertTimeToAMPMWithZonedTime, getListOfDaysInMonth } from "src/utils/dateUtils";
import { getLeaveStatusColorsByPaidStatus, getStatusColors } from "src/utils/typographyUtils";

interface TimeSheetViewProps {
  selectedDate: string;
}

const defaultColumns: MRT_ColumnDef<TimeSheet>[] = [
  {
    accessorKey: "employee_name",
    header: "Employee Name",
    visibleInShowHideMenu: false,
    size: 250,
    Cell: ({ row }) => {
      const avatarSrc = React.useMemo(() => {
        return row?.original?.display_pic || "";
      }, [row?.original?.display_pic]);
      return (
        <EmployeeCellInfo
          name={row?.original?.employee_name}
          jobTitle=""
          employeeCode={row?.original?.employee_code}
          displayPic={avatarSrc}
        />
      );
    },
  },
  {
    accessorKey: "total_days",
    header: "Total Days",
    enablePinning: false,
    enableColumnActions: false,
    enableColumnFilter: false,
    visibleInShowHideMenu: false,
    muiTableHeadCellProps: {
      align: "center",
    },
    muiTableBodyCellProps: {
      align: "center",
    },
  },
  {
    accessorKey: "present_days",
    header: "Present Days",
    enablePinning: false,
    enableColumnActions: false,
    enableColumnFilter: false,
    visibleInShowHideMenu: false,
    muiTableHeadCellProps: {
      align: "center",
    },
    muiTableBodyCellProps: {
      align: "center",
    },
  },
  {
    accessorKey: "absent_days",
    enablePinning: false,
    enableColumnActions: false,
    enableColumnFilter: false,
    visibleInShowHideMenu: false,
    header: "Absent Days",
    muiTableHeadCellProps: {
      align: "center",
    },
    muiTableBodyCellProps: {
      align: "center",
    },
  },
];

const renderStatus = (timesheetStatus: TimeSheetStatus, index: number, array: TimeSheetStatus[]) => {
  if (timesheetStatus?.event_type === "Attendance") {
    return (
      <React.Fragment key={timesheetStatus?.status}>
        <Typography
          fontSize={14}
          sx={{
            color: getStatusColors(timesheetStatus?.status),
          }}
          textAlign="center"
        >
          {timesheetStatus?.status}
        </Typography>
        {index < array.length - 1 && <span>{", "}&nbsp;</span>}
      </React.Fragment>
    );
  }

  return (
    <Typography fontSize={14} textAlign="center">
      {timesheetStatus?.status?.split(",").map((status, idx, statusArray) => (
        <React.Fragment key={status.trim()}>
          <Box
            component="span"
            sx={{
              color: getLeaveStatusColorsByPaidStatus(timesheetStatus?.paid),
            }}
          >
            {status.trim()}
          </Box>
          {idx < statusArray.length - 1 && <span>{", "}&nbsp;</span>}
        </React.Fragment>
      ))}
      {index < array.length - 1 && <span>{", "}&nbsp;</span>}
    </Typography>
  );
};

const getEmployeeAttendanceRow = (
  employeeAttendance?: ActivityLogDetails,
  setActivityDetails?: (details: {
    details: Attendance[];
    selectedDate: string;
  }) => void,
) => {
  if (!employeeAttendance) {
    return <Typography textAlign="center">--</Typography>;
  }

  if (!employeeAttendance?.timesheet_status || employeeAttendance?.timesheet_status?.length === 0) {
    return <Typography textAlign="center">--</Typography>;
  }

  return (
    <Box display="flex" flexDirection="column" justifyContent="center">
      <Box display="flex" alignItems="center" justifyContent="center">
        {employeeAttendance?.timesheet_status?.map(renderStatus)}
        {employeeAttendance?.details?.length > 0 ? (
          <Tooltip title="View Attendance Logs">
            <IconButton
              onClick={() =>
                setActivityDetails?.({
                  details: employeeAttendance?.details || [],
                  selectedDate: employeeAttendance?.login_date || "",
                })
              }
            >
              <Timeline fontSize="small" color="success" />
            </IconButton>
          </Tooltip>
        ) : null}
      </Box>
      {!employeeAttendance?.check_in_time && !employeeAttendance?.check_out_time ? null : (
        <Typography textAlign="center" fontSize={12} color="#000">
          {`${convertTimeToAMPMWithZonedTime(employeeAttendance?.check_in_time)} - ${convertTimeToAMPMWithZonedTime(employeeAttendance?.check_out_time)}`}
        </Typography>
      )}
    </Box>
  );
};

const TimeSheetView: React.FC<TimeSheetViewProps> = ({ selectedDate }) => {
  const [activityDetails, setActivityDetails] = React.useState<{
    details: Attendance[];
    selectedDate: string;
  }>({
    details: [],
    selectedDate: "",
  });
  const { toggle, toggleState } = useToggle();
  const [attendanceDetails, selectedAttendanceDetails] = React.useState<{
    employeeCode: string;
    employeeName: string;
    yearMonth: string;
    activityLogDetails: ActivityLogDetails[];
  }>({
    employeeCode: "",
    employeeName: "",
    yearMonth: "",
    activityLogDetails: [],
  });
  const { data, isLoading, isFetching } = useQuery(
    ["get-timesheets"],
    async () => timesheetsService.getTimesheets(selectedDate),
    {
      enabled: !!selectedDate,
      refetchOnWindowFocus: false,
    },
  );

  const newAttendancesColumns = useMemo((): MRT_ColumnDef<TimeSheet>[] => {
    if (!selectedDate) {
      return [];
    }
    const [month, year] = selectedDate.split("-");
    return (
      getListOfDaysInMonth(Number(month), Number(year))?.map((attendance) => ({
        header: attendance,
        Header: () => format(new Date(attendance), "dd MMM"),
        muiTableHeadCellProps: {
          align: "center",
        },
        enablePinning: false,
        enableColumnActions: false,
        visibleInShowHideMenu: false,
        Cell: ({ cell, row }) => {
          const [_index, cellId] = cell.id.split("_");
          const employeeAttendance = row?.original.attendances?.find(
            (eachAttendance) => eachAttendance.login_date === format(new Date(cellId), "yyyy-MM-dd"),
          );
          return getEmployeeAttendanceRow(employeeAttendance, setActivityDetails);
        },
      })) || []
    );
  }, [data, selectedDate]);

  const onViewAttendanceAnalyticsClick = (timeSheet: TimeSheet) => {
    toggleState();
    selectedAttendanceDetails({
      employeeCode: timeSheet?.employee_code,
      yearMonth: selectedDate,
      employeeName: timeSheet?.employee_name,
      activityLogDetails: timeSheet?.attendances,
    });
  };

  return (
    <>
      <DataTable
        renderCaption={() => `Showing results for ${selectedDate}`}
        renderTopToolbar={({ ...props }) => (
          <Box display="flex" justifyContent="space-between" padding="12px 16px">
            <Box>
              <MRT_GlobalFilterTextField {...props} />
            </Box>
            <Box>
              <MRT_ToggleFiltersButton {...props} />
            </Box>
          </Box>
        )}
        enableHiding={false}
        enableStickyFooter
        enableTopToolbar
        enableSorting
        enableColumnPinning
        enableColumnFilterModes
        enableColumnActions
        data={data || []}
        state={{
          showSkeletons: isLoading && isFetching && !selectedDate,
          columnPinning: {
            left: ["employee_code", "employee_name", "mrt-row-actions"],
          },
          density: "compact",
        }}
        columns={[...defaultColumns, ...newAttendancesColumns]}
        enableRowActions
        displayColumnDefOptions={{
          "mrt-row-actions": {
            header: "",
            size: 50,
          },
        }}
        renderRowActions={({ row }) => (
          <Tooltip title="View Attendance Trends">
            <IconButton size="small" color="primary" onClick={() => onViewAttendanceAnalyticsClick(row?.original)}>
              <BarChartRounded fontSize="small" />
            </IconButton>
          </Tooltip>
        )}
      />
      <Modal
        isOpen={toggle}
        onClose={toggleState}
        title={`${attendanceDetails?.employeeName}'s Attendance Trends`}
        showBackButton
        showDivider
      >
        <AttendanceTrendModal
          key={`${attendanceDetails.employeeCode}-${attendanceDetails.yearMonth}`}
          attendanceDetails={attendanceDetails?.activityLogDetails}
        />
      </Modal>
      {activityDetails?.details?.length > 0 && activityDetails?.selectedDate && (
        <ActivityLogDetailsView
          isModalOpen
          onClose={() => {
            setActivityDetails({
              details: [],
              selectedDate: "",
            });
          }}
          attendanceLogs={activityDetails.details}
          selectedDate={activityDetails?.selectedDate}
        />
      )}
    </>
  );
};

export default TimeSheetView;
