import { Box, Button, Typography } from "@mui/material";
import React from "react";
import { SuccessIcon } from "src/assets/icons.svg";
import { default as languageConfig } from "src/configs/language/en.lang";
import Modal from "src/modules/Common/Modal/Modal";
import { ActivationModalStyles, ModalControllerStyles } from "../styles/styles.module";

interface ModalProps {
  open: boolean;
  handleClose: () => void;
}

const ActivationLinkModal: React.FC<ModalProps> = ({ open, handleClose }) => (
  <Modal
    isOpen={open}
    onClose={handleClose}
    sx={ModalControllerStyles.root}
    PaperProps={{
      style: ModalControllerStyles.paper,
    }}
    title={languageConfig.leads.title}
    subtitle={languageConfig.leads.subtitle}
    showBackButton
  >
    <Box sx={ActivationModalStyles.root}>
      <Typography variant="h6">{languageConfig.leads.modals.activationLink.title}</Typography>
      <Typography variant="body1" sx={ActivationModalStyles.caption}>
        {languageConfig.leads.modals.activationLink.subtitle}
      </Typography>
      <Box>
        <Box sx={ActivationModalStyles.svgContainer.root}>
          <Box sx={ActivationModalStyles.svgContainer.body}>
            <SuccessIcon />
          </Box>
        </Box>
        <Button variant="contained" onClick={handleClose} sx={ActivationModalStyles.button}>
          {languageConfig.leads.modals.activationLink.button.confirm}
        </Button>
      </Box>
    </Box>
  </Modal>
);

export default ActivationLinkModal;
