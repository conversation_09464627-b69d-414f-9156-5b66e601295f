import React from "react";
import { ActivationLinkModal, AddLeadsModal } from "./index";

type RenderModalProps = {
  open: boolean;
  modalId: string | null;
  refetch: () => void;
  handleClose: () => void;
};
interface ModalControllerProps extends RenderModalProps {
  open: boolean;
}

const RenderModal: React.FC<RenderModalProps> = ({ open, modalId, handleClose, refetch }) => {
  switch (modalId) {
    case "leads":
      return <AddLeadsModal open={open} handleClose={handleClose} refetch={refetch} />;
    case "activation":
      return <ActivationLinkModal open={open} handleClose={handleClose} />;
    default:
      return null;
  }
};

const ModalController: React.FC<ModalControllerProps> = ({ open, modalId, refetch, handleClose }) => (
  <RenderModal open={open} key={modalId} modalId={modalId} handleClose={handleClose} refetch={refetch} />
);

export default ModalController;
