import { Box, Button, Grid, Typography } from "@mui/material";
import { useMutation } from "@tanstack/react-query";
import React, { useMemo } from "react";
import { default as languageConfig } from "src/configs/language/en.lang";
import { useForm } from "src/customHooks/useForm";
import CustomTextField from "src/modules/Common/FormInputs/CustomTextField";
import Modal from "src/modules/Common/Modal/Modal";
import LeadsServiceAPI from "src/services/leads.service";
import validations from "src/utils/validators";
import { AddLeadsModalStyles, ModalControllerStyles } from "../styles/styles.module";

const formValidations = {
  orgName: [validations.validateInput, validations.validateName],
  spocEmail: [validations.validateInput, validations.validateEmail],
};

interface ModalProps {
  open: boolean;
  refetch: () => void;
  handleClose: () => void;
}

type DefaultFormState = {
  orgName: string;
  spocEmail: string;
};

const defaultState: DefaultFormState = {
  orgName: "",
  spocEmail: "",
};

const AddLeadsModal: React.FC<ModalProps> = ({ open, handleClose, refetch }) => {
  const { formDetails, formErrors, handleChange } = useForm({
    initialState: defaultState as DefaultFormState,
    validations: formValidations,
  });

  const sendLinkMutation = useMutation({
    mutationKey: ["send-leads-link"],
    mutationFn: async () => {
      LeadsServiceAPI.sendLinkAPI(
        (formDetails as DefaultFormState).orgName,
        (formDetails as DefaultFormState).spocEmail,
      );
      handleClose();
    },
    onSuccess: () => {
      handleClose();
      refetch();
    },
  });

  const canInviteLead = useMemo(() => {
    const areAllFeildsFilled = Object.values(formDetails as DefaultFormState).every((detail) => !!detail);
    const areAllErrorsResolved = Object.values(formErrors as DefaultFormState).every((error) => !error);
    return areAllFeildsFilled && areAllErrorsResolved;
  }, [formErrors as DefaultFormState]);

  const onInviteLeadClick = () => {
    sendLinkMutation.mutate();
    return;
  };

  return (
    <Modal
      isOpen={open}
      onClose={handleClose}
      sx={ModalControllerStyles.root}
      PaperProps={{
        style: ModalControllerStyles.paper,
      }}
      title={languageConfig.leads.title}
      subtitle={languageConfig.leads.subtitle}
      showBackButton
    >
      <Box style={AddLeadsModalStyles.root}>
        <Box sx={AddLeadsModalStyles.body.container}>
          <Grid container rowSpacing={2} sx={AddLeadsModalStyles.body.grid}>
            <Grid item xs={12} lg={6}>
              <Typography component="div" variant="body2" sx={AddLeadsModalStyles.textLabels}>
                {languageConfig.leads.modals.addLeads.textfields.orgName}
              </Typography>
              <CustomTextField
                type={"text"}
                id="orgName"
                sx={{
                  width: "90%",
                }}
                size="small"
                onChange={handleChange}
                value={(formDetails as DefaultFormState).orgName}
                error={!!(formErrors as DefaultFormState).orgName}
                helperText={!!(formErrors as DefaultFormState).orgName && (formErrors as DefaultFormState).orgName}
              />
            </Grid>
            <Grid item xs={12} lg={6}>
              <Typography component="div" variant="body2" sx={AddLeadsModalStyles.textLabels}>
                {languageConfig.leads.modals.addLeads.textfields.spocEmail}
              </Typography>
              <CustomTextField
                type={"text"}
                id="spocEmail"
                sx={{
                  width: "90%",
                }}
                size="small"
                onChange={handleChange}
                value={(formDetails as DefaultFormState).spocEmail}
                error={!!(formErrors as DefaultFormState).spocEmail}
                helperText={!!(formErrors as DefaultFormState).spocEmail && (formErrors as DefaultFormState).spocEmail}
              />
            </Grid>
          </Grid>
          <Box sx={AddLeadsModalStyles.buttonContainer}>
            <Button
              variant="contained"
              disabled={!canInviteLead}
              onClick={onInviteLeadClick}
              sx={AddLeadsModalStyles.button}
            >
              {languageConfig.leads.modals.addLeads.button.submit}
            </Button>
          </Box>
        </Box>
      </Box>
    </Modal>
  );
};

export default AddLeadsModal;
