import { <PERSON>, Button, CircularProgress } from "@mui/material";
import React, { Suspense, useCallback } from "react";
import { createColumnConfig } from "src/configs/table/leads.table.config";
import DataTable from "src/modules/Common/Table/DataTable";
import LeadsServiceAPI from "src/services/leads.service";
import { styles } from "./styles/styles.module";

import { default as languageConfig } from "src/configs/language/en.lang";
import { PATH_CONFIG } from "src/modules/Routing/config";
import { getACLFromFeaturekey } from "src/utils/screenUtils";

import { useMutation } from "@tanstack/react-query";
import { MRT_Row } from "material-react-table";

interface LeadsViewProps {
  data: DataEntryType[];
  isLoading: boolean;
  isFetching: boolean;
  handleOpen: (arg0: string) => void;
}

type DataEntryType = {
  organisation_name: string;
  email_id: string;
  created_on: string;
  created_by: string;
};

const LeadsView: React.FC<LeadsViewProps> = ({ data, handleOpen, isLoading, isFetching }) => {
  const leadsACL = getACLFromFeaturekey(PATH_CONFIG.LEADS.key);

  const sendLinkMutation = useMutation({
    mutationKey: ["resend-leads-link"],
    mutationFn: async ({ organisation_name, email_id }: { organisation_name: string; email_id: string }) => {
      return await LeadsServiceAPI.sendLinkAPI(organisation_name, email_id);
    },
    onSuccess: () => {
      handleOpen("activation");
    },
  });

  const ResendLinkComponent = ({ row }: { row: MRT_Row<DataEntryType> }) => {
    if (!leadsACL.canWrite) {
      return "N/A";
    }

    return (
      <Button
        variant="text"
        onClick={() => {
          sendLinkMutation.mutate({
            organisation_name: row.original.organisation_name,
            email_id: row.original.email_id,
          });
        }}
        sx={styles.tablerow.button}
      >
        {languageConfig.leads.table.button.resendLink}
      </Button>
    );
  };

  const columnConfigs = useCallback(() => createColumnConfig(), []);

  return (
    <Box>
      <Suspense fallback={<CircularProgress sx={{ alignItems: "center" }} />}>
        <DataTable
          data={data || []}
          columns={columnConfigs()}
          enablePagination={false}
          enableStickyHeader
          enableRowNumbers
          enableRowActions
          positionActionsColumn="last"
          renderRowActions={({ row }) => <ResendLinkComponent row={row as unknown as MRT_Row<DataEntryType>} />}
          state={{
            showSkeletons: isLoading && isFetching,
          }}
          localization={{
            rowNumber: "SI No",
            actions: "Activation Links",
          }}
        />
      </Suspense>
    </Box>
  );
};

export default LeadsView;
