const styles = {
  root: {
    display: "block",
    width: "auto",
    bgcolor: "background.paper",
    borderRadius: "20px",
    boxShadow: 24,
    p: 4,
  },
  header: {
    display: "flex",
    justifyContent: "space-between",
    flexDirection: "row",
    width: "100%",
  },
  caption: {
    mt: "10px",
    color: "#667085",
  },
  table: {
    display: "flex",
    width: "100%",
    // padding: '20px 0 0 0',
    flexDirection: "column",
    alignItems: "flex-start",
    gap: "18px",
    height: "auto",
  },
  tablerow: {
    button: {
      padding: "1.25rem",
      borderRadius: "5px",
      width: "max-content",
      height: "36px",
      fontSize: "1rem",
      fontWeight: "500",
      textTransform: "none",
      "&.MuiButton-text": {
        border: "none",
        backgroundColor: "#E6F2F1",
        "&:hover": {
          backgroundColor: "white",
        },
        "&:disabled": {
          backgroundColor: "#EFF4F8",
        },
      },
    },
  },
  button: {
    padding: "1.25rem",
    borderRadius: 8,
    width: "100%",
    height: "50px",
    fontSize: "1rem",
    fontWeight: "500",
    textTransform: "none",
    "&.MuiButton-contained": {
      "&:hover": {
        backgroundColor: "primary.light",
      },
      "&:disabled": {
        color: "primary.contrastText",
        backgroundColor: "primary.light",
        opacity: 0.5,
      },
    },
    "&.MuiButton-contained .MuiTouchRipple-child": {
      backgroundColor: "black",
    },
    "&.MuiButton-text": {
      border: "none",
      backgroundColor: "#EFF4F8",
      "&:hover": {
        backgroundColor: "#F2F3F3",
      },
      "&:disabled": {
        backgroundColor: "#EFF4F8",
      },
    },
  },
};

const ActivationModalStyles = {
  root: {
    ...styles.root,
    width: 536,
  },
  caption: {
    mt: "10px",
  },
  svgContainer: {
    root: {
      height: "230px",
      width: "100%",
      display: "flex",
      justifyContent: "center",
      flexDirection: "column",
    },
    body: {
      display: "flex",
      justifyContent: "center",
      flexDirection: "row",
    },
  },
  button: {
    ...styles.button,
  },
};

const AddLeadsModalStyles = {
  root: {
    width: 764,
    marginTop: 16,
    padding: 0,
  },
  header: {
    padding: 4,
    paddingBottom: 0,
    display: "flex",
    justifyContent: "space-between",
  },
  body: {
    container: {
      padding: "8px 16px",
    },
    grid: {
      padding: "0 1rem",
      minHeight: "180px",
    },
    text: {
      caption: {
        ...styles.caption,
      },
    },
  },
  textLabels: {
    color: "#667085",
    marginBottom: "8px",
  },
  closeIcon: {
    cursor: "pointer",
    padding: "4px",
    border: "1px solid #D5D7D8",
    borderRadius: "50px",
    color: "#D5D7D8",
    width: "32px",
    height: "32px",
    "&:hover": {
      color: "black",
      borderColor: "black",
    },
    transition: "0.195s all",
  },
  buttonContainer: {
    display: "flex",
    flexDirection: "row",
    justifyContent: "flex-end",
    padding: "32px 16px",
  },
  button: {
    ...styles.button,
    padding: "1.25rem",
    borderRadius: 8,
    width: "172px",
    height: "50px",
    fontSize: "1rem",
    fontWeight: "500",
    textTransform: "none",
    cursor: "pointer",
    "&.MuiButton-contained": {
      "&:hover": {
        backgroundColor: "primary.light",
      },
      "&:disabled": {
        color: "primary.contrastText",
        backgroundColor: "primary.light",
        opacity: 0.5,
      },
    },
    "&.MuiButton-contained .MuiTouchRipple-child": {
      backgroundColor: "black",
    },
    "&.MuiButton-text": {
      border: "none",
      backgroundColor: "#EFF4F8",
      "&:hover": {
        backgroundColor: "#F2F3F3",
      },
      "&:disabled": {
        backgroundColor: "#EFF4F8",
      },
    },
  },
};

const ModalControllerStyles = {
  root: {
    "& .MuiDialogContent-root": {
      width: "auto",
      padding: 0,
    },
  },
  paper: {
    ...styles.root,
  },
  button: {
    ...styles.button,
  },
};

export { styles, AddLeadsModalStyles, ActivationModalStyles, ModalControllerStyles };
