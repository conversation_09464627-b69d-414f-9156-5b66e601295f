import React from "react";
import { default as languageConfig } from "src/configs/language/en.lang";
import ContentHeader from "src/modules/Common/ContentHeader/ContentHeader";
import { PATH_CONFIG } from "src/modules/Routing/config";
import { getACLFromFeaturekey } from "src/utils/screenUtils";

interface LeadsHeaderProps {
  handleOpen: (modalId: string) => void;
}

const LeadsHeader: React.FC<LeadsHeaderProps> = ({ handleOpen }) => {
  const leadsACL = getACLFromFeaturekey(PATH_CONFIG.LEADS.key);
  return (
    <ContentHeader
      title={languageConfig.leads.title}
      subtitle={languageConfig.leads.subtitle}
      buttonTitle={leadsACL.canWrite ? languageConfig.leads.button.addLead : ""}
      primaryAction={() => handleOpen("leads")}
    />
  );
};

export default LeadsHeader;
