import { NavigateNext } from "@mui/icons-material";
import { Box, Breadcrumbs, Container, Link } from "@mui/material";
import React from "react";
import { useAppSelector } from "src/customHooks/useAppSelector";

const EffiBreadcrumbs = () => {
  const { breadcrumbs } = useAppSelector((state) => state.breadcrumbs);

  return (
    <Box>
      <Breadcrumbs separator={<NavigateNext fontSize="small" />} aria-label="breadcrumb" sx={{ margin: 1 }}>
        {breadcrumbs?.map((eachCrumb, index) => (
          <Link
            sx={{ cursor: "pointer", fontWeight: 600 }}
            key={eachCrumb.path}
            underline="hover"
            color={index > 0 && index === breadcrumbs?.length - 1 ? "#667085" : "ActiveCaption"}
          >
            {eachCrumb.label}
          </Link>
        ))}
      </Breadcrumbs>
    </Box>
  );
};

export default EffiBreadcrumbs;
