import { Box, Button, TableCellProps, Typography } from "@mui/material";
import { useQuery } from "@tanstack/react-query";
import { format } from "date-fns";
import { MRT_ColumnDef, MRT_RowSelectionState } from "material-react-table";
import React, { useEffect, useMemo, useState } from "react";
import EffiMonthDatePicker from "src/modules/Common/FormInputs/EffiMonthDatePicker";
import DataTable from "src/modules/Common/Table/DataTable";
import { ActivityLogDetails } from "src/services/api_definitions/employeeAttendance.service";
import { Attendance } from "src/services/api_definitions/timesheets.service";
import employeeAttendanceService from "src/services/employeeAttendance.service";
import { formatDateToDayMonthYear } from "src/utils/dateUtils";
import { getStatusColors } from "src/utils/typographyUtils";
import ActivityLogDetailsView from "./components/ActivityLogDetailsView";
import RegularisationModal from "./components/RegularisationModal";

const commonProps: Record<string, TableCellProps> = {
  muiTableBodyCellProps: {
    align: "left",
  },
  muiTableHeadCellProps: {
    align: "left",
  },
};

const styles = {
  customDimentions: { width: 153, height: 42 },
};

const REGULARISATION_REQUEST_STATUSES = ["pending", "absent", "regularisation pending"];
const DEFAULT_SELECTED_MONTH: string = format(new Date(), "MM-yyyy");

const ActivityLogRequests = () => {
  const [rowSelection, setRowSelection] = useState<MRT_RowSelectionState>({});
  const [selectedDate, setSelectedDate] = useState(DEFAULT_SELECTED_MONTH);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isAttendanceDetailsModalOpen, setIsAttendanceDetailsModalOpen] = useState(false);
  const [attendanceDetails, setAttendanceLogDetails] = useState<Attendance[]>([]);
  const [selectedRow, setSelectedRow] = useState<ActivityLogDetails | null>(null);

  const {
    data: activityLogs,
    isLoading,
    isFetching,
    refetch,
  } = useQuery(
    ["activity-logs", selectedDate],
    async () => employeeAttendanceService.getAttendanceActivityLog(selectedDate),
    {
      enabled: !!selectedDate,
      refetchOnWindowFocus: false,
    },
  );

  const selectedRows = useMemo(() => {
    return Object.keys(rowSelection)
      .map(Number)
      .map((index) => activityLogs?.[index]);
  }, [activityLogs, rowSelection]);

  const onRegulariseClick = () => {
    setIsModalOpen(true);
  };

  const onSelectChange = (value: string | null) => {
    if (value) {
      setSelectedDate(value);
    }
  };

  const onAttendanceLogClick = (row: ActivityLogDetails) => {
    setIsAttendanceDetailsModalOpen(true);
    setAttendanceLogDetails(row?.details);
    setSelectedRow(row);
  };

  const COLUMN_DEFS: MRT_ColumnDef<ActivityLogDetails>[] = useMemo(
    () => [
      {
        accessorKey: "login_date",
        header: "Date",
        size: 150,
        Cell: ({ row }) => formatDateToDayMonthYear(row.original.login_date),
      },
      {
        accessorKey: "check_in_time",
        header: "Check In",
        size: 200,
        ...commonProps,
        Cell: ({ row }) => row?.original?.check_in_time,
      },
      {
        accessorKey: "check_out_time",
        header: "Check Out",
        size: 200,
        ...commonProps,
        Cell: ({ row }) => row?.original?.check_out_time,
      },
      {
        accessorKey: "duration",
        header: "Duration",
        size: 200,
        ...commonProps,
      },
      {
        accessorKey: "status",
        header: "Status",
        size: 250,
        ...commonProps,
        Cell: ({ row }) =>
          row?.original?.status ? (
            <Typography fontSize={14} fontWeight={500} color={getStatusColors(row?.original?.status)}>
              {row?.original?.status}
            </Typography>
          ) : (
            <Typography>--</Typography>
          ),
      },
      {
        header: "Attendance Log",
        ...commonProps,
        Cell: ({ row }) => {
          return (
            <Button
              role="button"
              disabled={row?.original?.details?.length === 0}
              onClick={() => onAttendanceLogClick(row.original)}
            >
              View
            </Button>
          );
        },
      },
      {
        accessorKey: "location",
        ...commonProps,
        header: "Location",
        size: 200,
        Cell: ({ row }) => row?.original?.location,
      },
    ],
    [],
  );

  useEffect(() => {
    setRowSelection({});
  }, [isLoading, isFetching]);

  return (
    <Box display="flex" flexDirection="column" gap={2} width="100%">
      <Box display="flex" alignItems="center" justifyContent="flex-end" width="100%" gap={2}>
        <EffiMonthDatePicker
          onChange={onSelectChange}
          value={selectedDate}
          slotProps={{
            textField: {
              sx: styles.customDimentions,
              size: "small",
            },
          }}
        />
        <Button
          sx={{ alignSelf: "flex-end" }}
          onClick={onRegulariseClick}
          variant="contained"
          disabled={selectedRows?.length === 0}
        >
          Regularise
        </Button>
      </Box>
      <DataTable
        layoutMode="semantic"
        enableRowSelection={(row) =>
          row?.original?.status ? REGULARISATION_REQUEST_STATUSES.includes(row?.original?.status.toLowerCase()) : false
        }
        onRowSelectionChange={setRowSelection}
        data={activityLogs || []}
        state={{
          showSkeletons: isLoading || isFetching,
          rowSelection: rowSelection,
        }}
        columns={COLUMN_DEFS}
      />
      {isModalOpen && selectedRows && selectedRows?.length > 0 && (
        <RegularisationModal
          selectedRows={selectedRows as ActivityLogDetails[]}
          onClose={() => setIsModalOpen(false)}
          refetch={refetch}
        />
      )}
      {isAttendanceDetailsModalOpen && (
        <ActivityLogDetailsView
          isModalOpen={isAttendanceDetailsModalOpen}
          attendanceLogs={attendanceDetails}
          onClose={() => {
            setIsAttendanceDetailsModalOpen(false);
            setAttendanceLogDetails([]);
          }}
          selectedDate={selectedRow?.login_date || ""}
        />
      )}
    </Box>
  );
};

export default ActivityLogRequests;
