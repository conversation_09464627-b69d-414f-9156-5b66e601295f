import { formatDate } from "date-fns";
import React from "react";
import Modal from "src/modules/Common/Modal/Modal";
import ActivityLog from "src/modules/EmployeeAttendance/components/components/TimelineInfo";
import { Attendance } from "src/services/api_definitions/timesheets.service";
import { convertTimeToAMPMWithZonedTime, getCalendarDayDifference } from "src/utils/dateUtils";

interface ActivityLogDetailsProps {
  isModalOpen: boolean;
  onClose: () => void;
  attendanceLogs: Attendance[];
  selectedDate: string;
}

const isDayChange = (checkInTime: string, loginTime: string) => {
  const checkInDate = new Date(checkInTime);
  const loginDate = new Date(loginTime);
  return checkInDate.getDate() !== loginDate.getDate();
};

function convertToActivities(data: Attendance[], loginTime: string): any[] {
  return data.reduce(
    (activities, item) => [
      ...activities,
      {
        time: convertTimeToAMPMWithZonedTime(item.check_in_time),
        location: item.location,
        duration: item.duration,
        type: "login",
        isDayChange: isDayChange(item.check_in_time, loginTime),
        dateDifference: getCalendarDayDifference(item.check_in_time, loginTime),
      },
      {
        time: item.check_out_time ? convertTimeToAMPMWithZonedTime(item.check_out_time) : null,
        location: item.location,
        duration: "",
        type: "logout",
        isDayChange: isDayChange(item.check_out_time, loginTime),
        dateDifference: getCalendarDayDifference(item.check_out_time, loginTime),
      },
    ],
    [] as any[],
  );
}

const ActivityLogDetailsView: React.FC<ActivityLogDetailsProps> = ({
  isModalOpen,
  onClose,
  attendanceLogs,
  selectedDate,
}) => {
  return (
    <Modal
      title="Attendance Logs"
      subtitle={formatDate(selectedDate, "EEEE, dd MMM yyyy")}
      isOpen={isModalOpen}
      showBackButton
      showDivider
      onClose={onClose}
      maxWidth="500px"
    >
      <ActivityLog activities={convertToActivities(attendanceLogs, selectedDate)} />
    </Modal>
  );
};

export default ActivityLogDetailsView;
