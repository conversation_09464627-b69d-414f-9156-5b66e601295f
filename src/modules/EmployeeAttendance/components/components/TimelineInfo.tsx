import { LoginRounded, LogoutRounded } from "@mui/icons-material";
import {
  Timeline,
  TimelineConnector,
  TimelineContent,
  TimelineDot,
  TimelineItem,
  TimelineOppositeContent,
  TimelineSeparator,
} from "@mui/lab";
import { Box, Typography } from "@mui/material";
import { styled } from "@mui/system";
import React from "react";

interface Activity {
  time: string;
  location: string;
  duration: string;
  type: "login" | "logout";
  isDayChange: boolean;
  dateDifference: number;
}

interface ActivityLogProps {
  activities: Activity[];
}

const getLocationColor = (location: string) => {
  switch (location) {
    case "Home":
      return "#ECE9FF";
    case "Office":
      return "#E6F8F4";
    case "On-Site":
      return "#FFF1DF";
    default:
      return "#FFF1DF";
  }
};

const DottedConnector = styled(TimelineConnector)(() => ({
  border: "none",
  borderLeft: "2px dashed #bdbdbd",
  height: "45px",
  backgroundColor: "white",
  marginTop: "-10px",
  marginBottom: "-10px",
}));

const CustomConnector = styled(TimelineConnector)(() => ({
  border: "none",
  borderLeft: "2.5px solid #bdbdbd",
  // height: '30px',
  backgroundColor: "white",
  marginTop: "-10px",
  marginBottom: "-10px",
}));

const EndLineConnector = styled(TimelineConnector)(() => ({
  marginRight: "31px",
  marginTop: "-38px",
  borderLeft: "2.5px solid #bdbdbd",
  height: "45px",
  backgroundColor: "white",
}));

const LocationBox = styled(Box)(() => ({
  display: "inline-block",
  padding: "2.5px 8px",
  borderRadius: "4px",
  color: "black",
  fontSize: "0.75rem",
  zIndex: 100,
}));

const ActivityLog: React.FC<ActivityLogProps> = ({ activities }) => {
  const filteredActivity = activities.filter((activity) => activity.time !== null);
  return (
    <Box sx={{ marginLeft: "-100px" }}>
      <Timeline position="right">
        {filteredActivity.map((activity, index) => {
          const isLogin = activity.type === "login";
          return (
            <TimelineItem key={index}>
              <TimelineOppositeContent>
                {index < filteredActivity.length - 1 && isLogin && (
                  <Typography sx={{ marginTop: "54px" }} variant="body2" color="textSecondary">
                    {`+${activity.duration} Hrs`}
                  </Typography>
                )}
              </TimelineOppositeContent>
              <TimelineSeparator>
                <TimelineDot sx={{ backgroundColor: isLogin ? "#4CAF50" : "#FF5050" }}>
                  {isLogin ? <LoginRounded /> : <LogoutRounded />}
                </TimelineDot>
                {index < filteredActivity.length - 1 && (
                  <React.Fragment>{isLogin ? <CustomConnector /> : <DottedConnector />}</React.Fragment>
                )}
              </TimelineSeparator>
              <TimelineContent>
                <Box
                  sx={{
                    display: "flex",
                    flexDirection: "column",
                    marginTop: "12px",
                  }}
                >
                  <Box sx={{ display: "flex", flexDirection: "row" }}>
                    <Typography variant="body2">{activity.time}</Typography>
                    {activity.isDayChange && (
                      <div
                        style={{
                          display: "flex",
                          flexDirection: "column",
                          backgroundColor: "rgb(211, 211, 211, 0.3)",
                          borderRadius: "4px",
                          paddingTop: "2px",
                          marginLeft: "3px",
                        }}
                      >
                        {" "}
                        <LocationBox
                          sx={{
                            marginLeft: "5px",
                            padding: "2px 2px",
                            height: "max-content",
                            color: "red",
                            fontSize: "14px",
                            marginTop: "-5px",
                            marginRight: "3px",
                          }}
                        >
                          +{activity.dateDifference}
                        </LocationBox>
                        <LocationBox
                          sx={{
                            marginLeft: "5px",
                            padding: "2px 2px",
                            height: "max-content",
                            fontSize: "10px",
                            marginTop: "-5px",
                            color: "red",
                            marginRight: "3px",
                          }}
                        >
                          DAY
                        </LocationBox>
                      </div>
                    )}
                  </Box>
                  {isLogin && (
                    <LocationBox
                      sx={{
                        backgroundColor: getLocationColor(activity.location),
                        width: "max-content",
                        marginTop: activity.isDayChange ? "5px" : "20px",
                      }}
                    >
                      {activity.location}
                    </LocationBox>
                  )}
                </Box>
              </TimelineContent>
            </TimelineItem>
          );
        })}
        {!activities[activities.length - 1].time && (
          <TimelineItem>
            <EndLineConnector />
          </TimelineItem>
        )}
      </Timeline>
    </Box>
  );
};

export default ActivityLog;
