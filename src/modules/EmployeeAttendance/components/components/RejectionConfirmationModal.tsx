import { Box, Button, DialogActions } from "@mui/material";
import React from "react";
import { useForm } from "src/customHooks/useForm";
import CustomTextField from "src/modules/Common/FormInputs/CustomTextField";
import Modal from "src/modules/Common/Modal/Modal";
import validators from "src/utils/validators";
interface RejectionConfirmationModalProps {
  onClose: () => void;
  onSubmit: (comment: string) => void;
  title: string;
}

const defaultFormState = {
  comment: "",
};

const RejectionConfirmationModal: React.FC<RejectionConfirmationModalProps> = ({ onClose, onSubmit, title }) => {
  const { formDetails, handleChange, formErrors, areFormDetailsValid } = useForm({
    initialState: defaultFormState,
    isBulk: false,
    validations: {
      comment: [validators.validateInput],
    },
  });

  const typedFormDetails = formDetails as typeof defaultFormState;
  const typedFormErrors = formErrors as Record<keyof typeof defaultFormState, string>;

  const onRejectionRequestsSubmit = () => {
    if (onSubmit) {
      onSubmit(typedFormDetails.comment);
    }
  };

  return (
    <Modal
      title={title}
      isOpen
      onClose={onClose}
      showBackButton
      actions={
        <DialogActions>
          <Button
            disabled={!areFormDetailsValid}
            sx={{ minWidth: 150 }}
            variant="contained"
            onClick={onRejectionRequestsSubmit}
          >
            Submit
          </Button>
        </DialogActions>
      }
    >
      <Box display="flex" flexDirection="column" gap={2}>
        <Box>
          <CustomTextField
            fullWidth
            name="comment"
            id="comment"
            onChange={handleChange}
            type="textarea"
            multiline
            value={typedFormDetails.comment}
            rows={2}
            error={!!typedFormErrors?.comment}
            helperText={!!typedFormErrors?.comment && typedFormErrors?.comment}
            maxRows={4}
            title="Rejection Reason"
            placeholder="Enter Comments"
            required
          />
        </Box>
      </Box>
    </Modal>
  );
};

export default RejectionConfirmationModal;
