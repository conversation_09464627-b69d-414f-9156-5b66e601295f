import { Close } from "@mui/icons-material";
import { Box, Button, Chip, DialogActions, Grid } from "@mui/material";
import { useMutation } from "@tanstack/react-query";
import React, { useMemo, useState } from "react";
import { useForm } from "src/customHooks/useForm";
import CustomTextField from "src/modules/Common/FormInputs/CustomTextField";
import Modal from "src/modules/Common/Modal/Modal";
import { ActivityLogDetails } from "src/services/api_definitions/employeeAttendance.service";
import employeeAttendanceService from "src/services/employeeAttendance.service";
import { formatDateToDayMonthYear } from "src/utils/dateUtils";
import validators from "src/utils/validators";

interface RegularisationModalProps {
  onClose: () => void;
  selectedRows: ActivityLogDetails[];
  refetch: () => void;
}

const defaultFormState = {
  reason: "",
};

const RegularisationModal: React.FC<RegularisationModalProps> = ({ onClose, selectedRows, refetch }) => {
  const defaultChipsState = useMemo(() => selectedRows?.map((selectedRow) => selectedRow?.login_date), [selectedRows]);
  const [chips, setSelectedChips] = useState([...defaultChipsState]);
  const { formDetails, handleChange, formErrors, areFormDetailsValid } = useForm({
    initialState: defaultFormState,
    isBulk: false,
    validations: {
      reason: [validators.validateInput],
    },
  });

  const typedFormDetails = formDetails as typeof defaultFormState;
  const typedFormErrors = formErrors as Record<keyof typeof defaultFormState, string>;

  const regularisationMutation = useMutation({
    mutationFn: async () => employeeAttendanceService.applyForRegularisation(chips, typedFormDetails.reason),
    onSuccess: () => {
      onClose();
      refetch();
    },
  });

  const onDelete = (ev: React.ChangeEvent, index: number) => {
    ev.preventDefault();
    ev.stopPropagation();
    setSelectedChips((prevChips) => {
      const elementToRemove = prevChips[index];
      const newChips = [...prevChips].filter((chip) => chip !== elementToRemove);
      return newChips;
    });
  };

  const onSubmit = () => {
    regularisationMutation.mutate();
  };

  return (
    <Modal
      title="Apply Regularisation"
      subtitle="Apply to regularise your missed attendances, leaves"
      isOpen
      onClose={onClose}
      showBackButton
      actions={
        <DialogActions>
          <Button disabled={!areFormDetailsValid} sx={{ minWidth: 150 }} variant="contained" onClick={onSubmit}>
            Submit
          </Button>
        </DialogActions>
      }
    >
      <Box display="flex" flexDirection="column" gap={2}>
        <Grid container spacing={2}>
          {chips?.map((selectedRow, index) => (
            <Grid key={selectedRow} item xs={12} sm={4} md={2}>
              <Chip
                deleteIcon={chips?.length === 1 ? <></> : <Close />}
                onDelete={(ev) => onDelete(ev, index)}
                sx={{ background: "#E6F2F1", borderRadius: 2, minWidth: 120 }}
                label={formatDateToDayMonthYear(selectedRow)}
              />
            </Grid>
          ))}
        </Grid>
        <Box>
          <CustomTextField
            fullWidth
            name="reason"
            id="reason"
            onChange={handleChange}
            type="textarea"
            multiline
            value={typedFormDetails.reason}
            rows={2}
            error={!!typedFormErrors?.reason}
            helperText={!!typedFormErrors?.reason && typedFormErrors?.reason}
            maxRows={4}
            title="Reason"
            placeholder="Enter Reason"
            required
          />
        </Box>
      </Box>
    </Modal>
  );
};

export default RegularisationModal;
