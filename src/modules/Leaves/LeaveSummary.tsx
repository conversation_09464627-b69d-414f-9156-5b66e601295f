import HelpOutlineOutlinedIcon from "@mui/icons-material/HelpOutlineOutlined";
import HistoryIcon from "@mui/icons-material/History";
import { Box, Card, Divider, Grid2, Icon<PERSON>utton, Skeleton, Stack, Tooltip, Typography } from "@mui/material";
import React, { useState } from "react";
import LeaveInfoPopover from "./components/LeaveInfoPopover";
import LeaveTransactionModal from "./components/LeaveTransactionModal";

import { LeaveSummaryData } from "src/services/data_transformers/leave.transforms";

type Props = {
  leaveSummary: LeaveSummaryData[];
  isLoading?: boolean;
};
interface LeaveCardProps {
  summary: LeaveSummaryData;
  color: string;
  onHistoryClick: (summary: LeaveSummaryData) => void;
  onInfoClick: (event: React.MouseEvent<HTMLButtonElement>, summary: LeaveSummaryData, color: string) => void;
}

const LeaveCard: React.FC<LeaveCardProps> = ({ summary, color, onHistoryClick, onInfoClick }) => {
  return (
    <Card
      sx={{
        backgroundColor: color,
        borderRadius: 2,
        boxShadow: 2,
        minWidth: 300,
        maxWidth: 300,
        height: "90px",
        display: "flex",
        flexDirection: "row",
        gap: 1,
        alignItems: "center",
        justifyContent: "space-between",
        padding: 1.5,
      }}
    >
      {/* Left Side: Title & Actions */}
      <Box
        sx={{
          flex: 1,
          display: "flex",
          flexDirection: "column",
          justifyContent: "center",
          height: "100%",
          overflow: "hidden",
        }}
      >
        {/* Title with Ellipsis */}
        <Tooltip title={summary.leaveType}>
          <Typography
            variant="h6"
            fontWeight={600}
            color="text.primary"
            sx={{
              whiteSpace: "nowrap",
              overflow: "hidden",
              textOverflow: "ellipsis", // Truncate overflowing text
              fontSize: "16px",
              maxWidth: "100%", // Ensures text doesn't overflow its container
              marginBottom: "8px",
            }}
          >
            {summary.leaveType}
          </Typography>
        </Tooltip>

        {/* Actions */}
        <Box display="flex" gap={1}>
          <Tooltip title={`${summary.leaveType} history`} placement="top">
            <IconButton size="small" onClick={() => onHistoryClick(summary)} sx={{ color: "#42526B", padding: "6px" }}>
              <HistoryIcon fontSize="small" />
            </IconButton>
          </Tooltip>
          <Tooltip title={`Need info about ${(summary.leaveType)?.toLowerCase()}? Click here.`}>
            <IconButton
              size="small"
              onClick={(event) => onInfoClick(event, summary, color)}
              sx={{ color: "#42526B", padding: "6px" }}
            >
              <HelpOutlineOutlinedIcon fontSize="small" />
            </IconButton>
          </Tooltip>
        </Box>
      </Box>

      {/* Right Side: Number */}
      <Box
        sx={{
          flex: "0 0 80px",
          height: "100%",
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
          backgroundColor: "rgba(0, 0, 0, 0.05)",
          borderRadius: 1,
        }}
      >
        <Typography variant="h5" fontWeight={700} color="text.primary">
          {summary.noOfLeaves}
        </Typography>
      </Box>
    </Card>
  );
};

const LeaveSummary: React.FC<Props> = ({ leaveSummary, isLoading = false }) => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedLeave, setSelectedLeave] = useState<LeaveSummaryData | null>(null);
  const [popoverAnchorEl, setPopoverAnchorEl] = useState<HTMLButtonElement | null>(null);
  const [popoverData, setPopoverData] = useState<{ info: string[]; color: string } | null>(null);

  const colors = ["#CEF2E9", "#D7EDFF", "#DAD4FF", "#FFE2EB", "#FFE4C1"]; // Example palette

  const handleHistoryClick = (summary: LeaveSummaryData) => {
    setSelectedLeave(summary);
    setIsModalOpen(true);
  };

  const handleInfoClick = (event: React.MouseEvent<HTMLButtonElement>, summary: LeaveSummaryData, color: string) => {
    setPopoverAnchorEl(event.currentTarget);
    setPopoverData({ info: summary.info, color });
    setSelectedLeave(summary);
  };

  const handleModalClose = () => {
    setIsModalOpen(false);
    setSelectedLeave(null);
  };

  const handlePopoverClose = () => {
    setPopoverAnchorEl(null);
    setPopoverData(null);
  };

  if (isLoading) {
    return (
      <Stack>
        <Skeleton height={150} />
      </Stack>
    );
  }

  return (
    <Box sx={{ padding: "16px", overflowX: "auto" }}>
      <Grid2 container spacing={2}>
        <Stack direction="row" spacing={2} divider={<Divider orientation="vertical" flexItem />}>
          {leaveSummary.map((summary, index) => (
            <Grid2 size={12} key={summary.leaveType}>
              <LeaveCard
                summary={summary}
                color={colors[index % colors.length]}
                onHistoryClick={handleHistoryClick}
                onInfoClick={handleInfoClick}
              />
            </Grid2>
          ))}
        </Stack>
      </Grid2>
      {isModalOpen && selectedLeave && (
        <LeaveTransactionModal isModalOpen={isModalOpen} onClose={handleModalClose} leaveDetails={selectedLeave} />
      )}

      {/* Popover for Leave Info */}
      {popoverData && (
        <LeaveInfoPopover
          open={Boolean(popoverAnchorEl)}
          anchorEl={popoverAnchorEl}
          onClose={handlePopoverClose}
          data={{
            leaveType: selectedLeave?.leaveType || "",
            info: popoverData.info,
            color: popoverData.color,
          }}
        />
      )}
    </Box>
  );
};

export default LeaveSummary;
