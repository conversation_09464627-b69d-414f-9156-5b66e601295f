import React from "react";
import { LeaveSummaryData } from "src/services/data_transformers/leave.transforms";
import Tabs, { TabType } from "../Common/CustomTabs/CustomTabs";
import LeaveApproval from "./LeaveApproval";
import LeaveRequest from "./LeaveRequest";

interface ManagerLeaveViewProps {
  leaveSummary: LeaveSummaryData[];
  refetchLeaveSummary: () => void;
  isLoading: boolean;
}

const ManagerLeaveView: React.FC<ManagerLeaveViewProps> = ({ leaveSummary, refetchLeaveSummary, isLoading }) => {
  const tabs: TabType[] = [
    {
      id: "leave-requests",
      component: (
        <LeaveRequest isLoading={isLoading} leaveSummary={leaveSummary} refetchLeaveSummary={refetchLeaveSummary} />
      ),
      label: "Leave Requests",
    },
    {
      id: "leave-approvals",
      component: <LeaveApproval />,
      label: "Leave Approvals",
    },
  ];
  return <Tabs tabs={tabs} />;
};

export default ManagerLeaveView;
