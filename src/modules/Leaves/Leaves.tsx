import { Box, Container } from "@mui/material";
import { useQuery } from "@tanstack/react-query";
import React from "react";
import LeaveServiceAPI from "src/services/leaves.service";
import ContentHeader from "../Common/ContentHeader/ContentHeader";
import LeaveSummary from "./LeaveSummary";
import LeavesView from "./LeavesView";

const Leaves = () => {
  const {
    data: leaveSummary,
    isFetching,
    isLoading,
    refetch,
  } = useQuery({
    queryKey: ["get-leave-summary"],
    queryFn: async () => LeaveServiceAPI.getLeaveSummary(),
    retryOnMount: false,
    refetchOnWindowFocus: false,
  });

  return (
    <Container disableGutters component={Box} display="flex" flexDirection="column" gap={1} maxWidth="xl">
      <ContentHeader subtitle="Check your leave balance and apply for leave" />
      <LeaveSummary leaveSummary={leaveSummary || []} isLoading={isLoading || isFetching} />
      <LeavesView isLoading={isLoading || isFetching} leaveSummary={leaveSummary || []} refetch={refetch} />
    </Container>
  );
};

export default Leaves;
