import {
  Timeline,
  TimelineConnector,
  TimelineContent,
  TimelineDot,
  TimelineItem,
  TimelineOppositeContent,
  TimelineSeparator,
} from "@mui/lab";
import {
  Box,
  Chip,
  CircularProgress,
  Dialog,
  DialogContent,
  DialogTitle,
  IconButton,
  Paper,
  Typography,
  useTheme,
} from "@mui/material";
import React from "react";

import AddCircleIcon from "@mui/icons-material/AddCircle";
import CancelIcon from "@mui/icons-material/Cancel";
import CloseIcon from "@mui/icons-material/Close";
import KeyboardTabIcon from "@mui/icons-material/KeyboardTab";
import RemoveCircleIcon from "@mui/icons-material/RemoveCircle";
import StartIcon from "@mui/icons-material/Start";
import TimelapseIcon from "@mui/icons-material/Timelapse";
import { styled } from "@mui/system";
import { useQuery } from "@tanstack/react-query";
import { format } from "date-fns";
import {
  LeaveTransaction,
  LeaveTransactionConfig,
  LeaveTransactionModalProps,
  LeaveTransactionType,
} from "src/services/api_definitions/leave.service";
import LeaveServiceAPI from "src/services/leaves.service";
import { DD_MM_YYYY } from "src/utils/dateUtils";

const StyledDialog = styled(Dialog)(({ theme }) => ({
  "& .MuiDialog-paper": {
    borderRadius: theme.spacing(1),
    [theme.breakpoints.up("sm")]: {
      minWidth: "550px",
      maxWidth: "750px",
    },
  },
}));

const StyledTimeline = styled(Timeline)(({ theme }) => ({
  padding: theme.spacing(2),
  "& .MuiTimelineItem-root": {
    "&:before": {
      display: "none",
    },
    minHeight: 80,
    margin: 0,
    padding: 0,
  },
  maxWidth: "600px",
  margin: "0 auto",
}));

const StyledTimelineDot = styled(TimelineDot)(() => ({
  margin: 0,
  padding: 0,
  borderRadius: "50%",
  width: "60px",
  height: "60px",
  display: "flex",
  alignItems: "center",
  justifyContent: "center",
}));

const getTransactionConfig = (type: LeaveTransactionType): LeaveTransactionConfig => {
  const configs: Record<LeaveTransactionType, LeaveTransactionConfig> = {
    Accrued: {
      icon: <AddCircleIcon />,
      color: "#007BFF",
      backgroundColor: "#007BFF",
      lightBackground: "#e3f2fd",
    },
    Availed: {
      icon: <RemoveCircleIcon />,
      color: "#28A745 ",
      backgroundColor: "#28A745 ",
      lightBackground: "#e8f5e9",
    },
    Canceled: {
      icon: <CancelIcon />,
      color: "#6C757D",
      backgroundColor: "#6C757D",
      lightBackground: "#ffebee",
    },
    Lapsed: {
      icon: <TimelapseIcon />,
      color: "#DC3545",
      backgroundColor: "#DC3545",
      lightBackground: "#fffde7",
    },
  };
  return configs[type] || configs.Accrued;
};

const TransactionItem = ({ transaction }: { transaction: LeaveTransaction }) => {
  const config = getTransactionConfig(transaction.transaction_type);
  const dates = transaction.transaction_dates.map((d) => new Date(d.transaction_date));

  const startDate = dates.reduce((a, b) => (a < b ? a : b), dates[0]);
  const endDate = dates.reduce((a, b) => (a > b ? a : b), dates[0]);

  const formatTimeToWords = (dateString: string) => {
    const date = new Date(dateString);
    return `On ${format(date, DD_MM_YYYY)} at ${format(date, "h:mm aa")}`;
  };

  const formatDateRange = (start: Date, end: Date) => {
    return `From ${format(start, DD_MM_YYYY)} to ${format(end, DD_MM_YYYY)}`;
  };

  const getDisplayValue = () => {
    const value = Math.abs(transaction.leave_days);
    const prefix = ["Canceled", "Accrued"].includes(transaction.transaction_type) ? "+" : "-";
    return `${prefix}${value}`;
  };

  return (
    <TimelineItem>
      <TimelineOppositeContent
        sx={{
          flex: "none",
          width: "190px",
          pr: 2,
          m: 0,
          textAlign: "right",
          display: "flex",
          flexDirection: "column",
          alignItems: "flex-end",
          justifyContent: "center",
          height: "60px",
        }}
      >
        <Chip
          size="small"
          icon={config.icon}
          label={transaction.transaction_type}
          sx={{
            backgroundColor: config.lightBackground,
            color: config.color,
            mb: 0.25,
            "& .MuiChip-icon": {
              color: config.color,
            },
          }}
        />
        <Typography variant="caption" color="text.secondary" noWrap>
          {formatTimeToWords(transaction.created_at)}
        </Typography>
      </TimelineOppositeContent>

      <TimelineSeparator>
        <StyledTimelineDot sx={{ backgroundColor: config.color }}>
          <Typography sx={{ color: "white", fontWeight: "bold", fontSize: "0.875rem" }}>{getDisplayValue()}</Typography>
        </StyledTimelineDot>
        <TimelineConnector sx={{ backgroundColor: config.color }} />
      </TimelineSeparator>

      <TimelineContent
        sx={{
          flex: 1,
          pl: 2,
          py: 0.5,
          m: 0,
        }}
      >
        {transaction.transaction_dates.length > 0 && (
          <Paper
            elevation={0}
            sx={{
              p: 1,
              backgroundColor: config.lightBackground,
            }}
          >
            <Typography variant="body2" color="text.secondary">
              {formatDateRange(startDate, endDate)}
            </Typography>
          </Paper>
        )}
      </TimelineContent>
    </TimelineItem>
  );
};

const LeaveTransactionModal = ({ isModalOpen, onClose, leaveDetails }: LeaveTransactionModalProps) => {
  const theme = useTheme();

  const { data: transactionData, isLoading } = useQuery({
    queryKey: ["leaveTransaction", leaveDetails.leaveType],
    queryFn: () => LeaveServiceAPI.getLeaveTransactions(leaveDetails.leaveType),
    enabled: isModalOpen,
  });

  return (
    <StyledDialog open={isModalOpen} onClose={onClose} maxWidth="md" fullWidth>
      <DialogTitle
        sx={{
          m: 0,
          p: 2,
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
        }}
      >
        <Typography variant="h6" component="div">
          {leaveDetails.leaveType} Transaction History
        </Typography>
        <IconButton aria-label="close" onClick={onClose} sx={{ color: theme.palette.grey[500] }}>
          <CloseIcon />
        </IconButton>
      </DialogTitle>

      <DialogContent dividers>
        {isLoading ? (
          <Box sx={{ display: "flex", justifyContent: "center", p: 3 }}>
            <CircularProgress />
          </Box>
        ) : transactionData && transactionData.length === 0 ? (
          <Box sx={{ p: 2, textAlign: "center" }}>
            <Typography variant="body1" color="text.secondary">
              No transactions found
            </Typography>
          </Box>
        ) : (
          <Box sx={{ py: 2 }}>
            <StyledTimeline>
              <TimelineItem>
                <TimelineOppositeContent
                  sx={{
                    flex: "none",
                    width: "190px",
                    m: 0,
                    pr: 2,
                    textAlign: "right",
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "flex-end",
                    height: "60px",
                  }}
                >
                  <Chip
                    size="small"
                    label="Total Leaves"
                    icon={<StartIcon />}
                    sx={{
                      backgroundColor: "grey.200",
                      color: "primary.main",
                      position: "relative",
                      "& .MuiChip-icon": {
                        color: "primary.main",
                      },
                    }}
                  />
                </TimelineOppositeContent>

                <TimelineSeparator>
                  <StyledTimelineDot
                    sx={{
                      backgroundColor: "primary.main",
                      width: "60px",
                      height: "60px",
                      m: 0,
                      p: 0,
                    }}
                  >
                    <Typography sx={{ color: "white", fontWeight: "bold", fontSize: "1.125rem" }}>
                      {leaveDetails.totalLeaves}
                    </Typography>
                  </StyledTimelineDot>
                  <TimelineConnector
                    sx={{
                      backgroundColor: "primary.main",
                      width: 2,
                      m: 0,
                      p: 0,
                    }}
                  />
                </TimelineSeparator>

                <TimelineContent sx={{ m: 0, p: 0 }} />
              </TimelineItem>

              {transactionData?.map((transaction: LeaveTransaction, index: number) => (
                <TransactionItem key={index} transaction={transaction} />
              ))}

              <TimelineItem>
                <TimelineOppositeContent
                  sx={{
                    flex: "none",
                    width: "190px",
                    m: 0,
                    pr: 2,
                    textAlign: "right",
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "flex-end",
                    height: "60px",
                  }}
                >
                  <Chip
                    size="small"
                    icon={<KeyboardTabIcon />}
                    label="Leaves Remaining"
                    sx={{
                      backgroundColor: "grey.200",
                      color: "primary.main",
                      position: "relative",
                      "& .MuiChip-icon": {
                        color: "primary.main",
                      },
                    }}
                  />
                </TimelineOppositeContent>

                <TimelineSeparator>
                  <StyledTimelineDot
                    sx={{
                      backgroundColor: "primary.main",
                      width: "60px",
                      height: "60px",
                      m: 0,
                      p: 0,
                    }}
                  >
                    <Typography sx={{ color: "white", fontWeight: "bold", fontSize: "1.125rem" }}>
                      {leaveDetails.noOfLeaves}
                    </Typography>
                  </StyledTimelineDot>
                </TimelineSeparator>

                <TimelineContent sx={{ m: 0, p: 0 }} />
              </TimelineItem>
            </StyledTimeline>
          </Box>
        )}
      </DialogContent>
    </StyledDialog>
  );
};

export default LeaveTransactionModal;
