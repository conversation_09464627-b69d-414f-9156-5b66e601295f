import { Box, Popover, Typography } from "@mui/material";
import React from "react";

interface LeaveInfoPopoverProps {
  open: boolean;
  anchorEl: HTMLButtonElement | null;
  onClose: (event: React.MouseEvent<HTMLElement>) => void;
  data: {
    leaveType: string;
    info: string[];
    color: string;
  };
}

const LeaveInfoPopover: React.FC<LeaveInfoPopoverProps> = ({ open, anchorEl, onClose, data }) => {
  const infoItems = data?.info || [];

  return (
    <Popover
      open={open}
      anchorEl={anchorEl}
      onClose={onClose}
      anchorOrigin={{
        vertical: "bottom", //bottom
        horizontal: "center", //center
      }}
      transformOrigin={{
        vertical: "top", //top
        horizontal: "center", //center
      }}
      sx={{
        "& .MuiPopover-paper": {
          border: `4px solid ${data?.color || "#000"}`,
          borderRadius: 4,
          bgcolor: "#f5f5f5",
          minWidth: 200,
          width: "max-content",
          // maxWidth: 350,
          position: "relative",
          "&::before": {
            content: '""',
            position: "absolute",
            left: -10,
            top: "50%",
            transform: "translateY(-50%)",
            borderWidth: 5,
            borderStyle: "solid",
            borderColor: `transparent ${data?.color || "#000"} transparent transparent`,
          },
          "&::after": {
            content: '""',
            position: "absolute",
            left: -7,
            top: "50%",
            transform: "translateY(-50%)",
            borderWidth: 4,
            borderStyle: "solid",
            borderColor: "transparent #f5f5f5 transparent transparent",
          },
        },
      }}
    >
      <Box sx={{ p: 2 }}>
        {/* <Box 
          sx={{ 
            pb: 1,
            borderBottom: `2px solid ${data?.color || "#000"}`,
            mb: 2
          }}
        >
          <Typography variant="body2">
            <strong>{data?.leaveType} info: </strong>
          </Typography>
        </Box> */}
        {infoItems.length > 0 && (
          <Typography variant="body2">
            <ul style={{ margin: "8px 0", paddingLeft: "20px" }}>
              {infoItems.map((item, index) => (
                <li key={index}>{item}</li>
              ))}
            </ul>
          </Typography>
        )}
      </Box>
    </Popover>
  );
};

export default LeaveInfoPopover;
