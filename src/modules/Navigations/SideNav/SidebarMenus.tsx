import { Box } from "@mui/material";
import React from "react";
// import { SideBarMenu } from "src/configs/app.config";
import { useAppSelector } from "src/customHooks/useAppSelector";
import SidebarMenuItem from "./SidebarMenuItem";

interface SidebarMenusProps {
  open: boolean;
}

const SidebarMenus: React.FC<SidebarMenusProps> = ({ open }) => {
  const { authorisedScreens } = useAppSelector((state) => state.userManagement);
  // const hasSettings = useMemo(() => authorisedScreens.some((screen) => screen.key === "settings"), [authorisedScreens]);
  return (
    <Box
      maxHeight={open ? "75vh" : "80vh"}
      sx={{ overflowY: "auto", overflowX: "hidden" }}
      position="relative"
      width="100%"
    >
      {authorisedScreens
        .filter(
          (screen) => !screen.isInternal,
          // && screen.key !== "settings" // uncomment this if there is an requirement to stick items to the footer of the sidenavbar otherwise remove
        )
        .map((authorisedScreen, index) => (
          <SidebarMenuItem
            authorisedScreen={authorisedScreen}
            open={open}
            index={index}
            key={authorisedScreen["title"]}
          />
        ))}
      {/* uncomment this if there is an requirement to stick items to the footer of the sidenavbar otherwise remove */}
      {/* <Box position="absolute" bottom={100} width="100%">
        {hasSettings && (
          <SidebarMenuItem
            authorisedScreen={authorisedScreens.find((screen) => screen.key === "settings") as SideBarMenu}
            open={open}
            index={authorisedScreens.length - 1}
          />
        )}
      </Box> */}
    </Box>
  );
};

export default SidebarMenus;
