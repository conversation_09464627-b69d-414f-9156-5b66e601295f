import { ChevronLeftOutlined, ChevronRightOutlined } from "@mui/icons-material";
import { Box, IconButton, Paper, Typography } from "@mui/material";
import React, { Suspense } from "react";
import { useNavigate } from "react-router-dom";

import languageConfig from "src/configs/language/en.lang";
import StyledDivider from "src/modules/Common/Divider";

import { effiHRLogo as EffiHRLogo } from "src/assets/icons.svg";
import { useAppSelector } from "src/customHooks/useAppSelector";
import { Drawer, DrawerHeader, DrawerOpenContainer, DrawerStyles, FootNote } from "./MenuSideBar.styles";
import SidebarMenus from "./SidebarMenus";

interface SideBarProps {
  open: boolean;
  handleDrawerToggle: () => void;
  isHidden?: boolean;
}

const MenuSideBar: React.FC<SideBarProps> = ({ open, handleDrawerToggle, isHidden }) => {
  const navigate = useNavigate();
  const { userDetails, selectedOrganisation } = useAppSelector((state) => state.userManagement);
  const OrgLogo = userDetails.organisations?.find((ele) => ele.name === selectedOrganisation)?.logo;

  if (isHidden) {
    return null;
  }

  return (
    <Box sx={DrawerStyles.root}>
      <DrawerOpenContainer open={open}>
        <IconButton onClick={handleDrawerToggle} sx={{ padding: "1px" }}>
          {!open ? <ChevronRightOutlined /> : <ChevronLeftOutlined />}
        </IconButton>
      </DrawerOpenContainer>
      <Drawer variant="permanent" hideBackdrop open={open}>
        <DrawerHeader open={open}>
          <Box sx={DrawerStyles.DrawerHeader.logo.root} onClick={() => navigate("/")}>
            {!open ? (
              <EffiHRLogo
                alt="company logo"
                style={{
                  ...DrawerStyles.DrawerHeader.logo.main,
                  paddingLeft: 4,
                  paddingRight: 6,
                  maxWidth: "100%",
                  maxHeight: "100%",
                }}
              />
            ) : (
              <>
                {OrgLogo && (
                  <Suspense>
                    <img
                      src={OrgLogo}
                      alt="company logo"
                      style={{
                        ...DrawerStyles.DrawerHeader.logo.logoText,
                        paddingLeft: 6,
                        paddingRight: 12,
                        maxWidth: "100%",
                        maxHeight: "100%",
                      }}
                    />
                  </Suspense>
                )}
              </>
            )}
          </Box>
        </DrawerHeader>
        <SidebarMenus open={open} />
        <FootNote open={open} component={Paper} zIndex={100}>
          <StyledDivider />
          <Typography variant="caption" fontSize={10}>
            {languageConfig.version}
          </Typography>
          <Typography variant="body2" fontSize={12}>
            {languageConfig.copyright}
          </Typography>
        </FootNote>
      </Drawer>
    </Box>
  );
};

export default MenuSideBar;
