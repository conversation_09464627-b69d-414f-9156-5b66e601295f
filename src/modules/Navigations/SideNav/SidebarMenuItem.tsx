import { Box, ListItemIcon, ListItemText, Tooltip } from "@mui/material";
import React, { useCallback, useMemo } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import { SideBarMenu } from "src/configs/app.config";
import { useAppDispatch } from "src/customHooks/useAppDispatch";
import { useAppSelector } from "src/customHooks/useAppSelector";
import { PATH_CONFIG } from "src/modules/Routing/config";
import { setBreadcrumbs } from "src/store/slices/breadcrumbs.slice";
import { StyledDivider } from "../TopNav/appbarMenus/styles";
import { CustomListItem, CustomListItemButton, DrawerStyles } from "./MenuSideBar.styles";

interface SidebarMenuProps {
  authorisedScreen: SideBarMenu;
  index: number;
  open: boolean;
}

const SidebarMenuItem: React.FC<SidebarMenuProps> = ({ authorisedScreen, index, open }) => {
  const location = useLocation();
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  const { tenantDetails, selectedRole, userDetails } = useAppSelector((state) => state.userManagement);

  const renderDivider: boolean = index !== 0 && index % 8 === 0;
  const isScreenSelected = useMemo(() => {
    return location.pathname.includes(authorisedScreen?.pathname);
  }, [location.pathname]);

  const areScreensDisabled = useCallback(
    (currentScreen: SideBarMenu) => {
      const isCurrentSelectedRoleEnabledForOnboarding = ["HR Admin", "Super Admin"].some(
        (role) => role === selectedRole,
      );
      const isInHROnboardingMode = userDetails?.organisations?.[0]?.status === "HR Onboarding";
      const isEmployeeScreen = currentScreen.key === PATH_CONFIG.EMPLOYEES.key;

      return isCurrentSelectedRoleEnabledForOnboarding && isInHROnboardingMode && !isEmployeeScreen;
    },
    [tenantDetails, selectedRole],
  );

  const handleRoute = (authorisedScreen: SideBarMenu) => {
    if (authorisedScreen?.redirectURL) {
      window.open(authorisedScreen?.redirectURL, "_blank");
      return;
    }
    dispatch(
      setBreadcrumbs([
        {
          isActive: true,
          isDisabled: false,
          label: authorisedScreen?.title,
          path: authorisedScreen?.pathname,
        },
      ]),
    );
    navigate(authorisedScreen?.pathname || PATH_CONFIG.HOME.path);
  };

  return (
    <Box key={authorisedScreen["title"]}>
      {renderDivider ? <StyledDivider /> : null}
      <CustomListItem disablePadding>
        <CustomListItemButton
          disabled={areScreensDisabled(authorisedScreen)}
          selected={isScreenSelected}
          onClick={() => {
            handleRoute(authorisedScreen);
          }}
        >
          <Tooltip title={authorisedScreen["title"]} placement="bottom-end" disableHoverListener={open}>
            <ListItemIcon>
              <authorisedScreen.icon
                style={{
                  ...DrawerStyles.List.ListIcon,
                  ...(isScreenSelected ? { fill: "black" } : { fill: "#505B59" }),
                }}
              />
            </ListItemIcon>
          </Tooltip>
          <ListItemText sx={{ opacity: open ? 1 : 0 }}>{authorisedScreen["title"]}</ListItemText>
        </CustomListItemButton>
      </CustomListItem>
    </Box>
  );
};

export default SidebarMenuItem;
