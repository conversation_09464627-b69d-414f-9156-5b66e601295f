import {
  Box,
  ListItem,
  ListItemButton,
  AppBarProps as MuiAppBarProps,
  Drawer as MuiDraw<PERSON>,
  Paper,
  PaperProps,
} from "@mui/material";
import { CSSObject, Theme, styled } from "@mui/material/styles";

import { DrawerWidth } from "../navigation.config";

const drawerWidth = DrawerWidth;

const openedMixin = (theme: Theme): CSSObject => ({
  width: drawerWidth,
  transition: theme.transitions.create("width", {
    easing: theme.transitions.easing.sharp,
    duration: theme.transitions.duration.enteringScreen,
  }),
  overflowX: "hidden",
});

const closedMixin = (theme: Theme): CSSObject => ({
  transition: theme.transitions.create("width", {
    easing: theme.transitions.easing.sharp,
    duration: theme.transitions.duration.leavingScreen,
  }),
  overflowX: "hidden",
  width: "78px",
});

const DrawerStyles = {
  root: {
    position: "relative",
    display: "flex",
    flexDirection: "column",
  },
  DrawerHeader: {
    logo: {
      root: {
        display: "flex",
        justifyContent: "center",
        alignItems: "center",
        cursor: "pointer",
        maxHeight: "64px",
        width: "100%",
        height: "100%",
        padding: "4px",
      },
      main: {
        width: "100%",
        height: "100%",
        objectFit: "contain" as const,
        objectPosition: "center" as const,
      },
      logoText: {
        width: "100%",
        height: "100%",
        objectFit: "contain" as const,
        objectPosition: "center" as const,
      },
    },
  },
  List: {
    ListIcon: {
      marginLeft: "12px",
      width: 25,
      height: 25,
    },
  },
};

const Drawer = styled(MuiDrawer, { shouldForwardProp: (prop) => prop !== "open" })(({ theme, open }) => ({
  zIndex: theme.zIndex.appBar + 1,
  width: drawerWidth,
  flexShrink: 0,
  whiteSpace: "nowrap",
  boxSizing: "border-box",
  ...(open && {
    ...openedMixin(theme),
    "& .MuiDrawer-paper": openedMixin(theme),
  }),
  ...(!open && {
    ...closedMixin(theme),
    "& .MuiDrawer-paper": closedMixin(theme),
  }),
}));

interface DrawerHeaderProps extends MuiAppBarProps {
  open?: boolean;
}

const DrawerHeader = styled("div", {
  shouldForwardProp: (prop) => prop !== "open",
})<DrawerHeaderProps>(({ theme, open }) => ({
  display: "flex",
  alignItems: "center",
  justifyContent: "flex-start",
  padding: "8px 8px",
  ...theme.mixins.toolbar,
  ...(open && {
    justifyContent: "flex-start",
  }),
}));

interface CustomPaperProps extends PaperProps {
  open?: boolean;
}

const DrawerOpenContainer = styled(Paper, {
  shouldForwardProp: (prop) => prop !== "open",
})<CustomPaperProps>(({ theme, open }) => ({
  position: "fixed",
  top: "1.5rem",
  padding: 0,
  zIndex: theme.zIndex.drawer + 1,
  marginLeft: "4rem",
  backgroundColor: "#B8F2EB",
  borderRadius: "50px",
  transition: theme.transitions.create(["width", "margin"], {
    easing: theme.transitions.easing.sharp,
    duration: theme.transitions.duration.leavingScreen,
  }),
  ...(open && {
    marginLeft: `${drawerWidth - 16}px`,
    transition: theme.transitions.create(["width", "margin"], {
      easing: theme.transitions.easing.sharp,
      duration: theme.transitions.duration.enteringScreen,
    }),
  }),
}));

const CustomListItem = styled(ListItem)(() => ({
  display: "block",
}));

const CustomListItemButton = styled(ListItemButton)(() => ({
  minHeight: 48,
  justifyContent: "flex-start",
  padding: "8px 12px",
  borderRadius: "0 50px 50px 0",
  marginRight: "8px",
  fontSize: "16px",
  "&.Mui-selected": {
    backgroundColor: "#E6F2F1",
    color: "black",
  },
  "&.Mui-selected:hover": {
    backgroundColor: "#E6F2F1",
  },
}));

interface FootNoteProps {
  open: boolean;
}

const FootNote = styled(Box, {
  shouldForwardProp: () => true,
})<FootNoteProps>(({ open }) => ({
  flexGrow: 1,
  display: open ? "flex" : "none",
  flexDirection: "column",
  justifyContent: "flex-end",
  alignItems: "flex-start",
  padding: "16px",
}));

export { FootNote, CustomListItemButton, CustomListItem, DrawerOpenContainer, DrawerHeader, Drawer, DrawerStyles };
