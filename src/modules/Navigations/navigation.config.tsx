import { Notifications } from "@mui/icons-material";
import React from "react";
import { BaseObject } from "src/app/global";
import { AddMenuIcon, InfoMenuIcon } from "src/assets/icons.svg";

const DrawerWidth = 300;

const menuButtonConfig: {
  id: string;
  title: string;
  icon: (props: BaseObject) => React.ReactNode;
  isMobileView: boolean;
}[] = [
  {
    id: "add",
    title: "Tasks",
    icon: (props: BaseObject) => <AddMenuIcon {...props} />,
    isMobileView: false,
  },
  {
    id: "info",
    title: "Info",
    icon: (props: BaseObject) => <InfoMenuIcon {...props} />,
    isMobileView: false,
  },
  {
    id: "notification",
    title: "Notifications",
    icon: (props: BaseObject) => <Notifications {...props} />,
    isMobileView: false,
  },
];

export { DrawerWidth, menuButtonConfig };
