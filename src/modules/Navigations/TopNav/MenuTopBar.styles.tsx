import { AppBarProps, Box, Grid, <PERSON><PERSON>Bar as <PERSON><PERSON><PERSON>ppB<PERSON>, IconButton as MUIIconButton, styled } from "@mui/material";

import { DrawerWidth } from "../navigation.config";

const drawerWidth = DrawerWidth;

interface CustomAppBarProps extends AppBarProps {
  open?: boolean;
}

const AppBar = styled(MUIAppBar, {
  shouldForwardProp: (prop) => prop !== "open",
})<CustomAppBarProps>(({ theme, open }) => ({
  zIndex: theme.zIndex.appBar,
  backgroundColor: "#F9F9F9",
  color: "black",
  padding: "0.5rem",
  transition: theme.transitions.create(["width", "margin"], {
    easing: theme.transitions.easing.sharp,
    duration: theme.transitions.duration.leavingScreen,
  }),
  ...(open && {
    marginLeft: drawerWidth,
    width: `calc(100% - ${drawerWidth}px)`,
    transition: theme.transitions.create(["width", "margin"], {
      easing: theme.transitions.easing.sharp,
      duration: theme.transitions.duration.enteringScreen,
    }),
  }),
}));

const ToolbarStyles = {
  root: {
    display: "flex",
    justifyContent: "flex-end",
  },
  actions: {
    display: "flex",
    flexDirection: "row",
  },
};

interface GridProps {
  isMobileView?: boolean;
}

const CustomGrid = styled(Grid, {
  shouldForwardProp: () => true,
})<GridProps>(({ theme, isMobileView }) => ({
  display: isMobileView ? "none" : "flex",
  [theme.breakpoints.down("sm")]: {
    display: "flex",
  },
}));

const IconContainer = styled(Box)(() => ({
  display: "flex",
  flexDirection: "column",
  justifyContent: "center",
  position: "relative",
}));

const IconButton = styled(MUIIconButton)(() => ({
  fontSize: "1.5rem",
  backgroundColor: "#EFF1F3",
}));

const IconStyles = {
  root: {
    height: "36px",
    width: "36px",
  },
  primaryIcon: {
    height: "40px",
    width: "40px",
    transform: "scale(1.9)",
  },
  icon: {
    height: "20px",
    width: "20px",
  },
  badge: {
    position: "absolute",
    backgroundColor: "#007F6F",
    color: "white",
    borderRadius: "50%",
    top: "-2px",
    right: "0",
    fontSize: "0.75rem",
    height: "20px",
    width: "20px",
    display: "flex",
    justifyContent: "center",
    alignItems: "center",
  },
};

const MenuStyles = {
  root: {
    "& .MuiMenu-paper": {
      backgroundColor: "#FFFFFF",
      borderRadius: "5px",
      boxShadow: "0px 4px 15px rgba(0, 0, 0, 0.10)",
      mt: 2,
    },
    "& .MuiMenu-list": {
      padding: 0,
      minWidth: 256,
    },
  },
};

export { MenuStyles, IconStyles, IconButton, IconContainer, CustomGrid, ToolbarStyles, AppBar };
