import {
  Box,
  MenuItem as MUIMenuItem,
  Typography as MuiTypography,
  Paper,
  Tab,
  Tabs,
  TypographyProps,
} from "@mui/material";
import { styled } from "@mui/material/styles";
import React from "react";
import StyledDivider from "src/modules/Common/Divider";

const Typography: React.FC<TypographyProps> = styled(MuiTypography)(() => ({
  color: "black",
}));

interface StyledTabsProps {
  children?: React.ReactNode;
  value: number;
  onChange: (event: React.SyntheticEvent, newValue: number) => void;
}

const StyledTabs = styled((props: StyledTabsProps) => (
  <Tabs {...props} TabIndicatorProps={{ children: <span className="MuiTabs-indicatorSpan" /> }} />
))({
  color: "black",
  minHeight: 0,
  padding: "0 6px",
  "& .MuiTabs-flexContainer": {
    gap: "12px",
  },
  "& .MuiTabs-indicator": {
    display: "flex",
    justifyContent: "center",
    backgroundColor: "transparent",
  },
  "& .MuiTabs-indicatorSpan": {
    maxWidth: 40,
    width: "100%",
    height: "100%",
    // backgroundColor: '#635ee7',
  },
});

interface OverlayContainerProps {
  isVisible: boolean;
}

const OverlayContainer = styled(Paper, {
  shouldForwardProp: (prop) => prop !== "isVisible",
})<OverlayContainerProps>(({ isVisible }) => ({
  display: isVisible ? "block" : "none",
  minWidth: "100%",
  minHeight: "100%",
  position: "absolute",
  zIndex: 100,
}));

interface StyledTabProps {
  label: string;
}

const StyledTab = styled((props: StyledTabProps) => <Tab disableRipple {...props} />)(({ theme }) => ({
  textTransform: "none",
  fontWeight: 500,
  color: "black",
  fontSize: "12px",
  padding: "0 10px",
  borderRadius: "50px",
  minHeight: "20px",
  minWidth: 0,
  "&.Mui-selected": {
    color: "white",
    backgroundColor: theme.palette.primary.main,
  },
  "&.Mui-focusVisible": {
    // backgroundColor: theme.palette.primary.main,
  },
}));

const MenuItem = styled(MUIMenuItem)(() => ({
  padding: "0.75rem",
  borderRadius: "5px",
  color: "black",
}));

const MenuStyles = {
  container: {
    padding: "12px 12px 8px",
    maxHeight: "50vh",
    display: "flex",
    flexDirection: "column",
    gap: 2,
  },
  root: {
    display: "flex",
    flexDirection: "row",
    justifyContent: "center",
    alignItems: "center",
  },
  overlay: {
    header: {
      container: {
        display: "flex",
        flexDirection: "row",
        alignItems: "center",
        gap: "8px",
      },
    },
  },
  Divider: {
    margin: 0,
  },
  text: {
    caption: {
      color: "#AAAAAA",
      lineHeight: 1,
    },
  },
  iconContainer: {
    mr: "0.75rem",
    display: "flex",
    justifyContent: "flex-end",
  },
  adornment: {
    position: "absolute",
    right: "2px",
    display: "flex",
    justifyContent: "center",
  },
};

export { MenuItem, MenuStyles, Box, Typography, StyledDivider, StyledTabs, StyledTab, OverlayContainer };
