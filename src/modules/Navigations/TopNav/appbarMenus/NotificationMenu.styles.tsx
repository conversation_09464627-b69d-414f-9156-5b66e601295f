import { MenuStyles } from "./styles";

const NotificationMenuStyles = {
  Divider: {
    ...MenuStyles.Divider,
  },
  Overlay: {
    header: {
      root: {
        ...MenuStyles.container,
        paddingBottom: "8px",
      },
      ...MenuStyles.overlay.header,
      body: {
        root: {
          ...MenuStyles.container,
          fontSize: "12px",
          padding: "8px 20px 12px",
          background: "#f9f9f9",
        },
        list: {
          root: {
            ...MenuStyles.container,
            padding: "4px 4px 12px",
            gap: "8px",
          },
          item: {
            main: {
              padding: 0,
              borderRadius: "50px",
              display: "flex",
              justifyContent: "flex-start",
            },
            checkbox: {
              color: "black",
            },
            content: {
              color: "black",
              fontSize: "12px",
              fontWeight: 500,
            },
          },
        },
      },
      iconButton: {
        padding: 0,
        height: "21px",
        width: "21px",
      },
    },
  },
  NotificationHeader: {
    root: {
      ...MenuStyles.container,
      padding: "12px 12px 8px",
    },
    content: {
      display: "flex",
      flexDirection: "row",
      justifyContent: "space-between",
    },
    title: {
      fontWeight: 500,
      fontSize: "14px",
      color: "black",
      alignSelf: "center",
    },
    settings: {
      fontSize: "12px",
      color: "#007F6F",
      alignSelf: "center",
      cursor: "pointer",
    },
  },
  Tabs: {
    root: {
      ...MenuStyles.container,
      padding: "8px 6px 12px",
      background: "#f9f9f9",
    },
  },
  NotificationMessages: {
    root: {
      ...MenuStyles.container,
      padding: "12px 4px",
      overflow: "auto",
      gap: "8px",
    },
    list: {
      maxWidth: 340,
      maxHeight: 316,
      display: "flex",
      flexDirection: "column",
      gap: "0.5rem",
      padding: 0,
    },
    message: {
      body: {
        main: {
          padding: 0,
          display: "flex",
          alignItems: "center",
        },
        // avatar: {
        //   height: "30px",
        //   width: "30px",
        //   marginTop: "4px",
        // },
        text: {
          root: {
            "& .MuiListItemText-primary": {
              fontSize: "10px",
              color: "#696969",
            },
          },
          main: {
            display: "flex",
            flexDirection: "row",
          },
          title: {
            color: "black",
            fontWeight: 500,
            display: "flex",
          },
          timestamp: {
            color: "#919897",
            marginLeft: "auto",
            // flex: 1,
            textWrap: "nowrap",
          },
          content: {
            width: "100%",
            marginTop: "4px",
          },
        },
      },
      notifyRoot: {
        display: "flex",
        height: "fit-content",
        marginRight: "4px",
        marginTop: "4px",
      },
      unread: {
        height: "6px",
        width: "6px",
        backgroundColor: "#007F6F",
        borderRadius: "50px",
      },
      read: {
        height: "6px",
        width: "6px",
        backgroundColor: "transparent",
        borderRadius: "50px",
      },
    },
  },
};

export { NotificationMenuStyles };
