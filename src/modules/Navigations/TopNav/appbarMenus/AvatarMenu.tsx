import { AccountCircleOutlined, Business, ChevronLeft, ChevronRight, LogoutOutlined } from "@mui/icons-material";
import { Avatar, IconButton } from "@mui/material";
import React from "react";
import { useAppDispatch } from "src/customHooks/useAppDispatch";
import { useAppSelector } from "src/customHooks/useAppSelector";
import { getJwtRoles, getSessionToken, useAuth } from "src/modules/Login/LoginHook";

import { PATH_CONFIG } from "src/modules/Routing/config";
import { setBreadcrumbs } from "src/store/slices/breadcrumbs.slice";
import { setSelectedOrganisation, setSelectedRole } from "src/store/slices/userManagement.slice";
import { deleteCookie } from "src/utils/cookieUtils";
import { AvatarMenuStyles } from "./AvatarMenu.styles";
import SelectableList from "./SelectableList";
import { Box, MenuItem, OverlayContainer, StyledDivider, Typography } from "./styles";
export enum MenuItemType {
  ROLE = "Role",
  ORGANISATION = "Organisation",
}

interface AvatarMenuProps {
  handleMenuClose: () => void;
  avatarSrc: string;
}

const AvatarMenu: React.FC<AvatarMenuProps> = ({ handleMenuClose, avatarSrc }) => {
  const { selectedRole, selectedOrganisation, tenantDetails } = useAppSelector((state) => state.userManagement);
  const { userDetails } = useAppSelector((state) => state.userManagement);
  const dispatch = useAppDispatch();
  const [selectedOptionId, setSelectedOptionId] = React.useState<MenuItemType | null>(null);
  const { logout } = useAuth();

  const handleOverlayOpen = (event: React.MouseEvent<HTMLElement>) => {
    setSelectedOptionId(event.currentTarget.id as MenuItemType);
  };

  const handleOverlayClose = () => {
    setSelectedOptionId(null);
  };

  const handleLogOut = async () => {
    try {
      await logout();
      deleteCookie("role");
      deleteCookie("org");
      window.location.href = PATH_CONFIG.LOGIN.path;
    } catch (error) {
      console.error("Error logging out", error);
    }
  };

  const getSelectedListItems = React.useMemo(() => {
    if (!selectedOptionId || !getSessionToken()) {
      return [];
    }

    switch (selectedOptionId) {
      case MenuItemType.ROLE:
        return getJwtRoles(getSessionToken(), tenantDetails?.tenant_id) || [];
      case MenuItemType.ORGANISATION:
        return userDetails?.organisations?.map((ele) => ele.name) || [];
      default:
        return [];
    }
  }, [selectedOptionId, getSessionToken, tenantDetails, userDetails]);

  const onSelectedItemClick = (item: string, type: MenuItemType) => {
    if (type === MenuItemType.ROLE && selectedRole !== item) {
      dispatch(setBreadcrumbs([]));
      dispatch(setSelectedRole(item));
      const primaryOrg = userDetails?.organisations?.find((org) => org.primary);
      // this check is in place cause the business requirement is to keep the primary org selected in role change
      if (primaryOrg) {
        dispatch(setSelectedOrganisation(primaryOrg.name));
      }
    }

    if (type === MenuItemType.ORGANISATION) {
      dispatch(setBreadcrumbs([]));
      dispatch(setSelectedOrganisation(item));
    }

    setSelectedOptionId(null);
    handleMenuClose();
  };

  if (!userDetails || !tenantDetails) {
    return null;
  }

  return (
    <>
      <OverlayContainer isVisible={!!selectedOptionId}>
        <Box sx={AvatarMenuStyles.Overlay.container}>
          <Box sx={AvatarMenuStyles.Overlay.header}>
            <IconButton sx={{ padding: 0 }} onClick={handleOverlayClose}>
              <ChevronLeft />
            </IconButton>
            <Typography component="div" variant="body1">
              {selectedOptionId}
            </Typography>
          </Box>
          <SelectableList
            items={getSelectedListItems}
            selectedItem={selectedOptionId === MenuItemType.ROLE ? selectedRole : selectedOrganisation}
            setSelectedItem={(item) => onSelectedItemClick(item, selectedOptionId as MenuItemType)}
          />
        </Box>
      </OverlayContainer>
      <Box sx={AvatarMenuStyles.Header.root}>
        <Box sx={AvatarMenuStyles.Header.container}>
          <Avatar alt={userDetails?.display_name} sx={AvatarMenuStyles.Header.avatar} src={avatarSrc} />
          <Box sx={{ display: "flex", justifyContent: "space-between", flexDirection: "column" }}>
            <Typography component="div" sx={AvatarMenuStyles.Header.name}>
              {userDetails?.display_name}
            </Typography>
            <Typography component="div" variant="caption" sx={AvatarMenuStyles.Header.role}>
              {selectedRole}
            </Typography>
            <Typography component="div" variant="caption" sx={AvatarMenuStyles.Header.code}>
              {userDetails?.employee_code}
            </Typography>
          </Box>
        </Box>
      </Box>
      <StyledDivider style={AvatarMenuStyles.Divider} />
      <Box sx={AvatarMenuStyles.Main.root}>
        <MenuItem id={MenuItemType.ROLE} onClick={handleOverlayOpen}>
          <Typography component="div" sx={AvatarMenuStyles.Main.menuItem}>
            <Box sx={AvatarMenuStyles.Main.iconContainer}>
              <AccountCircleOutlined />
            </Box>
            <Box>
              <Typography component="div" variant="caption" sx={AvatarMenuStyles.Main.text.primary}>
                Current Role
              </Typography>
              <Typography component="div">{selectedRole}</Typography>
            </Box>
            <Box sx={AvatarMenuStyles.Main.adornment}>
              <ChevronRight />
            </Box>
          </Typography>
        </MenuItem>
        <MenuItem id={MenuItemType.ORGANISATION} onClick={handleOverlayOpen}>
          <Typography component="div" sx={AvatarMenuStyles.Main.menuItem}>
            <Box sx={AvatarMenuStyles.Main.iconContainer}>
              <Business />
            </Box>
            <Box>
              <Typography component="div" variant="caption" sx={AvatarMenuStyles.Main.text.primary}>
                Current Organisation
              </Typography>
              <Typography component="div">{selectedOrganisation}</Typography>
            </Box>
            <Box sx={AvatarMenuStyles.Main.adornment}>
              <ChevronRight />
            </Box>
          </Typography>
        </MenuItem>
        <MenuItem onClick={handleLogOut}>
          <Typography component="div" sx={AvatarMenuStyles.Main.menuItem}>
            <Box sx={AvatarMenuStyles.Main.iconContainer}>
              <LogoutOutlined />
            </Box>
            Logout
          </Typography>
        </MenuItem>
      </Box>
    </>
  );
};

export default AvatarMenu;
