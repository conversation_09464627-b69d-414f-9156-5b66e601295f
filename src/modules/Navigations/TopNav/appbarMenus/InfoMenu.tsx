import { ContactPhone } from "@mui/icons-material";
// import { AutoFixHigh, ContactSupport, HeadsetMic, TipsAndUpdates } from "@mui/icons-material";
import React from "react";
import { Box, MenuItem, MenuStyles, Typography } from "./styles";

const InfoMenuListElements: { title: string; icon: React.ReactNode; link: string }[] = [
  // {
  //   title: "Recommended for you",
  //   icon: <AutoFixHigh />,
  //   link: "",
  // },
  // {
  //   title: "Help and Support",
  //   icon: <ContactSupport />,
  //   link: "",
  // },
  // {
  //   title: "Chat with us",
  //   icon: <HeadsetMic />,
  //   link: "",
  // },
  // {
  //   title: "Product and updates",
  //   icon: <TipsAndUpdates />,
  //   link: "",
  // },
  {
    title: "Contact us",
    icon: <ContactPhone />,
    link: "",
  },
];

const InfoMenu = () => {
  const handleMenuClick = (item: { title: string; icon: React.ReactNode; link: string }) => {
    switch (item.title) {
      case "Contact us":
        window.location.href = "mailto:<EMAIL>";
        break;
      default:
        break;
    }
  };
  return (
    <>
      <Box sx={MenuStyles.container}>
        {InfoMenuListElements.map((item, index) => {
          return (
            <MenuItem key={index} onClick={() => handleMenuClick(item)}>
              <Typography component="div" sx={MenuStyles.root}>
                <Box sx={MenuStyles.iconContainer}>{item.icon}</Box>
                {item.title}
              </Typography>
            </MenuItem>
          );
        })}
      </Box>
    </>
  );
};

export default InfoMenu;
