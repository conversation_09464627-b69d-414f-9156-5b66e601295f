import { Campaign, ChevronLeft, TaskSharp } from "@mui/icons-material";
import {
  Checkbox,
  IconButton,
  List,
  ListItem,
  ListItemAvatar,
  ListItemButton,
  ListItemText,
  Tooltip,
} from "@mui/material";
import { useMutation, useQueries } from "@tanstack/react-query";
import { differenceInDays, format, formatDistanceToNowStrict, isToday, isYesterday, parseISO } from "date-fns";
import React, { useEffect } from "react";
import { useNavigate } from "react-router-dom";

import Span from "src/modules/Common/Span/Span";
import { NotificationResponse, NotificationSettings } from "src/services/api_definitions/notification.service";
import notificationService from "src/services/notification.service";
import { showToast } from "src/utils/toast";

import { NotificationMenuStyles } from "./NotificationMenu.styles";
import { Box, StyledDivider, StyledTab, StyledTabs, Typography } from "./styles";

interface NotificationMenuProps {
  onNotificationRead?: () => void;
}

const LinkStyle = {
  color: "#007F6F",
  fontSize: "12px",
  borderBottom: "1px solid #007F6F",
  fontWeight: 600,
  cursor: "pointer",
};

type NotificationItemProps = NotificationResponse & {
  onClick: (id: string, action_link: string | null) => void;
};

const categoriesType = {
  ALL: "All",
  UNREAD: "Unread",
};

const categoriesList = Object.values(categoriesType);

const formatRelativeTime = (dateString: string) => {
  const date = parseISO(dateString);
  const now = new Date();

  if (isToday(date)) {
    const distance = formatDistanceToNowStrict(date, { addSuffix: false });
    const [value, unit] = distance.split(" ");

    if (unit === "seconds") return "just now";
    if (unit === "minutes") return `${value}m ago`;
    if (unit === "hours") return `${value}h ago`;

    return distance + " ago";
  }

  if (isYesterday(date)) {
    return "1 day ago";
  }

  const distanceInDays = differenceInDays(now, date);

  if (distanceInDays < 7) {
    return `${distanceInDays}d ago`;
  }

  // For dates older than a week, return the date in a readable format
  return date.toLocaleDateString("en-US", { month: "short", day: "numeric", year: "numeric" });
};

const getIconsWrtCategory = (category: string) => {
  if (category === "Announcement") {
    return (
      <Tooltip title="Announcement">
        <Campaign fontSize="medium" />
      </Tooltip>
    );
  }

  if (category === "Task") {
    return (
      <Tooltip title="Task">
        <TaskSharp fontSize="medium" />
      </Tooltip>
    );
  }
};

const NotificationItem = ({
  id,
  title,
  body,
  is_read,
  created_at,
  action_link,
  onClick,
  category,
}: NotificationItemProps) => {
  return (
    <Box
      key={id}
      sx={{
        background: !is_read ? "#E9FCE9" : "white",
        padding: "4px 8px",
        borderRadius: "8px",
        cursor: "pointer",
      }}
      onClick={() => onClick(id, action_link)}
    >
      <ListItem sx={NotificationMenuStyles.NotificationMessages.message.body.main}>
        <ListItemAvatar sx={{ minWidth: "40px" }}>{getIconsWrtCategory(category)}</ListItemAvatar>
        <ListItemText
          sx={NotificationMenuStyles.NotificationMessages.message.body.text.root}
          primary={
            <React.Fragment>
              <Box sx={NotificationMenuStyles.NotificationMessages.message.body.text.main}>
                <Box sx={NotificationMenuStyles.NotificationMessages.message.body.text.title}>
                  {!is_read && (
                    <Box sx={NotificationMenuStyles.NotificationMessages.message.notifyRoot}>
                      <Box sx={NotificationMenuStyles.NotificationMessages.message.unread}></Box>
                    </Box>
                  )}{" "}
                  {title}
                </Box>
                <Box sx={NotificationMenuStyles.NotificationMessages.message.body.text.timestamp}>
                  {formatRelativeTime(created_at)}
                </Box>
              </Box>
              <Box sx={NotificationMenuStyles.NotificationMessages.message.body.text.content}>{body}</Box>
            </React.Fragment>
          }
        />
      </ListItem>
    </Box>
  );
};

const NotificationMenu: React.FC<NotificationMenuProps> = ({ onNotificationRead }) => {
  const [isSettingsOpen, setIsSettingsOpen] = React.useState(false);
  const [selectedCategoriesIndex, setSelectedCategoriesIndex] = React.useState(1);
  const [filteredNotifications, setFilterdNotifications] = React.useState<NotificationResponse[]>([]);

  const navigate = useNavigate();

  const result = useQueries({
    queries: [
      {
        queryKey: ["get-all-notifications"],
        queryFn: async (): Promise<NotificationResponse[]> => {
          return notificationService.getAllNotifications("7");
        },
        retryOnMount: false,
        refetchOnWindowFocus: false,
      },
      {
        queryKey: ["get-notifications-settings"],
        queryFn: async (): Promise<NotificationSettings[] | null> => notificationService.getNotificationSettings(),
        retryOnMount: false,
        refetchOnWindowFocus: false,
      },
    ],
  });
  const [
    { data: notificationList, refetch: refetchNotifications, isLoading: isNotificationsLoading },
    { data: notificationsSettings, refetch: refetchNotificationsSettings },
  ] = result;

  const markNotificationAsRead = useMutation<boolean, unknown, { id: string; action_link: string | null }>({
    mutationKey: ["mark-notification-as-read"],
    mutationFn: async ({ id }) => {
      return notificationService.markNotificationAsRead(id);
    },
    onSuccess: (_data, variables) => {
      refetchNotifications();
      onNotificationRead?.();

      if (variables.action_link) {
        navigate(variables.action_link);
      }
    },
  });

  const handleNotificationClick = async (id: string, action_link: string | null) => {
    markNotificationAsRead.mutate({ id, action_link });
  };

  const markAllNotificationAsRead = useMutation({
    mutationKey: ["mark-all-notification-as-read"],
    mutationFn: async (date: string) => notificationService.markAllNotificationAsRead(date),
    onSuccess: () => {
      refetchNotifications();
      onNotificationRead?.();
    },
  });

  const clearAllNotifications = useMutation({
    mutationKey: ["clear-all-notifications"],
    mutationFn: async () => notificationService.clearAllNotifications(),
    onSuccess: () => {
      refetchNotifications();
      onNotificationRead?.();
    },
  });

  useEffect(() => {
    if (!isNotificationsLoading && notificationList) {
      const selectedCategory = categoriesList[selectedCategoriesIndex];
      if (selectedCategory === categoriesType.ALL) {
        setFilterdNotifications(notificationList);
      } else if (selectedCategory === categoriesType.UNREAD) {
        setFilterdNotifications(notificationList.filter(({ is_read }) => !is_read));
      }
    }
  }, [selectedCategoriesIndex, notificationList, isNotificationsLoading]);

  const updateNotificationSettings = useMutation({
    mutationKey: ["update-notification-settings"],
    mutationFn: async ({ notificationType, checked }: { notificationType: string; checked: boolean }) =>
      notificationService.updateNotificationSettings(notificationType, checked),
    onSuccess: () => {
      refetchNotificationsSettings();
      showToast("Notification settings updated successfully", {
        type: "success",
      });
    },
  });

  const handleOverlayOpen = () => {
    setIsSettingsOpen(true);
  };

  const handleOverlayClose = () => {
    setIsSettingsOpen(false);
  };

  const handleCheckBoxToggle = (notificationType: string, checked: boolean) => {
    updateNotificationSettings.mutate({ notificationType, checked });
  };

  const handleCategoryChange = (_: React.SyntheticEvent<Element, Event>, newValue: number) => {
    setSelectedCategoriesIndex(newValue);
  };

  const onMarkAllNotificationAsRead = () => {
    const now = new Date();
    const formattedDate = format(now, "yyyy-MM-dd'T'HH:mm:ss.SSSSSS'Z'");
    markAllNotificationAsRead.mutate(formattedDate);
  };

  const onClearAllNotifications = () => {
    clearAllNotifications.mutate();
  };

  return (
    <Box minWidth="340px" minHeight="200px">
      {!isSettingsOpen ? (
        <Box>
          <Box sx={NotificationMenuStyles.NotificationHeader.root}>
            <Box sx={NotificationMenuStyles.NotificationHeader.content}>
              <Box sx={NotificationMenuStyles.NotificationHeader.title}>Notifications</Box>
              <Box sx={NotificationMenuStyles.NotificationHeader.settings} onClick={handleOverlayOpen}>
                Settings
              </Box>
            </Box>
          </Box>
          <StyledDivider style={NotificationMenuStyles.Divider} />
          <Box sx={NotificationMenuStyles.Tabs.root}>
            <StyledTabs
              value={selectedCategoriesIndex}
              onChange={handleCategoryChange}
              aria-label="styled tabs example"
            >
              {categoriesList.map((cat) => (
                <StyledTab key={cat} label={cat} />
              ))}
            </StyledTabs>
          </Box>
          <Box sx={NotificationMenuStyles.NotificationMessages.root}>
            {filteredNotifications.length > 0 && (
              <Box display="flex" gap="16px" justifyContent="flex-end" paddingRight="12px" marginBottom="4px">
                <Span sx={LinkStyle} onClick={onMarkAllNotificationAsRead}>
                  Mark all as read
                </Span>{" "}
                <Span sx={LinkStyle} onClick={onClearAllNotifications}>
                  Clear all
                </Span>
              </Box>
            )}
            <List sx={NotificationMenuStyles.NotificationMessages.list}>
              {filteredNotifications && filteredNotifications.length > 0 ? (
                filteredNotifications.map((props) => (
                  <NotificationItem
                    key={props.id}
                    {...props}
                    onClick={(id, action_link) => {
                      if (!props.is_read) {
                        handleNotificationClick(id, action_link);
                      } else if (action_link) {
                        navigate(action_link);
                      }
                    }}
                  />
                ))
              ) : (
                <Typography variant="body2" sx={{ textAlign: "center", marginTop: "24px" }}>
                  No new notifications
                </Typography>
              )}
            </List>
          </Box>
        </Box>
      ) : (
        <Box>
          <Box sx={NotificationMenuStyles.Overlay.header.root}>
            <Box sx={NotificationMenuStyles.Overlay.header.container}>
              <IconButton sx={NotificationMenuStyles.Overlay.header.iconButton} onClick={handleOverlayClose}>
                <ChevronLeft />
              </IconButton>
              <Typography component="div" variant="body2">
                Settings
              </Typography>
            </Box>
          </Box>
          <StyledDivider style={NotificationMenuStyles.Divider} />
          <Box sx={NotificationMenuStyles.Overlay.header.body.root}>Notify me when :</Box>
          <List sx={NotificationMenuStyles.Overlay.header.body.list.root}>
            {notificationsSettings &&
              notificationsSettings.map(({ notification_type: value, enabled_channels }) => {
                const labelId = `checkbox-list-secondary-label-${value}`;
                return (
                  <ListItem
                    key={value}
                    sx={NotificationMenuStyles.Overlay.header.body.list.item.main}
                    secondaryAction={
                      <Checkbox
                        edge="end"
                        onChange={(_, checked) => handleCheckBoxToggle(value, checked)}
                        inputProps={{ "aria-labelledby": labelId }}
                        checked={enabled_channels.includes("Push Notification")}
                        sx={NotificationMenuStyles.Overlay.header.body.list.item.checkbox}
                      />
                    }
                    disablePadding
                  >
                    <ListItemButton>
                      <Box id={labelId} sx={NotificationMenuStyles.Overlay.header.body.list.item.content}>
                        {value}
                      </Box>
                    </ListItemButton>
                  </ListItem>
                );
              })}
          </List>
        </Box>
      )}
    </Box>
  );
};

export default NotificationMenu;
