import { LogoutRounded, PersonAddAlt } from "@mui/icons-material";
import React from "react";
import { useNavigate } from "react-router-dom";
import { useAppSelector } from "src/customHooks/useAppSelector";
import { Box, MenuItem, MenuStyles, Typography } from "./styles";

const AddMenuList: { title: string; icon: React.ReactNode; link: string; key: string }[] = [
  {
    title: "Add Employee",
    icon: <PersonAddAlt />,
    link: "/employees?action=addEmployee",
    key: "employees",
  },
  // {
  //   title: "Check In",
  //   icon: <ListAlt />,
  //   link: "/dashboard",
  //   key: "dashboard",
  // },
  {
    title: "Apply Leave",
    icon: <LogoutRounded />,
    link: "/leaves?action=apply",
    key: "leaves",
  },
];

const AddMenu = ({ handleMenuClose }: { handleMenuClose: () => void }) => {
  const navigate = useNavigate();
  const { authorisedScreens } = useAppSelector((state) => state.userManagement);
  const authorisedKeys = authorisedScreens.map((item) => item.key);
  const authorisedMenuList = AddMenuList.filter((item) => authorisedKeys.includes(item.key));
  const handleClick = (link: string) => {
    navigate(link);
    handleMenuClose();
  };
  return (
    <>
      <Box sx={MenuStyles.container}>
        {authorisedMenuList.map((item) => {
          return (
            <MenuItem key={item.title} onClick={() => handleClick(item.link)}>
              <Typography component="div" sx={MenuStyles.root}>
                <Box sx={MenuStyles.iconContainer}>{item.icon}</Box>
                {item.title}
              </Typography>
            </MenuItem>
          );
        })}
      </Box>
    </>
  );
};

export default AddMenu;
