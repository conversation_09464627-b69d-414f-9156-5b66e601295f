import { Box, MenuItem } from "@mui/material";
import React from "react";

interface SelectableListProps {
  items: string[] | [];
  setSelectedItem: (selectedItem: string) => void;
  selectedItem: string | null;
}

const SelectableList: React.FC<SelectableListProps> = ({ items, setSelectedItem, selectedItem }) => {
  return (
    <Box>
      {items.map((eachItem) => (
        <MenuItem selected={eachItem === selectedItem} key={eachItem} onClick={() => setSelectedItem(eachItem)}>
          {eachItem}
        </MenuItem>
      ))}
    </Box>
  );
};

export default SelectableList;
