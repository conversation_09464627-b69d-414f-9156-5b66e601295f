import { MenuStyles } from "./styles";

const AvatarMenuStyles = {
  Overlay: {
    container: {
      ...MenuStyles.container,
    },
    header: {
      ...MenuStyles.overlay.header.container,
    },
  },
  Header: {
    root: {
      ...MenuStyles.container,
    },
    container: {
      display: "flex",
      justifyContent: "flex-start",
      flexDirection: "row",
      padding: "8px 16px 0 8px",
    },
    avatar: {
      height: "50px",
      width: "50px",
      marginRight: "1rem",
      cursor: "pointer",
    },
    name: {
      color: "black",
      lineHeight: 1.2,
      cursor: "pointer",
    },
    role: {
      color: "#AAAAAA",
      lineHeight: 1.2,
    },
    code: {
      color: "#AAAAAA",
      lineHeight: 1.2,
    },
  },
  Divider: {
    ...MenuStyles.Divider,
  },
  Main: {
    root: {
      ...MenuStyles.container,
    },
    menuItem: {
      ...MenuStyles.root,
    },
    iconContainer: {
      ...MenuStyles.iconContainer,
    },
    text: {
      primary: {
        ...MenuStyles.text.caption,
      },
    },
    adornment: {
      ...MenuStyles.adornment,
    },
  },
};

export { AvatarMenuStyles };
