import { TextField, styled } from "@mui/material";

const SearchBarField = styled(TextField)(({ theme }) => ({
  [theme.breakpoints.up("sm")]: {
    minWidth: 200,
  },
  [theme.breakpoints.up("md")]: {
    minWidth: 400,
  },
  width: "100%",
  input: {
    color: "#282B2A",
    fontWeight: 400,
    padding: "0.625rem",
    wordWrap: "break-word",
    "&::placeholder": {
      color: "#282B2A",
      opacity: 1,
    },
  },
  "& .MuiInputAdornment-root": {
    ml: "0.625rem",
    color: "#282B2A",
  },
  "& .MuiOutlinedInput-root": {
    color: "#282B2A",
    backgroundColor: "transparent",
    borderRadius: theme.spacing(8),
    "& fieldset": {
      borderColor: "#E6E3E3",
    },
    "&:hover fieldset": {
      borderColor: "success.main",
    },
    "&.Mui-focused fieldset": {
      borderColor: "success.main",
    },
    "& .MuiInputBase-input.Mui-disabled": {
      backgroundColor: "#F5F5F5",
    },
  },
}));

export default SearchBarField;
