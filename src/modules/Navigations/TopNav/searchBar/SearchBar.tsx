import { Autocomplete, Box, InputAdornment, ListItem, Paper, Typography } from "@mui/material";
import { UseQueryResult, useQuery } from "@tanstack/react-query";
/* eslint-disable react/prop-types */
import React from "react";
import { useNavigate } from "react-router-dom";
import { SearchIcon } from "src/assets/icons.svg";
import useDebounce from "src/customHooks/useDebounce";
import SearchServiceAPI from "src/services/search.service";
import { SearchBarField, SearchBarStyles } from "./SearchBar.styles";

interface SearchBarPaperProps {
  children?: React.ReactNode;
}

const SearchBarPaper: React.FC<SearchBarPaperProps> = ({ children }) => {
  return (
    <Paper sx={SearchBarStyles.paper.root}>
      <Typography variant="body1" style={SearchBarStyles.paper.text.normal}>
        Searches for <span style={SearchBarStyles.paper.text.bold}>employees, tasks, documents</span>
      </Typography>
      {children}
    </Paper>
  );
};

interface SearchResults {
  value: string;
  href: string;
  email: string;
}

const SearchBar = () => {
  const [searchInputValue, setSearchInputValue] = React.useState("");

  const debouncedValue = useDebounce(searchInputValue, 200);

  const { data }: UseQueryResult<SearchResults[]> = useQuery({
    queryKey: ["search", debouncedValue],
    queryFn: () => {
      if (
        debouncedValue !== null &&
        debouncedValue !== undefined &&
        debouncedValue !== "" &&
        !debouncedValue.includes("(")
      ) {
        return SearchServiceAPI.searchValue(debouncedValue);
      }
      return null;
    },
  });

  const navigate = useNavigate();

  const handleSearchItemSelect = (selectedResult: SearchResults) => {
    const redirectLink = selectedResult.href;
    const employeeCode = redirectLink?.split("code=")[1];
    navigate(`employee/${employeeCode}` || "/error");
    setSearchInputValue("");
  };

  const handleSearchInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const newInputValue = event.target.value;
    setSearchInputValue(newInputValue);
  };
  return (
    <Box sx={SearchBarStyles.container}>
      <Autocomplete
        disableClearable
        freeSolo
        options={data !== null && data !== undefined ? data : []}
        getOptionLabel={(option) => (option as SearchResults)?.value || ""}
        sx={SearchBarStyles.root}
        PaperComponent={SearchBarPaper}
        inputValue={searchInputValue}
        renderInput={(params) => (
          <SearchBarField
            placeholder="Search"
            type="search"
            {...params}
            onChange={handleSearchInputChange}
            InputProps={{
              ...params.InputProps,
              startAdornment: (
                <InputAdornment position="end" sx={{ marginRight: "0.375rem" }}>
                  <SearchIcon />
                </InputAdornment>
              ),
            }}
          />
        )}
        renderOption={(props, option) => (
          <ListItem
            {...props}
            style={{
              ...props?.style,
              ...SearchBarStyles.listItems,
            }}
            onClick={() => handleSearchItemSelect(option)}
          >
            {option.value}
          </ListItem>
        )}
      />
    </Box>
  );
};

export default SearchBar;
