import SearchBarField from "./SearchBarField.styles";

const SearchBarStyles = {
  container: {
    maxWidth: "100%",
    display: { xs: "none", sm: "flex" },
    flexDirection: "row",
    justifyContent: "center",
    padding: "0 8px",
    gap: "8px",
    transition: "0.195s cubic-bezier(0.4, 0, 0.6, 1) all",
  },
  root: {
    display: {
      xs: "none",
      sm: "inherit",
    },
    "& .MuiAutocomplete-inputRoot": {
      padding: "2.5px 1rem",
    },
  },
  select: {
    minWidth: 120,
    color: "text.primary",
    "& .MuiSelect-select": {
      padding: "0.625rem 0.75rem",
      paddingRight: "1rem",
    },
    "& .MuiSelect-standard": {
      backgroundColor: "#FFFFFF",
      border: "1px solid #D0D5DD",
      borderRadius: "5px",
    },
    "& .MuiSelect-standard:focus": {
      borderRadius: "5px",
    },
  },
  selectMenuProps: {
    PaperProps: {
      style: {
        boxShadow: "none",
        backgroundColor: "transparent",
        marginTop: "-4px",
        borderRadius: "0 0 5px 5px",
      },
    },
    MenuListProps: {
      style: {
        padding: 0,
        backgroundColor: "#FFFFFF",
        border: "1px solid #D0D5DD",
        borderRadius: "0 0 5px 5px",
      },
    },
  },
  paper: {
    root: {
      mt: 1,
      font: "Poppins",
      color: "black",
      padding: "1.125rem",
      boxShadow: "0px 4px 15px rgba(0, 0, 0, 0.10)",
      borderRadius: "5px",
    },
    text: {
      normal: {
        fontWeight: 400,
      },
      bold: {
        fontWeight: 700,
      },
    },
  },
  listItems: {
    borderRadius: "10px",
    padding: "10px 16px",
    margin: "4px 0",
    fontSize: "14px",
  },
};

export { SearchBarStyles, SearchBarField };
