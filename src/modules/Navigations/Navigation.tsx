import React from "react";
import { useLocation } from "react-router-dom";
import { PATH_CONFIG } from "src/modules/Routing/config";
import MenuSideBar from "./SideNav/MenuSideBar";
import MenuTopBar from "./TopNav/MenuTopBar";

interface NavigationProps {
  isDrawerOpen: boolean;
  setIsDrawerOpen: (isDrawerOpen: boolean) => void;
}

const Navigation: React.FC<NavigationProps> = ({ isDrawerOpen, setIsDrawerOpen }) => {
  const location = useLocation();

  const { isHeaderVisible, isSideNavVisible } = {
    isHeaderVisible: ![PATH_CONFIG.LOGIN.path].includes(location.pathname),
    isSideNavVisible: ![PATH_CONFIG.LOGIN.path].includes(location.pathname),
  };

  const handleDrawerToggle = () => {
    setIsDrawerOpen(!isDrawerOpen);
  };

  return (
    <React.Fragment>
      <MenuSideBar open={isDrawerOpen} handleDrawerToggle={handleDrawerToggle} isHidden={!isSideNavVisible} />
      {isHeaderVisible ? <MenuTopBar open={isDrawerOpen} isHidden={!isHeaderVisible} /> : null}
    </React.Fragment>
  );
};

export default Navigation;
