import { <PERSON>, <PERSON>ton, Chip, <PERSON>alog<PERSON><PERSON>, Grid, Grid2, Typography } from "@mui/material";
import { useMutation } from "@tanstack/react-query";
import React from "react";
import Modal from "src/modules/Common/Modal/Modal";
import { EmployeeSeperations } from "src/services/api_definitions/offboarding.service";
import offboardingService from "src/services/offboarding.service";

interface FnfConfirmationModalProps {
  onClose: () => void;
  selectedRows: EmployeeSeperations[];
  refetch: () => void;
  isOpen: boolean;
  setRowSelection: (rowSelection: any) => void;
}
const FnfConfirmationModal: React.FC<FnfConfirmationModalProps> = ({
  onClose,
  selectedRows,
  refetch,
  isOpen,
  setRowSelection,
}) => {
  const FnfSubmitMutation = useMutation({
    mutationFn: async () => await offboardingService.fnfOffboardingSubmit(selectedRows.map((row) => row.employee_code)),
    onSuccess: () => {
      onClose();
      refetch();
      setRowSelection({});
    },
  });

  const onSubmit = () => {
    FnfSubmitMutation.mutate();
  };

  return (
    <Modal
      title="Submit F&F Payroll"
      subtitle=""
      isOpen={isOpen}
      onClose={onClose}
      showBackButton
      actions={
        <DialogActions>
          <Button sx={{ minWidth: 150 }} variant="contained" onClick={onSubmit}>
            Confirm
          </Button>
        </DialogActions>
      }
    >
      <Box display="flex" flexDirection="column" gap={2}>
        <Typography variant="subtitle1" color="#42526B">
          Selected Employees
        </Typography>
        <Grid container spacing={2}>
          {selectedRows?.map((selectedRow) => (
            <Grid2 key={selectedRow?.employee_code} size={{ xs: 12, sm: 4, md: 2 }} ml={2} mt={1}>
              <Chip
                sx={{ background: "#E6F2F1", borderRadius: 2, minWidth: 120 }}
                label={`${selectedRow?.name} (${selectedRow?.employee_code})`}
              />
            </Grid2>
          ))}
        </Grid>
        <Box p={1} sx={{ background: "#E6F2F1" }} borderRadius={2}>
          <Typography variant="subtitle2" color="#667085">
            Note: You are about to submit the Full & Final (F&F) settlement for approval. Once approved, the salary will
            be processed.
          </Typography>
        </Box>
      </Box>
    </Modal>
  );
};

export default FnfConfirmationModal;
