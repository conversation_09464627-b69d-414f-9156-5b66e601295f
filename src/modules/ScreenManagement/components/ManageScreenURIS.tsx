/* eslint-disable react/prop-types */
/* eslint-disable @typescript-eslint/no-explicit-any */
import { Box, Button, Checkbox, CircularProgress } from "@mui/material";
import { useMutation, useQueries } from "@tanstack/react-query";
import { MRT_ColumnDef } from "material-react-table";
import React, { lazy, Suspense, useEffect, useMemo, useState } from "react";
import { Screens } from "src/services/api_definitions/screenManagement";
import screenManagementService from "src/services/screenManagement.service";

type ScreenURI = {
  screen_name: string;
  uri: string;
};

const LazyDataTable = lazy(() => import("src/modules/Common/Table/DataTable"));

const convertData = (screens: Screens[], uris: string[], screenUris: ScreenURI[]) => {
  const screenSet = new Set(screens.map((screen) => screen.name));

  const uriToScreenMap = new Map();
  screenUris.forEach((mapping) => {
    const { uri, screen_name } = mapping;
    if (!uriToScreenMap.has(uri)) {
      uriToScreenMap.set(uri, new Set());
    }
    uriToScreenMap.get(uri).add(screen_name);
  });

  const data = uris.map((uri) => {
    // biome-ignore lint/suspicious/noExplicitAny: <explanation>
    const row: Record<string, any> = { uri };
    const mappedScreens = uriToScreenMap.get(uri) || new Set();

    for (const screen of screenSet) {
      row[screen] = false;
    }

    for (const screen of mappedScreens) {
      row[screen] = true;
    }

    return row;
  });

  return data;
};

// biome-ignore lint/suspicious/noExplicitAny: <explanation>
const getFormState = (tableData: Record<string, any>[]) => {
  const indexKeyMap = new Map();

  tableData.forEach((obj, index) => {
    Object.entries(obj).forEach(([key, value]) => {
      if (typeof value === "boolean") {
        indexKeyMap.set(`${index}-${key}`, value);
      }
    });
  });
  return indexKeyMap;
};

// biome-ignore lint/suspicious/noExplicitAny: <explanation>
const getRequestBody = (tableData: Record<string, any>[], formState: Map<string, boolean>) => {
  return tableData.reduce((requestData, currData, index) => {
    Object.keys(currData).forEach((dataKey) => {
      const key = `${index}-${dataKey}`;
      if (formState.has(key) && formState.get(key)) {
        requestData.push({
          uri: currData.uri,
          screen_name: dataKey,
        });
      }
    });
    return requestData;
  }, []);
};

const ManageScreenURIS = () => {
  const [screenUris, screens, uris] = useQueries({
    queries: [
      {
        queryKey: ["screen-uris"],
        queryFn: screenManagementService.getScreenURIs,
        refetchOnWindowFocus: false,
        refetchOnMount: true,
        refetchOnReconnect: true,
      },
      {
        queryKey: ["screens"],
        queryFn: screenManagementService.getAllScreens,
        refetchOnWindowFocus: false,
        refetchOnMount: true,
        refetchOnReconnect: true,
      },
      {
        queryKey: ["uris"],
        queryFn: screenManagementService.getAllURIs,
        refetchOnWindowFocus: false,
        refetchOnMount: true,
        refetchOnReconnect: true,
      },
    ],
  });

  const screenURIMutation = useMutation({
    mutationKey: ["save-screen-uris"],
    mutationFn: screenManagementService.saveScreenURISettings,
    onSuccess: () => {
      screenUris.refetch();
      screens.refetch();
      uris.refetch();
    },
  });

  const tableData = useMemo(() => {
    const SUCCESS_STATUS = "success";
    const areAllQueriesSuccessful = [screenUris.status, screens.status, uris.status].every(
      (status) => status === SUCCESS_STATUS,
    );

    if (areAllQueriesSuccessful) {
      return convertData(screens.data as Screens[], uris.data as string[], screenUris.data as unknown as ScreenURI[]);
    }
    return [];
  }, [screenUris.status, screens.status, uris.status]);

  const [formState, setFormState] = useState(new Map());

  useEffect(() => {
    if (tableData && tableData?.length > 0) {
      setFormState(getFormState(tableData));
    }
  }, [tableData]);

  const handleChange = (key: string, index: number, checked: boolean) => {
    const newFormState = new Map(formState).set(`${index}-${key}`, checked);
    setFormState(newFormState);
  };

  // biome-ignore lint/suspicious/noExplicitAny: <explanation>
  const columnDefs = useMemo((): MRT_ColumnDef<any, unknown>[] => {
    if (tableData?.length === 0) {
      return [];
    }
    return Object.keys(tableData[0]).map((screenKey) => ({
      accessorKey: screenKey,
      header: screenKey,
      Cell: ({ cell, row }) => {
        if (cell.column.columnDef.header === "uri") {
          return tableData[row.index].uri;
        }
        return (
          <Checkbox
            checked={formState.get(`${row.index}-${cell.column.columnDef.header}`) || false}
            onChange={(_ev, checked) => handleChange(cell.column.columnDef.header, cell.row.index, checked)}
          />
        );
      },
    }));
  }, [formState, screenUris.status, screens.status, uris.status]);

  const onSave = () => {
    // biome-ignore lint/suspicious/noExplicitAny: <explanation>
    screenURIMutation.mutate(getRequestBody(tableData, formState) as any);
  };

  return (
    <Suspense fallback={<CircularProgress />}>
      <LazyDataTable
        enableStickyHeader
        enableTopToolbar
        enableBottomToolbar
        data={tableData}
        enableColumnPinning
        initialState={{
          columnPinning: {
            left: ["uri"],
          },
        }}
        state={{
          showSkeletons: screens.isLoading || screenUris.isLoading || uris.isLoading,
          isLoading: screens.isLoading || screenUris.isLoading || uris.isLoading,
        }}
        // biome-ignore lint/suspicious/noExplicitAny: <explanation>
        columns={columnDefs as any}
        renderBottomToolbarCustomActions={() => (
          <Box sx={{ display: "flex", alignItems: "center", justifyContent: "flex-end", width: "100%", padding: 1 }}>
            <Button variant="contained" onClick={onSave} sx={{ textTransform: "none" }} size="large">
              Save
            </Button>
          </Box>
        )}
      />
    </Suspense>
  );
};

export default ManageScreenURIS;
