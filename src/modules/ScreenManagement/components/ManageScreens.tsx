import { Checkbox } from "@mui/material";
import { useMutation, useQuery } from "@tanstack/react-query";
import React, { useMemo, useState } from "react";
import { useForm } from "src/customHooks/useForm";
import { CrudTable } from "src/modules/Settings/components/Common/CrudTable";
import { CreateScreen, UpdateScreen } from "src/services/api_definitions/screenManagement";
import masterdataService from "src/services/masterdata.service";
import screenManagementService from "src/services/screenManagement.service";

const defaultState = {
  name: "",
  description: "",
  moduleType: "",
  isActive: true,
};

const ManageScreens = () => {
  const [selectedRow, setSelectedRow] = useState(null);
  const { data, isFetched, refetch } = useQuery(
    ["get-all-screens"],
    async () => screenManagementService.getAllScreens(),
    {
      refetchOnMount: true,
      refetchOnWindowFocus: false,
    },
  );
  const { data: masterData, isFetched: isMasterDataFetched } = useQuery(
    ["get-module-type"],
    async () => masterdataService.getACLs("ModuleType"),
    {
      refetchOnMount: true,
      refetchOnWindowFocus: false,
    },
  );

  const createScreen = useMutation({
    mutationKey: ["create-screen"],
    mutationFn: async (payload: CreateScreen) => screenManagementService.createScreen(payload),
    onSuccess: () => {
      refetch();
    },
  });

  const updateScreen = useMutation({
    mutationKey: ["update-screen"],
    mutationFn: async (payload: UpdateScreen) => screenManagementService.updateScreen(payload),
    onSuccess: () => {
      refetch();
    },
  });

  const deleteScreen = useMutation({
    mutationKey: ["delete-screen"],
    mutationFn: async (payload: string) => screenManagementService.deleteScreen(payload),
    onSuccess: () => {
      refetch();
    },
  });

  const screenData = useMemo(
    () =>
      data?.map((screen) => ({
        ...screen,
        moduleType: screen.module_type,
        isActive: screen.is_active,
      })),
    [data],
  );

  const inputElements = [
    {
      name: "name",
      label: "Screen Name",
      variant: "text",
      xs: 12,
      placeholder: "Screen Name",
      isEditable: true,
      isRequired: true,
    },
    {
      name: "description",
      label: "Description",
      variant: "text",
      xs: 12,
      placeholder: "Screen Description",
      isEditable: true,
      isRequired: true,
    },
    {
      name: "moduleType",
      label: "Module Type",
      variant: "select",
      xs: 12,
      placeholder: "Module Type",
      isEditable: true,
      isRequired: true,
    },
    {
      name: "isActive",
      label: "Is Active",
      variant: "checkbox",
      isEditable: true,
      allowEmpty: true,
      isRequired: true,
    },
  ];

  const formValidators = {
    name: [],
    isActive: [],
  };

  const useFormDetails = useForm<any>({
    initialState: selectedRow === null ? defaultState : screenData?.[selectedRow],
    isBulk: false,
    validations: formValidators,
  });

  const onEditClick = (screenData: typeof defaultState, selectedIndex: number) => {
    if (screenData) {
      const { description, moduleType, name, isActive } = screenData;
      const selectedScreen = data?.[selectedIndex];
      updateScreen.mutate({
        active: isActive,
        description: selectedScreen?.description,
        module_type: selectedScreen?.module_type,
        name: selectedScreen?.name,
        new_name: name,
        new_description: description,
        new_module_type: moduleType,
      });
    }
  };

  const onAddClick = (data: typeof defaultState) => {
    if (data) {
      const { description, moduleType, name, isActive } = data;
      createScreen.mutate({
        active: isActive,
        description,
        module_type: moduleType,
        name,
      });
    }
  };

  const onDeleteConfirmed = (screenIndex: number) => {
    const screen = data?.[screenIndex];
    if (screen) {
      deleteScreen.mutate(screen.name);
    }
  };

  const editFormConfig = {
    nextButtonText: "Save",
    onNextClick: onEditClick,
    formTitle: "Edit Screen",
  };

  const addFormConfig = {
    nextButtonText: "Create",
    onNextClick: onAddClick,
    formTitle: "Add Screen",
    addButtonText: "Create",
  };

  const deleteFormConfig = {
    nextButtonText: "Delete",
    onNextClick: onDeleteConfirmed,
    formTitle: "Delete Screen",
    getQuestion: (rowIndex: number) => `Are you sure you want to delete this ${data?.[rowIndex]?.name} screen?`,
  };

  const formConfig = {
    editFormConfig,
    addFormConfig,
    deleteFormConfig,
    tableHeaderTitle: "Screen",
  };

  const columns = useMemo(() => {
    return [
      {
        accessorKey: "name",
        header: "Screen",
      },
      {
        accessorKey: "description",
        header: "Description",
      },
      {
        accessorKey: "moduleType",
        header: "Module Type",
      },
      {
        accessorKey: "is_active",
        header: "Is Screen Active",
        Cell: ({ cell }) => {
          return <Checkbox checked={cell?.row?.original.is_active} disableFocusRipple disabled />;
        },
      },
    ];
  }, []);

  return (
    <CrudTable
      isLoading={!isFetched && !isMasterDataFetched}
      defaultFormState={screenData}
      formConfig={formConfig}
      columns={columns}
      inputElements={inputElements}
      rowAdditionaInitialValues={defaultState}
      captureFormChange={undefined}
      selectOptions={{
        moduleType: masterData?.map((module) => ({
          label: module,
          value: module,
        })),
      }}
      useFormDetails={useFormDetails}
      setSelectedRow={setSelectedRow}
      selectedRow={selectedRow}
    />
  );
};

export default ManageScreens;
