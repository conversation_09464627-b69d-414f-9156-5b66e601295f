import React from "react";
import { Id, TypeOptions, toast } from "react-toastify";
import "./toast.css";

interface Props {
  title: string;
  toastId?: Id;
  type: TypeOptions;
}
const ToastComponent: React.FC<Props> = ({ title, toastId, type = "default" }) => {
  const icons: Record<TypeOptions, string> = {
    success: "✅",
    error: "❌",
    info: "ℹ️",
    warning: "⚠️",
    default: "",
  };

  return (
    <div
      className={`custom-toast-container ${type}`}
      role="alert"
      aria-live="assertive"
      aria-atomic="true"
      onClick={() => toast.dismiss(toastId)}
    >
      <span className="custom-toast-icon" aria-hidden="true">
        {icons[type]}
      </span>
      <div className="custom-toast-message">{title}</div>
    </div>
  );
};

export default ToastComponent;
