/* custom-toast.css */
.custom-toast-container {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px 20px;
  border-radius: 8px;
  font-family: 'Roboto', sans-serif;
  font-size: 14px;
  font-weight: 500;
  background-color: #ffffff; /* Neutral background */
  color: #333333; /* Dark text for contrast */
  border: 2px solid; /* Add border for type-based color */
  box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.1); /* Subtle shadow */
}

.custom-toast-container.success {
  border-color: #4caf50; /* Green border for success */
}

.custom-toast-container.error {
  border-color: #e57373; /* Red border for error */
}

.custom-toast-container.info {
  border-color: #64b5f6; /* Blue border for info */
}

.custom-toast-container.warning {
  border-color: #ffb74d; /* Amber border for warning */
}

.custom-toast-icon {
  font-size: 20px;
  color: inherit; /* Inherit border color for consistency */
}

.custom-toast-message {
  flex-grow: 1;
  word-wrap: break-word;
}

/* Remove React Toastify default styles */
.Toastify__toast {
  background: transparent !important;
  box-shadow: none !important;
  padding: 0 !important;
}
