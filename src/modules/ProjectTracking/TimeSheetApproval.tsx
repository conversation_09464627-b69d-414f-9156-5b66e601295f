import { Cancel, CheckCircle } from "@mui/icons-material";
import { Box, Chip, IconButton, Tooltip } from "@mui/material";
import { useQuery } from "@tanstack/react-query";
import { MRT_ColumnDef } from "material-react-table";
import React, { useState } from "react";
import { TimesheetTracking } from "src/services/api_definitions/timesheetTracking.service";
import { convertToHourMinute } from "src/utils/dateUtils";
import timesheetTrackingService from "../../services/timesheetTracking.service";
import { EmployeeCellInfo } from "../Common/EmployeeViews/EmployeeCellInfo";
import TimeSheetLogs from "./TimeSheetLogs";

const getTimesheetStatusColor = (status?: string) => {
  switch (status) {
    case "Sent Back":
      return {
        backgroundColor: "#F44336", //color name is red
        color: "white",
      };
    case "Submitted":
      return {
        backgroundColor: "#FFC107", //color name is yellow
        color: "black",
      };
    case "Approved":
      return { backgroundColor: "#4CAF50", color: "white" }; //color name is green
    default:
      return { backgroundColor: "default", color: "white" };
  }
};

const columns: MRT_ColumnDef<TimesheetTracking>[] = [
  {
    header: "Employee",
    accessorKey: "raised_by.display_name",
    Cell: ({
      row: {
        original: {
          raised_by: { display_name, job_title, display_pic },
        },
      },
    }) => <EmployeeCellInfo name={display_name} jobTitle={job_title} displayPic={display_pic} />,
  },
  {
    header: "Start Date",
    accessorKey: "start_date",
    size: 120,
  },
  {
    header: "End Date",
    accessorKey: "end_date",
    size: 120,
  },
  {
    header: "Duration",
    accessorKey: "duration",
    size: 110,
  },
  {
    header: "Status",
    accessorKey: "status",
    size: 120,
    Cell: ({ row }) => (
      <Chip
        label={row.original.status}
        size="small"
        sx={{
          width: "100px",
          ...getTimesheetStatusColor(row.original.status),
          fontFamily: "Poppins",
        }}
      />
    ),
  },
  {
    header: "Billable Time",
    accessorKey: "billable_recorded_time",
    size: 120,
    Cell: ({ row }) => {
      return convertToHourMinute(row.original.billable_recorded_time);
    },
  },
  {
    header: "Non-Billable Time",
    accessorKey: "non_billable_recorded_time",
    size: 130,
    Cell: ({ row }) => {
      return convertToHourMinute(row.original.non_billable_recorded_time);
    },
  },
];
const TimeSheetApproval = () => {
  const { data: timesheetApprovals, refetch } = useQuery({
    queryKey: ["timesheet-approvals"],
    queryFn: () => timesheetTrackingService.getAllTimesheetApprovals(),
    refetchOnWindowFocus: false,
    // refetchOnMount: false,
  });

  const [selectedRows, setRowSelection] = useState<any>({});

  const onApproveClick = async () => {
    await timesheetTrackingService.approveTimesheet(
      Object.keys(selectedRows).map((key) => timesheetApprovals?.[key as any]?.timesheet_request_id ?? ""),
    );
    refetch();
    setRowSelection({});
  };

  const onRejectClick = async () => {
    await timesheetTrackingService.rejectTimesheet(
      Object.keys(selectedRows).map((key) => timesheetApprovals?.[key as any]?.timesheet_request_id ?? ""),
    );
    refetch();
    setRowSelection({});
  };

  return (
    <Box display="flex" flexDirection="column" gap={1} position="relative">
      <Tooltip title="Approve">
        <IconButton
          sx={{
            alignSelf: "flex-start",
            position: "absolute",
            zIndex: 1000,
            top: 6,
            left: 0,
          }}
          onClick={onApproveClick}
          disabled={Object.keys(selectedRows).length === 0}
          color="success"
          size="large"
        >
          <CheckCircle sx={{ height: 30, width: 30 }} />
        </IconButton>
      </Tooltip>
      <Tooltip title="Send Back">
        <IconButton
          sx={{
            alignSelf: "flex-start",
            position: "absolute",
            zIndex: 1000,
            top: 6,
            left: 45,
          }}
          onClick={onRejectClick}
          disabled={Object.keys(selectedRows).length === 0}
          color="error"
          size="large"
        >
          <Cancel sx={{ height: 30, width: 30 }} />
        </IconButton>
      </Tooltip>
      <TimeSheetLogs
        selectedRows={selectedRows}
        onRowSelectionChange={setRowSelection}
        enableRowSelection={true}
        timesheetRequests={timesheetApprovals || []}
        columns={columns}
        approvalScreen={true}
      />
    </Box>
  );
};

export default TimeSheetApproval;
