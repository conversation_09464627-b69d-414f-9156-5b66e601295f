import {
  AddBox,
  CheckCircleOutlineRounded,
  CheckCircleRounded,
  PlayCircleOutlineOutlined,
  Replay,
  StopCircleOutlined,
} from "@mui/icons-material";
import DeleteIcon from "@mui/icons-material/Delete";
import EditIcon from "@mui/icons-material/Edit";
import {
  Box,
  Button,
  Chip,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  IconButton,
  Tooltip,
  Typography,
} from "@mui/material";
import { type MRT_ColumnDef } from "material-react-table";
import React, { useEffect, useMemo, useState } from "react";
import projectTrackingService from "src/services/projectTracking.service";
import DataTable from "../Common/Table/DataTable";

interface TaskRow {
  id: string | null;
  parentId?: string;
  taskType: "Project" | "Task" | "SubTask";
  name: string;
  dueDate?: string;
  priority?: "High" | "Medium" | "Low";
  estimatedHours?: number;
  clockedHours?: number;
  showClockIn?: boolean;
  clientCode?: string;
  status?: string;
}

const getChildName = (row: TaskRow) => {
  if (row.taskType === "Project") {
    return "Task";
  }
  if (row.taskType === "Task") {
    return "SubTask";
  }
  if (row.taskType === "SubTask") {
    return "SubTask";
  }
  return "";
};

const getPriorityColor = (priority?: string) => {
  switch (priority) {
    case "High":
      return {
        backgroundColor: "#F44336",
        color: "white",
      };
    case "Medium":
      return {
        backgroundColor: "#FFC107",
        color: "black",
      };
    case "Low":
      return { backgroundColor: "#4CAF50", color: "white" };
    default:
      return { backgroundColor: "default", color: "white" };
  }
};
const getStatusStyles = (status: string) => {
  switch (status) {
    case "Completed":
      return {
        backgroundColor: "#E8F5E9",
        color: "#2E7D32",
      };
    case "In Progress":
      return {
        backgroundColor: "#E3F2FD",
        color: "#1565C0",
      };
    case "Pending":
    default:
      return {
        backgroundColor: "#ECEFF1",
        color: "#37474F",
      };
  }
};

const getTimeString = (time: string | number | undefined) => {
  if (!time) {
    return "";
  }
  const timeNumber = parseFloat(time.toString());
  if (timeNumber > 1) {
    return `${timeNumber} Hrs`;
  }
  return `${timeNumber} Hr`;
};

const TaskTableMRT = ({
  initialData,
  onEditClick,
  refetchAllTasks,
  onCreateTask,
  addDuplicateTask,
  employeeCode,
  isLoading,
}: {
  initialData: TaskRow[];
  onEditClick: (row: TaskRow) => void;
  refetchAllTasks: () => void;
  onCreateTask: () => void;
  addDuplicateTask: (row: TaskRow) => void;
  employeeCode?: string;
  isLoading: boolean;
}) => {
  // const isFirstRender = useRef(true);
  const [showDeleteConfirmation, setShowDeleteConfirmation] = useState(false);
  const [rowToDelete, setRowToDelete] = useState<TaskRow | null>(null);
  const [columnVisibility, setColumnVisibility] = useState({});

  useEffect(() => {
    const columnVisibility = localStorage.getItem("mrt_columnVisibility_table_1");
    if (columnVisibility) {
      setColumnVisibility(JSON.parse(columnVisibility));
    }

    // isFirstRender.current = false;
  }, []);

  useEffect(() => {
    // if (isFirstRender.current) return;
    if (Object.keys(columnVisibility).length > 0) {
      localStorage.setItem("mrt_columnVisibility_table_1", JSON.stringify(columnVisibility));
    }
  }, [columnVisibility]);

  const handleDelete = async (id: string | null) => {
    await projectTrackingService.deleteTask(id);
    await refetchAllTasks();
    setShowDeleteConfirmation(false);
  };

  const handleClockIn = async (rowData: TaskRow) => {
    if (rowData.showClockIn) {
      await projectTrackingService.clockIn(rowData.id);
    } else {
      await projectTrackingService.clockOut(rowData.id);
    }
    refetchAllTasks();
  };

  const handleMarkComplete = async (rowData: TaskRow) => {
    await projectTrackingService.markComplete(rowData.id);
    refetchAllTasks();
  };

  const handleReopenTask = async (rowData: TaskRow) => {
    await projectTrackingService.reopenTask(rowData.id, employeeCode);
    refetchAllTasks();
  };

  const columns = useMemo<MRT_ColumnDef<TaskRow>[]>(
    () => [
      {
        accessorKey: "task",
        header: "Task",
        enableColumnFilter: false,
        size: 540,
        minSize: 350,
        maxSize: 640,
        muiTableBodyCellProps: {
          sx: {
            marginBottom: 0.01,
          },
        },
        // enableColumnPinning: true,
        Cell: ({ row }) => {
          const isCompleted = row.original.status === "Completed";
          return (
            <Box width="100%" display="flex" gap={1} pl={row.depth * 2} justifyContent="space-between">
              <Tooltip title={row.original.name}>
                <Typography noWrap sx={{ maxWidth: "100%" }} alignSelf="center">
                  {row.original.name}
                </Typography>
              </Tooltip>
              {row.original.taskType !== "Project" && !isCompleted && !employeeCode && (
                <Tooltip title={row.original.showClockIn ? "Clock In" : "Clock Out"} color="primary">
                  <IconButton
                    size="small"
                    onClick={() => handleClockIn(row.original)}
                    // sx={{ padding: 0, marginRight: 2 }}
                    // sx={{ backgroundColor: row.original.showClockIn ? "#4CAF50" : "#FF5050", color: "#fff" }}
                  >
                    {row.original.showClockIn ? <PlayCircleOutlineOutlined /> : <StopCircleOutlined color="warning" />}
                  </IconButton>
                </Tooltip>
              )}
              {row.original.taskType !== "Project" && isCompleted && (
                <Tooltip title="Reopen" color="primary">
                  <IconButton size="small" onClick={() => handleReopenTask(row.original)}>
                    <Replay />
                  </IconButton>
                </Tooltip>
              )}
            </Box>
          );
        },
      },
      {
        accessorKey: "priority",
        header: "Priority",
        size: 90,
        enableResizing: false,
        Cell: ({ cell }) => (
          <Chip
            label={cell.getValue<string>()}
            size="small"
            sx={{
              width: "70px",
              ...getPriorityColor(cell.getValue<string>()),
              fontFamily: "Poppins",
            }}
          />
        ),
      },
      {
        accessorKey: "dueDate",
        header: "Due Date",
        grow: 1,
        size: 110,
        enableResizing: false,
        Cell: ({ cell }) => cell.getValue<string>(),
      },
      {
        accessorKey: "status",
        header: "Status",
        size: 110,
        enableResizing: false,
        Cell: ({ cell }) => (
          <Chip
            label={cell.getValue<string>()}
            size="small"
            sx={{
              width: "100px",
              ...getStatusStyles(cell.getValue<string>()),
              fontFamily: "Poppins",
              fontWeight: "bold",
            }}
          />
        ),
      },
      {
        accessorKey: "estimatedHours",
        header: "Estimated Time",
        enableResizing: false,
        size: 155,
        Cell: ({ row }) => {
          // const isLeaf = getIsLeaf(row.original, initialData);
          return getTimeString(row.original.estimatedHours);
        },
      },
      {
        accessorKey: "clockedHours",
        header: "Clocked Time",
        size: 140,
        enableResizing: false,
        Cell: ({ row }) => {
          return getTimeString(row.original.clockedHours);
        },
      },
      {
        accessorKey: "clientCode",
        header: "Client Code",
        size: 140,
        enableResizing: false,
        Cell: ({ row }) => row.original.clientCode,
      },
      {
        header: "Actions",
        enableResizing: false,
        size: 160,
        Cell: ({ row }) => {
          const isCompleted = row.original.status === "Completed";
          return (
            <Box display="flex" gap={1} height={34}>
              {!isCompleted && (
                <Tooltip title="Edit" color="primary">
                  <IconButton onClick={() => onEditClick(row.original)} size="small">
                    <EditIcon fontSize="small" />
                  </IconButton>
                </Tooltip>
              )}
              {!isCompleted && !employeeCode && (
                <Tooltip title="Delete" color="primary">
                  <IconButton
                    onClick={() => {
                      setShowDeleteConfirmation(true);
                      setRowToDelete(row.original);
                    }}
                    size="small"
                  >
                    <DeleteIcon fontSize="small" />
                  </IconButton>
                </Tooltip>
              )}
              {!isCompleted && (
                <Tooltip title={`Add ${getChildName(row.original)}`} color="primary">
                  <IconButton onClick={() => addDuplicateTask(row.original)} size="small">
                    <AddBox fontSize="small" />
                  </IconButton>
                </Tooltip>
              )}
              {row.original.taskType !== "Project" && !isCompleted && !employeeCode && (
                <Tooltip title={isCompleted ? "Completed" : "Mark as Complete"} color="primary">
                  <IconButton size="small" onClick={() => handleMarkComplete(row.original)} disabled={isCompleted}>
                    {isCompleted ? <CheckCircleRounded /> : <CheckCircleOutlineRounded />}
                  </IconButton>
                </Tooltip>
              )}
            </Box>
          );
        },
      },
    ],
    [initialData, onEditClick],
  );

  const getSubRows = (row: TaskRow) => initialData.filter((r) => r.parentId === row.id);

  return (
    <Box sx={{ position: "relative" }}>
      <Box sx={{ position: "absolute", top: 8, left: 8, zIndex: 1000 }}>
        <Tooltip title="Create Task">
          <IconButton onClick={onCreateTask}>
            <AddBox color="primary" fontSize="medium" />
          </IconButton>
        </Tooltip>
      </Box>

      <DataTable
        columns={columns}
        data={initialData.filter((row) => !row.parentId)}
        enableExpanding
        getSubRows={getSubRows}
        initialState={{
          expanded: true,
          density: "compact",
          columnPinning: {
            left: ["mrt-row-expand", "task"],
          },
        }}
        state={{ columnVisibility, isLoading }}
        onColumnVisibilityChange={setColumnVisibility}
        enablePagination={false}
        // enableSorting
        enableColumnActions
        enableTopToolbar
        enableColumnFilters
        enableGlobalFilter
        enableDensityToggle
        enableFullScreenToggle
        enableColumnResizing
        maxHeightDeduction={employeeCode ? 260 : 200}
        muiTopToolbarProps={{
          sx: {
            justifyContent: "flex-start",
            marginLeft: "60px",
          },
        }}

        // displayColumnDefOptions={{
        //   "mrt-row-expand": {
        //     size: maxDepth * 23, // ✅ increase this (default is 56)
        //     minSize: 70, // optional: to prevent collapsing too much
        //   },
        // }}
      />
      <Dialog open={showDeleteConfirmation} onClose={() => setShowDeleteConfirmation(false)}>
        <DialogTitle>Delete Task</DialogTitle>
        <DialogContent>Are you sure you want to delete this task? Child tasks will also be deleted.</DialogContent>
        <DialogActions>
          <Button onClick={() => setShowDeleteConfirmation(false)}>Cancel</Button>
          <Button onClick={() => handleDelete(rowToDelete?.id || null)}>Delete</Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default TaskTableMRT;
