import { AddBox } from "@mui/icons-material";
import ArrowBackIosIcon from "@mui/icons-material/ArrowBackIos";
import ArrowForwardIosIcon from "@mui/icons-material/ArrowForwardIos";
import { Box, Button, ButtonGroup, IconButton, Typography } from "@mui/material";
import { TimeField } from "@mui/x-date-pickers";
import { useForm } from "@tanstack/react-form";
import { useMutation, useQuery } from "@tanstack/react-query";
import { addDays, differenceInHours, format, isAfter, parse, startOfWeek } from "date-fns";
import { MRT_ColumnDef } from "material-react-table";
import React, { useState, useMemo, useEffect, useRef } from "react";
import { useSearchParams } from "react-router-dom";
import { useAppSelector } from "src/customHooks/useAppSelector";
import DataTable from "src/modules/Common/Table/DataTable";
import { Project } from "src/services/api_definitions/timesheetTracking.service";
import timesheetTrackingService from "src/services/timesheetTracking.service";
import { convertToHourMinute } from "src/utils/dateUtils";
import { EmployeeCellInfo } from "../Common/EmployeeViews/EmployeeCellInfo";
import Modal from "../Common/Modal/Modal";
import BillableSegmentedBar from "./RecordedTimeDisplay";

interface TimesheetEntryProps {
  projects?: Project[];
  maxHoursPerDay?: number;
}

// Define the structure of the timesheet API response
interface TimesheetResponse {
  total_recorded_time: string;
  billable_recorded_time: string;
  non_billable_recorded_time: string;
  timesheet: Array<{
    project_id: string;
    project_name: string;
    project_code: string;
    timesheet_records: Array<{
      login_date: string;
      recorded_time: string;
    }>;
  }>;
  status: string;
}

// Define a type for project data
interface ProjectData {
  project_id: string;
  project_name: string;
  project_code: string;
  [key: string]: unknown;
}

// Helper function to convert HH:MM to Date object
export const timeStringToDate = (timeString: string): Date | null => {
  if (!timeString) return null;

  const [hours, minutes] = timeString.split(":").map(Number);
  const date = new Date();
  date.setHours(hours || 0);
  date.setMinutes(minutes || 0);
  date.setSeconds(0);
  date.setMilliseconds(0);
  return date;
};

// Helper function to convert Date to minutes for total calculation
const dateToMinutes = (date: Date | null): number => {
  if (!date) return 0;
  return date.getHours() * 60 + date.getMinutes();
};

// Prepare sample projects for demo
const TimesheetEntry: React.FC<TimesheetEntryProps> = () => {
  const [searchParams] = useSearchParams();
  const startDate = searchParams.get("startDate");
  const initialViewType = searchParams.get("viewType") || "Weekly";
  const today = startDate ? new Date(startDate) : new Date();
  const [viewType, setViewType] = useState<"Weekly" | "Monthly">(initialViewType as "Weekly" | "Monthly");
  const [weekOffset, setWeekOffset] = useState(0);
  const [rowSelection, setRowSelectionState] = useState<Record<string, boolean>>({});
  const [isAddProjectModalOpen, setIsAddProjectModalOpen] = useState<boolean>(false);
  const [summaryDetails, setSummaryDetails] = useState<{
    total_recorded_time?: string;
    billable_recorded_time?: string;
    non_billable_recorded_time?: string;
  }>({});
  const { userDetails } = useAppSelector((state) => state.userManagement);
  const getWorkHours = useMemo(() => {
    if (!userDetails?.work_start_time || !userDetails?.work_end_time) return 9;
    const today = new Date();
    const startTime = parse(userDetails?.work_start_time?.substring(0, 5), "HH:mm", today);
    const endTime = parse(userDetails?.work_end_time?.substring(0, 5), "HH:mm", today);

    // Handle time spans across midnight
    if (endTime < startTime) {
      return differenceInHours(addDays(endTime, 1), startTime);
    }

    return differenceInHours(endTime, startTime);
  }, [userDetails]);

  const weekStart = useMemo(
    () => addDays(startOfWeek(today, { weekStartsOn: 1 }), weekOffset * 7),
    [today, weekOffset],
  );

  // Calculate the start of the month for month view
  const monthStart = useMemo(() => {
    const date = new Date(today);
    date.setDate(1); // First day of the month
    date.setMonth(date.getMonth() + weekOffset); // Use weekOffset for month navigation too
    return date;
  }, [today, weekOffset]);

  // Get the number of days in the current month
  const getDaysInMonth = (date: Date) => {
    const nextMonth = new Date(date);
    nextMonth.setMonth(nextMonth.getMonth() + 1);
    nextMonth.setDate(0);
    return nextMonth.getDate();
  };

  // Choose the appropriate start date and number of days based on view type
  const periodStart = useMemo(
    () => (viewType === "Weekly" ? weekStart : monthStart),
    [viewType, weekStart, monthStart],
  );
  const daysCount = useMemo(() => (viewType === "Weekly" ? 7 : getDaysInMonth(monthStart)), [viewType, monthStart]);

  const days = useMemo(
    () => Array.from({ length: daysCount }).map((_, idx) => addDays(periodStart, idx)),
    [periodStart, daysCount],
  );

  const { data: projs } = useQuery(["get-employee-client-projects"], async () =>
    timesheetTrackingService.getAllProjects("employee"),
  );

  // Query for timesheet data
  const { refetch, data: timesheetData } = useQuery({
    queryKey: ["get-timesheet-entries", format(periodStart, "yyyy-MM-dd"), viewType],
    queryFn: async () => {
      const endDate =
        viewType === "Weekly"
          ? format(addDays(periodStart, 6), "yyyy-MM-dd")
          : format(addDays(periodStart, daysCount - 1), "yyyy-MM-dd");

      const response = await timesheetTrackingService.getAllTimesheets(format(periodStart, "yyyy-MM-dd"), endDate);
      return response as unknown as TimesheetResponse;
    },
    enabled: !!projs?.length,
    refetchOnWindowFocus: false,
    onSuccess: (data: TimesheetResponse) => {
      const rowSelectedProjects: Record<number, boolean> = {};
      setSummaryDetails({
        total_recorded_time: data?.total_recorded_time,
        billable_recorded_time: data?.billable_recorded_time,
        non_billable_recorded_time: data?.non_billable_recorded_time,
      });
      if (data?.timesheet?.length) {
        // Create project structure with prefilled values
        const projectsWithTimesheet = data.timesheet.map((project) => {
          const projectRowSelections = projs?.findIndex((p) => p.project_id === project.project_id);
          if (projectRowSelections !== -1) {
            rowSelectedProjects[projectRowSelections as any] = true;
          }
          const projectData: ProjectData = {
            project_id: project.project_id,
            project_name: project.project_name,
            project_code: project.project_code,
          };

          // Initialize all days with default values
          days.forEach((day) => {
            const dateKey = format(day, "yyyy-MM-dd");
            projectData[dateKey] = null;
          });

          // Fill in the recorded times from the timesheet
          project.timesheet_records.forEach((entry) => {
            const dateKey = entry.login_date;
            if (Object.prototype.hasOwnProperty.call(projectData, dateKey)) {
              projectData[dateKey] = timeStringToDate(entry.recorded_time);
            }
          });

          return projectData;
        });

        // Update the form with the prefilled projects
        form.setFieldValue("projects", projectsWithTimesheet);
      }
      setRowSelectionState(rowSelectedProjects);
    },
  });

  const updateTimesheetMutation = useMutation({
    mutationFn: async (data: any) => {
      return timesheetTrackingService.updateTimesheet(data);
    },
    onSuccess: (response: any) => {
      if (response) {
        refetch();
      }
    },
  });

  const publishTimesheetMutation = useMutation({
    mutationFn: async (data: any) => {
      return timesheetTrackingService.publishTimesheet(data);
    },
    onSuccess: (response: any) => {
      if (response) {
        refetch();
      }
    },
  });

  const actionTypeRef = useRef<"DRAFT" | "PUBLISHED">("DRAFT");

  // Define the form
  const form = useForm({
    defaultValues: {
      projects: [] as ProjectData[],
      _forceUpdate: Date.now(), // Add a field to force re-renders
    },
    onSubmit: async ({ value }) => {
      const projects = value.projects;
      // Format the data for submission
      const formattedEntries = projects.flatMap((project) => {
        return Object.entries(project)
          .filter(([key]) => /^\d{4}-\d{2}-\d{2}$/.test(key))
          .map(([loginDate, dateValue]) => {
            // Convert Date to HH:MM format
            let recordedTime = "00:00";
            if (dateValue instanceof Date) {
              const hours = String(dateValue.getHours()).padStart(2, "0");
              const minutes = String(dateValue.getMinutes()).padStart(2, "0");
              recordedTime = `${hours}:${minutes}`;
            }

            return {
              project_id: project.project_id,
              login_date: loginDate,
              recorded_time: recordedTime,
            };
          });
      });

      // Call the API to update the timesheet
      if (actionTypeRef.current === "PUBLISHED") {
        await publishTimesheetMutation.mutateAsync(formattedEntries);
      } else {
        await updateTimesheetMutation.mutateAsync(formattedEntries);
      }
    },
  });

  const forceKey = form.getFieldValue("_forceUpdate");
  const isSubmitted = timesheetData?.status === "Submitted" || timesheetData?.status === "Approved";

  // Columns including pinned Total and custom TimePicker cells
  const columns = useMemo(() => {
    const cols: MRT_ColumnDef<ProjectData>[] = [
      {
        accessorKey: "project_name",
        header: "Project Name",
        size: 200,
        Cell: ({ row }) => (
          <EmployeeCellInfo name={row.original.project_name} jobTitle={row.original.project_code} hideAvatar />
        ),
        footer: "Remaining",
      },
    ];

    // day columns
    days.forEach((day) => {
      const key = format(day, "yyyy-MM-dd");
      const isFutureDate = isAfter(day, new Date());
      const disableInput = isFutureDate || isSubmitted;

      cols.push({
        accessorKey: key,
        header: format(day, "EEE, dd MMM"),
        size: 120,
        muiTableHeadCellProps: {
          sx: {
            backgroundColor: disableInput ? "#f5f5f5" : "inherit",
            color: disableInput ? "#9e9e9e" : "inherit",
          },
        },
        muiTableBodyCellProps: {
          sx: {
            backgroundColor: disableInput ? "#f5f5f5" : "inherit",
          },
        },
        Cell: ({ row, column }) => {
          const fieldName = `projects.${row.index}.${column.id}`;

          return (
            <form.Field name={fieldName as any}>
              {(field) => {
                return (
                  <TimeField
                    ampm={false}
                    format="HH:mm"
                    slotProps={{
                      textField: {
                        size: "small",
                        sx: { width: 72 },
                      },
                    }}
                    maxTime={timeStringToDate(`${getWorkHours}:00`) as Date}
                    value={field.state.value as unknown as Date | null}
                    onChange={(newDate) => {
                      // Get the current projects array
                      const currentProjects = form.getFieldValue("projects");

                      // Create a deep copy of the projects array
                      const projects = currentProjects.map((project: ProjectData, idx: number) => {
                        if (idx === row.index) {
                          // For the current row, create a new object with the updated date
                          return {
                            ...project,
                            [column.id]: newDate,
                          };
                        }
                        return project;
                      });

                      // Update the form state with the new projects array
                      form.setFieldValue("projects", projects);

                      // Force re-render to update totals
                      form.setFieldValue("_forceUpdate", Date.now());
                    }}
                    readOnly={disableInput}
                    defaultValue={timeStringToDate("00:00")}
                  />
                );
              }}
            </form.Field>
          );
        },
        Footer: () => (
          <form.Subscribe
            selector={(state) => ({
              projects: state.values.projects,
              forceUpdate: state.values._forceUpdate,
            })}
          >
            {({ projects }) => {
              // sum up minutes for this date-key
              const used = projects?.reduce((sum, p) => {
                const v = p[key];
                if (v instanceof Date) return sum + dateToMinutes(v);
                return sum;
              }, 0);
              const rem = getWorkHours * 60 - used;
              const h = Math.floor(rem / 60);
              const m = rem % 60;
              return (
                <Box display="flex" alignItems="center" pl={2}>
                  {convertToHourMinute(`${h}:${m}`)}
                </Box>
              );
            }}
          </form.Subscribe>
        ),
      });
    });

    // total column pinned right
    cols.push({
      accessorKey: "total",
      header: "Total",
      size: 100,
      enableEditing: false,
      Cell: ({ row }) => {
        return (
          <form.Subscribe
            selector={(state) => ({
              projects: state.values.projects,
              forceUpdate: state.values._forceUpdate,
            })}
          >
            {({ projects }) => {
              // Get the current project data from the form
              const project = projects[row.index];

              // Calculate total from all day values for this project
              let totalMinutes = 0;

              if (project) {
                days.forEach((day) => {
                  const key = format(day, "yyyy-MM-dd");
                  const dateValue = project[key];

                  // Handle both Date objects and ISO strings (from JSON serialization)
                  if (dateValue) {
                    if (dateValue instanceof Date) {
                      totalMinutes += dateToMinutes(dateValue);
                    } else if (typeof dateValue === "string") {
                      // If it's a string (ISO format), convert to Date
                      totalMinutes += dateToMinutes(new Date(dateValue));
                    }
                  }
                });
              }

              const hours = Math.floor(totalMinutes / 60);
              const minutes = totalMinutes % 60;
              return convertToHourMinute(`${hours}:${minutes}`);
            }}
          </form.Subscribe>
        );
      },
    });

    return cols;
  }, [days, today, form, timesheetData]);

  const getProjectStructureFromForm = (projectCodes: any[], previousProjects: ProjectData[]) => {
    return (projectCodes || []).map((projectCode) => {
      const proj = projs?.find((p) => p.project_code === projectCode);
      const prevProj = previousProjects.find((p) => p.project_code === projectCode);

      const projectData: ProjectData = {
        project_id: proj?.project_id || "",
        project_name: proj?.project_name || "",
        project_code: proj?.project_code || "",
      };

      // Initialize all days with default values
      days.forEach((day) => {
        const dateKey = format(day, "yyyy-MM-dd");
        projectData[dateKey] = prevProj ? prevProj[dateKey] : null;
      });

      return projectData;
    });
  };

  const onAddProjectsClick = () => {
    setIsAddProjectModalOpen(true);
  };

  useEffect(() => {
    const selectedProjects = Object.keys(rowSelection)
      .filter((key) => rowSelection[key])
      .map((key) => projs?.[Number(key)]?.project_code)
      .filter(Boolean);
    form.setFieldValue("projects", getProjectStructureFromForm(selectedProjects, form.getFieldValue("projects")));
  }, [rowSelection]);

  return (
    <Box display="flex" flexDirection="column" gap={2}>
      <Box display="flex" alignItems="center" justifyContent="space-between" width="100%" p={1}>
        <ButtonGroup size="small">
          <Button
            variant={viewType === "Monthly" ? "contained" : "outlined"}
            onClick={() => {
              if (viewType !== "Monthly") {
                setViewType("Monthly");
                form.reset({
                  projects: [],
                  _forceUpdate: Date.now(),
                });
              }
            }}
          >
            Month
          </Button>
          <Button
            variant={viewType === "Weekly" ? "contained" : "outlined"}
            onClick={() => {
              if (viewType !== "Weekly") {
                setViewType("Weekly");
                form.reset({
                  projects: [],
                  _forceUpdate: Date.now(),
                });
              }
            }}
          >
            Week
          </Button>
        </ButtonGroup>

        <Box display="flex" alignItems="center">
          <IconButton
            size="small"
            onClick={() => {
              setWeekOffset((w) => w - 1);
              form.reset({
                projects: [],
                _forceUpdate: Date.now(),
              });
            }}
          >
            <ArrowBackIosIcon fontSize="small" />
          </IconButton>
          <Typography>
            {viewType === "Weekly" ? (
              <>
                {format(periodStart, "MMM dd, yyyy")} - {format(addDays(periodStart, 6), "MMM dd, yyyy")}
              </>
            ) : (
              format(periodStart, "MMMM yyyy")
            )}
          </Typography>
          <IconButton
            size="small"
            onClick={() => {
              setWeekOffset((w) => w + 1);
              form.reset({
                projects: [],
                _forceUpdate: Date.now(),
              });
            }}
          >
            <ArrowForwardIosIcon fontSize="small" />
          </IconButton>
        </Box>
      </Box>
      <form.Subscribe selector={(state) => state.values.projects}>
        {(projects) => (
          <DataTable
            key={forceKey}
            columns={columns}
            data={projects}
            maxHeightDeduction={350}
            enableTopToolbar
            enableGlobalFilter
            enableGlobalFilterRankedResults
            renderTopToolbar={() => (
              <Box display="flex" alignItems="center" p={1}>
                <IconButton onClick={() => onAddProjectsClick()} disabled={isSubmitted}>
                  <AddBox color={isSubmitted ? "disabled" : "primary"} />
                </IconButton>
                <BillableSegmentedBar
                  summary={{
                    total_recorded_time: summaryDetails?.total_recorded_time as string,
                    billable_recorded_time: summaryDetails?.billable_recorded_time as string,
                    non_billable_recorded_time: summaryDetails?.non_billable_recorded_time as string,
                  }}
                />
              </Box>
            )}
            enableTableFooter
            enableStickyFooter
            enableBottomToolbar
            renderBottomToolbar={() => (
              <Box display="flex" gap={2} p={1} justifyContent="flex-end">
                <Button
                  variant="outlined"
                  onClick={() => {
                    actionTypeRef.current = "DRAFT";
                    form.handleSubmit();
                  }}
                  disabled={isSubmitted}
                >
                  Save
                </Button>
                <Button
                  variant="contained"
                  onClick={() => {
                    actionTypeRef.current = "PUBLISHED";
                    form.handleSubmit();
                  }}
                  disabled={isSubmitted}
                >
                  Submit
                </Button>
              </Box>
            )}
            enableStickyHeader
            rowPinningDisplayMode="sticky"
            initialState={{
              columnPinning: { left: ["project_name"], right: ["total"] },
            }}
          />
        )}
      </form.Subscribe>
      <Modal
        isOpen={isAddProjectModalOpen}
        onClose={() => setIsAddProjectModalOpen(false)}
        title="Add Projects to Timesheet"
        subtitle="Select projects to add to the timesheet"
      >
        <DataTable
          enableTopToolbar
          enableGrouping={false}
          enableHiding={false}
          enableFilters={true}
          enableDensityToggle={false}
          enableColumnFilters={false}
          enableFullScreenToggle={false}
          onRowSelectionChange={setRowSelectionState}
          state={{
            density: "compact",
            rowSelection: rowSelection,
          }}
          columns={[
            {
              accessorKey: "project_code",
              header: "Project Code",
              size: 60,
            },
            {
              accessorKey: "project_name",
              header: "Project",
              size: 200,
            },
          ]}
          data={projs || []}
          enableMultiRowSelection
          enableSelectAll
          enableRowSelection
          positionToolbarAlertBanner="none"
        />
      </Modal>
    </Box>
  );
};

export default TimesheetEntry;
