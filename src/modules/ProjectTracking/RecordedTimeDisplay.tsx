import { Box, Chip, Stack } from "@mui/material";
import React from "react";
import { convertToHourMinute } from "src/utils/dateUtils";

type TimeSummary = {
  billable_recorded_time: string;
  non_billable_recorded_time: string;
  total_recorded_time: string;
};

const getStatusStyles = (status: string) => {
  switch (status) {
    case "Billable":
      return {
        backgroundColor: "#E8F5E9",
        color: "#2E7D32",
      };
    case "Total":
      return {
        backgroundColor: "#E3F2FD",
        color: "#1565C0",
      };
    case "Non-billable":
    default:
      return {
        backgroundColor: "#ECEFF1",
        color: "#37474F",
      };
  }
};

const BillableSegmentedBar = ({ summary }: { summary: TimeSummary }) => {
  const { billable_recorded_time, non_billable_recorded_time, total_recorded_time } = summary;
  const summaryLabels = [
    {
      label: `Billable: ${convertToHourMinute(billable_recorded_time)}`,
      status: "Billable",
    },
    {
      label: `Non-billable: ${convertToHourMinute(non_billable_recorded_time)}`,
      status: "Non-billable",
    },
    {
      label: `Total: ${convertToHourMinute(total_recorded_time)}`,
      status: "Total",
    },
  ];
  return (
    <Box sx={{ width: "100%", maxWidth: 600, ml: 2 }}>
      <Stack direction="row" spacing={2} justifyContent={"flex-start"}>
        {summaryLabels.map((label) => (
          <Chip
            key={label.status}
            label={label.label}
            size="small"
            sx={{
              width: "180px",
              ...getStatusStyles(label.status),
              fontFamily: "Poppins",
              fontWeight: "bold",
            }}
          />
        ))}
      </Stack>
    </Box>
  );
};

export default BillableSegmentedBar;
