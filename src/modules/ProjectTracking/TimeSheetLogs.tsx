import { Box } from "@mui/material";
import React, { useMemo, useState } from "react";
import { TimesheetTracking } from "src/services/api_definitions/timesheetTracking.service";
import DataTable from "../Common/Table/DataTable";

import { addDays, differenceInHours, format, parse, parseISO } from "date-fns";
import { MRT_ColumnDef, MRT_Row } from "material-react-table";
import { useNavigate } from "react-router-dom";
import { toast } from "react-toastify";
import { useAppSelector } from "src/customHooks/useAppSelector";
import { convertToHourMinute } from "src/utils/dateUtils";
import { EmployeeCellInfo } from "../Common/EmployeeViews/EmployeeCellInfo";
import Modal from "../Common/Modal/Modal";
import TableActions from "../Common/Table/TableActions";

const TimeSheetLogs = ({
  timesheetRequests,
  columns,
  enableRowSelection,
  onRowSelectionChange,
  selectedRows = [],
  approvalScreen = false,
  onRowDelete,
}: {
  timesheetRequests: TimesheetTracking[];
  columns: MRT_ColumnDef<TimesheetTracking>[];
  enableRowSelection: boolean;
  onRowSelectionChange: (rowSelection: TimesheetTracking[]) => void;
  selectedRows: TimesheetTracking[];
  approvalScreen: boolean;
  onRowDelete: (timesheetRequestID: string) => void;
}) => {
  const [selectedTimesheet, setSelectedTimesheet] = useState<any>(null);
  const { userDetails } = useAppSelector((state) => state.userManagement);

  const getWorkHours = useMemo(() => {
    if (!userDetails?.work_start_time || !userDetails?.work_end_time) return 9;
    const today = new Date();
    const startTime = parse(userDetails?.work_start_time?.substring(0, 5), "HH:mm", today);
    const endTime = parse(userDetails?.work_end_time?.substring(0, 5), "HH:mm", today);

    // Handle time spans across midnight
    if (endTime < startTime) {
      return differenceInHours(addDays(endTime, 1), startTime);
    }

    return differenceInHours(endTime, startTime);
  }, [userDetails]);

  const loginDates = selectedTimesheet?.[0]?.timesheet_records?.map?.((r: any) => r.login_date) || ([] as string[]);

  // Columns
  const recordColumns: MRT_ColumnDef<any>[] = [
    {
      accessorKey: "project_name",
      header: "Project Name",
      size: 200,
      Cell: ({ row }) => (
        <EmployeeCellInfo name={row.original.project_name} jobTitle={row.original.project_code} hideAvatar />
      ),
      Footer: () => {
        return (
          <div>
            <strong>Remaining</strong>
          </div>
        );
      },
    },
    ...loginDates.map((loginDate: string) => ({
      accessorKey: loginDate,
      header: format(parseISO(loginDate), "EEE dd"),
      size: 100,
      Cell: ({ row }: { row: MRT_Row<any> }) => {
        const record = row.original.timesheet_records.find((rec: any) => rec.login_date === loginDate);
        return record?.recorded_time ? convertToHourMinute(record.recorded_time) : "—";
      },
      Footer: () => {
        const usedMinutes =
          selectedTimesheet?.reduce((sum: number, project: any) => {
            const record = project.timesheet_records?.find((r: any) => r.login_date === loginDate);
            if (!record?.recorded_time) return sum;
            const [h, m] = record.recorded_time.split(":").map(Number);
            return sum + h * 60 + m;
          }, 0) || 0;

        const rem = getWorkHours * 60 - usedMinutes;
        const h = Math.floor(rem / 60);
        const m = rem % 60;

        return convertToHourMinute(`${h}:${m}`);
      },
    })),
    {
      accessorKey: "total",
      header: "Total",
      size: 100,
      Cell: ({ row }) => {
        const totalMinutes =
          row.original.timesheet_records?.reduce((sum: number, rec: any) => {
            const [h, m] = rec.recorded_time.split(":").map(Number);
            return sum + h * 60 + m;
          }, 0) || 0;

        const h = Math.floor(totalMinutes / 60);
        const m = totalMinutes % 60;
        return convertToHourMinute(`${h}:${m}`);
      },
    },
  ];
  const navigate = useNavigate();

  return (
    <Box>
      <DataTable
        columns={columns}
        data={timesheetRequests || []}
        state={{
          rowSelection: selectedRows as any,
        }}
        enableTopToolbar
        enableGrouping={false}
        enableHiding={false}
        enableFilters={true}
        enableDensityToggle={false}
        positionActionsColumn="last"
        // displayColumnDefOptions={{
        //   "mrt-row-actions": {
        //     header: "",
        //     size: 50,
        //   },
        // }}
        muiToolbarAlertBannerProps={{
          sx: { display: "none" }, // Hides the selected rows text
        }}
        // enableExpanding
        enableRowSelection={enableRowSelection && ((row) => row?.original?.status === "Submitted")}
        onRowSelectionChange={onRowSelectionChange as any}
        enableGlobalFilter={false}
        enableRowActions
        renderRowActions={({ row }) => (
          <TableActions
            edit={{ hide: true, onClick: () => {} }}
            remove={{
              onClick: () => {
                onRowDelete(row.original?.timesheet_request_id);
              },
              hide: row?.original?.status !== "Submitted",
            }}
            view={{
              onClick: () => {
                if (approvalScreen) {
                  setSelectedTimesheet(row.original?.timesheet);
                } else {
                  const duration = row.original?.duration;
                  if (duration === "Weekly" || duration === "Monthly") {
                    navigate(`/time-log?startDate=${row.original?.start_date}&tab=0&viewType=${duration}`);
                  } else {
                    toast.error("Invalid duration");
                  }
                }
              },
            }}
          />
        )}
      />
      <Modal
        title="Timesheet Logs"
        isOpen={!!selectedTimesheet}
        onClose={() => setSelectedTimesheet(null)}
        maxWidth="100vw"
        showBackButton
      >
        <Box p={1}>
          <DataTable
            columns={recordColumns}
            data={selectedTimesheet || []}
            positionActionsColumn="last"
            displayColumnDefOptions={{
              "mrt-row-actions": {
                header: "",
                size: 50,
              },
            }}
            enableTopToolbar
            enableGrouping={false}
            enableHiding={false}
            enableFilters={false}
            enableDensityToggle={false}
            enableColumnActions={false}
            enableColumnOrdering={false}
            enableColumnPinning={false}
            enableColumnResizing={false}
            enableGlobalFilter={false}
          />
        </Box>
      </Modal>
    </Box>
  );
};

export default TimeSheetLogs;
