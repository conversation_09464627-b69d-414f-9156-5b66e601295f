import React, { useCallback } from "react";
import { Route, Routes } from "react-router-dom";
import { SideBarMenu } from "src/configs/app.config";
import { useRouteConfig } from "src/contexts/RoutesConfigContext";
import { useAppSelector } from "src/customHooks/useAppSelector";
import { RouteConfig } from "./Routing.types";
import { PATH_CONFIG } from "./config";

const PageRoutes: React.FC = () => {
  const routingConfig = useRouteConfig();

  const { authorisedScreens } = useAppSelector((state) => state.userManagement);

  type RoutingConfig = typeof routingConfig;

  const flattenRoutes = useCallback(
    (routes: SideBarMenu[], routesMap: RoutingConfig) => {
      return routes.reduce((acc: RouteConfig[], routeConfig) => {
        if (routeConfig.subRoutes) {
          acc.push(...flattenRoutes(routeConfig.subRoutes, routesMap));
        }

        acc.push({
          path: routeConfig.pathname as string,
          element: routesMap?.[routeConfig.key] as JSX.Element,
          key: routeConfig.key,
        });

        return acc;
      }, []);
    },
    [routingConfig],
  );

  const getConfiguredRoutes = useCallback(
    (routesMap: RoutingConfig) => {
      // Flatten the sideBarMenus routes
      // const flatRoutes = flattenRoutes(appConfig.sideBarMenus, routesMap);

      // Only render those routes which have authorisation
      const flattenedAuthorisedScreens = flattenRoutes(authorisedScreens, routesMap);
      return [
        ...flattenedAuthorisedScreens,
        {
          path: PATH_CONFIG.LOGIN.path,
          element: routesMap?.[PATH_CONFIG.LOGIN.key],
        },
        {
          path: PATH_CONFIG.PAGE_NOT_FOUND.path,
          element: routesMap?.[PATH_CONFIG.PAGE_NOT_FOUND.key],
        },
      ];
    },
    [authorisedScreens],
  );

  return (
    <Routes>
      {getConfiguredRoutes(routingConfig).map((route) => (
        <Route key={route.path} path={route?.path as string} element={route?.element} />
      ))}
    </Routes>
  );
};

export default PageRoutes;
