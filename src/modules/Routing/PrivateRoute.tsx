import { CircularProgress } from "@mui/material";
import React, { useEffect, useMemo } from "react";
import { Navigate } from "react-router-dom";
import { AccessControl } from "src/configs/app.config";
import { useAppDispatch } from "src/customHooks/useAppDispatch";
import { useAppSelector } from "src/customHooks/useAppSelector";
import { useAuth } from "src/modules/Login/LoginHook";
import { PATH_CONFIG } from "src/modules/Routing/config";
import { PageNotFound } from "src/pages";
import { setFullviewMode } from "src/store/slices/app.slice";
import { setBreadcrumbs } from "src/store/slices/breadcrumbs.slice";
import { getACLFromFeaturekey } from "src/utils/screenUtils";

interface PrivateRouteProps {
  element: React.ReactElement;
  featureKey: (typeof PATH_CONFIG)[keyof typeof PATH_CONFIG]["path"];
  isInternal?: boolean;
}

const PrivateRoute = ({ element, featureKey, isInternal = false }: PrivateRouteProps) => {
  const { authorisedScreens } = useAppSelector((state) => state.userManagement);
  const { breadcrumbs } = useAppSelector((state) => state.breadcrumbs);
  const dispatch = useAppDispatch();

  useEffect(() => {
    dispatch(setFullviewMode(false));
  }, [breadcrumbs, dispatch]);

  // const { isAuthenticated, isSessionLoading } = useSession();
  const { isAuthenticated } = useAuth();
  const ACL: AccessControl = useMemo(() => getACLFromFeaturekey(featureKey), [authorisedScreens, featureKey]);
  if (!isAuthenticated) {
    return <Navigate to={PATH_CONFIG.LOGIN.path} />;
  }

  if (authorisedScreens.length === 0) {
    return <CircularProgress sx={{ color: "#007F6F" }} />;
  }

  if (isInternal) {
    return element;
  }

  const isUserAuthorisedToViewScreen = authorisedScreens.some(
    (authorisedScreen) => authorisedScreen.key === featureKey,
  );
  if (isUserAuthorisedToViewScreen) {
    const authorisedScreen = authorisedScreens?.find((screen) => screen.key === featureKey);

    if (breadcrumbs.length === 0 && authorisedScreen) {
      dispatch(
        setBreadcrumbs([
          {
            isActive: true,
            isDisabled: false,
            label: authorisedScreen.title,
            path: authorisedScreen.pathname,
          },
        ]),
      );
    }
    return React.cloneElement(element, {
      acl: ACL,
    });
  }
  return <PageNotFound />;
};

export default PrivateRoute;
