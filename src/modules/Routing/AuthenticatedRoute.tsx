import React from "react";
import { Navigate } from "react-router-dom";
import { useAuth } from "src/modules/Login/LoginHook";
import { PATH_CONFIG } from "src/modules/Routing/config";
import { isRightTenant } from "src/utils/authUtils";
interface AuthenticatedRouteProps {
  element: React.ReactElement;
}

const AuthenticatedRoute: React.FC<AuthenticatedRouteProps> = ({ element }) => {
  const { isAuthenticated, logout } = useAuth();
  let isTenantAllowed = true;
  if (isAuthenticated) {
    isTenantAllowed = isRightTenant([]);
    if (!isTenantAllowed) {
      logout();
      alert("404. User not found");
      window.location.reload();
    }
  }

  return isAuthenticated && isTenantAllowed ? <Navigate to={PATH_CONFIG.HOME.path} /> : element;
};

export default AuthenticatedRoute;
