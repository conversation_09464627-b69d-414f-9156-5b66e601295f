import { MoreVert } from "@mui/icons-material";
import { Avatar, Box, Chip, CircularProgress, IconButton } from "@mui/material";
import { MRT_VisibilityState } from "material-react-table";
import React, { Suspense, useCallback, useMemo } from "react";
import { createColumnConfig } from "src/configs/table/newjoinees.table.config";
import DataTable from "src/modules/Common/Table/DataTable";
import { TransformedEmployee } from "src/services/api_definitions/employees";
import SettingsMenu from "./SettingsMenu/SettingsMenu";
import { styles } from "./styles/styles.module";

const InitialVisibilityState = {
  first_name: true,
  last_name: true,
  employee_name: false,
  job_title: false,
  department: false,
  reporting_manager: false,
  employment_status: false,
  employee_code: false,
  employee_type: false,
  aadhar: false,
  blood_group: false,
  date_of_birth: false,
  date_of_joining: true,
  email: false,
  gender: false,
  location: false,
  marital_status: false,
  nationality: false,
  organisation: false,
  pan: false,
  passport: false,
  personal_email: true,
  phone: false,
  uan: false,
  bank_account: false,
  current_address: false,
  permanent_address: false,
  emergency_contacts: false,
  reportees: false,
  form_status: true,
};

interface NewJoineesViewProps {
  data: TransformedEmployee[];
  isLoading: boolean;
  isFetching: boolean;
  refetch: () => void;
}

const NewJoineesView: React.FC<NewJoineesViewProps> = ({ data, isLoading, isFetching, refetch }) => {
  const [anchorEl, setAnchorEl] = React.useState<null | HTMLElement>(null);
  const [columnVisibility, setColumnVisibility] = React.useState<MRT_VisibilityState>(InitialVisibilityState);
  const [candidateInfo, setCandidateInfo] = React.useState<null | TransformedEmployee>(null);

  // The event functions do not have to render everytime, thus we do not need to create them in every render
  const handleSettingsMenu = useCallback((event: React.MouseEvent<HTMLElement>, candidateInfo: TransformedEmployee) => {
    setAnchorEl(event.currentTarget);
    setCandidateInfo(candidateInfo);
  }, []);

  const handleClose = useCallback(() => {
    setAnchorEl(null);
  }, []);

  const columnCallbackMap = useMemo(
    () =>
      new Map([
        [
          "employee_name",
          (row: Record<string, unknown>) => {
            return (
              <Box sx={{ display: "flex", flexDirection: "row", alignItems: "center" }}>
                <Avatar alt={(row as TransformedEmployee)?.employee_name} sx={{ width: 30, height: 30, mr: "10px" }} />
                {(row as TransformedEmployee)?.employee_name}
              </Box>
            );
          },
        ],
        [
          "employment_status",
          (row: Record<string, unknown>) => (
            <Box sx={{ width: 140, display: "flex", justifyContent: "center" }}>
              <Chip label={(row as TransformedEmployee)?.employment_status} sx={styles.tablerow.chip} />
            </Box>
          ),
        ],
      ]),
    [],
  );

  const columnConfigs = useCallback(() => createColumnConfig(columnCallbackMap), [columnCallbackMap]);

  return (
    <Box>
      <Suspense fallback={<CircularProgress sx={{ alignItems: "center" }} />}>
        <DataTable
          data={data || []}
          columns={columnConfigs()}
          enablePagination={false}
          enableStickyHeader
          enableRowActions
          enableColumnPinning
          onColumnVisibilityChange={setColumnVisibility}
          initialState={{
            columnPinning: {
              right: ["mrt-row-actions"],
            },
          }}
          state={{
            showSkeletons: isLoading && isFetching,
            columnVisibility: columnVisibility,
          }}
          renderRowActions={({ row }) => (
            <IconButton
              onClick={(event: React.MouseEvent<HTMLElement>) =>
                handleSettingsMenu(event, row.original as TransformedEmployee)
              }
            >
              <MoreVert />
            </IconButton>
          )}
          localization={{
            actions: "Settings",
          }}
          muiTopToolbarProps={() => ({
            sx: {
              "& .MuiBox-root": {
                gap: 0,
              },
            },
          })}
        />
      </Suspense>
      <SettingsMenu anchorEl={anchorEl} candidateInfo={candidateInfo} handleClose={handleClose} refetch={refetch} />
    </Box>
  );
};

export default NewJoineesView;
