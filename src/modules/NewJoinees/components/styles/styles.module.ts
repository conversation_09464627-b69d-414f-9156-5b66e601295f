import { DatePickerSlotsComponentsProps } from "@mui/x-date-pickers/DatePicker/DatePicker.types";

const styles = {
  root: {
    display: "block",
    width: "auto",
    bgcolor: "background.paper",
    borderRadius: "20px",
    boxShadow: 24,
    p: 4,
  },
  header: {
    display: "flex",
    justifyContent: "space-between",
    flexDirection: "row",
    width: "100%",
  },
  caption: {
    mt: "10px",
    color: "#667085",
  },
  table: {
    display: "flex",
    width: "100%",
    // padding: '20px 0 0 0',
    flexDirection: "column",
    alignItems: "flex-start",
    gap: "18px",
    height: "auto",
  },
  tablerow: {
    chip: {
      backgroundColor: "#C6FFC2",
    },
  },
  button: {
    padding: "1.25rem",
    borderRadius: 8,
    width: "100%",
    height: "50px",
    fontSize: "1rem",
    fontWeight: "500",
    textTransform: "none",
    "&.MuiButton-contained": {
      "&:hover": {
        backgroundColor: "primary.light",
      },
      "&:disabled": {
        color: "primary.contrastText",
        backgroundColor: "primary.light",
        opacity: 0.5,
      },
    },
    "&.MuiButton-contained .MuiTouchRipple-child": {
      backgroundColor: "black",
    },
    "&.MuiButton-text": {
      border: "none",
      backgroundColor: "#EFF4F8",
      "&:hover": {
        backgroundColor: "#F2F3F3",
      },
      "&:disabled": {
        backgroundColor: "#EFF4F8",
      },
    },
  },
};

const ModalStyles = {
  root: {
    padding: 0,
    width: 764,
  },
  header: {
    padding: 4,
    paddingBottom: 0,
    display: "flex",
    justifyContent: "space-between",
  },
  body: {
    container: {
      padding: "16px",
    },
    grid: {
      padding: "0 1rem",
      minHeight: "180px",
    },
    text: {
      caption: {
        ...styles.caption,
      },
      info: {
        color: "#667085",
        fontSize: 14,
        fontFamily: "Poppins",
        fontWeight: "400",
        wordWrap: "break-word",
      },
    },
  },
  textLabels: {
    color: "#667085",
    marginBottom: "8px",
  },
  textFieldStyles: {
    width: "90%",
    input: {
      color: "text.primary",
    },
    ".css-1t8l2tu-MuiInputBase-input-MuiOutlinedInput-input": {
      padding: "0.625rem 1rem",
    },
    "& .MuiOutlinedInput-root": {
      "& fieldset": {
        borderColor: "info.light",
        borderRadius: "5px",
      },
      "&:hover fieldset": {
        borderColor: "info.main",
        borderRadius: "5px",
      },
      "&.Mui-focused fieldset": {
        borderColor: "info.main",
        borderRadius: "5px",
      },
      "& .MuiInputBase-input.Mui-disabled": {
        backgroundColor: "#F5F5F5",
      },
    },
  },
  closeIcon: {
    cursor: "pointer",
    padding: "4px",
    border: "1px solid #D5D7D8",
    borderRadius: "50px",
    color: "#D5D7D8",
    width: "32px",
    height: "32px",
    "&:hover": {
      color: "black",
      borderColor: "black",
    },
    transition: "0.195s all",
  },
  buttonContainer: {
    display: "flex",
    flexDirection: "row",
    justifyContent: "flex-end",
    padding: "32px 16px",
    gap: "16px",
  },
  button: {
    ...styles.button,
    padding: "1.25rem",
    borderRadius: 8,
    width: "172px",
    height: "50px",
    fontSize: "1rem",
    fontWeight: "500",
    textTransform: "none",
    cursor: "pointer",
    "&.MuiButton-contained": {
      "&:hover": {
        backgroundColor: "primary.light",
      },
      "&:disabled": {
        color: "primary.contrastText",
        backgroundColor: "primary.light",
        opacity: 0.5,
      },
    },
    "&.MuiButton-contained .MuiTouchRipple-child": {
      backgroundColor: "black",
    },
    "&.MuiButton-text": {
      border: "none",
      backgroundColor: "#EFF4F8",
      "&:hover": {
        backgroundColor: "#F2F3F3",
      },
      "&:disabled": {
        backgroundColor: "#EFF4F8",
      },
    },
  },
};

const AddNewJoinerModalStyles = {
  root: {
    ...ModalStyles.root,
  },
  header: {
    ...ModalStyles.header,
  },
  body: {
    ...ModalStyles.body,
    sampleFileRoot: {
      background: "#E6F2F1",
      display: "flex",
      alignItems: "center",
      justifyContent: "space-between",
      borderRadius: "15px",
    },
    sampleFileContainer: {
      display: "flex",
      justifyContent: "space-between",
      alignItems: "center",
      width: "100%",
      padding: "14px 32px",
    },
    sampleFileSubtitle: {
      color: "#667085",
      fontSize: 12,
      wordWrap: "break-word",
      width: "80%",
    },
  },
  textLabels: {
    ...ModalStyles.textLabels,
  },
  textFieldStyles: {
    ...ModalStyles.textFieldStyles,
  },
  closeIcon: {
    ...ModalStyles.closeIcon,
  },
  downloadIcon: {
    width: "52px",
    height: "52px",
    borderRadius: "50px",
    background: "white",
    display: "flex",
    justifyContent: "center",
    alignItems: "center",
    "&:hover": {
      opacity: 0.5,
      cursor: "pointer",
    },
  },
  buttonContainer: {
    ...ModalStyles.buttonContainer,
  },
  button: {
    ...ModalStyles.button,
  },
};

const FormSentModalStyles = {
  root: {
    ...styles.root,
  },
  caption: {
    mt: "10px",
  },
  svgContainer: {
    root: {
      height: "230px",
      width: "100%",
      display: "flex",
      justifyContent: "center",
      flexDirection: "column",
    },
    body: {
      display: "flex",
      justifyContent: "center",
      flexDirection: "row",
    },
  },
  button: {
    ...styles.button,
  },
};

const FormResentModalStyles = {
  ...FormSentModalStyles,
  errorButton: {
    padding: "1.25rem",
    borderRadius: 8,
    width: "100%",
    height: "50px",
    fontSize: "1rem",
    fontWeight: "500",
    textTransform: "none",
  },
  errorIcon: {
    width: 64,
    height: 64,
    color: "#FF4D4D",
  },
};

const ModalControllerStyles = {
  root: {
    "& .MuiDialogContent-root": {
      minWidth: 600,
      padding: 0,
    },
  },
  paper: {
    ...styles.root,
  },
};

const SettingsMenuStyles = {
  style: {
    color: "#667085",
    borderRadius: "10px",
    padding: "8px",
    gap: "8px",
    display: "flex",
  },
  menuitem: {
    padding: "0.75rem 1.25rem",
    borderRadius: "5px",
  },
};

const ActionModalStyles = {
  root: {
    ...ModalStyles.root,
  },
  header: {
    ...ModalStyles.header,
  },
  body: {
    container: {
      ...ModalStyles.body.container,
      padding: "0 24px",
    },
  },
  textLabels: {
    ...ModalStyles.textLabels,
  },
  textFieldStyles: {
    ...ModalStyles.textFieldStyles,
  },
  closeIcon: {
    ...ModalStyles.closeIcon,
  },
  buttonContainer: {
    ...ModalStyles.buttonContainer,
  },
  button: {
    ...ModalStyles.button,
  },
};

const ConvertToEmployeeModalStyles = {
  paper: {
    ...styles.root,
    maxHeight: "96vh",
  },
  header: {
    ...ModalStyles.header,
  },
  body: {
    ...ModalStyles.body,
  },
  textLabels: {
    ...ModalStyles.textLabels,
  },
  textFieldStyles: {
    ...ModalStyles.textFieldStyles,
  },
  closeIcon: {
    ...ModalStyles.closeIcon,
  },
  downloadIcon: {
    width: "52px",
    height: "52px",
    borderRadius: "50px",
    background: "white",
    display: "flex",
    justifyContent: "center",
    alignItems: "center",
    "&:hover": {
      opacity: 0.5,
      cursor: "pointer",
    },
  },
  buttonContainer: {
    ...ModalStyles.buttonContainer,
    justifyContent: "space-between",
  },
  button: {
    ...ModalStyles.button,
  },
};

const DateFieldSlotProps: DatePickerSlotsComponentsProps<Date> = {
  textField: {
    variant: "outlined",
    color: "primary",
    fullWidth: true,
    size: "small",
    InputProps: {
      style: {
        width: "90%",
      },
    },
    InputLabelProps: {
      style: {
        fontSize: "18px",
        color: "transparent",
      },
    },
  },
};

export {
  styles,
  AddNewJoinerModalStyles,
  ActionModalStyles,
  ModalControllerStyles,
  FormSentModalStyles,
  SettingsMenuStyles,
  FormResentModalStyles,
  ConvertToEmployeeModalStyles,
  DateFieldSlotProps,
};
