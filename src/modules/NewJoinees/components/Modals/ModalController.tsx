import React from "react";
import { AddNewJoinerModal, FormSentModal } from "./index";

type RenderModalProps = {
  open: boolean;
  modalId: string | null;
  handleClose: () => void;
  handleModalId: (arg0: string) => void;
};
interface ModalControllerProps extends RenderModalProps {
  open: boolean;
}

const RenderModal: React.FC<RenderModalProps> = ({ open, modalId, handleModalId, handleClose }) => {
  switch (modalId) {
    case "addNewJoiner":
      return <AddNewJoinerModal open={open} onClose={handleClose} handleModalId={handleModalId} />;
    case "joiningFormSent":
      return <FormSentModal open={open} onClose={handleClose} />;
    default:
      return null;
  }
};

const ModalController: React.FC<ModalControllerProps> = ({ open, modalId, handleClose, handleModalId }) => (
  <RenderModal open={open} key={modalId} modalId={modalId} handleClose={handleClose} handleModalId={handleModalId} />
);

export default ModalController;
