import { Box, Button, Typography } from "@mui/material";
import React from "react";
import { SuccessIcon } from "src/assets/icons.svg";
import { default as languageConfig } from "src/configs/language/en.lang";
import Modal from "src/modules/Common/Modal/Modal";
import { FormSentModalStyles, ModalControllerStyles } from "../styles/styles.module";

interface ModalProps {
  open: boolean;
  onClose: () => void;
}

const FormSentModal: React.FC<ModalProps> = ({ open, onClose }) => (
  <Modal
    isOpen={open}
    onClose={onClose}
    sx={ModalControllerStyles.root}
    PaperProps={{
      style: ModalControllerStyles.paper,
    }}
  >
    <Box sx={FormSentModalStyles.root}>
      <Typography variant="h6">{languageConfig.new_joinees.modals.joiningFormSentModal.title}</Typography>
      <Typography variant="body1" sx={FormSentModalStyles.caption}>
        {languageConfig.new_joinees.modals.joiningFormSentModal.subtitle}
      </Typography>
      <Box>
        <Box sx={FormSentModalStyles.svgContainer.root}>
          <Box sx={FormSentModalStyles.svgContainer.body}>
            <SuccessIcon />
          </Box>
        </Box>
        <Button variant="contained" onClick={onClose} sx={FormSentModalStyles.button}>
          {languageConfig.new_joinees.modals.joiningFormSentModal.button.confirm}
        </Button>
      </Box>
    </Box>
  </Modal>
);

export default FormSentModal;
