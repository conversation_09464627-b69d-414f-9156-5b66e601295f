import React from "react";
import languageConfig from "src/configs/language/en.lang";
import ContentHeader from "src/modules/Common/ContentHeader/ContentHeader";

interface EmployeesHeaderProps {
  handleOpen: (modalId: string) => void;
}

const EmployeesHeader: React.FC<EmployeesHeaderProps> = ({ handleOpen }) => {
  return (
    <ContentHeader
      title={languageConfig.new_joinees.title}
      subtitle={languageConfig.new_joinees.subtitle}
      buttonTitle={languageConfig.new_joinees.button.addNewCandidate}
      primaryAction={() => handleOpen("addNewJoiner")}
    />
  );
};

export default EmployeesHeader;
