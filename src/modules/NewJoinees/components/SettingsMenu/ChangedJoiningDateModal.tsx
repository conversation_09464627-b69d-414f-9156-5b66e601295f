import { Box, Button, DialogActions } from "@mui/material";
import React from "react";
import ActionModal from "./ActionModal";
import { ModalProps } from "./index";

const ChangedJoiningDateModal: React.FC<ModalProps> = ({ candidateInfo, open, onClose }) => {
  return (
    <ActionModal
      open={open}
      onClose={onClose}
      message={
        <Box sx={{ fontWeight: 600, fontSize: "16px", pt: "24px" }}>
          {candidateInfo?.first_name} {candidateInfo?.last_name} joining date has been changed successfully.
        </Box>
      }
      actions={
        <>
          <DialogActions sx={{ padding: 1, margin: 2, gap: 3 }}>
            <Button onClick={onClose} variant="text">
              Ok
            </Button>
          </DialogActions>
        </>
      }
    />
  );
};

export default ChangedJoiningDateModal;
