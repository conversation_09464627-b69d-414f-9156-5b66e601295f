import { Error } from "@mui/icons-material";
import { Box, Button, Typography } from "@mui/material";
import React from "react";
import { SuccessIcon } from "src/assets/icons.svg";
import { default as languageConfig } from "src/configs/language/en.lang";
import LoadingScreen from "src/modules/Common/LoadingScreen/LoadingScreen";
import Modal from "src/modules/Common/Modal/Modal";

import { ModalProps } from ".";
import { FormResentModalStyles, ModalControllerStyles } from "../styles/styles.module";

interface JoiningFormResentProps extends ModalProps {
  isLoading: boolean;
  isError: boolean;
  onClick: (ev: React.ChangeEvent<HTMLElement>) => void;
}

const JoiningFormResentModal: React.FC<JoiningFormResentProps> = ({ open, isLoading, isError, onClick, onClose }) => (
  <Modal
    onClick={onClick}
    isOpen={open}
    onClose={onClose}
    sx={ModalControllerStyles.root}
    PaperProps={{
      style: ModalControllerStyles.paper,
    }}
  >
    <Box sx={FormResentModalStyles.root}>
      {isLoading ? (
        <LoadingScreen />
      ) : isError ? (
        <>
          <Typography variant="h6">
            {languageConfig.new_joinees.settings.modals.joiningFormResentModal.error.title}
          </Typography>
          <Typography variant="body1" sx={FormResentModalStyles.caption}>
            {languageConfig.new_joinees.settings.modals.joiningFormResentModal.error.subtitle}
          </Typography>
          <Box>
            <Box sx={FormResentModalStyles.svgContainer.root}>
              <Box sx={FormResentModalStyles.svgContainer.body}>
                <Error sx={FormResentModalStyles.errorIcon} />
              </Box>
            </Box>
            <Button variant="contained" color="error" onClick={onClose} sx={FormResentModalStyles.errorButton}>
              {languageConfig.new_joinees.settings.modals.joiningFormResentModal.button.confirm}
            </Button>
          </Box>
        </>
      ) : (
        <>
          <Typography variant="h6">
            {languageConfig.new_joinees.settings.modals.joiningFormResentModal.title}
          </Typography>
          <Typography variant="body1" sx={FormResentModalStyles.caption}>
            {languageConfig.new_joinees.settings.modals.joiningFormResentModal.subtitle}
          </Typography>
          <Box>
            <Box sx={FormResentModalStyles.svgContainer.root}>
              <Box sx={FormResentModalStyles.svgContainer.body}>
                <SuccessIcon />
              </Box>
            </Box>
            <Button variant="contained" onClick={onClose} sx={FormResentModalStyles.button}>
              {languageConfig.new_joinees.settings.modals.joiningFormResentModal.button.confirm}
            </Button>
          </Box>
        </>
      )}
    </Box>
  </Modal>
);

export default JoiningFormResentModal;
