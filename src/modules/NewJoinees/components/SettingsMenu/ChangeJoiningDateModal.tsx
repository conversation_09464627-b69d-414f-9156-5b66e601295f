import { Box, Button, Grid, Typography } from "@mui/material";
import { useMutation } from "@tanstack/react-query";
import { format, parse } from "date-fns";
import React, { useMemo } from "react";
import languageConfig from "src/configs/language/en.lang";
import { useForm } from "src/customHooks/useForm";
import CustomDateField from "src/modules/Common/FormInputs/CustomDateField";
import CustomTextField from "src/modules/Common/FormInputs/CustomTextField";
import Modal from "src/modules/Common/Modal/Modal";
import candidateService from "src/services/candidate.service";
import validations from "src/utils/validators";
import { ModalProps } from ".";
import { AddNewJoinerModalStyles, DateFieldSlotProps, ModalControllerStyles } from "../styles/styles.module";

const formValidations = {
  firstName: [validations.validateInput, validations.validateName],
  lastName: [validations.validateInput, validations.validateName],
  personalEmail: [validations.validateInput, validations.validateEmail],
  joiningDate: [validations.validateInput],
};

interface ChangeJoiningDateModalProps extends ModalProps {
  handleModalId: (arg0: string) => void;
}

type DefaultFormState = {
  firstName: string;
  lastName: string;
  personalEmail: string;
  joiningDate: string;
};

const ChangeJoiningDateModal: React.FC<ChangeJoiningDateModalProps> = ({
  open,
  onClose,
  handleModalId,
  candidateInfo,
}) => {
  const defaultState: DefaultFormState = useMemo(() => {
    return {
      firstName: candidateInfo?.first_name || "error",
      lastName: candidateInfo?.last_name || "error",
      personalEmail: candidateInfo?.personal_email || "error",
      joiningDate: candidateInfo?.date_of_joining || "error",
    };
  }, [candidateInfo]);

  const { formDetails, formErrors, handleChange, setFormDetail } = useForm({
    initialState: defaultState as DefaultFormState,
    validations: formValidations,
  });

  const updateEmployeeMutation = useMutation({
    mutationKey: ["update-joining-date"],
    mutationFn: async (): Promise<string | null> =>
      candidateService.updateCandidateDetailsAPI(formDetails as DefaultFormState),
    onSuccess: (data: string | null) => {
      if (data) {
        handleModalId("changedJoiningDate");
        return;
      }
    },
  });

  const canInviteJoiner = React.useMemo(() => {
    const areAllFeildsFilled = Object.values(formDetails as DefaultFormState).every((detail) => !!detail);
    const areAllErrorsResolved = Object.values(formErrors as DefaultFormState).every((error) => !error);
    return areAllFeildsFilled && areAllErrorsResolved;
  }, [formErrors as DefaultFormState]);

  const onInviteCandidateClick = () => {
    updateEmployeeMutation.mutate();
  };

  return (
    <Modal
      isOpen={open}
      onClose={onClose}
      sx={ModalControllerStyles.root}
      PaperProps={{
        style: ModalControllerStyles.paper,
      }}
      title={languageConfig.new_joinees.settings.modals.changeJoiningDate.title}
      subtitle={languageConfig.new_joinees.settings.modals.changeJoiningDate.subtitle}
      showBackButton
    >
      <Box style={AddNewJoinerModalStyles.root}>
        <Box sx={AddNewJoinerModalStyles.body.container}>
          <Grid container spacing={1} sx={AddNewJoinerModalStyles.body.grid}>
            <Grid item xs={12} lg={6}>
              <Typography component="div" variant="body2" sx={AddNewJoinerModalStyles.textLabels}>
                {languageConfig.new_joinees.modals.addNewJoinees.textfields.firstName}
              </Typography>
              <CustomTextField
                disabled
                type={"text"}
                id="firstName"
                sx={{ width: "90%" }}
                onChange={handleChange}
                value={(formDetails as DefaultFormState).firstName}
                error={!!(formErrors as DefaultFormState).firstName}
                helperText={
                  (formErrors as DefaultFormState).firstName ? (formErrors as DefaultFormState).firstName : " "
                }
                size="small"
              />
            </Grid>
            <Grid item xs={12} lg={6}>
              <Typography component="div" variant="body2" sx={AddNewJoinerModalStyles.textLabels}>
                {languageConfig.new_joinees.modals.addNewJoinees.textfields.lastName}
              </Typography>
              <CustomTextField
                disabled
                type={"text"}
                id="lastName"
                sx={{ width: "90%" }}
                onChange={handleChange}
                value={(formDetails as DefaultFormState).lastName}
                error={!!(formErrors as DefaultFormState).lastName}
                helperText={(formErrors as DefaultFormState).lastName ? (formErrors as DefaultFormState).lastName : " "}
                size="small"
              />
            </Grid>
            <Grid item xs={12} lg={6}>
              <Typography component="div" variant="body2" sx={AddNewJoinerModalStyles.textLabels}>
                {languageConfig.new_joinees.modals.addNewJoinees.textfields.personalEmail}
              </Typography>
              <CustomTextField
                disabled
                type={"text"}
                id="personalEmail"
                sx={{ width: "90%" }}
                onChange={handleChange}
                value={(formDetails as DefaultFormState).personalEmail}
                error={!!(formErrors as DefaultFormState).personalEmail}
                helperText={
                  (formErrors as DefaultFormState).personalEmail ? (formErrors as DefaultFormState).personalEmail : " "
                }
                size="small"
              />
            </Grid>
            <Grid item xs={12} lg={6}>
              <Typography component="div" variant="body2" sx={AddNewJoinerModalStyles.textLabels}>
                {languageConfig.new_joinees.modals.addNewJoinees.textfields.joiningDate}
              </Typography>
              <CustomDateField
                defaultValue={
                  (formDetails as DefaultFormState).joiningDate !== "" &&
                  (formDetails as DefaultFormState).joiningDate !== undefined &&
                  (formDetails as DefaultFormState).joiningDate !== null
                    ? parse((formDetails as DefaultFormState).joiningDate as string, "yyyy-MM-dd", new Date())
                    : null
                }
                onChange={(date: Date | null) => {
                  if (date) {
                    setFormDetail("joiningDate", format(date, "yyyy-MM-dd"));
                  }
                }}
                slotProps={DateFieldSlotProps}
                views={["year", "month", "day"]}
              />
            </Grid>
          </Grid>
        </Box>
      </Box>
      <Box sx={AddNewJoinerModalStyles.buttonContainer}>
        <Button
          variant="contained"
          disabled={!canInviteJoiner}
          onClick={onInviteCandidateClick}
          sx={AddNewJoinerModalStyles.button}
        >
          {languageConfig.new_joinees.settings.modals.changeJoiningDate.button.update}
        </Button>
      </Box>
    </Modal>
  );
};

export default ChangeJoiningDateModal;
