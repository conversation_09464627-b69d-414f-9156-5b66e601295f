import { Box, Typography } from "@mui/material";
import React from "react";
import Modal from "src/modules/Common/Modal/Modal";
import { ActionModalStyles, ModalControllerStyles } from "../styles/styles.module";

interface ActionModalProps {
  open: boolean;
  title?: string;
  message: React.ReactNode;
  actions: React.ReactNode;
  onClose: () => void;
}

const ActionModal: React.FC<ActionModalProps> = ({ open, title, message, actions, onClose }) => {
  return (
    <Modal
      isOpen={open}
      showDivider={false}
      onClose={onClose}
      sx={ModalControllerStyles.root}
      PaperProps={{
        style: ModalControllerStyles.paper,
      }}
      title={title}
      actions={actions}
    >
      <Box sx={ActionModalStyles.root}>
        <Box sx={ActionModalStyles.body.container}>
          <Typography variant="body1" component={"div"}>
            {message}
          </Typography>
        </Box>
      </Box>
    </Modal>
  );
};

export default ActionModal;
