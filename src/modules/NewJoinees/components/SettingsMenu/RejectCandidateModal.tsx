import { Box, Button, DialogActions } from "@mui/material";
import { useMutation } from "@tanstack/react-query";
import React from "react";
import candidateService from "src/services/candidate.service";
import ActionModal from "./ActionModal";
import { ModalProps } from "./index";

interface RejectCandidateModalProps extends ModalProps {
  handleModalChange: (arg0: string) => void;
}

const RejectCandidateModal: React.FC<RejectCandidateModalProps> = ({
  candidateInfo,
  open,
  handleModalChange,
  onClose,
}) => {
  const rejectCandidateMutation = useMutation({
    mutationKey: ["reject-candidate"],
    mutationFn: async () => {
      if (!candidateInfo?.personal_email || candidateInfo?.personal_email === "N/A") return null;
      return candidateService.rejectCandidateAPI(candidateInfo?.personal_email);
    },
    onSuccess: (data: string | null) => {
      if (data) {
        handleModalChange("candidateRejected");
      }
    },
  });

  const onRejectCandidate = async () => {
    await rejectCandidateMutation.mutateAsync();
  };

  return (
    <ActionModal
      open={open}
      onClose={onClose}
      title={"Reject Candidate"}
      message={
        <Box sx={{ fontWeight: 500 }}>
          Are you sure you want to reject {candidateInfo?.first_name} {candidateInfo?.last_name}?
        </Box>
      }
      actions={
        <>
          <DialogActions sx={{ padding: 1, margin: 2, gap: 3 }}>
            <Button onClick={onClose} variant="outlined">
              Cancel
            </Button>
            <Button onClick={onRejectCandidate} variant="contained">
              Confirm
            </Button>
          </DialogActions>
        </>
      }
    />
  );
};

export default RejectCandidateModal;
