import { Box } from "@mui/material";
import React from "react";
import Modal from "src/modules/Common/Modal/Modal";
import ConvertToEmployee from "src/modules/Employees/ConvertToEmployee";
import { ModalControllerStyles, containerStyle } from "src/modules/Employees/EmployeeModalStyles";

type ConvertToEmployeeModalProps = {
  open: boolean;
  onClose: () => void;
  candidateEmail: string;
};

const ConvertToEmployeeModal: React.FC<ConvertToEmployeeModalProps> = ({ open, onClose, candidateEmail }) => {
  const onFormFinish = (isFinish: boolean) => {
    if (isFinish) onClose();
  };
  if (!candidateEmail) return;
  return (
    <Modal
      title="Onboarding Form"
      isOpen={open}
      onClose={onClose}
      showBackButton
      maxWidth="unset"
      sx={ModalControllerStyles.root}
      PaperProps={{ style: ModalControllerStyles.paper }}
    >
      <Box sx={containerStyle}>
        <ConvertToEmployee setFinishForm={onFormFinish} candidateEmail={candidateEmail} />
      </Box>
    </Modal>
  );
};

export default ConvertToEmployeeModal;
