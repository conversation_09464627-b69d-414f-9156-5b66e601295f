import { Collapse, Menu, MenuItem } from "@mui/material";
import { useMutation } from "@tanstack/react-query";
import React from "react";
import candidateService from "src/services/candidate.service";
import { SettingsMenuStyles } from "../styles/styles.module";
import CandidateRejectedModal from "./CandidateRejectedModal";
import ChangeJoiningDateModal from "./ChangeJoiningDateModal";
import ChangedJoiningDateModal from "./ChangedJoiningDateModal";
import ConvertToEmployeeModal from "./ConvertToEmployeeModal";
import JoiningFormResentModal from "./JoiningFormResentModal";
import RejectCandidateModal from "./RejectCandidateModal";
import { DataTableType } from "./index";

const options = [
  {
    title: "No show / Offer decline",
    modalId: "rejectCandidate",
  },
  {
    title: "Convert to employee",
    modalId: "convertToEmployee",
  },
  {
    title: "Resend link",
    modalId: "resendLink",
  },
  {
    title: "Change joining date",
    modalId: "changeJoiningDate",
  },
];

interface RenderModalProps {
  isModalOpen: boolean;
  modalId: null | string;
  candidateInfo: DataTableType | null;
  onClose: () => void;
  handleModalChange: (arg0: string) => void;
}

const RenderModal: React.FC<RenderModalProps> = ({
  isModalOpen,
  modalId,
  candidateInfo,
  handleModalChange,
  onClose,
}) => {
  const resendLinkMutation = useMutation({
    mutationKey: ["resend-candidate-link"],
    mutationFn: async () => {
      if (!candidateInfo?.personal_email || candidateInfo?.personal_email === "N/A")
        throw "Error: Candidate Email not found";
      return candidateService.resendLinktoCandidateAPI(candidateInfo?.personal_email);
    },
  });

  switch (modalId) {
    case "rejectCandidate":
      return (
        <RejectCandidateModal
          open={isModalOpen}
          handleModalChange={handleModalChange}
          onClose={onClose}
          candidateInfo={candidateInfo}
        />
      );
    case "candidateRejected":
      return <CandidateRejectedModal open={isModalOpen} onClose={onClose} candidateInfo={candidateInfo} />;
    case "changeJoiningDate":
      return (
        <ChangeJoiningDateModal
          open={isModalOpen}
          onClose={onClose}
          candidateInfo={candidateInfo}
          handleModalId={handleModalChange}
        />
      );
    case "changedJoiningDate":
      return <ChangedJoiningDateModal open={isModalOpen} onClose={onClose} candidateInfo={candidateInfo} />;
    case "resendLink":
      return (
        <JoiningFormResentModal
          onClick={() => resendLinkMutation.mutate()}
          open={isModalOpen}
          onClose={onClose}
          candidateInfo={candidateInfo}
          isLoading={resendLinkMutation.isLoading}
          isError={resendLinkMutation.isError}
        />
      );
    case "convertToEmployee":
      return (
        <ConvertToEmployeeModal
          open={isModalOpen}
          onClose={onClose}
          candidateEmail={candidateInfo?.personal_email || ""}
        />
      );
    default:
      return null;
  }
};

interface SettingsMenuProps {
  candidateInfo: DataTableType | null;
  anchorEl: null | HTMLElement;
  handleClose: () => void;
  refetch: () => void;
}

const SettingsMenu: React.FC<SettingsMenuProps> = ({ refetch, candidateInfo, anchorEl, handleClose }) => {
  const [modalId, setModalId] = React.useState<string | null>(null);
  const isModalOpen = React.useMemo(() => modalId !== null, [modalId]);

  const handleModalId = (id: string) => {
    setModalId(id);
  };

  const onClose = () => {
    setModalId(null);
    refetch();
  };

  return (
    <>
      <Menu
        elevation={2}
        id="long-menu"
        MenuListProps={{
          "aria-labelledby": "long-button",
          ...SettingsMenuStyles,
        }}
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleClose}
        anchorOrigin={{
          vertical: "bottom",
          horizontal: "right",
        }}
        transformOrigin={{
          vertical: "top",
          horizontal: "right",
        }}
      >
        <Collapse in={Boolean(anchorEl)} unmountOnExit>
          {options.map((option) => (
            <MenuItem
              key={option.title}
              onClick={() => {
                handleModalId(option.modalId);
                handleClose();
              }}
              sx={SettingsMenuStyles.menuitem}
            >
              {option.title}
            </MenuItem>
          ))}
        </Collapse>
      </Menu>
      <RenderModal
        candidateInfo={candidateInfo}
        modalId={modalId}
        isModalOpen={isModalOpen}
        handleModalChange={handleModalId}
        onClose={onClose}
      />
    </>
  );
};

export default SettingsMenu;
