import { Box, Input, Typography } from "@mui/material";
// import { Box, Input } from '@mui/material';
import React from "react";
import Dropzone, { DropEvent, FileRejection } from "react-dropzone";
import { CustomDropzoneContainer, styleConfig } from "./style";

export type FileDropVariants = "default" | "success" | "error" | "warning" | "disabled";

interface FileDropzoneProps {
  onFileDrop: <T extends File>(acceptedFiles: T[], fileRejections: FileRejection[], event: DropEvent) => void;
  acceptFileTypes?: Record<string, string[]>;
  height?: number | string;
  width?: number | string;
  files?: File[] | null;
  variant?: FileDropVariants;
  message?: string;
  disabled?: boolean;
}

const FileDropzone: React.FC<FileDropzoneProps> = ({
  variant = "default",
  onFileDrop,
  height = 200,
  width = "100%",
  message,
  files,
  acceptFileTypes,
  disabled = false,
}) => {
  const [isDragActive, setIsDragActive] = React.useState(false);
  const config = styleConfig(variant, message, files as File[]);

  return (
    <Dropzone
      accept={acceptFileTypes}
      onDrop={(acceptedFiles, fileRejections, event) => onFileDrop(acceptedFiles, fileRejections, event)}
      onDragEnter={() => setIsDragActive(true)}
      onDragLeave={() => setIsDragActive(false)}
      onDropAccepted={() => setIsDragActive(false)}
      onDropRejected={() => setIsDragActive(false)}
      // autoFocus
      disabled={disabled}
    >
      {({ getRootProps, getInputProps }) => (
        <CustomDropzoneContainer
          {...getRootProps()}
          sx={{
            height,
            width,
            borderRadius: "10px",
            border: `1px ${config.borderColor}`,
            backgroundColor: isDragActive ? config.backgroundActiveColor : config.backgroundColor,
          }}
        >
          <Input {...getInputProps()} color="primary" size="small" />
          <Box display="flex" flexDirection="column" justifyContent="center" alignItems="center" width="100%">
            <config.svgIcon />
            {files && files[0] && files[0].name && variant === "default" ? (
              <Typography>{files[0].name}</Typography>
            ) : (
              <config.displayText />
            )}
          </Box>
        </CustomDropzoneContainer>
      )}
    </Dropzone>
  );
};

export default FileDropzone;
