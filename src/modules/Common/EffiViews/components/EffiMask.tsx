import { Box } from "@mui/material";
import React from "react";

const EffiMask: React.FC<{
  isMasked: boolean;
  children: React.ReactNode;
}> = ({ children, isMasked }) => {
  return (
    <Box
      sx={{
        filter: isMasked ? "blur(4px)" : "none",
        opacity: isMasked ? 0.5 : 1,
        transition: "filter 0.3s ease, opacity 0.3s ease",
      }}
    >
      {children}
    </Box>
  );
};

export default EffiMask;
