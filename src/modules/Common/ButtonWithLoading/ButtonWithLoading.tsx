import { LoadingButton } from "@mui/lab";
import { Button, ButtonProps } from "@mui/material";
import React from "react";

interface ButtonWithLoadingProps extends ButtonProps {
  isLoading?: boolean;
  children: React.ReactNode;
}

export default function ButtonWithLoading({ isLoading, children, ...props }: ButtonWithLoadingProps) {
  return isLoading ? (
    <LoadingButton {...props} loading variant="outlined" sx={{ ...props.sx, backgroundColor: "white" }}>
      {children}
    </LoadingButton>
  ) : (
    <Button {...props}>{children}</Button>
  );
}
