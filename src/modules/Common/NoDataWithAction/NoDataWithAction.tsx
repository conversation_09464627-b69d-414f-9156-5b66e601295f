import { Box, Button, Typography } from "@mui/material";
import React from "react";

interface NoDataWithActionProps {
  title: string;
  actionText: string;
  onActionClick: () => void;
}

const NoDataWithAction: React.FC<NoDataWithActionProps> = ({ title, actionText, onActionClick }) => {
  return (
    <Box display="flex" flexDirection="column" justifyContent="center" alignItems="center" height={300} gap={2}>
      <Typography fontSize={16} color="#667085" textAlign="center">
        {title}
      </Typography>
      <Button variant="contained" color="primary" onClick={onActionClick}>
        {actionText}
      </Button>
    </Box>
  );
};

export default NoDataWithAction;
