import { Box, Container, Skeleton } from "@mui/material";
import React from "react";

const PageSkeleton = () => {
  return (
    <Container maxWidth="md">
      <Box sx={{ mt: 4 }}>
        {/* Top section with 3 rectangles */}
        <Box sx={{ display: "flex", justifyContent: "space-between", mb: 4 }}>
          {[1, 2, 3].map((i) => (
            <Skeleton key={i} variant="rectangular" width="30%" height={120} />
          ))}
        </Box>

        {/* Bottom section with paragraph loaders */}
        {[1, 2, 3].map((i) => (
          <Box key={i} sx={{ mb: 2 }}>
            <Skeleton variant="text" width="100%" height={20} />
            <Skeleton variant="text" width="100%" height={20} />
            <Skeleton variant="text" width="70%" height={20} />
          </Box>
        ))}

        <Box sx={{ display: "flex", justifyContent: "space-between", mb: 4 }}>
          {[1, 2, 3].map((i) => (
            <Skeleton key={i} variant="rectangular" width="30%" height={120} />
          ))}
        </Box>
        {/* Bottom section with paragraph loaders */}
        {[1, 2, 3].map((i) => (
          <Box key={i} sx={{ mb: 2 }}>
            <Skeleton variant="text" width="100%" height={20} />
            <Skeleton variant="text" width="100%" height={20} />
            <Skeleton variant="text" width="70%" height={20} />
          </Box>
        ))}
      </Box>
    </Container>
  );
};

export default PageSkeleton;
