import { Event as EventIcon } from "@mui/icons-material";
import { Box, Popover } from "@mui/material";
import { format } from "date-fns";
import React from "react";
import { DateRangePicker as DatePicker, RangeFocus } from "react-date-range";

import Span from "../Span/Span";
import "react-date-range/dist/styles.css"; // main style file
import "react-date-range/dist/theme/default.css"; // theme css file
import { getDateFormat } from "src/utils/dateUtils";
import { CustomInputLabel } from "../FormInputs/CustomInputLabel";

type DateRange = {
  startDate: string;
  endDate: string;
};

type Props = {
  title: string;
  value: DateRange;
  required?: boolean;
  onChange: (value: DateRange) => void;
  dateFormat?: string;
  error?: boolean;
  helperText?: string;
  minDate?: string;
  maxDate?: string;
  disabledDates?: string[];
  dayContentRenderer?: (day: Date) => React.ReactNode;
  onRangeFocusChange?: (focus: RangeFocus) => void;
  dragSelectionEnabled?: boolean;
};

const boxStyle = {
  height: "40px",
  width: "100%",
  border: "1px solid #D2D2D2",
  borderRadius: "4px",
  display: "flex",
  alignItems: "center",
  fontSize: "16px",
  fontWeight: "500",
  padding: "0 12px",
  cursor: "pointer",
};

const dateRangePickerStyle = {
  ".rdrDefinedRangesWrapper": {
    display: "none",
  },
  ".rdrDayToday .rdrDayNumber span:after": {
    background: "#007F6F",
  },
};

const DateRangePicker: React.FunctionComponent<Props> = ({
  value = {
    startDate: "",
    endDate: "",
  },
  onChange,
  title,
  required,
  dateFormat = "yyyy-MM-dd",
  error,
  helperText,
  minDate,
  maxDate,
  disabledDates,
  dayContentRenderer,
  onRangeFocusChange,
  dragSelectionEnabled,
}) => {
  const boxRef = React.useRef(null);
  const [isOpen, setIsOpen] = React.useState(false);

  const selectionRange = {
    startDate: value.startDate || new Date(),
    endDate: value.endDate || new Date(),
    key: "selection",
  };

  const handleChange = (ranges: { selection: typeof selectionRange }) => {
    const startDate = format(ranges.selection.startDate, dateFormat);
    const endDate = format(ranges.selection.endDate, dateFormat);
    onChange?.({ startDate, endDate });
  };

  return (
    <>
      <CustomInputLabel title={title} required={required} />
      <Box ref={boxRef} sx={boxStyle} onClick={() => setIsOpen(true)}>
        <Span>
          {format(selectionRange.startDate, "yyyy/MM/dd")} - {format(selectionRange.endDate, "yyyy/MM/dd")}
        </Span>
        <EventIcon sx={{ height: 24, width: 24, fill: "#00000089", marginLeft: "auto" }} />
      </Box>
      {error && <Span style={{ color: "red", fontSize: "12px" }}>{helperText}</Span>}
      <Popover
        open={isOpen}
        anchorEl={boxRef.current}
        onClose={() => setIsOpen(false)}
        anchorOrigin={{
          vertical: "bottom",
          horizontal: "left",
        }}
        transformOrigin={{
          vertical: "top",
          horizontal: "left",
        }}
      >
        <Box sx={dateRangePickerStyle}>
          <DatePicker
            inputRanges={[]}
            staticRanges={[]}
            onChange={handleChange}
            rangeColors={["#007F6F"]}
            ranges={[selectionRange] || []}
            startDatePlaceholder={selectionRange.startDate}
            endDatePlaceholder={selectionRange.endDate}
            minDate={minDate}
            maxDate={maxDate}
            disabledDates={disabledDates}
            dayContentRenderer={dayContentRenderer}
            {...(onRangeFocusChange ? { onRangeFocusChange: onRangeFocusChange } : {})}
            dragSelectionEnabled={dragSelectionEnabled}
            format={getDateFormat(["day", "month", "year"])}
          />
        </Box>
      </Popover>
    </>
  );
};

export default DateRangePicker;
