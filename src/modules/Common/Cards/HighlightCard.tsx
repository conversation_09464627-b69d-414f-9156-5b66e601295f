import { Box, Card, SxProps, Theme } from "@mui/material";
import React from "react";

interface HighlightCardProps {
  sx?: SxProps<Theme>;
  readonly children: JSX.Element[];
  readonly colorSchemes: string[];
}

const getRandomColor = (colorSchemes: string[]) => {
  const randomIndex = Math.floor(Math.random() * colorSchemes.length);
  return colorSchemes[randomIndex];
};

const HighlightCard: React.FC<HighlightCardProps> = ({ children, sx = {}, colorSchemes }) => {
  const [LHS, RHS] = children;
  return (
    <Card
      sx={{
        background: getRandomColor(colorSchemes),
        width: "100%",
        height: 60,
        borderRadius: 2,
        ...sx,
      }}
    >
      <Box display="flex" alignItems="center" justifyContent="space-between" sx={{ padding: "10px 16px" }}>
        <React.Fragment>{LHS}</React.Fragment>
        <React.Fragment>{RHS}</React.Fragment>
      </Box>
    </Card>
  );
};

export default HighlightCard;
