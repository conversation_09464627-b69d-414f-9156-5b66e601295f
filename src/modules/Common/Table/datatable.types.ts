export type ColumnConfig<T> = {
  customise?: (row: T) => React.ReactNode;
  columnName: keyof T;
  isSearchable: boolean;
  isVisible: boolean;
  title: string;
  width?: number | string;
};

type Paginator = {
  pageSize: number;
  pageNumber: number;
  setPagesize: () => void;
  setPageNumber: () => void;
};

export interface DataTableProps<T extends Record<string, unknown>> {
  data: T[];
  columnConfigs: ColumnConfig<T>[];
  totalRows: number;
  paginator?: Paginator;
}
