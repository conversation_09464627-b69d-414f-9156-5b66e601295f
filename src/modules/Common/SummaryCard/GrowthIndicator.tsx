import TrendingDownIcon from "@mui/icons-material/TrendingDown";
import TrendingUpIcon from "@mui/icons-material/TrendingUp";
import { Box, Chip, Typography } from "@mui/material";
import React from "react";

interface GrowthIndicatorProps {
  percentage: string | number | null;
  label: string | null;
  isPositive?: boolean;
}

const GrowthIndicator: React.FC<GrowthIndicatorProps> = ({ percentage, label, isPositive = true }) => {
  const iconColor = isPositive ? "#047857" : "#DF2121";
  const backgroundColor = isPositive ? "#D1FAE5" : "#FFEBEB";

  return (
    <Box display="flex" alignItems="center" gap={1}>
      <Chip
        size="small"
        icon={isPositive ? <TrendingUpIcon sx={{ fill: iconColor }} /> : <TrendingDownIcon sx={{ fill: iconColor }} />}
        label={
          <Typography fontWeight={600} sx={{ color: iconColor }}>
            {percentage}
          </Typography>
        }
        sx={{
          backgroundColor,
        }}
        variant="filled"
      />
      <Typography sx={{ color: iconColor }}>{label}</Typography>
    </Box>
  );
};

export default GrowthIndicator;
