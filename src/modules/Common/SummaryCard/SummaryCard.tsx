import { Box, Card, CardActions, CardContent, CardContentTypeMap, CardTypeMap, Paper, Typography } from "@mui/material";
import React from "react";
import EffiMask from "../EffiViews/components/EffiMask";

export interface EffiCardProps {
  title: string;
  value: string | number | boolean | null;
  actions?: React.ReactNode;
  width?: string | number;
  height?: string | number;
  cardProps?: CardTypeMap["props"];
  subBaseActions?: (value: string | number) => React.ReactNode;
  cardContentProps?: CardContentTypeMap["props"];
  isMasked?: boolean;
}

const EffiSummaryCard: React.FC<EffiCardProps> = ({
  title,
  value,
  actions,
  width = 250,
  cardProps = {},
  cardContentProps = {},
  isMasked = false,
  subBaseActions = null,
}) => {
  return (
    <Box sx={{ minWidth: width, width: "100%" }}>
      <Card variant="outlined" {...cardProps} sx={{ borderRadius: 4, ...cardProps?.sx }}>
        <CardContent {...cardContentProps}>
          <Box display="flex" flexDirection="column" gap={1}>
            <Typography variant="h5" color="textDarkText" fontWeight={600} fontSize={16}>
              {title}
            </Typography>
            <Box display="flex" justifyContent="space-between" alignItems="center">
              <EffiMask isMasked={isMasked}>
                <Typography variant="h4" color="textPrimaryText" fontWeight={600} fontSize={18}>
                  {value}
                </Typography>
              </EffiMask>
              {subBaseActions && <Box>{subBaseActions(value as string | number)}</Box>}
            </Box>
          </Box>
        </CardContent>
        {actions && <CardActions sx={{ p: 2 }}>{actions}</CardActions>}
      </Card>
    </Box>
  );
};

export default EffiSummaryCard;
