import React from "react";
import GrowthIndicator from "./GrowthIndicator";
import EffiSummaryCard, { EffiCardProps } from "./SummaryCard";

interface SummaryCardProps extends EffiCardProps {
  trend: string | number | null;
  trendLabel: string | null;
  isPositive?: boolean;
}

const TrendsSummaryCard: React.FC<SummaryCardProps> = ({ trend, trendLabel = null, isPositive = true, ...props }) => {
  return (
    <EffiSummaryCard
      {...props}
      actions={<GrowthIndicator percentage={trend} label={trendLabel} isPositive={isPositive} />}
    />
  );
};

export default TrendsSummaryCard;
