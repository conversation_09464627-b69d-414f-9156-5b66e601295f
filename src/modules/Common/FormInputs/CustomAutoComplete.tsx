import { Autocomplete, InputAdornment, ListItem, Paper, TextFieldProps, Tooltip } from "@mui/material";
import React from "react";
import { SearchIcon } from "src/assets/icons.svg";
import { EmployeeCellInfo } from "../EmployeeViews/EmployeeCellInfo";
import { CustomInputLabel } from "./CustomInputLabel";
import CustomTextField from "./CustomTextField";
import { SearchBarStyles } from "./styles";

interface CustomAutocompletePaperProps {
  children?: React.ReactNode;
}

const CustomAutocompletePaper: React.FC<CustomAutocompletePaperProps> = ({ children }) => {
  return <Paper sx={SearchBarStyles.paper.root}>{children}</Paper>;
};

type CustomAutocompleteProps = TextFieldProps & {
  title: string;
  required: boolean;
  options: string[];
  size?: "small" | "medium";
  width?: number | string;
  placeholder?: string;
  searchInputValue: string;
  setSearchInputValue: (arg0: string, originalRow?: any) => void;
  hideInputLabel?: boolean;
  isEmployeeView?: boolean;
};

const CustomAutocomplete: React.FC<CustomAutocompleteProps> = ({
  title,
  required,
  options,
  searchInputValue,
  setSearchInputValue,
  placeholder = "Search",
  width = "100%",
  size = "medium",
  hideInputLabel = false,
  isEmployeeView = false,
  ...otherProps
}) => {
  const [isTooltipOpen, setTooltipOpen] = React.useState({
    isHovered: false,
    isActive: false,
  });

  const handleTooltipTriggers = (name: "isHovered" | "isActive", newValue: boolean) => {
    setTooltipOpen((prevState) => {
      return {
        ...prevState,
        [name]: newValue,
      };
    });
  };

  const handleSearchInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchInputValue(event.target.value);
  };

  const renderOption = (props: any, option: any) => {
    if (isEmployeeView) {
      return (
        <ListItem {...props} style={{ ...props.style, ...SearchBarStyles.listItems }}>
          <EmployeeCellInfo
            name={option.name}
            jobTitle={option.jobTitle}
            employeeCode={option.employeeCode}
            displayPic={option.displayPic}
          />
        </ListItem>
      );
    }
    return (
      <ListItem {...props} style={{ ...props.style, ...SearchBarStyles.listItems }}>
        {option}
      </ListItem>
    );
  };

  return (
    <React.Fragment>
      {!hideInputLabel && <CustomInputLabel title={title} required={required} />}
      <Autocomplete
        disableClearable
        freeSolo
        size={size}
        onFocus={() => handleTooltipTriggers("isActive", true)}
        onBlur={() => handleTooltipTriggers("isActive", false)}
        onMouseEnter={() => handleTooltipTriggers("isHovered", true)}
        onMouseLeave={() => handleTooltipTriggers("isHovered", false)}
        options={options}
        getOptionLabel={(option) => (isEmployeeView ? (option as any).name : option)}
        sx={{
          ...SearchBarStyles.root,
          width: width,
        }}
        PaperComponent={CustomAutocompletePaper}
        inputValue={searchInputValue}
        onChange={(_, value) => {
          setSearchInputValue(isEmployeeView ? (value as any)?.name || "" : value || "", value);
        }}
        renderInput={(params) => (
          <Tooltip title={searchInputValue} arrow open={!isTooltipOpen.isActive && isTooltipOpen.isHovered}>
            <CustomTextField
              placeholder={placeholder}
              type="search"
              {...params}
              onChange={handleSearchInputChange}
              InputProps={{
                ...params.InputProps,
                startAdornment: (
                  <InputAdornment position="end" sx={{ marginLeft: "6px", marginRight: "0.2rem" }}>
                    <SearchIcon />
                  </InputAdornment>
                ),
              }}
              {...otherProps}
            />
          </Tooltip>
        )}
        renderOption={renderOption}
      />
    </React.Fragment>
  );
};

export { CustomAutocomplete };
