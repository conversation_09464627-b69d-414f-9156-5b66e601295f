import { DateView } from "@mui/x-date-pickers";
import { DatePicker, DatePickerProps } from "@mui/x-date-pickers/DatePicker";
import { isValid, parse } from "date-fns";
import React from "react";
import { getDateFormat } from "src/utils/dateUtils";
import { CustomInputLabel } from "./CustomInputLabel";

export interface CustomDateFieldProps extends DatePickerProps<Date> {
  title?: string;
  required?: boolean;
}

const defaultView: DateView[] = ["year", "month", "day"];

const CustomDateField: React.FC<CustomDateFieldProps> = ({ title, required = false, ...props }) => {
  const handleValue = (val?: string | Date | null) => {
    if (!val) {
      return null;
    }
    if (typeof val === "string") {
      const parsedDate = parse(val, "yyyy-MM-dd", new Date());
      return isValid(parsedDate) ? parsedDate : null;
    }
    return val;
  };

  return (
    <React.Fragment>
      <CustomInputLabel title={title} required={required} />
      <DatePicker
        label={""}
        views={defaultView}
        format={getDateFormat((props?.views || defaultView) as string[])}
        {...props}
        value={handleValue(props?.value)}
      />
    </React.Fragment>
  );
};

export default CustomDateField;
