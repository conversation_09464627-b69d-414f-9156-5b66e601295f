import { Visibility, VisibilityOff } from "@mui/icons-material";
import { IconButton, InputAdornment } from "@mui/material";
import React, { useState } from "react";
import CustomTextField from "./CustomTextField";

export const CustomPasswordField = ({
  title = "Password",
  placeholder = "Enter password",
  name = "password",
  required = false,
  labelProps,
  ...rest
}: {
  title?: string;
  placeholder?: string;
  name?: string;
  required?: boolean;
  labelProps?: any;
  [key: string]: any;
}) => {
  const [showPassword, setShowPassword] = useState(false);

  return (
    <CustomTextField
      size="small"
      fullWidth
      title={title}
      placeholder={placeholder}
      required={required}
      name={name}
      type={showPassword ? "text" : "password"}
      labelProps={labelProps}
      InputProps={{
        endAdornment: (
          <InputAdornment position="end">
            <IconButton onClick={() => setShowPassword((prev) => !prev)} edge="end" size="small">
              {showPassword ? <VisibilityOff /> : <Visibility />}
            </IconButton>
          </InputAdornment>
        ),
      }}
      {...rest}
    />
  );
};
