import {
  <PERSON>box,
  FormHelperText,
  ListItemText,
  ListSubheader,
  MenuItem,
  Select,
  SelectChangeEvent,
  Typography,
} from "@mui/material";
import React, { useEffect, useRef, useState } from "react";
import { Option } from "src/app/global";
import { CustomInputLabel } from "./CustomInputLabel";

const CustomMultiSelect = ({
  name,
  onChange,
  options,
  value,
  label,
  required = false,
  children,
  ...props
}: {
  name: string;
  onChange: (ev: SelectChangeEvent<string>) => void;
  options: Option<string, any>[];
  value: string[];
  label?: string;
  required?: boolean;
  [propName: string]: any;
}) => {
  const [searchTerm, setSearchTerm] = useState("");
  const [filteredOptions, setFilteredOptions] = useState(options);
  const searchInputRef = useRef<HTMLInputElement>(null);

  // Filter options based on search term
  useEffect(() => {
    if (searchTerm.trim() === "") {
      setFilteredOptions(options);
    } else {
      const filtered = options.filter((option) => option.label.toLowerCase().includes(searchTerm.toLowerCase()));
      setFilteredOptions(filtered);
    }
  }, [searchTerm, options]);

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
  };

  // Focus the search input when the dropdown is opened
  const handleSelectOpen = () => {
    // Use setTimeout to ensure the dropdown is fully rendered
    setTimeout(() => {
      if (searchInputRef.current) {
        searchInputRef.current.focus();
      }
    }, 10);
  };

  const handleChange = (event: SelectChangeEvent<string[]>) => {
    const { value, name } = event.target;
    const isAllSelected = value.length > filteredOptions.length;
    if (value.includes("select-all")) {
      const newEvent = {
        target: {
          name,
          value: isAllSelected ? [] : filteredOptions.map((option) => option.value) || [],
        },
      };
      onChange(newEvent as unknown as SelectChangeEvent<string>);
    } else {
      onChange(event as unknown as SelectChangeEvent<string>);
    }
  };

  return (
    <React.Fragment>
      <CustomInputLabel title={label} required={required} />
      <Select
        id={name}
        value={value as unknown as string[]}
        labelId={`label-${name}`}
        name={name}
        onChange={handleChange}
        onOpen={handleSelectOpen}
        displayEmpty
        renderValue={(selected) => {
          if (selected?.length === 0) {
            return (
              <Typography fontSize={16} color="gray">
                {props?.placeholder || "Select multiple entities"}
              </Typography>
            );
          }

          return (selected as unknown as string[]).join(", ");
        }}
        {...props}
      >
        <ListSubheader>
          <input
            ref={searchInputRef}
            autoFocus
            placeholder="Search..."
            style={{
              padding: "8px",
              width: "90%",
              border: "1px solid #ddd",
              borderRadius: "4px",
              margin: "8px",
            }}
            onChange={handleSearch}
            value={searchTerm}
            onClick={(e) => e.stopPropagation()}
            onKeyDown={(e) => {
              e.stopPropagation();
              // Prevent dropdown from closing on Enter key
              if (e.key === "Enter") {
                e.preventDefault();
              }
            }}
          />
        </ListSubheader>
        {searchTerm.length === 0 && (
          <MenuItem key="select-all" value="select-all" disabled={false}>
            <Checkbox checked={value.length === filteredOptions.length}></Checkbox>
            <ListItemText primary="Select All" />
          </MenuItem>
        )}
        {filteredOptions.map((option) => (
          <MenuItem key={option.value} value={option.value} disabled={option?.disabled}>
            <Checkbox checked={value.includes(option.value)}></Checkbox>
            <ListItemText primary={option.label} />
          </MenuItem>
        ))}
      </Select>
      {props?.helperText && props?.error && (
        <FormHelperText error={!!props?.error}>{props?.helperText || ""}</FormHelperText>
      )}
    </React.Fragment>
  );
};

export default CustomMultiSelect;
