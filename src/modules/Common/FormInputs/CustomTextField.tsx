import { styled, TextField, TextFieldProps, Tooltip } from "@mui/material";
import React from "react";
import { CustomInputLabel } from "./CustomInputLabel";

const disabledStyle = {
  background: "#0000000c",
  color: "#0000000c",
  ".Mui-disabled": {
    background: "none !important",
  },
};

const CustomTextField = ({
  title,
  labelProps,
  ...props
}: TextFieldProps & {
  showTooltip?: boolean;
  labelProps?: any;
}) => {
  const [isTooltipOpen, setTooltipOpen] = React.useState({
    isHovered: false,
    isActive: false,
  });

  const handleTooltipTriggers = (name: "isHovered" | "isActive", newValue: boolean) => {
    setTooltipOpen((prevState) => {
      return {
        ...prevState,
        [name]: newValue,
      };
    });
  };

  return (
    <React.Fragment>
      <CustomInputLabel labelProps={labelProps} title={title} required={props?.required} />
      <Tooltip
        title={props.value as React.ReactNode}
        arrow
        open={!!props?.showTooltip && !isTooltipOpen.isActive && isTooltipOpen.isHovered}
      >
        <TextField
          onFocus={() => handleTooltipTriggers("isActive", true)}
          onBlur={() => handleTooltipTriggers("isActive", false)}
          onMouseEnter={() => handleTooltipTriggers("isHovered", true)}
          onMouseLeave={() => handleTooltipTriggers("isHovered", false)}
          sx={props.disabled ? disabledStyle : {}}
          autoComplete="off"
          {...props}
        />
      </Tooltip>
    </React.Fragment>
  );
};

export const CurrencyTextField = styled(CustomTextField)(() => ({
  minWidth: "200px",
  marginTop: 1,
  "& .MuiInputBase-input": {
    padding: "10px 16px",
  },
  "& .MuiOutlinedInput-root": {
    borderRadius: "8px",
  },
  "& .MuiOutlinedInput-root:hover": {
    borderColor: "primary.main",
  },
}));

export default CustomTextField;
