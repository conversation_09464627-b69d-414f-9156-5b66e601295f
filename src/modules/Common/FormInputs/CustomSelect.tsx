import { CircularProgress, MenuItem, Select, SelectChangeEvent, SxProps, Theme, Typography } from "@mui/material";
import React from "react";
import { CustomInputLabel } from "./CustomInputLabel";

const CustomSelect = ({
  name,
  sx,
  onChange,
  options = [],
  value,
  label,
  required = false,
  noDataMessage = "Nothing to see here",
  ...props
}: {
  name: string;
  sx?: SxProps<Theme>;
  onChange: (ev: SelectChangeEvent<string | boolean | number>) => void;
  options: { value: string | boolean | number; label: string }[];
  value: string;
  label?: string;
  required?: boolean;
  noDataMessage?: string | "Nothing to see here";
  [propName: string]: unknown;
}) => {
  return (
    <React.Fragment>
      <CustomInputLabel title={label} required={required} />
      <Select
        sx={{
          ...sx,
          color: `rgba(0,0,0,${value === "" ? 0.4 : 1})`,
        }}
        id={name}
        value={value}
        labelId={`label-${name}`}
        name={name}
        onChange={onChange}
        startAdornment={props?.isLoading ? <CircularProgress /> : null}
        renderValue={(selected: string) => {
          if (!selected || selected?.length === 0) {
            if (props?.placeholder) {
              return <Typography style={{ color: "gray" }}>{props?.placeholder || "Select"}</Typography>;
            }
            return <Typography style={{ color: "gray" }}>{`Select ${label}`}</Typography>;
          }

          const selectedOption = options?.find((option) => option.value === selected);
          return selectedOption?.label || selected;
        }}
        displayEmpty
        {...props}
      >
        {options?.length === 0 && <MenuItem disabled>{noDataMessage}</MenuItem>}
        {options?.length > 0 &&
          options?.map((option, index) => (
            <MenuItem
              value={option.value as string}
              key={`${option.value?.toString() + option.label + index}`}
              disabled={option.value === ""}
            >
              {option.label}
            </MenuItem>
          ))}
      </Select>
    </React.Fragment>
  );
};

export default CustomSelect;
