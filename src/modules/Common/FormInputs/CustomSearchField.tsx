import { TextFieldProps } from "@mui/material";
import React from "react";
import { OmniSearchResultKeys } from "src/services/api_definitions/search.service";
import { SearchAutoComplete } from "../../EmployeeSearch/SearchAutoComplete";
import { CustomInputLabel } from "./CustomInputLabel";

type CustomSearchFieldProps = TextFieldProps & {
  getData?: OmniSearchResultKeys;
  title: string;
  required: boolean;
  searchInputValue: string;
  setSearchInputValue: (arg0: string) => void;
  placeholder: string;
  width?: number | string;
  size?: "medium" | "small";
};

const CustomSearchField: React.FC<CustomSearchFieldProps> = ({
  getData,
  title,
  required,
  searchInputValue,
  setSearchInputValue,
  placeholder,
  width,
  size,
  ...props
}) => {
  return (
    <React.Fragment>
      <CustomInputLabel title={title} required={required} />
      <SearchAutoComplete
        getData={getData}
        size={size}
        width={width}
        searchInputValue={searchInputValue}
        setSearchInputValue={setSearchInputValue}
        placeholder={placeholder}
        {...props}
      />
    </React.Fragment>
  );
};

export default CustomSearchField;
