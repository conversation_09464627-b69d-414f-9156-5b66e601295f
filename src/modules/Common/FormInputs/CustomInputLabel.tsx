import { InputLabel } from "@mui/material";
import React from "react";

const inputLabelStyle = {
  fontSize: "14px",
  marginBottom: "8px",
};

type CustomInputLabelProps = {
  labelProps?: any;
  title?: string;
  required?: boolean;
  id?: string;
};

export const CustomInputLabel = ({ labelProps = {}, title, id = "", required }: CustomInputLabelProps) => {
  if (!title) return null;
  return (
    <InputLabel sx={{ ...inputLabelStyle, ...labelProps }} id={id || `label-${title}`}>
      {title}
      {required && <span style={{ color: "red" }}> *</span>}
    </InputLabel>
  );
};
