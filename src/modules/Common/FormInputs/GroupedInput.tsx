import { Box, TextField } from "@mui/material";
import * as React from "react";

export default function GroupedAOCodeInput({
  segmentLengths = [3, 2, 3, 2],
  value,
  onChange,
  separator = <div style={{ width: "10px", alignSelf: "center", color: "gray" }}>/</div>,
  inputProps = {},
  ...props
}: {
  segmentLengths?: number[];
  value: string[];
  onChange: (val: string[]) => void;
  separator?: React.ReactNode;
  inputProps?: React.InputHTMLAttributes<HTMLInputElement>;
}) {
  const inputRefs = React.useRef<HTMLInputElement[]>([]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>, segmentIndex: number) => {
    const maxLen = segmentLengths[segmentIndex];
    const trimmed = e.target.value.slice(0, maxLen).toUpperCase();

    const updated = [...value];
    updated[segmentIndex] = trimmed;
    onChange(updated);

    if (trimmed.length === maxLen && segmentIndex < segmentLengths.length - 1) {
      inputRefs.current[segmentIndex + 1]?.focus();
    }
  };

  return (
    <Box sx={{ display: "flex", gap: 1 }}>
      {segmentLengths.map((segLength, i) => (
        <React.Fragment key={i}>
          <TextField
            inputRef={(el) => (inputRefs.current[i] = el)}
            value={value[i] ?? ""}
            onChange={(e) => handleChange(e, i)}
            inputProps={{
              maxLength: segLength,
              style: {
                width: 70,
                height: 40,
                padding: "8px",
                fontSize: "1rem",
                textAlign: "center",
              },
              ...inputProps,
            }}
            variant="outlined"
            size="small"
            {...props}
          />
          {i !== segmentLengths.length - 1 && separator}
        </React.Fragment>
      ))}
    </Box>
  );
}
