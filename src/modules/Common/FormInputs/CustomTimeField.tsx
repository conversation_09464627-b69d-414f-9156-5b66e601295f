import { TimePicker, TimePickerProps } from "@mui/x-date-pickers/TimePicker";
import React from "react";
import { CustomInputLabel } from "./CustomInputLabel";
import { TimePickerStyle } from "./styles";

interface CustomTimeFieldProps extends TimePickerProps<Date> {
  title?: string;
  required?: boolean;
  error?: boolean;
  value: Date | undefined;
}

const CustomTimeField: React.FC<CustomTimeFieldProps> = ({ title, required = false, value, ...props }) => {
  return (
    <React.Fragment>
      <CustomInputLabel title={title} required={required} />
      <TimePicker ampm={false} value={value || null} sx={TimePickerStyle.root} {...props} />
    </React.Fragment>
  );
};

export default CustomTimeField;
