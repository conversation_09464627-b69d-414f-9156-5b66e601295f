import { DatePickerProps } from "@mui/x-date-pickers";
import { useQuery } from "@tanstack/react-query";
import { format, parse } from "date-fns";
import React, { useCallback } from "react";
import masterdataService from "src/services/masterdata.service";
import CustomDateField from "./CustomDateField";
import { CustomInputLabel } from "./CustomInputLabel";

type EffiMonthDatePickerProps = Omit<DatePickerProps<Date>, "value" | "onChange"> & {
  value: string;
  onChange: (value: string | null) => void;
};

const EffiMonthDatePicker: React.FC<EffiMonthDatePickerProps> = ({ value, onChange, ...props }) => {
  const parseValue = useCallback((): Date => {
    const parsedDate = parse(value, "MM-yyyy", new Date());
    return isNaN(parsedDate.getTime()) ? new Date() : parsedDate;
  }, [value]);
  const { data: calendarYears } = useQuery(
    ["get-year-list"],
    async () => {
      const years = await masterdataService.getACLs("CalendarYear");
      const currentYear = new Date().getFullYear();
      return years.filter((year) => parseInt(year) <= currentYear);
    },
    {
      cacheTime: 3600,
      refetchOnMount: true,
      refetchOnReconnect: true,
      refetchOnWindowFocus: false,
      staleTime: Infinity,
    },
  );
  const minDate = calendarYears?.length ? parse(`${calendarYears[0]}-01-01`, "yyyy-MM-dd", new Date()) : new Date();

  const maxDate = new Date();

  const onDateChange = useCallback(
    (date: Date | null) => {
      if (date) {
        onChange(format(date, "MM-yyyy"));
      }
    },
    [onChange],
  );

  return (
    <>
      <CustomInputLabel title={props.label} required={props.label} />
      <CustomDateField
        views={["month", "year"]}
        minDate={minDate}
        maxDate={maxDate}
        format="MMM yyyy"
        {...props}
        label=""
        value={parseValue()}
        onAccept={onDateChange}
      />
    </>
  );
};

export default EffiMonthDatePicker;
