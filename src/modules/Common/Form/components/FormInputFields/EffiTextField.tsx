import { Box, FormControl, TextField } from "@mui/material";
import React from "react";
import { useFieldContext } from "src/modules/Common/Form/effiFormContext";
import { CustomInputLabel } from "src/modules/Common/FormInputs/CustomInputLabel";

export type EffiTextFieldProps = {
  label: string;
  required?: boolean;
  size?: "small" | "medium";
  [key: string]: any;
};

const numberFormatter = (value: string, type: string) => {
  if (!value && type === "number") return null;
  const output = type === "number" ? Number(value) : value;
  return output;
};

const EffiTextField: React.FC<EffiTextFieldProps> = ({ label, required, size = "small", ...otherProps }) => {
  const field = useFieldContext();
  return (
    <Box display="flex" flexDirection="column">
      <CustomInputLabel title={label} id={field.name} required={required} data-testId={field.name} />
      <FormControl error={field.state?.meta?.errors?.length > 0}>
        <TextField
          name={field.name}
          id={field.name}
          fullWidth
          data-testId={field.name}
          value={numberFormatter(field.state.value as string, otherProps?.type)}
          onChange={(e) => field.handleChange(numberFormatter(e.target.value, otherProps?.type))}
          error={field.state?.meta?.errors?.length > 0}
          helperText={field.state?.meta?.errors?.map((err) => err.message)}
          size={size}
          autoComplete="off"
          {...otherProps}
        />
      </FormControl>
    </Box>
  );
};

export default EffiTextField;
