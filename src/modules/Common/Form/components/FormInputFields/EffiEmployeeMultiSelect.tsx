import { Autocomplete, Box, Chip, InputAdornment, ListItem, Paper } from "@mui/material";
import { UseQueryResult, useQuery } from "@tanstack/react-query";
import React, { useState } from "react";
import { SearchIcon } from "src/assets/icons.svg";
import useDebounce from "src/customHooks/useDebounce";
import { CustomInputLabel } from "src/modules/Common/FormInputs/CustomInputLabel";
import CustomTextField from "src/modules/Common/FormInputs/CustomTextField";
import {
  OmniSearchResponse,
  OmniSearchResult,
  OmniSearchResultKeys,
} from "src/services/api_definitions/search.service";
import SearchServiceAPI from "src/services/search.service";
import { useFieldContext } from "../../effiFormContext";

interface EmployeeSearchPaperProps {
  children?: React.ReactNode;
}

const EmployeeSearchPaper: React.FC<EmployeeSearchPaperProps> = ({ children }) => {
  return (
    <Paper
      sx={{
        maxHeight: 200,
        overflow: "auto",
        "& .MuiAutocomplete-listbox": {
          padding: 0,
        },
      }}
    >
      {children}
    </Paper>
  );
};

interface EffiEmployeeMultiSelectProps {
  getData?: OmniSearchResultKeys;
  label: string;
  required?: boolean;
  placeholder?: string;
}

const EffiEmployeeMultiSelect: React.FC<EffiEmployeeMultiSelectProps> = ({
  getData = "email",
  label,
  required = false,
  placeholder = "Search employees",
}) => {
  const field = useFieldContext();
  const [inputValue, setInputValue] = useState("");
  const debouncedValue = useDebounce(inputValue, 400);

  const { data, isLoading }: UseQueryResult<OmniSearchResponse> = useQuery({
    queryKey: ["employee-search", debouncedValue],
    queryFn: async () => {
      if (debouncedValue) {
        const result = await SearchServiceAPI.searchValue(debouncedValue);
        return result || []; // Handle null case
      }
      return [];
    },
    enabled: !!debouncedValue,
    refetchOnWindowFocus: false,
  });

  const selectedEmails: string[] = Array.isArray(field.state.value) ? field.state.value : [];
  const searchOptions: OmniSearchResult[] = data || [];

  // Remove duplicates based on email to prevent selection issues
  const uniqueSearchOptions = searchOptions.filter(
    (option, index, self) => index === self.findIndex((o) => o[getData] === option[getData]),
  );

  // Filter out already selected options from search results
  const availableOptions = uniqueSearchOptions.filter((option) => !selectedEmails.includes(option[getData]));

  // Create a stable reference for selected options to prevent resets
  const selectedOptionsMap = new Map<string, OmniSearchResult>();

  // First, add any existing selected options from search results
  uniqueSearchOptions.forEach((option) => {
    if (selectedEmails.includes(option[getData])) {
      selectedOptionsMap.set(option[getData], option);
    }
  });

  // Then, add placeholder options for emails not found in current search
  selectedEmails.forEach((email) => {
    if (!selectedOptionsMap.has(email)) {
      selectedOptionsMap.set(email, {
        [getData]: email,
        value: email,
        email,
        href: "",
        code: "",
      });
    }
  });

  const selectedOptions = Array.from(selectedOptionsMap.values());

  const handleChange = (newValue: OmniSearchResult[]) => {
    // Extract emails and remove duplicates
    const emails = newValue
      .map((option) => option[getData])
      .filter((email, index, self) => self.indexOf(email) === index);

    field.handleChange(emails);

    // Clear the input after selection
    setInputValue("");
  };

  const handleSearchInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const newInputValue = event.target.value;
    setInputValue(newInputValue);
  };

  return (
    <Box display="flex" flexDirection="column">
      <CustomInputLabel title={label} id={field.name} required={required} data-testId={field.name} />
      <Autocomplete
        key={`autocomplete-${selectedEmails.join(",")}`}
        multiple
        options={availableOptions}
        value={selectedOptions}
        onChange={(_, newValue) => handleChange(newValue)}
        inputValue={inputValue}
        getOptionLabel={(option) => option.value || ""}
        isOptionEqualToValue={(option, value) => {
          // Ensure both option and value have the getData field and they match
          return option && value && option[getData] === value[getData];
        }}
        filterOptions={(options) => options} // Don't filter, let the API handle it
        slotProps={{
          paper: {
            component: EmployeeSearchPaper,
          },
        }}
        noOptionsText={
          debouncedValue
            ? uniqueSearchOptions.length > 0 && availableOptions.length === 0
              ? "All matching employees are already selected"
              : "No employees found"
            : "Type to search employees"
        }
        loading={isLoading && !!debouncedValue}
        renderTags={(tagValue, getTagProps) =>
          tagValue.map((option, index) => (
            <Chip
              label={option.value}
              {...getTagProps({ index })}
              key={option[getData]}
              size="small"
              sx={{ margin: 0.5 }}
            />
          ))
        }
        renderInput={(params) => (
          <CustomTextField
            {...params}
            placeholder={placeholder}
            size="small"
            onChange={handleSearchInputChange}
            error={field.state?.meta?.errors?.length > 0}
            helperText={field.state?.meta?.errors?.map((err) => err.message).join(", ")}
            slotProps={{
              input: {
                ...params.InputProps,
                startAdornment: (
                  <>
                    <InputAdornment position="start" sx={{ marginLeft: "6px", marginRight: "0.2rem" }}>
                      <SearchIcon />
                    </InputAdornment>
                    {params.InputProps?.startAdornment}
                  </>
                ),
              },
            }}
          />
        )}
        renderOption={(props, option) => (
          <ListItem
            {...props}
            style={{
              padding: "8px 16px",
              borderBottom: "1px solid #f0f0f0",
            }}
          >
            <Box display="flex" flexDirection="column">
              <Box fontWeight={500}>{option.value}</Box>
              {option.email && (
                <Box fontSize="0.875rem" color="text.secondary">
                  {option.email}
                </Box>
              )}
            </Box>
          </ListItem>
        )}
      />
    </Box>
  );
};

export default EffiEmployeeMultiSelect;
