import { Box, FormControl } from "@mui/material";
import React from "react";
import { CustomInputLabel } from "src/modules/Common/FormInputs/CustomInputLabel";
import { SearchAutoComplete } from "src/modules/EmployeeSearch/SearchAutoComplete";
import { useFieldContext } from "../../effiFormContext";

const EffiAutoComplete = ({ getData = "display_name", label, required, ...otherProps }: any) => {
  const field = useFieldContext();
  return (
    <Box display="flex" flexDirection="column">
      <CustomInputLabel title={label} id={field.name} required={required} data-testId={field.name} />
      <FormControl error={field.state?.meta?.errors?.length > 0}>
        <SearchAutoComplete
          searchInputValue={field.state.value?.searchResult || ""}
          setSearchInputValue={(searchResult: string, originalRow: any) => {
            field.handleChange({ searchResult, originalRow });
            // setSearchResponse(originalRow);
          }}
          getData={getData}
          {...otherProps}
        />
      </FormControl>
    </Box>
  );
};

export default EffiAutoComplete;
