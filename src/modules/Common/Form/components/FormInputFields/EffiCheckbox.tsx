import { InfoOutlined } from "@mui/icons-material";
import { Box, Checkbox, FormControlLabel, Tooltip, Typography } from "@mui/material";
import React from "react";
import { useFieldContext } from "../../effiFormContext";

type EffiCheckboxProps = {
  label: string;
  required?: boolean;
  infoText?: string;
  disabled?: boolean;
  size?: "small" | "medium";
  checkedHoverText?: string;
  uncheckedHoverText?: string;
};

const EffiCheckbox: React.FC<EffiCheckboxProps> = ({
  label,
  required,
  size = "small",
  infoText,
  checkedHoverText,
  uncheckedHoverText,
  ...otherProps
}) => {
  const field = useFieldContext();
  return (
    <Box display="flex" flexDirection="column">
      <FormControlLabel
        required={required}
        control={
          <Tooltip title={field.state.value ? checkedHoverText : uncheckedHoverText} arrow>
            <span>
              <Checkbox
                id={field.name}
                name={field.name}
                data-testId={field.name}
                checked={!!field.state.value}
                onChange={(_ev, checked) => field.handleChange(checked)}
                size={size}
                {...otherProps}
              />
            </span>
          </Tooltip>
        }
        label={
          <Box display="flex" alignItems="center">
            <Typography variant="body2">{label}</Typography>
            {infoText && (
              <Tooltip title={infoText} arrow>
                <InfoOutlined fontSize="small" sx={{ ml: 0.5, color: "gray" }} />
              </Tooltip>
            )}
          </Box>
        }
      />
    </Box>
  );
};

export default EffiCheckbox;
