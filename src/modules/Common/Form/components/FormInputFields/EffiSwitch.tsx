import { Box, FormControl, FormControlLabel, Switch } from "@mui/material";
import React from "react";
import { CustomInputLabel } from "src/modules/Common/FormInputs/CustomInputLabel";
import { useFieldContext } from "../../effiFormContext";

type EffiSwitchProps = {
  label: string;
  required?: boolean;
  vertical?: boolean;
};

const EffiSwitch: React.FC<EffiSwitchProps> = ({ label, required, vertical = false }) => {
  const field = useFieldContext();
  if (vertical) {
    return (
      <Box display="flex" flexDirection="column">
        <CustomInputLabel title={label} id={field.name} required={required} data-testId={field.name} />
        <FormControl>
          <Switch
            id={field.name}
            name={field.name}
            data-testId={field.name}
            checked={!!field.state.value}
            onChange={(_ev, checked) => field.handleChange(checked)}
          />
        </FormControl>
      </Box>
    );
  }
  return (
    <Box display="flex" flexDirection="column">
      <FormControlLabel
        label={label}
        required={required}
        control={
          <Switch
            id={field.name}
            name={field.name}
            data-testId={field.name}
            checked={!!field.state.value}
            onChange={(_ev, checked) => field.handleChange(checked)}
          />
        }
      />
    </Box>
  );
};

export default EffiSwitch;
