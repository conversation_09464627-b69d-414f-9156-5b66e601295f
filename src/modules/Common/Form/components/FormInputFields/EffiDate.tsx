import { Box, FormControl } from "@mui/material";
import { DatePicker, DatePickerSlotProps, DateView } from "@mui/x-date-pickers";
import { format, parse } from "date-fns";
import React, { useMemo } from "react";
import { useFieldContext } from "src/modules/Common/Form/effiFormContext";
import { CustomInputLabel } from "src/modules/Common/FormInputs/CustomInputLabel";
import { getDateFormat } from "src/utils/dateUtils";

export type EffiDateProps = DatePickerSlotProps<Date, true> & {
  label: string;
  required?: boolean;
  size?: "small" | "medium";
};

const defaultView: DateView[] = ["year", "month", "day"];

const EffiDate: React.FC<EffiDateProps> = ({ label, required, size = "small", ...otherProps }) => {
  const field = useFieldContext();
  const dateParser = field?.state?.value
    ? useMemo(() => parse(field?.state?.value as string, "yyyy-MM-dd", new Date()), [field?.state?.value])
    : null;
  return (
    <Box display={"flex"} flexDirection="column">
      <CustomInputLabel title={label} id={field.name} required={required} />
      <FormControl error={field.state?.meta?.errors?.length > 0}>
        <DatePicker
          views={defaultView}
          format={getDateFormat(((otherProps as any)?.views || defaultView) as string[])}
          value={dateParser}
          onChange={(date) => {
            if (date) {
              field.handleChange(format(date, "yyyy-MM-dd"));
            }
          }}
          slotProps={{
            textField: {
              size,
              id: field.name,
              fullWidth: true,
              name: field.name,
              error: field.state?.meta?.errors?.length > 0,
              helperText: field.state?.meta?.errors?.map((err) => err.message),
            },
          }}
          {...otherProps}
        />
      </FormControl>
    </Box>
  );
};

export default EffiDate;
