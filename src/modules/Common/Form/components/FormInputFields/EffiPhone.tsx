import { Box, TextField } from "@mui/material";
import React from "react";
import { CustomInputLabel } from "src/modules/Common/FormInputs/CustomInputLabel";
import { CountrySelect } from "src/modules/Common/PhoneInput/CountrySelect";
import { useFieldContext } from "../../effiFormContext";

const textFieldStyle = {
  ".MuiInputBase-root": {
    padding: 0,
    input: {
      padding: "8.5px 14px",
    },
  },
};

type EffiPhoneProps = {
  label: string;
  required?: boolean;
  disabled?: boolean;
  placeholder?: string;
  width?: string;
};

const EffiPhone: React.FC<EffiPhoneProps> = ({ label, disabled, placeholder, required, width = "100%" }) => {
  const field = useFieldContext();
  return (
    <Box display="flex" flexDirection="column">
      <CustomInputLabel title={label} id={field.name} required={required} data-testId={field.name} />
      <TextField
        type="tel"
        size="small"
        label=""
        value={(field.state.value as any)?.phone}
        disabled={disabled}
        placeholder={placeholder}
        onChange={(e) =>
          field.handleChange({
            ...(field.state.value as any),
            phone: e.target.value,
          })
        }
        slotProps={{
          input: {
            startAdornment: (
              <CountrySelect
                key={(field.state.value as any)?.countryCode}
                disabled={disabled}
                countryCode={(field.state.value as any)?.countryCode}
                handleChange={(countryCode) =>
                  field.handleChange({
                    ...(field.state.value as any),
                    countryCode,
                  })
                }
              />
            ),
          },
        }}
        sx={{ ...textFieldStyle, width }}
        error={field.state?.meta?.errors?.length > 0}
        helperText={field.state?.meta?.errors?.map((err) => err.message)}
      />
    </Box>
  );
};

export default EffiPhone;
