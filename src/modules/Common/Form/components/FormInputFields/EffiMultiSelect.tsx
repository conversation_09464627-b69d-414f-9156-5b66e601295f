import { Checkbox, FormHelperText, ListItemText, MenuItem, Select, SelectChangeEvent, Typography } from "@mui/material";
import { Box } from "@mui/system";
import React, { useState } from "react";
import { Option } from "src/app/global";
import { CustomInputLabel } from "src/modules/Common/FormInputs/CustomInputLabel";
import { useFieldContext } from "../../effiFormContext";

const EffiMultiSelect = ({
  options,
  label,
  required = false,
  size = "small",
  ...props
}: {
  options: Option<string, any>[];
  label?: string;
  required?: boolean;
  size?: "small" | "medium";
  [propName: string]: any;
}) => {
  const field = useFieldContext();

  const onChange = (event: SelectChangeEvent<string[]>) => {
    const { value } = event.target;
    const isAllSelected = value.length > options?.length;
    if (value.includes("select-all")) {
      field.handleChange(isAllSelected ? [] : options?.map((option) => option.value) || []);
    } else {
      field.handleChange(typeof value === "string" ? value.split(",") : value || []);
      if (isAllSelected) {
        field.handleChange(options?.map((option) => option.value) || []);
      }
    }
  };
  return (
    <Box display="flex" flexDirection="column">
      <CustomInputLabel title={label} id={field.name} required={required} />
      <Select
        id={field.name}
        value={(field.state.value as string[]) || []}
        size={size}
        name={field.name}
        onChange={onChange}
        label=""
        error={field.state?.meta?.errors?.length > 0}
        displayEmpty
        multiple
        renderValue={(selected: string[]) => {
          if (selected?.length === 0) {
            return (
              <Typography fontSize={16} color="gray">
                {props?.placeholder || "Select multiple entities"}
              </Typography>
            );
          }

          return (selected as unknown as string[])?.join(", ");
        }}
        {...props}
      >
        <MenuItem key="select-all" value="select-all" disabled={false}>
          <Checkbox checked={field.state.value?.length === options?.length}></Checkbox>
          <ListItemText primary="Select All" />
        </MenuItem>
        {options?.map((option) => (
          <MenuItem key={option.value} value={option.value} disabled={option?.disabled}>
            <Checkbox checked={(field.state.value as string[])?.includes(option.value)}></Checkbox>
            <ListItemText primary={option.label} />
          </MenuItem>
        ))}
      </Select>
      {props?.helperText && props?.error && (
        <FormHelperText error={!!props?.error}>
          {field.state?.meta?.errors?.map((err) => err.message) || ""}
        </FormHelperText>
      )}
    </Box>
  );
};

export default EffiMultiSelect;
