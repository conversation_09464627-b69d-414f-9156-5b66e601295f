import { InputAdornment } from "@mui/material";
import React from "react";
import EffiTextField from "../EffiTextField";

type EffiPercentageFieldProps = {
  label: string;
  required?: boolean;
  size?: "small" | "medium";
  endHelperText?: string;
  [key: string]: any;
};

const EffiPercentageField: React.FC<EffiPercentageFieldProps> = ({
  label,
  required,
  endHelperText = "",
  size = "small",
  ...otherProps
}) => {
  return (
    <EffiTextField
      label={label}
      required={required}
      size={size}
      {...otherProps}
      type="number"
      min={0}
      max={100}
      slotProps={{
        input: {
          endAdornment: <InputAdornment position="end">% {endHelperText}</InputAdornment>,
        },
      }}
    />
  );
};

export default EffiPercentageField;
