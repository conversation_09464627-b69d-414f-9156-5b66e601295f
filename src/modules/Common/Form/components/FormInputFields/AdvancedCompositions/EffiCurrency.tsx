import { InputAdornment, Tooltip } from "@mui/material";
import React from "react";
import { ToWords } from "to-words";
import EffiTextField from "../EffiTextField";

type EffiCurrencyProps = {
  label: string;
  required?: boolean;
  size?: "small" | "medium";
  currency?: string;
  endHelperText?: string;
  [key: string]: any;
};

const getCurrencySymbol = (currencyCode: string): string => {
  try {
    const formatter = new Intl.NumberFormat("en", {
      style: "currency",
      currency: currencyCode,
      currencyDisplay: "narrowSymbol", // or 'symbol' for full
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    });

    // This will format something like "$100", "₹100", "£100"
    const parts = formatter.formatToParts(1);
    const symbolPart = parts.find((p) => p.type === "currency");
    return symbolPart?.value || currencyCode;
  } catch {
    return currencyCode; // fallback
  }
};

export const formatCurrencyToSpelledOut = (amount: number): string => {
  if (!amount) {
    return "";
  }
  return new ToWords({
    localeCode: "en-IN",
    converterOptions: {
      currency: true,
      ignoreDecimal: false,
      ignoreZeroCurrency: false,
      doNotAddOnly: true,
      currencyOptions: {
        // can be used to override defaults for the selected locale
        name: "Rupee",
        plural: "Rupees",
        symbol: "₹",
        fractionalUnit: {
          name: "Paisa",
          plural: "Paise",
          symbol: "",
        },
      },
    },
  }).convert(amount);
};

const EffiCurrency: React.FC<EffiCurrencyProps> = ({
  label,
  required,
  size = "small",
  currency = "INR",
  endHelperText = "",
  ...otherProps
}) => {
  return (
    <Tooltip title={otherProps?.tooltip} hidden={!otherProps?.tooltip} arrow>
      <EffiTextField
        label={label}
        required={required}
        size={size}
        {...otherProps}
        type="number"
        inputProps={{
          readOnly: otherProps?.readOnly,
          sx: otherProps?.readOnly
            ? {
                cursor: "default", // Removes text cursor
                caretColor: "transparent", // Hides blinking caret
                userSelect: "none", // Optional: disables text selection
              }
            : {},
        }}
        slotProps={{
          input: {
            startAdornment: <InputAdornment position="start">{getCurrencySymbol(currency)}</InputAdornment>,
            endAdornment: <InputAdornment position="end">{endHelperText}</InputAdornment>,
            inputMode: "numeric",
          },
        }}
      />
    </Tooltip>
  );
};

export default EffiCurrency;
