import { InfoOutlined } from "@mui/icons-material";
import {
  Box,
  Checkbox,
  FormControl,
  FormControlLabel,
  IconButton,
  Radio,
  RadioGroup,
  Tooltip,
  Typography,
} from "@mui/material";
import React from "react";
import { useFieldContext } from "src/modules/Common/Form/effiFormContext";

type RadioOption = {
  value: string;
  label: string;
  subLabel?: string;
  disabled?: boolean;
};

type RadioGroupWithCheckboxValue = {
  checked: boolean;
  radio_group_value: string;
};

type EffiRadioGroupWithCheckboxProps = {
  label: string;
  checkbox_label: string;
  required?: boolean;
  options: RadioOption[];
  layout?: "horizontal" | "vertical";
  size?: "small" | "medium";
};

const EffiRadioGroupWithCheckbox: React.FC<EffiRadioGroupWithCheckboxProps> = ({
  label,
  required,
  options,
  layout = "vertical",
  size = "small",
  ...otherProps
}) => {
  const field = useFieldContext();

  // Initialize field value if it doesn't exist or is not in the expected format
  const fieldValue = (field.state.value as RadioGroupWithCheckboxValue) || { checked: false, radio_group_value: "" };
  const isCheckboxChecked = fieldValue.checked || false;
  const radioGroupValue = fieldValue.radio_group_value || "";

  const handleCheckboxChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const checked = event.target.checked;
    field.handleChange({
      checked,
      radio_group_value: checked ? radioGroupValue : "",
    });
  };

  const handleRadioChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    field.handleChange({
      checked: isCheckboxChecked,
      radio_group_value: event.target.value,
    });
  };

  const renderRadioOption = (option: RadioOption, otherProps: any) => {
    const radioControl = <Radio size={size} disabled={option.disabled || !isCheckboxChecked} />;

    const labelContent = (
      <Box display="flex" flexDirection="column">
        <Typography variant="body2" component="span">
          {option.label}
        </Typography>
        {option.subLabel && (
          <Tooltip title={option.subLabel}>
            <IconButton>
              <InfoOutlined fontSize="small" />
            </IconButton>
          </Tooltip>
        )}
      </Box>
    );

    return (
      <FormControlLabel
        key={option.value}
        value={option.value}
        control={radioControl}
        label={labelContent}
        disabled={option.disabled || !isCheckboxChecked || otherProps?.disabled}
      />
    );
  };

  return (
    <Box display="flex" flexDirection="column">
      <Box display="flex" alignItems="end">
        <FormControlLabel
          label={label}
          required={required}
          slotProps={{
            typography: {
              variant: "body2",
            },
          }}
          control={<Checkbox checked={isCheckboxChecked} onChange={handleCheckboxChange} size={size} />}
          {...otherProps}
        />
      </Box>
      <FormControl error={field.state?.meta?.errors?.length > 0} {...otherProps}>
        <RadioGroup
          name={field.name}
          value={radioGroupValue}
          onChange={handleRadioChange}
          row={layout === "horizontal"}
          sx={{
            flexDirection: layout === "horizontal" ? "row" : "column",
            display: !isCheckboxChecked ? "none" : "block",
            padding: "0px 16px",
          }}
          {...otherProps}
        >
          {options?.map((option) => renderRadioOption(option, otherProps))}
        </RadioGroup>
      </FormControl>
    </Box>
  );
};

export default EffiRadioGroupWithCheckbox;
