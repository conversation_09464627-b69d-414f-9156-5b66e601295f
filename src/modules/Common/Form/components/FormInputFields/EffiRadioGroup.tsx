import { InfoOutlined } from "@mui/icons-material";
import { Box, FormControl, FormControlLabel, IconButton, Radio, RadioGroup, Tooltip, Typography } from "@mui/material";
import React from "react";
import { CustomInputLabel } from "src/modules/Common/FormInputs/CustomInputLabel";
import { useFieldContext } from "../../effiFormContext";

type RadioOption = {
  value: string;
  label: string;
  subLabel?: string;
  // infoText?: string;
  disabled?: boolean;
};

type EffiRadioGroupProps = {
  label: string;
  required?: boolean;
  options: RadioOption[];
  layout?: "horizontal" | "vertical";
  size?: "small" | "medium";
};

const EffiRadioGroup: React.FC<EffiRadioGroupProps> = ({
  label,
  required,
  options,
  layout = "vertical",
  size = "small",
  ...otherProps
}) => {
  const field = useFieldContext();

  const renderRadioOption = (option: RadioOption) => {
    const radioControl = <Radio size={size} disabled={option.disabled} />;

    const labelContent = (
      <Box display="flex" alignItems="center">
        <Typography variant="body2" component="span">
          {option.label}
        </Typography>
        {option.subLabel && (
          <Tooltip title={option.subLabel} placement="top">
            <IconButton>
              <InfoOutlined fontSize="small" />
            </IconButton>
          </Tooltip>
        )}
      </Box>
    );

    return (
      <FormControlLabel
        key={option.value}
        value={option.value}
        control={radioControl}
        label={labelContent}
        disabled={option.disabled}
        sx={{
          alignItems: option.subLabel ? "flex-start" : "center",
          marginBottom: layout === "vertical" ? "8px" : "0",
          marginRight: layout === "horizontal" ? "16px" : "0",
          "& .MuiFormControlLabel-label": {
            marginTop: option.subLabel ? "0px" : "0px",
            marginLeft: "4px",
          },
          "& .MuiRadio-root": {
            paddingTop: option.subLabel ? "6px" : "9px",
            paddingBottom: "9px",
          },
        }}
      />
    );
  };

  return (
    <Box display="flex" flexDirection="column">
      <CustomInputLabel title={label} id={field.name} required={required} />
      <FormControl error={field.state?.meta?.errors?.length > 0} {...otherProps}>
        <RadioGroup
          name={field.name}
          value={field.state.value || ""}
          onChange={(event) => field.handleChange(event.target.value)}
          row={layout === "horizontal"}
          sx={{
            flexDirection: layout === "horizontal" ? "row" : "column",
          }}
          {...otherProps}
        >
          {options?.map(renderRadioOption)}
        </RadioGroup>
      </FormControl>
    </Box>
  );
};

export default EffiRadioGroup;
