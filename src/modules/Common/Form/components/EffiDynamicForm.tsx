import { Grid2 } from "@mui/material";
import React from "react";
import { HTMLInputTypeAttribute } from "react";
import EffiInputRenderer from "./EffiInputRenderer";

type FormInputTypes = "select" | "text" | "date" | "number" | HTMLInputTypeAttribute;

type FormProps = {
  type: FormInputTypes;
  label: string;
  required?: boolean;
  hidden?: boolean;
  size?: "small" | "medium";
  disabled?: boolean;
  helperText?: string;
  [key: string]: any;
};

export type InputFormStruct = {
  fieldProps?: any; // this is needed cause default field type is too big
  formProps: FormProps;
  containerProps?: {
    [key: string]: any;
  };
};

type EffiDynamicFormProps = {
  form: any; // this is needed cause default form type is too big
  inputFields: InputFormStruct[];
  formStyle?: React.CSSProperties;
  spacing?: number;
};

const EffiDynamicForm: React.FC<EffiDynamicFormProps> = ({ form, inputFields, formStyle, spacing = 2 }) => {
  if (inputFields?.length === 0) {
    throw new Error("Required: Please provide the ui structure to render");
  }
  return (
    <Grid2 container spacing={spacing} sx={formStyle}>
      {inputFields?.map(({ fieldProps = {}, formProps = {}, containerProps }) => (
        <EffiInputRenderer
          key={formProps.label}
          fieldProps={fieldProps}
          formProps={formProps}
          containerProps={containerProps}
          form={form}
        />
      ))}
    </Grid2>
  );
};

export default EffiDynamicForm;
