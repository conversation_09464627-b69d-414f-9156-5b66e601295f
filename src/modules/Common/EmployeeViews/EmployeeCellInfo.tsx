import { <PERSON><PERSON>, <PERSON>, <PERSON>, Typography } from "@mui/material";
import React from "react";

interface EmployeeCellInfoProps {
  name?: string;
  jobTitle?: string;
  employeeCode?: string;
  displayPic?: string;
  hideAvatar?: boolean;
  onLinkClick?: () => void;
}

export const EmployeeCellInfo: React.FC<EmployeeCellInfoProps> = ({
  name,
  jobTitle,
  employeeCode,
  displayPic,
  hideAvatar = false,
  onLinkClick,
}) => {
  return (
    <Box display="flex" gap={1} alignItems="center" width={"max-content"}>
      {!hideAvatar && <Avatar alt={name} src={displayPic || "#"} />}
      <Box display="flex" flexDirection="column">
        {onLinkClick ? (
          <Typography
            underline="hover"
            sx={{ cursor: "pointer" }}
            component={Link}
            color="primary"
            fontSize={14}
            onClick={onLinkClick}
          >
            {name}
          </Typography>
        ) : (
          <Typography fontSize={14}>{name}</Typography>
        )}
        <Typography fontSize={12} color="#667085">
          {jobTitle}
        </Typography>
        {employeeCode && (
          <Typography fontSize={12} color="#667085">
            {employeeCode}
          </Typography>
        )}
      </Box>
    </Box>
  );
};
