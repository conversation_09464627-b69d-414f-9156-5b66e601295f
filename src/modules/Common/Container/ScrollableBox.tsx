import { Box } from "@mui/material";
import React, { ReactNode } from "react";

interface ScrollableBoxProps {
  children: ReactNode;
  maxHeight: number;
  marginTop?: number;
}

const ScrollableBox: React.FC<ScrollableBoxProps> = ({ children, maxHeight, marginTop = 0 }) => {
  return (
    <Box
      maxHeight={maxHeight}
      overflow="hidden"
      marginTop={marginTop}
      sx={{ scrollbarGutter: "stable", "&:hover": { overflow: "auto" } }}
    >
      {children}
    </Box>
  );
};

export default ScrollableBox;
