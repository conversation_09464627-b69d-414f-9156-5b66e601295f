import { LockOutlined } from "@mui/icons-material";
import { Box, Container, Typography } from "@mui/material";
import React from "react";

const NoAccessScreen = () => {
  return (
    <Container maxWidth="sm" style={{ textAlign: "center", marginTop: "50px" }}>
      <Box display="flex" flexDirection="column" alignItems="center">
        <LockOutlined style={{ fontSize: 80, color: "gray" }} />
        <Typography variant="h4" component="h1" gutterBottom>
          No Access
        </Typography>
        <Typography variant="body1" color="textSecondary" paragraph>
          You don&apos;t have access to the contents of this screen
        </Typography>
        <Typography variant="caption" color="gray" paragraph>
          Please visit your administrator for screen permissions
        </Typography>
      </Box>
    </Container>
  );
};

export default NoAccessScreen;
