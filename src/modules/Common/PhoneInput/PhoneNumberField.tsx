import { TextField } from "@mui/material";
import React from "react";

import { PhoneNumberType } from "src/modules/Employees/types/FormDataTypes";
import { CustomInputLabel } from "../FormInputs/CustomInputLabel";
import { CountrySelect } from "./CountrySelect";

interface PhoneNumberFieldProps {
  title?: string;
  required?: boolean;
  placeholder?: string;
  value: PhoneNumberType;
  onChange: (value: PhoneNumberType) => void;
  error?: boolean;
  helperText?: string;
  width?: string;
  disabled?: boolean;
}

const textFieldStyle = {
  ".MuiInputBase-root": {
    padding: 0,
    input: {
      padding: "8.5px 14px",
    },
  },
};

const PhoneNumberField: React.FC<PhoneNumberFieldProps> = ({
  title,
  placeholder,
  required,
  value,
  onChange,
  error,
  helperText,
  disabled = false,
  width = "100%",
}) => {
  return (
    <>
      <CustomInputLabel title={title} required={required} />
      <TextField
        type="tel"
        size="small"
        value={value.number}
        disabled={disabled}
        placeholder={placeholder}
        onChange={(e) => onChange({ ...value, number: e.target.value })}
        InputProps={{
          startAdornment: (
            <CountrySelect
              key={value.countryCode}
              disabled={disabled}
              countryCode={value.countryCode}
              handleChange={(countryCode) => onChange({ ...value, countryCode })}
            />
          ),
        }}
        sx={{ ...textFieldStyle, width }}
        error={error}
        helperText={helperText}
      />
    </>
  );
};

export default PhoneNumberField;
