import { Box, Select } from "@mui/material";
import { MenuItem } from "@mui/material";
import { useQuery } from "@tanstack/react-query";
import React, { useState } from "react";
import locationService, { CountryListResponse } from "src/services/location.service";
import Span from "../Span/Span";

export interface CountrySelectProps {
  countryCode?: string;
  handleChange: (value: string) => void;
  disabled?: boolean;
}

const countrySelectStyle = {
  width: "30%",
  minWidth: "30%",
  background: "#f7f7f7",
  fieldset: {
    border: "none",
  },
  ".MuiSelect-select": {
    padding: "8px",
    paddingRight: "24px !important",
    textAlign: "center",
  },
  ".MuiSvgIcon-root": {
    right: 4,
  },
};

export const CountrySelect: React.FC<CountrySelectProps> = ({ countryCode, handleChange, disabled }) => {
  const { data: countryCodeList, isLoading } = useQuery(
    ["get-country-list"],
    async (): Promise<CountryListResponse[]> => locationService.getCountryList(),
  );
  const [selectedValue, setSelectedValue] = useState<string | undefined>(countryCode);

  if (isLoading || !countryCodeList) return null;

  return (
    <Select
      size="small"
      value={selectedValue}
      onChange={(e) => {
        setSelectedValue(e.target.value);
        handleChange(e.target.value);
      }}
      sx={countrySelectStyle}
      renderValue={(value: string) => {
        const country = countryCodeList.find((country) => country.mobile_prefix === value);
        return (
          <Box>
            {country && <img src={country.flag} style={{ width: "20px", height: "auto", marginRight: "2px" }} />}
            <Span sx={{ fontSize: "14px", fontWeight: 600 }}>{value}</Span>
          </Box>
        );
      }}
      placeholder="Country"
      disabled={disabled}
    >
      {countryCodeList &&
        countryCodeList.map((country) => (
          <MenuItem selected={country.mobile_prefix === selectedValue} key={country.name} value={country.mobile_prefix}>
            <img src={country.flag} style={{ width: "20px", height: "auto", marginRight: "5px" }} />(
            {country.mobile_prefix})&nbsp;
            {country.code}
          </MenuItem>
        ))}
    </Select>
  );
};
