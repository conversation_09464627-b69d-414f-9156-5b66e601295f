import { CloseSharp } from "@mui/icons-material";
import { Icon<PERSON>utton, PaperPropsVariantOverrides } from "@mui/material";
import DialogContent from "@mui/material/DialogContent";
import DialogTitle from "@mui/material/DialogTitle";
import { SxProps } from "@mui/material/styles";
import * as React from "react";
import ContentHeader from "../ContentHeader/ContentHeader";
import { BootstrapDialog, styles } from "./styles";

export interface CustomModalProps {
  isOpen: boolean;
  onClose: () => void;
  title?: React.ReactNode;
  subtitle?: React.ReactNode;
  showBackButton?: boolean;
  showDivider?: boolean;
  children: React.ReactNode;
  actions?: React.ReactNode;
  sx?: SxProps;
  maxWidth?: number | string;
  isLoading?: boolean;
  PaperProps?: PaperPropsVariantOverrides;
}

const Modal: React.FC<CustomModalProps & Record<string, unknown>> = ({
  isOpen = false,
  onClose,
  title,
  showBackButton = true,
  showDivider = true,
  children,
  actions,
  subtitle,
  sx,
  maxWidth = "820px",
  PaperProps,
  isLoading = false,
  ...otherProps
}) => {
  const handleClose = (reason: "backdropClick" | "escapeKeyDown") => {
    if (onClose && reason !== "backdropClick" && !isLoading) {
      onClose();
    }
  };

  return (
    <BootstrapDialog
      setmaxwidth={maxWidth}
      onClose={(_ev, reason) => handleClose(reason)}
      aria-labelledby="customized-dialog-title"
      open={isOpen}
      sx={sx}
      PaperProps={{
        sx: {
          borderRadius: 4,
        },
        ...PaperProps,
      }}
      {...otherProps}
    >
      {title || subtitle ? (
        <DialogTitle sx={{ m: 0, pl: 3 }} id="customized-dialog-title">
          <ContentHeader
            title={title}
            subtitle={subtitle}
            primaryAction={onClose}
            Icon={
              showBackButton && (
                <IconButton aria-label="close" disableRipple onClick={handleClose} sx={styles.closeButton}>
                  <CloseSharp />
                </IconButton>
              )
            }
          />
        </DialogTitle>
      ) : (
        <></>
      )}
      <DialogContent
        dividers={showDivider}
        // sx={{
        //   width: 100,
        // }}
      >
        {children}
      </DialogContent>
      {/* <DialogActions sx={{ display: 'flex' }}> */}
      {actions}
      {/* </DialogActions> */}
    </BootstrapDialog>
  );
};

export default Modal;
