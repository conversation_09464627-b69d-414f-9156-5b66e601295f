import Dialog from "@mui/material/Dialog";
import { styled } from "@mui/material/styles";

export const styles = {
  closeButton: {
    cursor: "pointer",
    padding: "4px",
    border: "1px solid #D5D7D8",
    borderRadius: "50px",
    color: "#D5D7D8",
    width: "32px",
    height: "32px",
    "&:hover": {
      color: "black",
      borderColor: "black",
    },
    transition: "0.195s all",
  },
};

interface BootstrapDialogProps {
  setmaxwidth: number | string;
}

export const BootstrapDialog = styled(Dialog, {
  shouldForwardProp: () => true,
})<BootstrapDialogProps>(({ theme, setmaxwidth }) => ({
  "& .MuiDialog-container": {
    "& .MuiPaper-root": {
      width: "100%",
      maxWidth: setmaxwidth,
    },
  },
  "& .MuiDialogContent-root": {
    padding: theme.spacing(2),
    width: "100%",
    height: "auto",
  },
  "& .MuiDialogActions-root": {
    padding: theme.spacing(1),
  },
}));
