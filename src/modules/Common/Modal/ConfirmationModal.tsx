import { Box, Button, DialogActions } from "@mui/material";
import React from "react";
import Modal from "./Modal";

interface ConfirmationModalProps {
  onSubmit: () => void;
  onCancel: () => void;
  title: string;
  isOpen: boolean;
  isSaveDisabled?: boolean;
  children?: React.ReactNode;
}

const ConfirmationModal: React.FC<ConfirmationModalProps> = ({
  onSubmit,
  onCancel,
  title,
  isOpen,
  isSaveDisabled,
  children,
  ...restModalProps
}) => {
  return (
    <Modal
      isOpen={isOpen}
      title={title}
      onClose={() => onCancel()}
      showDivider={false}
      actions={
        <DialogActions>
          <Box display="flex" gap={2} padding={2}>
            <Button onClick={onCancel} variant="outlined">
              Cancel
            </Button>
            <Button disabled={isSaveDisabled} onClick={onSubmit} variant="contained">
              Confirm
            </Button>
          </Box>
        </DialogActions>
      }
      {...restModalProps}
    >
      {children}
    </Modal>
  );
};

export default ConfirmationModal;
