import { Add } from "@mui/icons-material";
import { Button } from "@mui/material";
import React from "react";

interface AddMoreButtonProps {
  variant?: "text" | "outlined" | "contained";
  sx?: object;
  onClick: () => void;
  startIcon?: React.ReactNode;
  title?: string;
}

const AddMore: React.FC<AddMoreButtonProps> = ({
  variant = "text",
  sx = {},
  onClick,
  startIcon = <Add fontSize="small" />,
  title = "Add more",
}) => (
  <Button variant={variant} sx={sx} onClick={onClick} startIcon={startIcon}>
    {title}
  </Button>
);

export default AddMore;
