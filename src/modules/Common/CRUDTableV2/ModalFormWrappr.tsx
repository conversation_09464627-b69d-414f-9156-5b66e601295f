import React from "react";

import Modal from "src/modules/Common/Modal/Modal";
import { FormInputOption, FormInputType } from "src/modules/Employees/types/FormDataTypes";
import { CommonForm } from "../../Employees/components/CommonForm";

interface ModalFormWrapperProps {
  modalConfig: {
    isOpen: boolean;
    setIsOpen: (open: boolean) => void;
    formTitle: string;
  };
  inputElements: FormInputType[];
  onChange: (name: string, value: unknown) => void;
  selectOptions?: { [key: string]: FormInputOption[] | string[] };
  formValues: Record<string, unknown>;
  formErrors: Record<string, string>;
  disabledInputFields?: Record<string, boolean>;
  isViewOnlyMode?: boolean;
  gridStyles?: any;
  onSubmitClick?: () => void;
  formActions?: React.ReactNode;
}

export const ModalFormWrapper = ({ modalConfig, formActions, ...props }: ModalFormWrapperProps) => {
  return (
    <Modal
      title={modalConfig.formTitle}
      subtitle={""}
      showBackButton
      isOpen={modalConfig.isOpen}
      onClose={() => modalConfig.setIsOpen(false)}
      PaperProps={{ sx: { borderRadius: "20px" } }}
      actions={formActions}
      // fullWidth
    >
      <CommonForm {...props} />
    </Modal>
  );
};
