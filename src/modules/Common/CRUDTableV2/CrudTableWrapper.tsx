import React from "react";

import { Box, CircularProgress } from "@mui/material";
import { MRT_ColumnDef } from "material-react-table";
import ContentHeader from "src/modules/Common/ContentHeader/ContentHeader";
import DataTable from "src/modules/Common/Table/DataTable";

interface CRUDTableWrapperProps {
  tableHeaderTitle: string;
  onAddButtonClick?: () => void;
  isLoading?: boolean;
  tableColumns: MRT_ColumnDef<any>[];
  rowData: any[];
  enableRowNumbers?: boolean;
  getRowActions?: (row: any) => React.ReactNode;
  allowAdd?: boolean;
  addButtonText?: string;
}

export const CRUDTableWrapper = ({
  tableHeaderTitle,
  onAddButtonClick,
  isLoading = false,
  tableColumns,
  rowData,
  enableRowNumbers = true,
  getRowActions,
  allowAdd = true,
  addButtonText = "Add",
}: CRUDTableWrapperProps) => {
  return (
    <Box>
      <ContentHeader
        title={tableHeaderTitle}
        subtitle={""}
        primaryAction={onAddButtonClick}
        buttonTitle={addButtonText}
        allowAction={allowAdd}
      />
      <Box sx={{ margin: "20px 0px" }}>
        {isLoading ? (
          <Box sx={{ display: "flex", justifyContent: "center", alignItems: "center", height: "50vh" }}>
            <CircularProgress sx={{ width: "100%", margin: "auto" }} />
          </Box>
        ) : (
          <DataTable
            columns={tableColumns}
            data={rowData}
            enableRowActions
            enableStickyHeader
            enableEditing
            positionActionsColumn="last"
            enableRowNumbers={enableRowNumbers}
            renderBottomToolbar={false}
            renderRowActions={getRowActions}
          />
        )}
      </Box>
    </Box>
  );
};
