import React from "react";

import { Button, DialogActions, Typography } from "@mui/material";
import Modal from "src/modules/Common/Modal/Modal";

interface DeleteModalFormWrapperProps {
  formTitle: string;
  onDeleteSubmit: () => void;
  deleteModalOpen: boolean;
  setDeleteModalOpen: (open: boolean) => void;
  nextButtonText: string;
  question: string;
}

export const DeleteModalFormWrapper = ({
  formTitle,
  onDeleteSubmit,
  deleteModalOpen,
  setDeleteModalOpen,
  nextButtonText,
  question,
}: DeleteModalFormWrapperProps) => {
  return (
    <Modal
      title={formTitle}
      subtitle={""}
      isOpen={deleteModalOpen}
      showBackButton
      onClose={() => setDeleteModalOpen(false)}
      fullWidth
      actions={
        <DialogActions>
          <Button variant="outlined" onClick={() => setDeleteModalOpen(false)}>
            Cancel
          </Button>
          <Button variant="contained" onClick={onDeleteSubmit}>
            {nextButtonText || "Delete"}
          </Button>
        </DialogActions>
      }
    >
      <Typography>{question}</Typography>
    </Modal>
  );
};
