import { Delete, Download, Edit, Visibility } from "@mui/icons-material";
import { Box, IconButton } from "@mui/material";
import React from "react";

interface TableRowActionsProps {
  row: any;
  onEditClicked?: (row: any) => void;
  onDeleteClick?: (row: any) => void;
  onDownloadClick?: (row: any) => void;
  onPreviewClick?: (row: any) => void;
}

export const TableRowActions = ({
  row,
  onEditClicked,
  onDeleteClick,
  onDownloadClick,
  onPreviewClick,
}: TableRowActionsProps) => {
  const { hideEdit = false, hideDelete = false, hideDownload = false, hidePreview = false } = row.original;
  const allowRowEdit = onEditClicked && !hideEdit;
  const allowRowDelete = onDeleteClick && !hideDelete;
  const allowRowDownload = onDownloadClick && !hideDownload;
  const allowRowPreview = onPreviewClick && !hidePreview;

  return (
    <Box width={150}>
      {allowRowEdit && (
        <IconButton size="small" onClick={() => onEditClicked(row)}>
          <Edit />
        </IconButton>
      )}
      {allowRowDelete && (
        <IconButton size="small" onClick={() => onDeleteClick(row)}>
          <Delete />
        </IconButton>
      )}
      {allowRowDownload && (
        <IconButton size="small" onClick={() => onDownloadClick(row)}>
          <Download />
        </IconButton>
      )}
      {allowRowPreview && (
        <IconButton size="small" onClick={() => onPreviewClick(row)}>
          <Visibility />
        </IconButton>
      )}
    </Box>
  );
};
