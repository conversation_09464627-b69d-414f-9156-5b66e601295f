import { Button, DialogActions } from "@mui/material";
import React from "react";

interface FormActionsProps {
  onSubmitClick: () => void;
  cancelButtonText?: string;
  submitButtonText?: string;
  onCancelClick: () => void;
  disabled?: boolean | any;
  hideCancelButton?: boolean;
}

export const FormActions = ({
  onSubmitClick,
  cancelButtonText,
  submitButtonText,
  onCancelClick,
  disabled,
  hideCancelButton,
}: FormActionsProps) => {
  return (
    <DialogActions sx={{ margin: "16px" }}>
      {!hideCancelButton && (
        <Button variant="outlined" onClick={onCancelClick}>
          {cancelButtonText || "Cancel"}
        </Button>
      )}
      <Button variant="contained" onClick={onSubmitClick} disabled={disabled}>
        {submitButtonText || "Submit"}
      </Button>
    </DialogActions>
  );
};
