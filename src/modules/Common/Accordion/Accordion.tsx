import { ExpandMore } from "@mui/icons-material";
import {
  AccordionDetails,
  AccordionSummary,
  Accordion as MUIAccordion,
  AccordionProps as MUIAccordionProps,
  Paper,
  Typography,
} from "@mui/material";
import React, { ReactNode } from "react";
import Divider from "src/modules/Common/Divider";

interface AccordionProps extends MUIAccordionProps {
  heading: ReactNode;
  isOpen: boolean;
  handleChange: (event: React.SyntheticEvent, isExpanded: boolean) => void;
}

const Accordion: React.FC<AccordionProps> = ({ heading, children, sx, isOpen, handleChange, ...otherProps }) => (
  <MUIAccordion
    expanded={isOpen}
    onChange={handleChange}
    component={Paper}
    sx={{
      ...sx,
      background: "#F8F8F8",
      borderRadius: 4,
    }}
    {...otherProps}
  >
    <AccordionSummary expandIcon={<ExpandMore />}>
      <Typography color="text.secondary" fontWeight={600}>
        {heading}
      </Typography>
    </AccordionSummary>
    <Divider />
    <AccordionDetails sx={{ mt: 1 }}>{children}</AccordionDetails>
  </MUIAccordion>
);

export default Accordion;
