import { KeyboardArrowLeft, KeyboardArrowRight } from "@mui/icons-material";
import { Box, IconButton, Typography } from "@mui/material";
import React, { useMemo } from "react";

interface SwiperProps {
  currentValue: string;
  setCurrentValue: (currentValue: string) => void;
  options: string[];
}

const Swiper: React.FC<SwiperProps> = ({ currentValue, setCurrentValue, options }) => {
  const isFirst = useMemo(() => {
    return options?.findIndex((option) => option === currentValue) === 0;
  }, [currentValue, options]);
  const isLast = useMemo(
    () => options?.findIndex((option) => option === currentValue) === options?.length - 1,
    [currentValue, options],
  );

  const onPrev = () => {
    const index = options.findIndex((option) => option === currentValue);

    if (index === 0) {
      return;
    }
    setCurrentValue(options[index - 1]);
  };

  const onNext = () => {
    const index = options.findIndex((option) => option === currentValue);

    if (index === options?.length - 1) {
      return;
    }
    setCurrentValue(options[index + 1]);
  };

  return (
    <Box display="flex" gap={2} alignItems="center" height={20}>
      <IconButton onClick={onPrev} disabled={isFirst}>
        <KeyboardArrowLeft />
      </IconButton>
      <Typography>{currentValue}</Typography>
      <IconButton onClick={onNext} disabled={isLast}>
        <KeyboardArrowRight />
      </IconButton>
    </Box>
  );
};

export default Swiper;
