import { Clear, ContentCopy } from "@mui/icons-material";
import {
  Accordion,
  AccordionDetails,
  AccordionSummary,
  Alert,
  AlertProps,
  Box,
  Button,
  IconButton,
  Tooltip,
} from "@mui/material";
import React from "react";
import { AlertStyles } from "./styles";

interface AlertsAccordionProps extends AlertProps {
  message: string[];
  onClose: () => void;
}

const AlertsAccordion: React.FC<AlertsAccordionProps> = ({ severity, message, onClose }) => {
  const [isExpanded, setExpanded] = React.useState<boolean>(false);
  const [isCopied, setIsCopied] = React.useState<boolean>(false);

  const handleExpandedToggle = () => setExpanded((prevState) => !prevState);

  const copyToClipboard = () => {
    navigator.clipboard.writeText(message.join("\r\n"));
    setIsCopied(true);
  };

  return (
    <Alert
      severity={severity}
      sx={AlertStyles.root}
      action={
        <Box sx={AlertStyles.actionContainer}>
          <Tooltip title={isCopied ? "Copied !" : "Copy to Clipboard"} placement="top-start">
            <IconButton
              sx={AlertStyles.iconButton.root}
              onClick={copyToClipboard}
              onMouseLeave={() => {
                setTimeout(() => setIsCopied(false), 1000);
              }}
            >
              <ContentCopy sx={AlertStyles.iconButton.icon} />
            </IconButton>
          </Tooltip>
          <Tooltip title="Close" placement="top-start">
            <IconButton sx={AlertStyles.iconButton.root} onClick={onClose}>
              <Clear sx={AlertStyles.iconButton.icon} />
            </IconButton>
          </Tooltip>
        </Box>
      }
      onClose={onClose}
    >
      <Box sx={{ flexGrow: 1 }}>
        <Accordion expanded={isExpanded} disableGutters sx={AlertStyles.body.accordion}>
          <AccordionSummary expandIcon={<></>} sx={AlertStyles.body.accordionSummary}>
            <Box>The following {severity}(s) have been detected :</Box>
            <Box>
              <Button variant="text" sx={AlertStyles.body.expandButton} onClick={handleExpandedToggle}>
                {isExpanded ? "Hide view" : "View more"}
              </Button>
            </Box>
          </AccordionSummary>
          <AccordionDetails sx={{ padding: "8px 16px 0 16px" }}>
            {(message as string[]).map((error) => (
              <Box key={error}>{error}</Box>
            ))}
          </AccordionDetails>
        </Accordion>
      </Box>
    </Alert>
  );
};

export default AlertsAccordion;
