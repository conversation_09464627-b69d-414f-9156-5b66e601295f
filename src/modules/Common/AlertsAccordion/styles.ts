export const AlertStyles = {
  root: {
    color: "rgb(255, 77, 77)",
    border: "1px solid #FFC3C3",
    borderRadius: "5px",
    margin: "16px 0",
    display: "flex",
    flexDirection: "row",
    alignSelf: "center",
    fontSize: "12px",
  },
  body: {
    accordion: {
      background: "inherit",
      boxShadow: "none",
      color: "inherit",
    },
    accordionSummary: {
      minHeight: 0,
      "& .MuiAccordionSummary-content": {
        margin: 0,
        display: "flex",
        justifyContent: "row",
        gap: "16px",
        alignItems: "center",
      },
    },
    expandButton: {
      color: "inherit",
      fontSize: "inherit",
      textTransform: "none",
      fontWeight: 500,
      padding: "0 8px",
    },
  },
  actionContainer: {
    display: "flex",
    justifyContent: "flex-start",
    gap: "4px",
  },
  iconButton: {
    root: {
      color: "inherit",
      padding: "4px",
    },
    icon: {
      height: 18,
      width: 18,
    },
  },
};
