import React from "react";
import { Option } from "src/app/global";
import { useMasterData } from "src/customHooks/useMasterData";
import EffiDynamicForm, { InputFormStruct } from "src/modules/Common/Form/components/EffiDynamicForm";

const CreateSubscription = <T extends Record<string, any>>({ form }: T) => {
  const { data: subscriptionPlans } = useMasterData("SubscriptionPlan");
  const subscriptionPlanOptions: Option<string, unknown>[] = (subscriptionPlans || [])?.map((plan) => ({
    label: plan as string,
    value: plan,
    disabled: false,
  }));
  const inputFormElements: InputFormStruct[] = [
    {
      fieldProps: {
        name: "plan",
      },
      formProps: {
        type: "select",
        label: "Plan",
        required: true,
        options: subscriptionPlanOptions,
      },
    },
    {
      fieldProps: {
        name: "startDate",
      },
      formProps: {
        type: "date",
        label: "Start Date",
        required: true,
      },
    },
    {
      fieldProps: {
        name: "endDate",
      },
      formProps: {
        type: "date",
        label: "End Date",
        required: true,
      },
    },
    {
      fieldProps: {
        name: "monthlyChargePerEmployee",
      },
      formProps: {
        type: "number",
        label: "Monthly Charge Per Employee",
        required: true,
      },
    },
    {
      fieldProps: {
        name: "onboardingCharge",
      },
      formProps: {
        type: "number",
        label: "Onboarding Charge",
      },
    },
  ];
  return <EffiDynamicForm form={form} inputFields={inputFormElements} />;
};

export default CreateSubscription;
