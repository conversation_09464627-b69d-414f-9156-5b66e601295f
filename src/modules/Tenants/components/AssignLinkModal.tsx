import { Button, DialogActions } from "@mui/material";
import { useMutation } from "@tanstack/react-query";
import React, { useMemo } from "react";
import languageConfig from "src/configs/language/en.lang";
import { useForm } from "src/customHooks/useForm";
import CustomTextField from "src/modules/Common/FormInputs/CustomTextField";
import Modal from "src/modules/Common/Modal/Modal";
import { TenantDetailsModel } from "src/services/api_definitions/tenants";
import tenantsService from "src/services/tenants.service";
import validators from "src/utils/validators";

interface AssignLinkModalProps {
  readonly onClose: () => void;
  readonly isModalOpen: boolean;
  readonly refetch: () => void;
  readonly tenant: TenantDetailsModel | null;
}

type DefaultFormState = {
  tenantUrl: string;
};

const defaultFormState: DefaultFormState = {
  tenantUrl: "",
};

const AssignLinkModal: React.FC<AssignLinkModalProps> = ({ isModalOpen, onClose, refetch, tenant }) => {
  const { formDetails, formErrors, handleChange, areFormDetailsValid } = useForm({
    initialState: defaultFormState,
    validations: {
      tenantUrl: [validators.validateInput, validators.shouldNotContainSpecialCharacters],
    },
  });
  const typedFormErrors = formErrors as DefaultFormState;
  const assignLinkAssignMutation = useMutation({
    mutationKey: ["assign-link"],
    mutationFn: async () =>
      tenantsService.updateTenantUrl(tenant?.tenant_id || "", (formDetails as DefaultFormState).tenantUrl),
    onSuccess: () => {
      onClose();
      refetch();
    },
  });

  const areFormDetailsFilled = useMemo(() => {
    return Object.values(formDetails).every((detail) => !!detail);
  }, [formDetails]);

  const onSaveClick = () => {
    assignLinkAssignMutation.mutate();
  };

  return (
    <Modal
      title={languageConfig.tenants.assignLinkModalTitle}
      subtitle={languageConfig.tenants.costCenter.modalSubtitle}
      showBackButton
      isOpen={isModalOpen}
      onClose={onClose}
      fullWidth
      actions={
        <DialogActions sx={{ margin: 2 }}>
          <Button
            disabled={!areFormDetailsValid || !areFormDetailsFilled}
            size="large"
            variant="contained"
            onClick={onSaveClick}
          >
            {languageConfig.tenants.button.create}
          </Button>
        </DialogActions>
      }
    >
      <CustomTextField
        size="small"
        sx={{ flex: 6 }}
        placeholder="Assign url"
        fullWidth
        id="tenantUrl"
        value={(formDetails as DefaultFormState).tenantUrl}
        onChange={handleChange}
        error={!!typedFormErrors.tenantUrl}
        helperText={!!typedFormErrors.tenantUrl && typedFormErrors.tenantUrl}
      />
    </Modal>
  );
};

export default AssignLinkModal;
