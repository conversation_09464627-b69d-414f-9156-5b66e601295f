import { Delete } from "@mui/icons-material";
import { Box, CircularProgress, IconButton, Tooltip } from "@mui/material";
import { useMutation, useQuery } from "@tanstack/react-query";
import { MRT_Row } from "material-react-table";
import React, { lazy, Suspense, useCallback, useState } from "react";
import { useParams } from "react-router-dom";
import languageConfig from "src/configs/language/en.lang";
import ContentHeader from "src/modules/Common/ContentHeader/ContentHeader";
import DeleteConfirmationModal from "src/modules/Settings/components/Common/DeleteConfirmationModal";
import { WorkRole } from "src/services/api_definitions/employees";
import tenantsService from "src/services/tenants.service";
import { getACLFromFeaturekey } from "src/utils/screenUtils";
import AddEditWorkRole from "./AddEditWorkRole";
import ImportModal from "./ImportModal";

const LazyDataTable = lazy(() => import("src/modules/Common/Table/DataTable"));

interface TenantWorkRoleProps {
  featureKey: string;
  isAddWorkRoleFeatureActive?: boolean;
}

const TenantWorkRoles: React.FC<TenantWorkRoleProps> = ({ featureKey, isAddWorkRoleFeatureActive = false }) => {
  const TENANT_WORK_ROLE_ACL = getACLFromFeaturekey(featureKey);
  const { tenantId = "" } = useParams();
  const [selectedRow, setSelectedRow] = useState<string | null>();
  const [isModalOpen, setIsModalOpen] = useState<boolean>(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [isAddWorkRoleModalOpen, setIsAddWorkRoleModalOpen] = useState(false);
  const { data, isLoading, refetch, isFetching } = useQuery(
    ["work-roles"],
    async () => tenantsService.getWorkRoleDetails(tenantId),
    {
      retryOnMount: false,
      refetchOnWindowFocus: false,
      cacheTime: 0,
    },
  );

  const deleteMutation = useMutation({
    mutationFn: async (payload: string) => tenantsService.deleteWorkRole(payload),
    onSuccess: () => {
      refetch();
      setSelectedRow(null);
      setIsDeleteModalOpen(false);
    },
  });

  const onImportClick = () => {
    setIsModalOpen(true);
  };

  const onAddWorkRoleClick = () => {
    setIsAddWorkRoleModalOpen(true);
  };

  const onDeleteClick = (row: WorkRole) => {
    setSelectedRow(row.name);
    setIsDeleteModalOpen(true);
  };

  const getEditRow = useCallback(
    (row: MRT_Row<WorkRole>) => (
      <Box width={200}>
        <IconButton disabled={!TENANT_WORK_ROLE_ACL?.canWrite} onClick={() => onDeleteClick(row?.original)}>
          <Tooltip title="Delete">
            <Delete />
          </Tooltip>
        </IconButton>
      </Box>
    ),
    [TENANT_WORK_ROLE_ACL?.canWrite],
  );

  return (
    <React.Fragment>
      <ContentHeader
        title={languageConfig.tenants.workRoles.title}
        subtitle={""}
        buttonTitle={TENANT_WORK_ROLE_ACL.canWrite ? languageConfig.tenants.button.import : ""}
        secondaryButtonTitle={
          TENANT_WORK_ROLE_ACL.canWrite && isAddWorkRoleFeatureActive ? languageConfig.tenants.button.addWorkRole : ""
        }
        primaryAction={onImportClick}
        secondaryAction={onAddWorkRoleClick}
      />
      <Box sx={{ margin: "20px 0px" }}>
        <Suspense fallback={<CircularProgress />}>
          <LazyDataTable
            data={data || []}
            state={{
              showSkeletons: isLoading && isFetching,
            }}
            enableRowActions={isAddWorkRoleFeatureActive}
            positionActionsColumn="last"
            renderRowActions={(table) =>
              isAddWorkRoleFeatureActive ? getEditRow(table.row as unknown as MRT_Row<WorkRole>) : null
            }
            columns={[
              {
                accessorKey: "band.name",
                header: "Band",
              },
              {
                accessorKey: "level.name",
                header: "Level",
              },
              {
                accessorKey: "grade.name",
                header: "Grade",
              },
            ]}
          />
        </Suspense>
      </Box>
      {isModalOpen && TENANT_WORK_ROLE_ACL.canWrite && (
        <ImportModal refetch={refetch} isModalOpen={isModalOpen} onClose={() => setIsModalOpen(false)} />
      )}
      {isAddWorkRoleFeatureActive && isAddWorkRoleModalOpen && TENANT_WORK_ROLE_ACL.canWrite && (
        <AddEditWorkRole
          isModalOpen={isAddWorkRoleModalOpen}
          onClose={() => {
            setIsAddWorkRoleModalOpen(false);
            refetch();
          }}
          selectedRow={null}
        />
      )}
      {isDeleteModalOpen && selectedRow && (
        <DeleteConfirmationModal
          onDelete={() => deleteMutation.mutate(selectedRow)}
          onCancel={() => {
            setIsDeleteModalOpen(false);
            setSelectedRow(null);
          }}
          selectedRole={selectedRow}
          isModalOpen={isDeleteModalOpen}
          title="This action cannot be reversed"
        />
      )}
    </React.Fragment>
  );
};

export default TenantWorkRoles;
