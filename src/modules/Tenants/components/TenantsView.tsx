import { Delete } from "@mui/icons-material";
import { Box, Button, CircularProgress, IconButton, Tooltip, Typography } from "@mui/material";
import { useMutation, useQuery } from "@tanstack/react-query";
import { MRT_Row } from "material-react-table";
import React, { useCallback, useMemo, lazy, Suspense, useState } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import languageConfig from "src/configs/language/en.lang";
import { createColumnConfig } from "src/configs/table/tenants.table.config";
import { useForm } from "src/customHooks/useForm";
import ContentHeader from "src/modules/Common/ContentHeader/ContentHeader";
import { CommonForm } from "src/modules/Employees/components/CommonForm";
import { PATH_CONFIG } from "src/modules/Routing/config";
import DeleteConfirmationModal from "src/modules/Settings/components/Common/DeleteConfirmationModal";
import { TenantDetailsModel } from "src/services/api_definitions/tenants";
import tenantsService from "src/services/tenants.service";
import { getACLFromFeaturekey } from "src/utils/screenUtils";
import validators from "src/utils/validators";
import AssignLinkModal from "./AssignLinkModal";

const DataTable = lazy(() => import("src/modules/Common/Table/DataTable"));
const DEFAULT_FORM_VIEW = {
  tenantNameForConfirmation: "",
};

const TenantsView = () => {
  const tenantACL = getACLFromFeaturekey(PATH_CONFIG.TENANTS.key);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [selectedRow, setSelectedRow] = useState<TenantDetailsModel | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedTenant, setSelectedTenant] = useState<TenantDetailsModel | null>(null);
  const navigate = useNavigate();
  const location = useLocation();

  const { formDetails, formErrors, setFormDetail, areFormDetailsValid } = useForm({
    initialState: DEFAULT_FORM_VIEW,
    isBulk: false,
    validations: {
      tenantNameForConfirmation: [validators.validateInput],
    },
  });

  const typedFormDetails = formDetails as typeof DEFAULT_FORM_VIEW;

  const {
    data: tenantsData,
    isLoading,
    isFetching,
    refetch,
  } = useQuery(["get-tenant-details"], tenantsService.getTenants, {
    enabled: location.pathname === PATH_CONFIG.TENANTS_LIST.path,
    refetchOnWindowFocus: false,
  });

  const deleteMutation = useMutation({
    mutationFn: async () => tenantsService.deleteTenant(selectedRow?.tenant_id as string),
    onSuccess: () => {
      setSelectedRow(null);
      setIsDeleteModalOpen(false);
      setFormDetail("tenantNameForConfirmation", null);
      refetch();
    },
  });

  const onAssignLinkClick = (row: TenantDetailsModel | null) => {
    setSelectedTenant(row);
    setIsModalOpen(true);
  };

  const columnCallbackMap = useMemo(
    () =>
      new Map([
        ["organisation_name", (row: TenantDetailsModel) => navigate(`/tenants/${row.tenant_id}/${row.name}/edit?`)],
        [
          "auth_signatory_mobile",
          ({ auth_signatory_mobile }: TenantDetailsModel) => (
            <Typography>{`(${auth_signatory_mobile.country_code})${auth_signatory_mobile.number}`}</Typography>
          ),
        ],
        [
          "tenant_url",
          (row: TenantDetailsModel) => {
            if (row?.tenant_url) {
              return <Typography>{row?.tenant_url}</Typography>;
            }

            if (row?.status?.toLowerCase() === "inactive") {
              return null;
            }
            return (
              <Box display="flex" alignItems="center">
                <Button
                  disabled={row?.status?.toLowerCase() === "pending"}
                  variant="text"
                  onClick={() => onAssignLinkClick(row)}
                  sx={{ textTransform: "none", color: "#0031AF" }}
                >
                  Assign URL
                </Button>
              </Box>
            );
          },
        ],
      ]),
    [],
  );

  const columnConfigs = useCallback(() => createColumnConfig(columnCallbackMap), [columnCallbackMap]);

  const onDeleteClick = (row: TenantDetailsModel) => {
    setIsDeleteModalOpen(true);
    setSelectedRow(row);
  };

  const getEditRow = (row: MRT_Row<TenantDetailsModel>) => {
    if (row?.original?.status?.toLowerCase() === "inactive") {
      return null;
    }

    return (
      <Box display="flex" alignItems="center" width={200}>
        <IconButton onClick={() => onDeleteClick(row.original)}>
          <Tooltip title="Deactivate Tenant (Coming Soon!)">
            <Delete />
          </Tooltip>
        </IconButton>
      </Box>
    );
  };

  return (
    <>
      <ContentHeader title={languageConfig.tenants.title} subtitle={languageConfig.tenants.subtitle} />
      <Box sx={{ mt: "18px" }}>
        <Suspense fallback={<CircularProgress />}>
          <DataTable
            state={{
              showSkeletons: isLoading && isFetching,
            }}
            data={tenantsData || []}
            // biome-ignore lint/suspicious/noExplicitAny: <explanation>
            columns={columnConfigs() as any}
            positionActionsColumn="last"
            enableRowActions
            enableEditing
            renderRowActions={({ row }) => (
              <>{row.original?.isDefaultRole ? null : getEditRow(row as unknown as MRT_Row<TenantDetailsModel>)}</>
            )}
            enablePagination={false}
          />
          {isModalOpen && tenantACL?.canWrite && (
            <AssignLinkModal
              tenant={selectedTenant}
              isModalOpen={isModalOpen}
              onClose={() => setIsModalOpen(false)}
              refetch={refetch}
            />
          )}
          {isDeleteModalOpen && selectedRow && (
            <DeleteConfirmationModal
              onCancel={() => {
                setSelectedRow(null);
                setIsDeleteModalOpen(false);
              }}
              onDelete={() => {
                deleteMutation.mutate();
              }}
              title={`Please confirm that you want to delete the tenant by typing out its name as mentioned above`}
              isModalOpen={isDeleteModalOpen}
              selectedRole={selectedRow.name}
              isSaveDisabled={
                !areFormDetailsValid ||
                typedFormDetails?.tenantNameForConfirmation?.trim() !== selectedRow?.name?.trim()
              }
            >
              <Box margin="20px 0px">
                <CommonForm
                  formErrors={formErrors as Record<string, string>}
                  formValues={formDetails as Record<string, unknown>}
                  inputElements={[
                    {
                      name: "tenantNameForConfirmation",
                      label: "Confirm Tenant Deletion",
                      isRequired: true,
                      variant: "text",
                      width: "100%",
                    },
                  ]}
                  onChange={setFormDetail}
                />
              </Box>
            </DeleteConfirmationModal>
          )}
        </Suspense>
      </Box>
    </>
  );
};

export default TenantsView;
