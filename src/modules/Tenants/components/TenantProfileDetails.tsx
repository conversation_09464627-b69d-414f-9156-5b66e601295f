import { <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, Container, Di<PERSON>r, <PERSON><PERSON><PERSON>, Typography, styled } from "@mui/material";
import { nanoid } from "@reduxjs/toolkit";
import { useMutation, useQuery } from "@tanstack/react-query";
import React, { useMemo, useState } from "react";
import { useParams } from "react-router-dom";
import { OrganisationIcon } from "src/assets/icons.svg";
import { useForm } from "src/customHooks/useForm";
import { useMasterData } from "src/customHooks/useMasterData";
import FileDropzone, { FileDropVariants } from "src/modules/Common/FileDropzone/FileDropzone";
import { CommonForm } from "src/modules/Employees/components/CommonForm";
import { FormInputOption, FormInputType } from "src/modules/Employees/types/FormDataTypes";
import { apiRegister } from "src/services";
import fileuploaderService from "src/services/fileuploader.service";
import locationService, { ZIPCODE_APIResponse } from "src/services/location.service";
import tenantsService from "src/services/tenants.service";
import { showToast } from "src/utils/toast";
import validators from "src/utils/validators";

const CustomTypography = styled(Typography)(() => ({
  fontWeight: 600,
}));

const DEFAULT_FORM_STATE = {
  name: "",
  logo: "",
  auth_signatory_email: "",
  auth_signatory_name: "",
  auth_signatory_mobile: {
    number: "",
    countryCode: "",
  },
  domains: "",
  sso_types: [] as string[],
  modules: [] as string[],
};

const DEFAULT_ADDRESS_FORM_STATE = {
  address_line1: "",
  address_line2: "",
  state: "",
  city: "",
  zip_code: "",
  country: "",
};

const basicFormState: FormInputType[] = [
  {
    name: "name",
    label: "Name",
    variant: "text",
    isRequired: true,
  },
  {
    name: "auth_signatory_name",
    label: "Authorised Signatory Name",
    variant: "text",
    isRequired: true,
  },
  {
    name: "auth_signatory_email",
    label: "Authorised Signatory Email",
    variant: "text",
    isRequired: true,
  },
  {
    name: "auth_signatory_mobile",
    label: "Authorised Signatory Mobile",
    variant: "phone",
    isRequired: true,
  },
  {
    name: "domains",
    label: "Domain(s)",
    variant: "text",
    placeholder: "Please add comma seperated values",
    isRequired: true,
  },
  {
    name: "sso_types",
    label: "SSO Type",
    variant: "multi-select",
    isRequired: true,
  },
  {
    name: "modules",
    label: "Modules",
    variant: "multi-select",
    isRequired: true,
  },
];

const addressFormState: FormInputType[] = [
  {
    name: "address_line1",
    label: "Address Line 1",
    variant: "text",
    isRequired: true,
  },
  {
    name: "address_line2",
    label: "Address Line 2",
    variant: "text",
    isRequired: false,
  },
  {
    name: "zip_code",
    label: "Zip Code",
    variant: "text",
    isRequired: true,
  },
  {
    name: "city",
    label: "City",
    variant: "text",
    isRequired: true,
  },
  {
    name: "state",
    label: "State",
    variant: "text",
    isRequired: true,
  },
  {
    name: "country",
    label: "Country",
    variant: "text",
    isRequired: true,
  },
];

const TenantProfileDetails = () => {
  const [files, setFiles] = useState<File[] | null>(null);
  const [fileUploadResponseVariant, setFileUploadResponseVariant] = useState("default");
  const { data: moduleTypes = [] } = useMasterData("ModuleType");
  const { data: ssoTypes = [] } = useMasterData("SsoType");
  const moduleTypeOptions = useMemo(() => {
    return moduleTypes?.map((moduleType) => ({
      label: moduleType || "",
      value: moduleType || "",
    }));
  }, [moduleTypes]);

  const ssoTypeOptions = useMemo(() => {
    return ssoTypes?.map((ssoType) => ({
      label: ssoType || "",
      value: ssoType || "",
    }));
  }, [ssoTypes]);
  const { tenantId = "" } = useParams();
  const { data, refetch } = useQuery(["tenant-detail", tenantId], async () => tenantsService.getTenant(tenantId), {
    refetchOnWindowFocus: false,
    enabled: !!tenantId,
  });

  const getDetailsfromZipcode = useMutation({
    mutationKey: ["get-address-info"],
    mutationFn: (zipcode: string): Promise<ZIPCODE_APIResponse | null> =>
      locationService.getAddressDetailsFromZipcode(zipcode),
  });

  const defaultBaseInititialState = useMemo(() => {
    return {
      ...DEFAULT_FORM_STATE,
      auth_signatory_email: data?.auth_signatory_email,
      auth_signatory_name: data?.auth_signatory_name,
      name: data?.name,
      domains: data?.domains,
      sso_types: data?.sso_types,
      modules: data?.modules?.filter((eachModule) => eachModule.enabled)?.map((module) => module.module_type) || [],
      auth_signatory_mobile: {
        number: data?.auth_signatory_mobile.number,
        countryCode: data?.auth_signatory_mobile?.country_code,
      },
      logo: data?.logo,
    };
  }, [data]);

  const defaultBaseAddressState = useMemo(() => {
    return {
      ...DEFAULT_ADDRESS_FORM_STATE,
      address_line1: data?.address_line1,
      address_line2: data?.address_line2,
      city: data?.city,
      state: data?.state,
      zip_code: data?.zip_code,
      country: data?.country,
    };
  }, [data]);

  const {
    formDetails: basicFormDetails,
    formErrors: basicFormErrors,
    setFormDetail: setBasicFormDetail,
  } = useForm({
    initialState: defaultBaseInititialState,
    isBulk: false,
    validations: {
      auth_signatory_email: [validators.validateEmail, validators.validateInput],
      auth_signatory_name: [validators.validateInput],
      auth_signatory_mobile: [validators.validateInput, validators.validatePhone],
      logo: [validators.validateInput],
      name: [validators.validateInput],
      domains: [],
      sso_types: [],
      modules: [validators.validateInput],
    },
  });

  const {
    formDetails: addressFormDetails,
    formErrors: addressErrors,
    setFormDetail,
  } = useForm({
    initialState: defaultBaseAddressState,
    isBulk: false,
    validations: {
      address_line1: [validators.validateInput],
      address_line2: [],
      city: [validators.validateInput],
      state: [validators.validateInput],
      zip_code: [validators.validateInput, validators.validateZipCode],
      country: [validators.validateInput],
    },
  });

  const typedBasicFormDetails = basicFormDetails as unknown as typeof DEFAULT_FORM_STATE;
  const typedAddressFormDetails = addressFormDetails as unknown as typeof DEFAULT_ADDRESS_FORM_STATE;

  const tenantsUpdateMutation = useMutation({
    mutationFn: async () => {
      if (!typedBasicFormDetails?.auth_signatory_mobile.countryCode) {
        showToast("Please select country code", {
          type: "info",
        });
        return;
      }
      return tenantsService.updateTenant(tenantId, {
        address_line1: typedAddressFormDetails?.address_line1,
        address_line2: typedAddressFormDetails?.address_line2,
        city: typedAddressFormDetails?.city,
        state: typedAddressFormDetails?.state,
        country: typedAddressFormDetails?.country,
        zip_code: typedAddressFormDetails?.zip_code,
        auth_signatory_email: typedBasicFormDetails?.auth_signatory_email,
        auth_signatory_mobile: `${typedBasicFormDetails.auth_signatory_mobile.countryCode}${typedBasicFormDetails.auth_signatory_mobile.number}`,
        auth_signatory_name: typedBasicFormDetails?.auth_signatory_name,
        logo: typedBasicFormDetails?.logo,
        name: typedBasicFormDetails?.name,
        domains: typedBasicFormDetails?.domains,
        modules: typedBasicFormDetails?.modules,
        sso_types: typedBasicFormDetails?.sso_types,
      });
    },
    onSuccess: () => {
      refetch();
    },
  });

  const areModulesEqual = () => {
    const existingModules = new Set(
      data?.modules?.filter((eachModule) => eachModule.enabled)?.map((module) => module.module_type),
    );
    const newModules = new Set(typedBasicFormDetails?.modules);
    return existingModules.size === newModules.size && [...existingModules].every((item) => newModules.has(item));
  };
  const areSsoTypesEqual = () => {
    const existingSsoTypes = new Set(data?.sso_types);
    const newSsoTypes = new Set(typedBasicFormDetails.sso_types);
    return existingSsoTypes.size === newSsoTypes.size && [...existingSsoTypes].every((item) => newSsoTypes.has(item));
  };

  const enableSave = useMemo(() => {
    if (!data) {
      return false;
    }

    const haveAuthSignatoryMobileFieldsChanged =
      typedBasicFormDetails.auth_signatory_mobile.number !== data?.auth_signatory_mobile?.number ||
      typedBasicFormDetails.auth_signatory_mobile.countryCode !== data?.auth_signatory_mobile?.country_code;

    const haveBasicFieldsChanged = Object.keys(typedBasicFormDetails).some(
      (key) =>
        key !== "country_code" &&
        key !== "auth_signatory_mobile" &&
        key !== "sso_types" &&
        key !== "modules" &&
        typedBasicFormDetails[key as keyof typeof typedBasicFormDetails] !== data[key as keyof typeof data],
    );

    const haveAddressFieldsChanged = Object.keys(addressFormDetails).some(
      (key) => addressFormDetails[key as keyof typeof addressFormDetails] !== data[key as keyof typeof data],
    );
    return (
      haveBasicFieldsChanged ||
      haveAddressFieldsChanged ||
      haveAuthSignatoryMobileFieldsChanged ||
      !areModulesEqual() ||
      !areSsoTypesEqual()
    );
  }, [data, typedBasicFormDetails, addressFormDetails]);

  const uploadLogoAndGetPath = async (selectedFiles: File[]) => {
    const [_, extenstion] = selectedFiles[0].name.split(".");
    const hostname = data?.tenant_url?.split(".")[0];
    const fileName = `${!hostname ? btoa(data?.name as string) : hostname}-logo-${nanoid()}-tenant.${extenstion}`;
    const formData = new FormData();
    formData.append("file", selectedFiles[0]);
    formData.append("key", fileName);

    const fileUploadResponse = await fileuploaderService.uploadFile(apiRegister.AWS_S3.paths["upload-logo"], formData);
    setFileUploadResponseVariant(fileUploadResponse?.type);
    if (fileUploadResponse.type === "success") {
      return fileUploadResponse.message;
    }

    return null;
  };

  const onFileDrop = async (data: File[]) => {
    setFiles(data);
    const path = await uploadLogoAndGetPath(data);

    if (path) {
      setBasicFormDetail("logo", path);
    }
  };

  const onSave = () => {
    tenantsUpdateMutation.mutate();
  };

  const handleChange = async (name: string, value: unknown) => {
    setFormDetail(name, value);
    if (name === "zip_code" && (value as string).length >= 6) {
      const resp = await getDetailsfromZipcode.mutateAsync(value as string);
      if (resp) {
        Object.keys(resp).forEach((zipCodeDetailKey) =>
          setFormDetail(zipCodeDetailKey, resp[zipCodeDetailKey as keyof typeof resp]),
        );
      }
    }
  };

  return (
    <Container maxWidth="xl" disableGutters>
      {data?.status?.toLowerCase() === "inactive" && (
        <Alert sx={{ p: 2 }} severity="info">
          {" "}
          Cannot update tenant details as tenant is inactive{" "}
        </Alert>
      )}
      <Box display="flex" justifyContent="space-between" alignItems="flex-start" gap={2}>
        <Box display="flex" justifyContent="flex-start" alignItems="center" gap={2}>
          {data?.logo ? (
            <Tooltip title={`${data?.name}-logo`}>
              <img width={68} height={50} key={data?.name} src={data?.logo} alt={data?.name} />
            </Tooltip>
          ) : (
            <OrganisationIcon />
          )}
          <Box display="flex" flexDirection="column" gap={1}>
            <Typography fontSize={16}>Organisation Logo</Typography>
            <Typography fontSize={10} width={225}>
              Update your company logo and then choose where you want it to display
            </Typography>
          </Box>
        </Box>
        <FileDropzone
          files={files}
          acceptFileTypes={{
            "image/jpeg": [".jpg"],
            "image/png": [".png"],
            "image/svg+xml": [".svg"],
          }}
          height={150}
          onFileDrop={onFileDrop}
          variant={fileUploadResponseVariant as FileDropVariants}
        />
      </Box>
      <CustomTypography fontWeight={600}>Tenant Profile</CustomTypography>
      <Box margin="18px 0px 0px 0px" display="flex" flexDirection="column" gap={3}>
        <CommonForm
          formErrors={basicFormErrors as any}
          onChange={setBasicFormDetail}
          formValues={basicFormDetails as any}
          inputElements={basicFormState}
          selectOptions={{
            modules: moduleTypeOptions as FormInputOption[],
            sso_types: ssoTypeOptions as FormInputOption[],
          }}
        />
      </Box>
      <Divider sx={{ margin: "44px 0px" }} />
      <CustomTypography>Tenant Address Details</CustomTypography>
      <Box margin="18px 0px 0px 0px">
        <CommonForm
          formErrors={addressErrors as any}
          onChange={handleChange}
          formValues={addressFormDetails as any}
          inputElements={addressFormState}
          disabledInputFields={{
            state: true,
            country: true,
          }}
        />
      </Box>
      <Box display="flex" justifyContent="flex-end" sx={{ margin: "24px 0px" }}>
        <Button
          disabled={!enableSave || data?.status?.toLowerCase() === "inactive"}
          onClick={onSave}
          sx={{ textTransform: "none" }}
          variant="contained"
        >
          Save
        </Button>
      </Box>
    </Container>
  );
};

export default TenantProfileDetails;
