import { Box, Button, Grid2 } from "@mui/material";
import { useMutation, useQuery } from "@tanstack/react-query";
import React from "react";
import { useParams } from "react-router-dom";
import ContentHeader from "src/modules/Common/ContentHeader/ContentHeader";
import { useAppForm } from "src/modules/Common/Form/effiFormContext";
import tenantsService from "src/services/tenants.service";
import { z } from "zod";

interface TenantCompensationProps {
  featureKey: string;
  isAddWorkRoleFeatureActive?: boolean;
}

const submitSchema = z.object({
  aggregatorType: z.string({
    message: "required",
  }),
  attendanceBasis: z.string({
    message: "required",
  }),
  payrollMode: z.string({
    message: "required",
  }),
});

const compensationAggregates: React.FC<TenantCompensationProps> = () => {
  const { tenantId = "" } = useParams();
  const { data: compensationAggregation, refetch } = useQuery(
    ["compensation-aggregation"],
    async () => tenantsService.getCompensationAggregation(tenantId),
    {
      enabled: !!tenantId,
      refetchOnWindowFocus: false,
    },
  );

  const updateCompensationAggregation = useMutation({
    mutationKey: ["update-compensation-aggregation"],
    mutationFn: async (payload: any) =>
      tenantsService.updateCompensationAggregation({ ...payload }, tenantId, !!compensationAggregation),
    onSuccess: () => {
      refetch();
    },
  });

  const form = useAppForm({
    defaultValues: {
      aggregatorType: compensationAggregation?.compensation_basis || "ctc",
      attendanceBasis: compensationAggregation?.pay_days_basis || "Calendar Days",
      payrollMode: compensationAggregation?.pay_run_automated ? "Automated" : "Manual",
    },
    validators: {
      onChange: submitSchema,
    },
    onSubmit: (props) => {
      updateCompensationAggregation.mutate({
        aggregatorType: props.value.aggregatorType,
        attendanceBasis: props.value.attendanceBasis,
        payrollMode: props.value.payrollMode,
      });
    },
  });

  return (
    <Box display="flex" flexDirection="column" gap={3}>
      <ContentHeader
        title="Payroll Configuration"
        subtitle="Configure payroll settings for your organisation"
        actions={
          <form.Subscribe selector={(state) => [state.canSubmit, state.errorMap]}>
            {([canSubmit, errorMap]) => {
              return (
                <Button disabled={!canSubmit} variant="contained" onClick={form.handleSubmit}>
                  Save
                </Button>
              );
            }}
          </form.Subscribe>
        }
      />
      <Grid2 container spacing={2}>
        <Grid2 size={3}>
          <form.AppField name="aggregatorType">
            {(field: any) => (
              <field.EffiRadioGroup
                label="Compensation structure based on:"
                disabled={updateCompensationAggregation.isLoading}
                layout="vertical"
                required
                options={[
                  { label: "CTC", value: "ctc" },
                  { label: "Gross", value: "gross" },
                ]}
              />
            )}
          </form.AppField>
        </Grid2>
        <Grid2 size={3}>
          <form.AppField name="attendanceBasis">
            {(field: any) => (
              <field.EffiRadioGroup
                label="Attendance Basis"
                disabled={updateCompensationAggregation.isLoading}
                layout="vertical"
                required
                options={[
                  { label: "Calendar Days", value: "Calendar Days" },
                  { label: "Working Days", value: "Working Days" },
                ]}
              />
            )}
          </form.AppField>
        </Grid2>
        <Grid2 size={3}>
          <form.AppField name="payrollMode">
            {(field: any) => (
              <field.EffiRadioGroup
                label="Payroll Mode"
                disabled={updateCompensationAggregation.isLoading}
                layout="vertical"
                required
                options={[
                  { label: "Automated", value: "Automated" },
                  { label: "Manual", value: "Manual" },
                ]}
              />
            )}
          </form.AppField>
        </Grid2>
      </Grid2>
    </Box>
  );
};

export default compensationAggregates;
