import { Container } from "@mui/material";
import { useMutation, useQuery } from "@tanstack/react-query";
import { MRT_Row, MRT_TableInstance } from "material-react-table";
import React, { useMemo } from "react";
import { useParams } from "react-router-dom";
import { useForm } from "src/customHooks/useForm";
import DataTable from "src/modules/Common/Table/DataTable";
import { IntegrationScreen } from "src/services/api_definitions/tenants";
import tenantsService from "src/services/tenants.service";
import validators from "src/utils/validators";

const TenantIntegrations = () => {
  const { tenantId = "" } = useParams();
  const {
    data: integrations,
    isLoading,
    refetch,
  } = useQuery(["get-tenant-integrations"], async () => tenantsService.getTenantIntegrations(tenantId), {
    refetchOnWindowFocus: false,
  });

  const mutation = useMutation({
    mutationKey: ["update-tenant-url"],
    mutationFn: async ({ screen_name, redirect_url }: IntegrationScreen) =>
      tenantsService.updateTenantIntegrations(tenantId, screen_name, redirect_url as string),
    onSuccess: () => {
      refetch();
    },
  });

  const defaultFormState = useMemo(() => {
    return (
      integrations?.map((integration) => ({
        screen: integration?.screen_name || "",
        redirect_url: integration?.redirect_url || "",
      })) || []
    );
  }, [integrations]);

  const { formDetails, setFormDetail } = useForm({
    initialState: defaultFormState,
    isBulk: true,
    validations: {
      screen: [],
      redirect_url: [validators.validateInput],
    },
  });

  const typedFormDetails = formDetails as any[];

  const onSave = (props: {
    exitEditingMode: () => void;
    row: MRT_Row<IntegrationScreen>;
    table: MRT_TableInstance<IntegrationScreen>;
  }) => {
    mutation
      .mutateAsync({
        screen_name: props.row.original.screen_name,
        redirect_url: typedFormDetails[props.row.index]?.redirect_url,
      })
      .then(() => props.exitEditingMode())
      .catch(() => console.error({ props }));
  };

  return (
    <Container maxWidth="xl">
      <DataTable
        data={integrations || []}
        layoutMode="semantic"
        columns={[
          {
            accessorKey: "screen_name",
            header: "Screen Name",
            enableEditing: false,
            size: 200,
          },
          {
            accessorKey: "redirect_url",
            header: "Redirect URL",
            muiEditTextFieldProps: ({ row }) => {
              const isValid = !typedFormDetails[row?.index].redirect_url
                ? null
                : validators.validateURL(typedFormDetails[row?.index].redirect_url);
              return {
                required: true,
                value: typedFormDetails[row?.index].redirect_url,
                error: !!isValid?.message,
                helperText: !!isValid?.message && isValid?.message,
                onChangeCapture: (ev) => setFormDetail("redirect_url", (ev?.target as any)?.value || "", row.index),
              };
            },
          },
        ]}
        editDisplayMode="row"
        createDisplayMode="row"
        enableEditing
        enableRowActions
        onEditingRowSave={onSave}
        state={{
          showLoadingOverlay: isLoading,
        }}
      />
    </Container>
  );
};

export default TenantIntegrations;
