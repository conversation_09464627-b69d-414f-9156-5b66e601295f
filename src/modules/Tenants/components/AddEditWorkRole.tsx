import { Box, Button, DialogActions } from "@mui/material";
import { useMutation } from "@tanstack/react-query";
import React, { useMemo } from "react";
import { useForm } from "src/customHooks/useForm";
import Modal from "src/modules/Common/Modal/Modal";
import { CommonForm } from "src/modules/Employees/components/CommonForm";
import { FormInputType } from "src/modules/Employees/types/FormDataTypes";
import { WorkRole } from "src/services/api_definitions/employees";
import tenantsService from "src/services/tenants.service";
import validators from "src/utils/validators";

interface AddEditTenantProps {
  isModalOpen: boolean;
  onClose: () => void;
  selectedRow?: WorkRole | null;
}

const FORM_STRUCT: FormInputType[] = [
  {
    name: "band",
    isRequired: true,
    variant: "text",
    label: "Band",
  },
  {
    name: "level",
    isRequired: true,
    variant: "text",
    label: "Level",
  },
  {
    name: "grade",
    isRequired: false,
    variant: "text",
    label: "Grade",
  },
];

const DEFAULT_FORM_STATE = {
  band: null,
  level: null,
  grade: null,
};

const AddEditWorkRole: React.FC<AddEditTenantProps> = ({ isModalOpen, onClose, selectedRow }) => {
  const initialFormState = useMemo(() => {
    if (selectedRow) {
      return {
        ...DEFAULT_FORM_STATE,
        ...selectedRow,
      };
    }
    return {
      ...DEFAULT_FORM_STATE,
    };
  }, [selectedRow, DEFAULT_FORM_STATE]);

  const { formDetails, formErrors, setFormDetail, areFormDetailsValid } = useForm({
    initialState: initialFormState,
    validations: {
      band: [validators.validateInput],
      level: [validators.validateInput],
      grade: [],
    },
    isBulk: false,
  });

  const typedFormDetails = formDetails as WorkRole;
  const typedFormErrors = formErrors as Record<keyof WorkRole, string>;

  const insertWorkRole = useMutation({
    mutationFn: async () => tenantsService.addWorkRole(typedFormDetails),
    onSuccess: () => {
      onClose();
    },
  });

  const onSave = () => {
    insertWorkRole.mutate();
  };

  const onCancel = () => {
    onClose();
  };

  const isFormDisabled = useMemo(() => {
    const areRequiredFieldsFilled =
      Object.keys(formDetails).length !== 0 &&
      Object.keys(formDetails).every((key) => {
        if (key !== "grade") {
          return (typedFormDetails[key as keyof typeof typedFormDetails] as unknown as string) !== "";
        }
        return true;
      });
    return !areFormDetailsValid || !areRequiredFieldsFilled;
  }, [formDetails, formErrors, areFormDetailsValid]);

  return (
    <Modal
      isOpen={isModalOpen}
      onClose={onClose}
      showBackButton
      title={selectedRow ? "Add Work Role" : "Edit Tenant"}
      actions={
        <DialogActions>
          <Button onClick={onCancel} variant="outlined">
            Cancel
          </Button>
          <Button onClick={onSave} disabled={isFormDisabled} variant="contained">
            Save
          </Button>
        </DialogActions>
      }
    >
      <Box>
        <CommonForm
          formErrors={typedFormErrors}
          formValues={typedFormDetails as unknown as Record<string, unknown>}
          inputElements={FORM_STRUCT}
          onChange={setFormDetail}
        />
      </Box>
    </Modal>
  );
};

export default AddEditWorkRole;
