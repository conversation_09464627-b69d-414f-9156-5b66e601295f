import { CloudDownload, Folder } from "@mui/icons-material";
import { Box, DialogActions, Typography } from "@mui/material";
import { useMutation } from "@tanstack/react-query";
import React, { useState } from "react";
import { useParams } from "react-router-dom";
import languageConfig from "src/configs/language/en.lang";
import AlertsAccordion from "src/modules/Common/AlertsAccordion/AlertsAccordion";
import ButtonWithLoading from "src/modules/Common/ButtonWithLoading/ButtonWithLoading";
import FileDropzone, { FileDropVariants } from "src/modules/Common/FileDropzone/FileDropzone";
import Modal from "src/modules/Common/Modal/Modal";
import { FileUploadResponse } from "src/services/api_definitions/default.service";
import tenantsService from "src/services/tenants.service";
import { showToast } from "src/utils/toast";
import { ImportModalStyles, ModalControllerStyles } from "./styles/styles.module";

interface ImportModalProps {
  onClose: () => void;
  isModalOpen: boolean;
  refetch: () => void;
}

const ImportModal: React.FC<ImportModalProps> = ({ onClose, isModalOpen = false, refetch }) => {
  const { tenantId = "" } = useParams();
  const [files, setFiles] = useState<File[] | null>(null);
  const [fileDropzoneVariant, setFileDropzoneVariant] = useState<FileDropVariants>("default");
  const [errorDetails, setErrorDetails] = useState<FileUploadResponse<string | string[]>>();

  const mutation = useMutation({
    mutationKey: ["download-sample-template"],
    mutationFn: async () => tenantsService.downloadSampleWorkRoleTemplate(),
  });

  const fileImportMutation = useMutation({
    mutationKey: ["import-work-roles", tenantId],
    mutationFn: async (file: File) => tenantsService.uploadFile(file, tenantId),
    onSuccess: (data: FileUploadResponse<string[]>) => {
      if (data.type === "success") {
        setFileDropzoneVariant(data.type);
        if (data.message?.length > 0) {
          data.message.forEach((message) =>
            showToast(message, {
              type: "info",
            }),
          );
          return;
        }
        showToast("Details updated successfully", {
          type: "success",
        });
        onClose();
      }
      if (data.type === "error") {
        setErrorDetails(data);
      }
      refetch();
    },
    onError: (error: FileUploadResponse<string>) => {
      setFileDropzoneVariant("error");
      setErrorDetails(error);
    },
  });

  const resetFileDropState = () => {
    setFileDropzoneVariant("default");
    setErrorDetails(undefined);
  };

  const onFileDrop = async <T extends File>(files: T[]) => {
    resetFileDropState();
    setFiles(files);
  };

  const onImportClick = async () => {
    if (files && files?.length > 0) fileImportMutation.mutate(files[0]);
  };

  const onDownloadSampleIconClick = () => {
    mutation.mutate();
  };

  return (
    <Modal
      title={languageConfig.tenants.workRoles.modalTitle}
      subtitle={languageConfig.tenants.workRoles.subtitle}
      isOpen={isModalOpen}
      onClose={onClose}
      showBackButton
      fullWidth
      maxWidth="md"
      showDivider={false}
      sx={ModalControllerStyles.root}
      PaperProps={{
        style: ModalControllerStyles.paper,
      }}
      actions={
        <DialogActions
          sx={{
            padding: "32px 16px !important",
          }}
        >
          <ButtonWithLoading
            onClick={onClose}
            variant="text"
            sx={ImportModalStyles.button}
            disabled={fileImportMutation.isLoading}
          >
            {languageConfig.tenants.modals.import.buttons.cancel}
          </ButtonWithLoading>
          <ButtonWithLoading
            onClick={onImportClick}
            disabled={!(files && files?.length > 0)}
            variant="contained"
            sx={ImportModalStyles.button}
            isLoading={fileImportMutation.isLoading}
          >
            {languageConfig.tenants.modals.import.buttons.import}
          </ButtonWithLoading>
        </DialogActions>
      }
      isLoading={fileImportMutation.isLoading}
    >
      <Box style={ImportModalStyles.root}>
        <Box sx={ImportModalStyles.body.container}>
          <FileDropzone
            variant={fileDropzoneVariant}
            files={files}
            width="100%"
            height={250}
            onFileDrop={onFileDrop}
            acceptFileTypes={{
              "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet": [".xlsx"],
            }}
            message={typeof errorDetails?.message === "string" ? errorDetails.message : undefined}
          />
          <Box sx={{ width: "75%" }}>
            {typeof errorDetails?.message === "object" && (
              <AlertsAccordion
                severity={errorDetails?.type}
                message={errorDetails?.message as string[]}
                onClose={() => setErrorDetails(undefined)}
              />
            )}
          </Box>
          <Box display="flex" justifyContent="space-between" alignItems="center" sx={{ margin: "15px 0px" }}>
            <Typography sx={ImportModalStyles.body.text.info}>Supported Format: xlsx</Typography>
            <Typography sx={ImportModalStyles.body.text.info}>Maximum Size: 5mb</Typography>
          </Box>
          <Box sx={ImportModalStyles.body.sampleFileRoot}>
            <Box sx={ImportModalStyles.body.sampleFileContainer}>
              <Box display="flex" flexDirection="column" gap={1}>
                <Box display="flex" gap={1}>
                  <Folder />
                  <Typography>{languageConfig.tenants.modals.import.sampleFileTitle}</Typography>
                </Box>
                <Typography sx={ImportModalStyles.body.sampleFileSubtitle}>
                  {languageConfig.tenants.modals.import.sampleFileSubtitle}
                </Typography>
              </Box>
              <Box sx={ImportModalStyles.downloadIcon} onClick={onDownloadSampleIconClick}>
                <CloudDownload sx={{ fill: "black" }} />
              </Box>
            </Box>
          </Box>
        </Box>
      </Box>
    </Modal>
  );
};

export default ImportModal;
