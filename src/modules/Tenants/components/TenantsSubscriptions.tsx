import { AddBox } from "@mui/icons-material";
import { Box, Button, Grid2, IconButton, Tooltip, Typography } from "@mui/material";
import { useMutation, useQuery } from "@tanstack/react-query";
import { format, isEqual, isValid } from "date-fns";
import React from "react";
import { useParams } from "react-router-dom";
import ContentHeader from "src/modules/Common/ContentHeader/ContentHeader";
import { useAppForm } from "src/modules/Common/Form/effiFormContext";
import CustomDateField from "src/modules/Common/FormInputs/CustomDateField";
import ConfirmationModal from "src/modules/Common/Modal/ConfirmationModal";
import Modal from "src/modules/Common/Modal/Modal";
import DataTable from "src/modules/Common/Table/DataTable";
import TableActions from "src/modules/Common/Table/TableActions";
import { CancelSubscription, CreateTenantSubscription } from "src/services/api_definitions/tenants";
import tenantsService from "src/services/tenants.service";
import { getStatusColors } from "src/utils/typographyUtils";
import { z } from "zod";
import CreateSubscription from "./Subscriptions/CreateSubscription";

const subscriptionSchema = z
  .object({
    plan: z.string().nonempty(),
    startDate: z.string().nonempty({
      message: "Start date cannot be empty",
    }),
    endDate: z.string().nonempty({
      message: "End date cannot be empty",
    }),
    monthlyChargePerEmployee: z.number().gt(0),
    onboardingCharge: z.number(),
  })
  .refine(
    (arg) =>
      arg?.startDate && arg?.endDate ? isEqual(arg?.endDate, arg?.startDate) || arg?.endDate > arg?.startDate : true,
    {
      message: "end date cannot be before start date",
      path: ["endDate"],
    },
  );

const TenantSubscriptions: React.FC = () => {
  const { tenantId = "" } = useParams();
  const [isAddModalOpen, setIsAddModalOpen] = React.useState(false);
  const [cancelSubscriptionModalOpen, setCancelSubscriptionModalOpen] = React.useState(false);
  const [cancelSubscriptionDetails, setCancelSubscriptionDetails] = React.useState<CancelSubscription | null>(null);

  const { data: tenantSubscriptions, refetch } = useQuery(
    ["get-tenant-subscriptions"],
    async () => tenantsService.getTenantSubscriptions(tenantId),
    {
      refetchOnWindowFocus: false,
    },
  );

  const form = useAppForm({
    defaultValues: {
      plan: "",
      startDate: "",
      endDate: "",
      monthlyChargePerEmployee: 0,
      onboardingCharge: 0,
    },
    onSubmit: (props) => {
      const payload: CreateTenantSubscription = {
        plan: props.value.plan,
        start_date: format(props.value.startDate as unknown as Date, "yyyy-MM-dd"),
        end_date: format(props.value.endDate as unknown as Date, "yyyy-MM-dd"),
        monthly_charge_per_employee: props.value.monthlyChargePerEmployee,
        onboarding_charge: props.value.onboardingCharge,
      };
      createSubscriptionMutation.mutate(payload);
    },
    validators: {
      onChange: subscriptionSchema,
    },
  });

  const onClose = () => {
    setIsAddModalOpen(false);
    setCancelSubscriptionModalOpen(false);
    setCancelSubscriptionDetails(null);
    refetch();
    form.reset();
  };

  const createSubscriptionMutation = useMutation({
    mutationFn: async (payload: CreateTenantSubscription) => tenantsService.createTenantSubscription(tenantId, payload),
    onSuccess: () => {
      onClose();
    },
  });

  const cancelSubscriptionMutation = useMutation({
    mutationFn: async (payload: CancelSubscription) => tenantsService.cancelSubscription(payload),
    onSuccess: () => {
      onClose();
    },
  });

  const onAddSubscriptionClick = () => {
    setIsAddModalOpen(true);
  };

  return (
    <Grid2 container spacing={1}>
      <Grid2 size={12}>
        <DataTable
          data={tenantSubscriptions || []}
          enableTopToolbar
          enableRowActions
          muiTopToolbarProps={{
            sx: {
              justifyContent: "flex-start",
            },
          }}
          positionActionsColumn="last"
          renderRowActions={({ row }) => (
            <TableActions
              edit={{
                hide: true,
                onClick: () => {},
              }}
              view={{
                hide: true,
                onClick: () => {},
              }}
              remove={{
                hide: row?.original?.status?.toLowerCase() === "canceled",
                tooltip: "Cancel Subscription",
                onClick: () => {
                  setCancelSubscriptionDetails({
                    subscription_id: row?.original?.id,
                    effective_date: "",
                  });
                  setCancelSubscriptionModalOpen(true);
                },
              }}
            />
          )}
          renderTopToolbar={() => (
            <Box display="flex" justifyContent="flex-start" p={1} gap={2}>
              <Tooltip title="Add Subscription">
                <IconButton onClick={onAddSubscriptionClick}>
                  <AddBox color="primary" fontSize="medium" />
                </IconButton>
              </Tooltip>
            </Box>
          )}
          columns={[
            {
              accessorKey: "plan",
              header: "Plan",
            },
            {
              accessorKey: "start_date",
              header: "Start Date",
            },
            {
              accessorKey: "end_date",
              header: "End Date",
            },
            {
              accessorKey: "status",
              header: "Status",
              Cell: ({ row }) => (
                <Typography color={getStatusColors(row?.original?.status)}>{row?.original?.status}</Typography>
              ),
            },
            {
              accessorKey: "monthly_charge_per_employee",
              header: "Monthly Charge Per Employee",
            },
            {
              accessorKey: "onboarding_charge",
              header: "Onboarding Charge",
            },
            {
              accessorKey: "currency",
              header: "Currency",
            },
          ]}
          renderDetailPanel={({ row }) => (
            <Box display="flex" flexDirection="column" gap={1}>
              <ContentHeader title="Month Wise Seggregation" />
              <DataTable
                data={row?.original?.monthly_employee_counts || []}
                muiTableHeadProps={{
                  sx: {
                    zIndex: 1,
                  },
                }}
                muiTableContainerProps={{
                  sx: {
                    maxHeight: "inherit",
                  },
                }}
                columns={[
                  {
                    accessorKey: "month",
                    header: "Month",
                  },
                  {
                    accessorKey: "employee_count",
                    header: "Employee Count",
                  },
                ]}
              />
            </Box>
          )}
        />
      </Grid2>
      {isAddModalOpen && (
        <Modal
          isOpen={isAddModalOpen}
          onClose={() => setIsAddModalOpen(false)}
          showBackButton
          showDivider
          title="Add Subscription"
          actions={
            <Box>
              <form.Subscribe selector={(state) => [state.canSubmit, state.isSubmitting, state.isPristine]}>
                {([canSubmit, isSubmitting, isPristine]) => {
                  return (
                    <Box display="flex" p={2} gap={1} justifyContent="flex-end">
                      <Button variant="outlined" onClick={() => onClose()}>
                        Cancel
                      </Button>
                      <Button
                        variant="contained"
                        type="submit"
                        disabled={!canSubmit || isPristine || isSubmitting}
                        onClick={form.handleSubmit}
                      >
                        Submit
                      </Button>
                    </Box>
                  );
                }}
              </form.Subscribe>
            </Box>
          }
        >
          <CreateSubscription form={form} />
        </Modal>
      )}
      {cancelSubscriptionModalOpen && (
        <ConfirmationModal
          isOpen={cancelSubscriptionModalOpen}
          onCancel={onClose}
          onSubmit={() => cancelSubscriptionMutation.mutate(cancelSubscriptionDetails as CancelSubscription)}
          title="You sure you want to cancel the subscription?"
          isSaveDisabled={
            !cancelSubscriptionDetails?.subscription_id || !isValid(cancelSubscriptionDetails?.effective_date)
          }
        >
          <CustomDateField
            title="Effective Date"
            value={cancelSubscriptionDetails?.effective_date as unknown as Date}
            onChange={(date) =>
              setCancelSubscriptionDetails((prev) => ({
                subscription_id: prev?.subscription_id || "",
                effective_date: (date as unknown as string) || "",
              }))
            }
            minDate={new Date()}
            slotProps={{
              textField: {
                id: "effective_date",
                name: "effective_date",
                fullWidth: true,
                size: "small",
                error: false,
                slotProps: {
                  input: {
                    readOnly: true,
                  },
                },
              },
            }}
          />
        </ConfirmationModal>
      )}
    </Grid2>
  );
};

export default TenantSubscriptions;
