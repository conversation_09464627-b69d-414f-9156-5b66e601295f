import { KeyboardBackspace, NavigateNext } from "@mui/icons-material";
import { AppBar, Box, Breadcrumbs, Link, Tab, Tabs, styled } from "@mui/material";
import React from "react";
import { useNavigate, useParams } from "react-router-dom";
import { PATH_CONFIG } from "src/modules/Routing/config";
import TenantOrganisationDetails from "./TenantConfigurations";
import TenantProfileDetails from "./TenantProfileDetails";
import TenantIntegrations from "./TenantsIntegrations";
import TenantSubscriptions from "./TenantsSubscriptions";

const CustomTab = styled(Tab)(() => ({
  background: "#E6EFEF",
  color: "#667085",
  textTransform: "none",
  margin: "10px 10px",
  "&.Mui-selected": {
    background: "white",
    margin: "10px 10px",
    color: "#007F6F",
    textTransform: "none",
    borderRadius: 8,
    textDecoration: "none",
  },
}));

const CustomTabs = styled(Tabs)(() => ({
  "&.MuiTabs-indicator": {
    display: "none",
  },
}));

const ComponentContainer = styled(Box)(() => ({
  margin: "30px 0px",
  height: "100%",
}));

const tabs = {
  0: {
    component: <TenantProfileDetails />,
    label: "Profile Information",
    id: 0,
  },
  1: {
    component: <TenantOrganisationDetails />,
    label: "Configuration",
    id: 1,
  },
  2: {
    component: <TenantIntegrations />,
    label: "Integrations",
    id: 2,
  },
  3: {
    component: <TenantSubscriptions />,
    label: "Subscriptions",
    id: 3,
  },
};

const CustomLink = styled(Link)(() => ({
  color: "black",
  outline: "none",
  textDecoration: "none",
  cursor: "pointer",
  fontWeight: "bold",
  "&:hover": {
    opacity: 0.7,
  },
}));

type TabKeys = keyof typeof tabs;

const TenantsEditView = () => {
  const { tenantName } = useParams();
  const navigate = useNavigate();

  const onBreadcrumbClick = () => {
    navigate(PATH_CONFIG.TENANTS.path);
  };

  const breadcrumbRoutes = [
    <CustomLink key={1} onClick={onBreadcrumbClick}>
      Tenants
    </CustomLink>,
    <CustomLink key={2}>{tenantName}</CustomLink>,
  ];

  const [value, setValue] = React.useState<TabKeys>(tabs[0].id as TabKeys);

  const handleChange = (__event: React.SyntheticEvent, newValue: TabKeys) => {
    setValue(tabs[newValue].id as TabKeys);
  };
  return (
    <Box sx={{ width: "100%" }}>
      <Box display="flex" alignItems="center" gap={1} sx={{ marginBottom: "16px" }}>
        <KeyboardBackspace onClick={onBreadcrumbClick} sx={{ cursor: "pointer" }} />
        <Breadcrumbs separator={<NavigateNext />}>{breadcrumbRoutes}</Breadcrumbs>
      </Box>
      <AppBar position="static" elevation={0}>
        <CustomTabs
          value={value}
          onChange={handleChange}
          textColor="inherit"
          scrollButtons="auto"
          variant="scrollable"
          // role="navigation"
          TabIndicatorProps={{
            style: {
              display: "none",
              transition: "0.3s ease-in",
            },
          }}
          aria-label="full width tabs example"
          sx={{
            background: "#E6EFEF",
          }}
        >
          {Object.values(tabs).map((tab) => (
            <CustomTab key={tab.id} label={tab.label} />
          ))}
        </CustomTabs>
      </AppBar>
      <ComponentContainer>{tabs[value].component}</ComponentContainer>
    </Box>
  );
};

export default TenantsEditView;
