import { SxProps, Theme } from "@mui/material";

export const container = <SxProps<Theme>>{
  display: "flex",
  width: "100%",
  padding: "20px 20px 363px 20px",
  flexDirection: "column",
  alignItems: "flex-start",
  gap: "18px",
  height: "auto",
};

export const headerContainer = <SxProps<Theme>>{
  display: "flex",
  width: "100%",
  justifyContent: "space-between",
  alignItems: "center",
};

export const textContainer = <SxProps<Theme>>{
  display: "flex",
  flexDirection: "column",
  gap: 1,
};

export const title = <SxProps<Theme>>{
  fontSize: 20,
  fontWeight: "bold",
};

export const subtitle = <SxProps<Theme>>{
  fontSize: 12,
  color: "#667085",
};

export const button = <SxProps<Theme>>{
  borderRadius: 8,
  textTransform: "none",
};

export const ModalControllerStyles = {
  root: {
    "& .MuiDialogContent-root": {
      width: "auto",
      padding: 0,
    },
  },
  paper: {
    display: "block",
    width: "auto",
    bgcolor: "background.paper",
    borderRadius: "20px",
    boxShadow: 24,
    p: 4,
  },
};

const ModalStyles = {
  body: {
    container: {
      padding: "16px",
    },
    grid: {
      padding: "0 1rem",
      minHeight: "180px",
    },
    text: {
      caption: {
        mt: "10px",
        color: "#667085",
      },
      info: {
        color: "#667085",
        fontSize: 14,
        fontFamily: "Poppins",
        fontWeight: "400",
        wordWrap: "break-word",
      },
    },
  },
  textLabels: {
    color: "#667085",
    marginBottom: "8px",
  },
  textFieldStyles: {
    width: "90%",
    input: {
      color: "text.primary",
    },
    ".css-1t8l2tu-MuiInputBase-input-MuiOutlinedInput-input": {
      padding: "0.625rem 1rem",
    },
    "& .MuiOutlinedInput-root": {
      "& fieldset": {
        borderColor: "info.light",
        borderRadius: "5px",
      },
      "&:hover fieldset": {
        borderColor: "info.main",
        borderRadius: "5px",
      },
      "&.Mui-focused fieldset": {
        borderColor: "info.main",
        borderRadius: "5px",
      },
      "& .MuiInputBase-input.Mui-disabled": {
        backgroundColor: "#F5F5F5",
      },
    },
  },
  closeIcon: {
    cursor: "pointer",
    padding: "4px",
    border: "1px solid #D5D7D8",
    borderRadius: "50px",
    color: "#D5D7D8",
    width: "32px",
    height: "32px",
    "&:hover": {
      color: "black",
      borderColor: "black",
    },
    transition: "0.195s all",
  },
  buttonContainer: {
    display: "flex",
    flexDirection: "row",
    justifyContent: "flex-end",
    padding: "32px 0",
    gap: "16px",
  },
  button: {
    padding: "1.25rem",
    borderRadius: 8,
    width: "172px",
    height: "50px",
    fontSize: "1rem",
    fontWeight: "500",
    textTransform: "none",
    cursor: "pointer",
    "&.MuiButton-contained": {
      "&:hover": {
        backgroundColor: "primary.light",
      },
      "&:disabled": {
        color: "primary.contrastText",
        backgroundColor: "primary.light",
        opacity: 0.5,
      },
    },
    "&.MuiButton-contained .MuiTouchRipple-child": {
      backgroundColor: "black",
    },
    "&.MuiButton-text": {
      border: "none",
      backgroundColor: "#EFF4F8",
      "&:hover": {
        backgroundColor: "#F2F3F3",
      },
      "&:disabled": {
        backgroundColor: "#EFF4F8",
      },
    },
  },
};

export const ImportModalStyles = {
  root: {
    padding: 0,
    minWidth: 764,
  },
  header: {
    padding: 4,
    paddingBottom: 0,
    display: "flex",
    justifyContent: "space-between",
  },
  body: {
    ...ModalStyles.body,
    sampleFileRoot: {
      background: "#E6F2F1",
      display: "flex",
      alignItems: "center",
      justifyContent: "space-between",
      borderRadius: "15px",
    },
    sampleFileContainer: {
      display: "flex",
      justifyContent: "space-between",
      alignItems: "center",
      width: "100%",
      padding: "14px 32px",
    },
    sampleFileSubtitle: {
      color: "#667085",
      fontSize: 12,
      wordWrap: "break-word",
      width: "80%",
    },
  },
  textLabels: {
    ...ModalStyles.textLabels,
  },
  textFieldStyles: {
    ...ModalStyles.textFieldStyles,
  },
  closeIcon: {
    ...ModalStyles.closeIcon,
  },
  downloadIcon: {
    width: "52px",
    height: "52px",
    borderRadius: "50px",
    background: "white",
    display: "flex",
    justifyContent: "center",
    alignItems: "center",
    "&:hover": {
      opacity: 0.5,
      cursor: "pointer",
    },
  },
  buttonContainer: {
    ...ModalStyles.buttonContainer,
  },
  button: {
    ...ModalStyles.button,
  },
};
