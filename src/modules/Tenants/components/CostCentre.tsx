import { Box, CircularProgress, Container } from "@mui/material";
import { useQuery } from "@tanstack/react-query";
import React, { lazy, Suspense, useState } from "react";
import { useParams } from "react-router-dom";
import languageConfig from "src/configs/language/en.lang";
import ContentHeader from "src/modules/Common/ContentHeader/ContentHeader";
import tenantsService from "src/services/tenants.service";
import { getACLFromFeaturekey } from "src/utils/screenUtils";
import AddCostCenterModal from "./AddCostCenterModal";

const LazyDataTable = lazy(() => import("src/modules/Common/Table/DataTable"));

interface CostCenterProps {
  featureKey: string;
}

const CostCentre: React.FC<CostCenterProps> = ({ featureKey }) => {
  const { tenantId = "" } = useParams();
  const COST_CENTER_ACL = getACLFromFeaturekey(featureKey);

  const [isModalOpen, setIsModalOpen] = useState(false);

  const {
    data = [],
    isLoading,
    isFetching,
    refetch,
  } = useQuery(["cost-center-details"], async () => tenantsService.getCostCenterDetails(tenantId), {
    retryOnMount: false,
    refetchOnWindowFocus: false,
    cacheTime: 0,
  });

  const onCreateCostCenterClick = () => {
    setIsModalOpen(true);
  };

  return (
    <Container maxWidth="xl" disableGutters>
      <ContentHeader
        title={languageConfig.tenants.costCenter.title}
        subtitle={""}
        primaryAction={onCreateCostCenterClick}
        buttonTitle={COST_CENTER_ACL?.canWrite ? languageConfig.tenants.button.createCostCenter : ""}
      />
      <Box sx={{ margin: "20px 0px" }}>
        <Suspense fallback={<CircularProgress />}>
          <LazyDataTable
            data={data || []}
            state={{
              showSkeletons: isLoading && isFetching,
            }}
            columns={[
              {
                accessorKey: "code",
                header: "Cost Center Code",
              },
            ]}
          />
        </Suspense>
      </Box>
      {isModalOpen && COST_CENTER_ACL?.canWrite && (
        <AddCostCenterModal refetch={refetch} isModalOpen={isModalOpen} onClose={() => setIsModalOpen(false)} />
      )}
    </Container>
  );
};

export default CostCentre;
