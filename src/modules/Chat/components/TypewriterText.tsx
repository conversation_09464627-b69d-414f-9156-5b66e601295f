import { Typography, TypographyProps } from "@mui/material";
import React, { memo, useEffect, useRef, useState } from "react";
import "./TypewriterText.css";
import Markdown from "react-markdown";

interface TypewriterTextProps extends Omit<TypographyProps, "children"> {
  text: string;
  typingSpeed?: number;
  isTyping?: boolean;
}

const TypewriterText: React.FC<TypewriterTextProps> = memo(
  ({
    text,
    typingSpeed = 15, // milliseconds per character
    isTyping = true,
    ...typographyProps
  }) => {
    const [displayedText, setDisplayedText] = useState("");
    const [currentIndex, setCurrentIndex] = useState(0);
    const previousTextRef = useRef("");
    const timerRef = useRef<NodeJS.Timeout | null>(null);
    const isComplete = displayedText.length === text.length;

    // Clean up timer on unmount
    useEffect(() => {
      return () => {
        if (timerRef.current) {
          clearTimeout(timerRef.current);
        }
      };
    }, []);

    // Reset the animation when the text changes completely
    useEffect(() => {
      // If the text is completely new (not an extension of previous text)
      if (previousTextRef.current !== text && !text.startsWith(previousTextRef.current)) {
        setDisplayedText("");
        setCurrentIndex(0);
      }
      // If the text is longer than what we're currently displaying
      else if (text.length > displayedText.length) {
        // Continue from where we left off
        setCurrentIndex(displayedText.length);
      }

      previousTextRef.current = text;
    }, [text, displayedText.length]);

    // Handle the typewriter effect
    useEffect(() => {
      if (!isTyping || isComplete || currentIndex >= text.length) {
        return;
      }

      // Clear any existing timer
      if (timerRef.current) {
        clearTimeout(timerRef.current);
      }

      // Calculate how many characters to type at once
      // For longer texts, we can type faster by adding more characters per tick
      const charsToAdd = Math.max(1, Math.floor(text.length / 200));
      const nextIndex = Math.min(text.length, currentIndex + charsToAdd);

      timerRef.current = setTimeout(() => {
        setDisplayedText(text.substring(0, nextIndex));
        setCurrentIndex(nextIndex);
      }, typingSpeed);

      return () => {
        if (timerRef.current) {
          clearTimeout(timerRef.current);
        }
      };
    }, [text, currentIndex, isTyping, isComplete, typingSpeed, text.length]);

    // If not typing, show the full text immediately
    useEffect(() => {
      if (!isTyping) {
        setDisplayedText(text);
        setCurrentIndex(text.length);
      }
    }, [isTyping, text]);

    return (
      <Typography {...typographyProps}>
        <Markdown>{displayedText}</Markdown>
        {!isComplete && isTyping && <span className="cursor">|</span>}
      </Typography>
    );
  },
);

TypewriterText.displayName = "TypewriterText";

export default TypewriterText;
