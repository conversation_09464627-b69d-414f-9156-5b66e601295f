import {
  Assignment,
  EventAvailable,
  Help,
  Payment,
  Person,
  QuestionAnswer,
  Schedule,
  TrendingUp,
} from "@mui/icons-material";
import { Box, Chip, Tooltip, Typography, useTheme } from "@mui/material";
import React from "react";

interface FrequentQuestionsProps {
  onQuestionClick: (question: string) => void;
}

const frequentQuestions = [
  {
    id: 1,
    text: "How do I apply for leave?",
    icon: EventAvailable,
    category: "Leave Management",
  },
  {
    id: 2,
    text: "What are my working hours and schedule?",
    icon: Schedule,
    category: "Time Management",
  },
  {
    id: 3,
    text: "How can I view and download my payslip?",
    icon: Payment,
    category: "Payroll",
  },
  {
    id: 4,
    text: "How to update my profile information?",
    icon: Person,
    category: "Profile",
  },
  {
    id: 5,
    text: "What are the company policies and procedures?",
    icon: Assignment,
    category: "Policies",
  },
  {
    id: 6,
    text: "How to track my performance and goals?",
    icon: TrendingUp,
    category: "Performance",
  },
  {
    id: 7,
    text: "Who can I contact for technical support?",
    icon: Help,
    category: "Support",
  },
  {
    id: 8,
    text: "How to submit and manage my timesheet entries?",
    icon: QuestionAnswer,
    category: "Time Tracking",
  },
];

const FrequentQuestions: React.FC<FrequentQuestionsProps> = ({ onQuestionClick }) => {
  const theme = useTheme();

  return (
    <Box
      sx={{
        display: "flex",
        flexDirection: "column",
        gap: 3,
        p: 3,
        background: `linear-gradient(135deg, ${theme.palette.primary.main}08 0%, ${theme.palette.secondary.main}05 100%)`,
        borderRadius: 3,
        border: `1px solid ${theme.palette.divider}`,
        backdropFilter: "blur(10px)",
      }}
    >
      <Box sx={{ textAlign: "center", mb: 1 }}>
        <Typography
          variant="h6"
          sx={{
            fontWeight: 600,
            color: theme.palette.text.primary,
            mb: 0.5,
          }}
        >
          ✨ Frequently Asked Questions
        </Typography>
        <Typography
          variant="body2"
          sx={{
            color: theme.palette.text.secondary,
            fontSize: "0.875rem",
          }}
        >
          Click on any question to get started
        </Typography>
      </Box>

      <Box
        sx={{
          display: "flex",
          flexWrap: "wrap",
          gap: 1.5,
          justifyContent: "center",
        }}
      >
        {frequentQuestions.map((question) => {
          const IconComponent = question.icon;
          return (
            <Tooltip
              key={question.id}
              title={question.text}
              arrow
              placement="top"
              sx={{
                "& .MuiTooltip-tooltip": {
                  bgcolor: theme.palette.grey[800],
                  color: theme.palette.common.white,
                  fontSize: "0.875rem",
                  borderRadius: 2,
                  maxWidth: 300,
                  textAlign: "center",
                },
                "& .MuiTooltip-arrow": {
                  color: theme.palette.grey[800],
                },
              }}
            >
              <Chip
                label={question.text}
                icon={<IconComponent sx={{ fontSize: "1rem" }} />}
                onClick={() => onQuestionClick(question.text)}
                sx={{
                  px: 2,
                  py: 1.5,
                  height: "auto",
                  borderRadius: 3,
                  fontSize: "0.875rem",
                  fontWeight: 500,
                  background: `linear-gradient(135deg, ${theme.palette.background.paper} 0%, ${theme.palette.grey[50]} 100%)`,
                  border: `1px solid ${theme.palette.divider}`,
                  color: theme.palette.text.primary,
                  transition: "all 0.3s cubic-bezier(0.4, 0, 0.2, 1)",
                  cursor: "pointer",
                  boxShadow: "0 2px 8px rgba(0,0,0,0.08)",
                  "&:hover": {
                    background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.primary.light} 100%)`,
                    color: theme.palette.primary.contrastText,
                    transform: "translateY(-2px)",
                    boxShadow: "0 8px 25px rgba(0,0,0,0.15)",
                    borderColor: theme.palette.primary.main,
                    "& .MuiChip-icon": {
                      color: theme.palette.primary.contrastText,
                    },
                  },
                  "&:active": {
                    transform: "translateY(0px)",
                    boxShadow: "0 4px 15px rgba(0,0,0,0.12)",
                  },
                  "& .MuiChip-icon": {
                    color: theme.palette.text.secondary,
                    transition: "color 0.3s ease",
                  },
                  "& .MuiChip-label": {
                    px: 1,
                    whiteSpace: "nowrap",
                    overflow: "hidden",
                    textOverflow: "ellipsis",
                    maxWidth: "200px",
                  },
                }}
              />
            </Tooltip>
          );
        })}
      </Box>

      <Box sx={{ textAlign: "center", mt: 1 }}>
        <Typography
          variant="caption"
          sx={{
            color: theme.palette.text.secondary,
            fontSize: "0.75rem",
            fontStyle: "italic",
          }}
        >
          Or type your own question below
        </Typography>
      </Box>
    </Box>
  );
};

export default FrequentQuestions;
