import { <PERSON>, SmartToy } from "@mui/icons-material";
import { Avatar, Box, Paper, Typography, useTheme } from "@mui/material";
import React, { memo } from "react";
import Markdown from "react-markdown";
import { ChatMessage } from "src/services/api_definitions/chat.service";
import TypewriterText from "./TypewriterText";

interface ChatMessageProps {
  message: ChatMessage;
  isLatestAssistantMessage?: boolean;
  isLoading?: boolean;
  isNewMessage?: boolean;
}

// Memoize the component to prevent unnecessary re-renders
const UserChatMessageItem = memo(
  ({ message, isLatestAssistantMessage = false, isLoading = false, isNewMessage = false }: ChatMessageProps) => {
    const theme = useTheme();
    const isUser = message.role === "user";

    // Determine if we should show the typewriter effect
    // Only show for the latest assistant message when it's actively being loaded (streaming)
    const showTypewriter = !isUser && isLatestAssistantMessage && isNewMessage;

    return (
      <Box
        sx={{
          display: "flex",
          flexDirection: isUser ? "row-reverse" : "row",
          gap: 2,
          maxWidth: "85%",
          alignSelf: isUser ? "flex-end" : "flex-start",
          mb: 2,
        }}
      >
        <Avatar
          sx={{
            bgcolor: isUser ? theme.palette.primary.main : theme.palette.secondary.main,
            width: 40,
            height: 40,
            boxShadow: "0 4px 12px rgba(0,0,0,0.15)",
            border: `2px solid ${theme.palette.background.paper}`,
          }}
        >
          {isUser ? (
            <Person sx={{ fontSize: "1.2rem", color: theme.palette.primary.contrastText }} />
          ) : (
            <SmartToy sx={{ fontSize: "1.2rem", color: theme.palette.secondary.contrastText }} />
          )}
        </Avatar>

        <Box
          sx={{
            position: "relative",
            maxWidth: "100%",
          }}
        >
          <Paper
            elevation={0}
            sx={{
              p: 2.5,
              borderRadius: isUser ? "20px 20px 4px 20px" : "20px 20px 20px 4px",
              background: isUser
                ? `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.primary.light} 100%)`
                : `linear-gradient(135deg, ${theme.palette.background.paper} 0%, ${theme.palette.grey[50]} 100%)`,
              color: isUser ? theme.palette.primary.contrastText : theme.palette.text.primary,
              border: isUser ? "none" : `1px solid ${theme.palette.divider}`,
              boxShadow: isUser ? "0 8px 25px rgba(0, 127, 111, 0.25)" : "0 4px 20px rgba(0,0,0,0.08)",
              position: "relative",
              "&::before": isUser
                ? {}
                : {
                    content: '""',
                    position: "absolute",
                    top: 12,
                    left: -6,
                    width: 0,
                    height: 0,
                    borderTop: "6px solid transparent",
                    borderBottom: "6px solid transparent",
                    borderRight: `6px solid ${theme.palette.background.paper}`,
                  },
              "&::after": isUser
                ? {
                    content: '""',
                    position: "absolute",
                    top: 12,
                    right: -6,
                    width: 0,
                    height: 0,
                    borderTop: "6px solid transparent",
                    borderBottom: "6px solid transparent",
                    borderLeft: `6px solid ${theme.palette.primary.main}`,
                  }
                : {},
            }}
          >
            {showTypewriter ? (
              <TypewriterText
                text={message.content}
                variant="body1"
                sx={{
                  whiteSpace: "pre-wrap",
                  fontSize: "0.95rem",
                  lineHeight: 1.6,
                }}
                typingSpeed={15}
                isTyping={isLoading || message.content.length > 0}
              />
            ) : (
              <Typography
                variant="body1"
                sx={{
                  whiteSpace: "pre-wrap",
                  fontSize: "0.95rem",
                  lineHeight: 1.6,
                  "& p": { margin: 0 },
                  "& p + p": { marginTop: 1 },
                }}
              >
                <Markdown>{message.content}</Markdown>
              </Typography>
            )}
          </Paper>

          {/* Timestamp */}
          <Typography
            variant="caption"
            sx={{
              display: "block",
              textAlign: isUser ? "right" : "left",
              mt: 0.5,
              color: theme.palette.text.secondary,
              fontSize: "0.75rem",
            }}
          >
            {new Date().toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" })}
          </Typography>
        </Box>
      </Box>
    );
  },
);

UserChatMessageItem.displayName = "UserChatMessageItem";

export default UserChatMessageItem;
