import { CircularProgress } from "@mui/material";
import { useQuery } from "@tanstack/react-query";
import React, { useEffect, useMemo, useState } from "react";
import { BaseObject } from "src/app/global";
import languageConfig from "src/configs/language/en.lang";
import businessunitsService from "src/services/businessunits.service";
import departmentService from "src/services/department.service";
import { getCurrentTenantId } from "src/utils/authUtils";
import validators from "src/utils/validators";
import { CommonForm } from "./CommonForm";

const {
  teams: teamsLang,
  businessUnits: businessUnitsLang,
  departments: departmentsLang,
} = languageConfig.tenants.tenantSettings;

const convertListToOptions = (list: Record<string, unknown>[], key = "id", value = "name") => {
  return list?.map((item) => ({ value: item[value], label: item[key] }));
};

const rowAdditionaInitialValues = [
  {
    businessUnits: "",
    department: "",
    team: "",
  },
];

export const Team = ({
  handleNextStep,
  handleBackStep,
  handleSkipStep,
}: {
  handleNextStep: () => void;
  handleBackStep: () => void;
  handleSkipStep: () => void;
}) => {
  const tenantId = getCurrentTenantId();
  const [departmentList, setDepartmentList] = useState<BaseObject[]>([]);

  const { data: businessUnits, isLoading: businessUnitsLoading } = useQuery(
    ["get-business-unit-details"],
    async () => businessunitsService.getBusinessUnitDetails(),
    {
      enabled: !!tenantId,
      retryOnMount: false,
      refetchOnWindowFocus: false,
    },
  );

  const {
    data: teamList,
    isLoading: teamListLoading,
    refetch: teamListRefetch,
  } = useQuery(["get-all-teams"], async () => departmentService.getAllTeams(), {
    enabled: !!tenantId,
    retryOnMount: false,
    refetchOnWindowFocus: false,
  });

  const { data: allDepartments, isLoading: allDepartmentsLoading } = useQuery(
    ["get-all-departments"],
    async () => departmentService.getAllDepartments(),
    {
      enabled: !!tenantId,
      retryOnMount: false,
      refetchOnWindowFocus: false,
    },
  );

  useEffect(() => {
    const departmentList = teamList?.map((team: BaseObject) => {
      const deaprtements = (allDepartments as BaseObject[])?.filter(
        (department: BaseObject) => department.business_unit === team.business_unit,
      );
      return convertListToOptions(deaprtements, "name", "name");
    });
    setDepartmentList(departmentList as unknown as BaseObject[]);
  }, [allDepartments, teamList]);

  const handleNextClick = async (formDetails: Record<string, unknown>[]) => {
    const payload = formDetails.map((formDetail) => ({
      business_unit: formDetail.businessUnits,
      name: formDetail.team,
      department: formDetail.department,
    }));
    if (payload.length > defaultFormState.length || deafaultResponse.length === 0) {
      await departmentService.setTeamDetails(payload);
      teamListRefetch();
    }

    handleNextStep();
  };
  const captureFormChange = async (ev: React.ChangeEvent<Record<string, unknown>>, index: number) => {
    const name = ev?.target?.id || ev?.target?.name;
    const value = (ev as unknown as Record<string, unknown>)?.value || ev?.target?.value;
    if (name === "businessUnits") {
      const response = (allDepartments as BaseObject[])?.filter((department) => department.business_unit === value);
      const updatedList = [
        ...departmentList.slice(0, index),
        convertListToOptions(response, "name", "name"),
        ...departmentList.slice(index + 1),
      ];
      setDepartmentList(updatedList as BaseObject[]);
    }
  };

  const businessUnitsOptions = convertListToOptions(businessUnits as [], "name", "name");
  const deafaultResponse = useMemo(() => {
    return (
      teamList?.map((team) => ({
        businessUnits: team.business_unit,
        department: team.department,
        team: team.name,
      })) || []
    );
  }, [teamList]);

  const defaultFormState = deafaultResponse.length ? deafaultResponse : rowAdditionaInitialValues;

  const formValidators = {
    businessUnits: [validators.validateInput],
    department: [validators.validateInput],
    team: [validators.validateInput],
  };
  const inputElements = [
    {
      name: "businessUnits",
      label: businessUnitsLang.inputTitle,
      type: "select",
      style: { flex: "30%" },
      placeholder: businessUnitsLang.selectBusinessUnit,
      isRequired: true,
    },
    {
      name: "department",
      label: departmentsLang.inputTitle,
      type: "select",
      style: { flex: "30%" },
      placeholder: departmentsLang.selectDepartment,
      isDynamicOptions: true,
      isRequired: true,
    },
    {
      name: "team",
      label: teamsLang.inputTitle,
      type: "text",
      style: { flex: "30%" },
      placeholder: teamsLang.enterTeam,
      isRequired: true,
    },
  ];
  const formConfig = {
    addButtonText: teamsLang.addTeam,
    onNextClick: handleNextClick,
    formTitle: teamsLang.addTeam,
    onBackClick: handleBackStep,
    onSkipClick: handleSkipStep,
  };
  const selectOptions = { businessUnits: businessUnitsOptions, department: departmentList };

  if (businessUnitsLoading || teamListLoading || allDepartmentsLoading) return <CircularProgress />;
  return (
    <CommonForm
      formConfig={formConfig}
      selectOptions={selectOptions}
      defaultFormState={defaultFormState}
      formValidators={formValidators}
      inputElements={inputElements}
      rowAdditionaInitialValues={rowAdditionaInitialValues}
      captureFormChange={captureFormChange}
    />
  );
};
