import { CircularProgress } from "@mui/material";
import { useQuery } from "@tanstack/react-query";
import React from "react";
import { BaseObject } from "src/app/global";
import languageConfig from "src/configs/language/en.lang";
import businessunitsService from "src/services/businessunits.service";
import tenantsService from "src/services/tenants.service";
import { getCurrentTenantId } from "src/utils/authUtils";
import validators from "src/utils/validators";
import { CommonForm } from "./CommonForm";

const { descriptionLable, businessUnits: businessUnitsLang } = languageConfig.tenants.tenantSettings;

const convertListToOptions = (list: BaseObject[], key = "id", value = "name") => {
  return list?.map((item) => ({ value: item[value], label: item[key] }));
};

export const BusinessUnit = ({ handleNextStep }: { handleNextStep: () => void }) => {
  const tenantId = getCurrentTenantId();
  const { data: costCenters, isLoading } = useQuery(
    ["cost-center-details"],
    async () => tenantsService.getCostCenterDetails(),
    {
      enabled: !!tenantId,
      retryOnMount: false,
      refetchOnWindowFocus: false,
    },
  );

  const { data: businessUnits, isLoading: businessUnitsLoading } = useQuery(
    ["get-business-unit-details"],
    async () => businessunitsService.getBusinessUnitDetails(),
    {
      enabled: !!tenantId,
      retryOnMount: false,
      refetchOnWindowFocus: false,
    },
  );

  const centerCodesOptions = convertListToOptions(costCenters as [], "code", "code");
  const deafaultResponse =
    businessUnits?.map((businessUnit) => ({
      name: businessUnit.name,
      costCenter: businessUnit.cost_center,
      description: businessUnit.description,
    })) || [];
  const rowAdditionaInitialValues = [
    {
      name: "",
      costCenter: "",
      description: "",
    },
  ];
  const defaultFormState: BaseObject[] = deafaultResponse.length ? deafaultResponse : rowAdditionaInitialValues;

  const handleNextClick = async (formDetails: BaseObject[]) => {
    const payload = formDetails.map((formDetail) => ({
      name: formDetail.name,
      cost_center: formDetail.costCenter,
      description: formDetail.description,
    }));
    if (payload.length > defaultFormState.length || deafaultResponse.length === 0) {
      await businessunitsService.setBusinessUnitDetails(payload);
    }

    handleNextStep();
  };

  const formValidators = {
    name: [validators.validateInput],
    costCenter: [validators.validateInput],
    description: [],
  };
  const inputElements = [
    {
      name: "name",
      label: businessUnitsLang.inputTitle,
      type: "text",
      options: centerCodesOptions,
      style: { flex: "49%" },
      placeholder: businessUnitsLang.enterBusinessUnit,
      isRequired: true,
    },
    {
      name: "costCenter",
      label: languageConfig.tenants.costCenter.title,
      type: "select",
      options: centerCodesOptions,
      style: { flex: "49%" },
      placeholder: languageConfig.tenants.costCenter.title,
      isRequired: true,
    },
    {
      name: "description",
      label: descriptionLable,
      type: "text",
      style: { flex: "100%" },
      placeholder: descriptionLable,
      rows: 1,
      allowEmpty: true,
    },
  ];
  const formConfig = {
    addButtonText: businessUnitsLang.addBusinessUnit,
    onNextClick: handleNextClick,
    formTitle: businessUnitsLang.title,
  };
  const selectOptions = { costCenter: centerCodesOptions };
  if (isLoading || businessUnitsLoading) return <CircularProgress sx={{ width: "100%", margin: "auto" }} />;
  return (
    <CommonForm
      formConfig={formConfig}
      selectOptions={selectOptions}
      defaultFormState={defaultFormState}
      formValidators={formValidators}
      inputElements={inputElements}
      rowAdditionaInitialValues={rowAdditionaInitialValues}
    />
  );
};
