import { CircularProgress } from "@mui/material";
import { useQuery } from "@tanstack/react-query";
import React, { useEffect, useMemo, useState } from "react";
import { BaseObject } from "src/app/global";
import languageConfig from "src/configs/language/en.lang";
import businessunitsService from "src/services/businessunits.service";
import departmentService from "src/services/department.service";
import { getCurrentTenantId } from "src/utils/authUtils";
import validators from "src/utils/validators";
import { CommonForm } from "./CommonForm";

const {
  subDepartments: subDepartmentLang,
  businessUnits: businessUnitsLang,
  departments: departmentsLang,
} = languageConfig.tenants.tenantSettings;

const convertListToOptions = (list: BaseObject[], key = "id", value = "name") => {
  return list?.map((item) => ({ value: item[value], label: item[key] }));
};

const rowAdditionaInitialValues = [
  {
    businessUnits: "",
    department: "",
    subDepartment: "",
  },
];

export const SubDepartment = ({
  handleNextStep,
  handleBackStep,
  handleSkipStep,
}: {
  handleNextStep: () => void;
  handleBackStep: () => void;
  handleSkipStep: () => void;
}) => {
  const tenantId = getCurrentTenantId();
  const [departmentList, setDepartmentList] = useState<unknown[]>([]);

  const { data: businessUnits, isLoading: businessUnitsLoading } = useQuery(
    ["get-business-unit-details"],
    async () => businessunitsService.getBusinessUnitDetails(),
    {
      enabled: !!tenantId,
      retryOnMount: false,
      refetchOnWindowFocus: false,
    },
  );

  const {
    data: subDepartmentList,
    isLoading: subDepartmentListLoading,
    refetch: subDepartmentListRefetch,
  } = useQuery(["get-all-sub-departments"], async () => departmentService.getAllSubDepartments(), {
    enabled: !!tenantId,
    retryOnMount: false,
    refetchOnWindowFocus: false,
  });

  const { data: allDepartments, isLoading: allDepartmentsLoading } = useQuery(
    ["get-all-departments"],
    async () => departmentService.getAllDepartments(),
    {
      enabled: !!tenantId,
      retryOnMount: false,
      refetchOnWindowFocus: false,
    },
  );

  useEffect(() => {
    const departmentList = subDepartmentList?.map((subDepartment) => {
      const departments: BaseObject[] =
        allDepartments?.filter((department) => department.business_unit === subDepartment.business_unit) || [];
      return convertListToOptions(departments, "name", "name");
    });
    setDepartmentList(departmentList as unknown as BaseObject[]);
  }, [allDepartments, subDepartmentList]);

  const handleNextClick = async (formDetails: BaseObject[]) => {
    const payload = formDetails.map((formDetail) => ({
      business_unit: formDetail.businessUnits,
      name: formDetail.subDepartment,
      department: formDetail.department,
    }));
    if (payload.length > defaultFormState.length || deafaultResponse.length === 0) {
      await departmentService.setSubDepartmentDetails(payload);
      subDepartmentListRefetch();
    }

    handleNextStep();
  };
  const captureFormChange = async (ev: React.ChangeEvent<Record<string, unknown>>, index: number) => {
    const name = ev?.target?.id || ev?.target?.name;
    const value = (ev as unknown as Record<string, unknown>)?.value || ev?.target?.value;
    if (name === "businessUnits") {
      const response: BaseObject[] = allDepartments?.filter((department) => department.business_unit === value) || [];
      const updatedList = [
        ...departmentList.slice(0, index),
        convertListToOptions(response, "name", "name"),
        ...departmentList.slice(index + 1),
      ];
      setDepartmentList(updatedList);
    }
  };

  const businessUnitsOptions = convertListToOptions(businessUnits as [], "name", "name");
  const deafaultResponse = useMemo(() => {
    return (
      subDepartmentList?.map((subDepartment) => ({
        businessUnits: subDepartment.business_unit,
        department: subDepartment.department,
        subDepartment: subDepartment.name,
      })) || []
    );
  }, [subDepartmentList]);

  const selectOptions = { businessUnits: businessUnitsOptions, department: departmentList };

  const defaultFormState = deafaultResponse.length ? deafaultResponse : rowAdditionaInitialValues;

  const formValidators = {
    businessUnits: [validators.validateInput],
    department: [validators.validateInput],
    subDepartment: [validators.validateInput],
  };
  const inputElements = [
    {
      name: "businessUnits",
      label: businessUnitsLang.inputTitle,
      type: "select",
      style: { flex: "30%" },
      placeholder: businessUnitsLang.selectBusinessUnit,
      isRequired: true,
    },
    {
      name: "department",
      label: departmentsLang.inputTitle,
      type: "select",
      style: { flex: "30%" },
      placeholder: departmentsLang.selectDepartment,
      isDynamicOptions: true,
      isRequired: true,
    },
    {
      name: "subDepartment",
      label: subDepartmentLang.inputTitle,
      type: "text",
      style: { flex: "30%" },
      placeholder: subDepartmentLang.enterSubDepartment,
      isRequired: true,
    },
  ];
  const formConfig = {
    addButtonText: "Add Sub Department",
    onNextClick: handleNextClick,
    formTitle: "Add Sub Department",
    onBackClick: handleBackStep,
    onSkipClick: handleSkipStep,
  };

  if (businessUnitsLoading || subDepartmentListLoading || allDepartmentsLoading) return <CircularProgress />;
  return (
    <CommonForm
      formConfig={formConfig}
      selectOptions={selectOptions}
      defaultFormState={defaultFormState}
      formValidators={formValidators}
      inputElements={inputElements}
      rowAdditionaInitialValues={rowAdditionaInitialValues}
      captureFormChange={captureFormChange}
    />
  );
};
