import Box from "@mui/material/Box";
import * as React from "react";
import Modal from "./../Common/Modal/Modal";
import { FinishForm } from "./FinishForm";
import HorizontalLinearStepper from "./StepperRoutes";
import { WelcomeScreen } from "./WelcomeScreen";

const style = {
  height: "85vh",
};

export default function BasicModal({ isOpen }: { isOpen: boolean }) {
  const [showStepper, setShowStepper] = React.useState(false);
  const [finishForm, setFinishForm] = React.useState(false);
  const handleWelcomeContinue = () => setShowStepper(true);
  const handleFinish = () => setFinishForm(true);

  return (
    <Modal
      isOpen={isOpen}
      onClose={() => {}}
      fullWidth
      sx={{ backdropFilter: "blur(5px)" }}
      PaperProps={{ sx: { maxWidth: "1080px" } }}
      setmaxwidth={"1080px"}
    >
      <Box sx={style}>
        {finishForm ? (
          <FinishForm onContinue={() => (window.location.href = "/employees")} />
        ) : showStepper ? (
          <HorizontalLinearStepper onFinish={handleFinish} />
        ) : (
          <WelcomeScreen onContinue={handleWelcomeContinue} />
        )}
      </Box>
    </Modal>
  );
}
