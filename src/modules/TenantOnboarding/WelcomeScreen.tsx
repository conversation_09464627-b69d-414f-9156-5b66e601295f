import { Box, Button, Typography } from "@mui/material";
import React from "react";
import { effiHRLogo as CompanyLogo } from "src/assets/icons.svg";
import { WelcomeIcon } from "src/assets/icons.svg";
import languageConfig from "src/configs/language/en.lang";

const { welcomeScreen: welcomeScreenLang } = languageConfig.tenants.tenantSettings;

const containerStyle = {
  display: "flex",
  flexDirection: "column",
  alignItems: "center",
  rowGap: "40px",
};

const subHeadingStyle = {
  width: 532,
  height: 66,
  margin: "0px 0 30px",
  fontFamily: "Poppins",
  fontSize: "16px",
  fontWeight: 500,
  lineHeight: 1.4,
  letterSpacing: "normal",
  textAlign: "center",
  color: "#767272",
};

const continueButtonStyle = {
  width: 456,
  height: 48,
  display: "flex",
  flexDirection: "row",
  justifyContent: "center",
  alignItems: "center",
  gap: "8px",
  margin: "15px 0 0",
  padding: "0 20px",
  borderRadius: "50px",
  border: "solid 1.5px #007f6f",
  backgroundColor: "#007f6f",
};

export const WelcomeScreen = ({ onContinue }: { onContinue: () => void }) => {
  return (
    <Box sx={containerStyle}>
      <CompanyLogo />
      <Typography sx={subHeadingStyle} align="center">
        {welcomeScreenLang.title}
      </Typography>
      <WelcomeIcon alt="Welcome" height={235} />
      <Button sx={continueButtonStyle} onClick={onContinue} variant="contained" color="primary">
        {welcomeScreenLang.button.title}
      </Button>
    </Box>
  );
};
