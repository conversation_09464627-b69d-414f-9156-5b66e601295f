import { Box, Button, Typography } from "@mui/material";
import React from "react";
import { effiHRLogo as CompanyLogo } from "src/assets/icons.svg";
import { SuccessIcon } from "src/assets/icons.svg";
import languageConfig from "src/configs/language/en.lang";

const { finishForm: finishFormLang } = languageConfig.tenants.tenantSettings;

const containerStyle = {
  display: "flex",
  flexDirection: "column",
  alignItems: "center",
  rowGap: "40px",
};
export const FinishForm = ({ onContinue }: { onContinue?: () => void }) => {
  return (
    <Box sx={containerStyle}>
      <CompanyLogo height={52} />
      <Box>
        <Typography align="center">{finishFormLang.title}</Typography>
        <Typography align="center">{finishFormLang.subTitle}</Typography>
      </Box>
      <SuccessIcon alt="Finish" height={100} />
      <Button onClick={onContinue} variant="contained" color="primary">
        {finishFormLang.button.title}
      </Button>
    </Box>
  );
};
