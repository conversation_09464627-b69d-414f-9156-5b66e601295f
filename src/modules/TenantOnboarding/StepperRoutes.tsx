import { styled } from "@mui/material";
import Box from "@mui/material/Box";
import Step from "@mui/material/Step";
import StepConnector, { stepConnectorClasses } from "@mui/material/StepConnector";
import StepLabel from "@mui/material/StepLabel";
import Stepper from "@mui/material/Stepper";
import Typography from "@mui/material/Typography";
import * as React from "react";
import { TickIcon } from "src/assets/icons.svg";
import languageConfig from "src/configs/language/en.lang";
import { BusinessUnit } from "./BusinessUnits";
import { Departments } from "./Departments";
import { EmployeeIDConfiguration } from "./EmployeeIDConfiguration";
import { JobFamily } from "./JobFamily";
import JobTitle from "./JobTitle";
import { SubDepartment } from "./SubDepartment";
import { Team } from "./Team";

const {
  businessUnits: businessUnitsLang,
  departments: departmentsLang,
  subDepartments: subDepartmentsLang,
  teams: teamsLang,
  jobFamilies: jobFamilyLang,
  jobTitles: jobTitleLang,
  employeeConfig: employeeIDConfigurationLang,
} = languageConfig.tenants.tenantSettings;

export default function HorizontalLinearStepper({ onFinish }: { onFinish: () => void }) {
  const [activeStep, setActiveStep] = React.useState(0);

  const handleNext = () => {
    setActiveStep((prevActiveStep) => prevActiveStep + 1);
  };

  const handleBack = () => {
    setActiveStep((prevActiveStep) => prevActiveStep - 1);
  };

  const handleSkip = () => {
    setActiveStep((prevActiveStep) => prevActiveStep + 1);
  };

  React.useEffect(() => {
    if (activeStep === steps.length) {
      onFinish();
    }
  }, [activeStep, onFinish]);

  const steps = [
    { heading: businessUnitsLang.title, component: <BusinessUnit handleNextStep={handleNext} /> },
    {
      heading: departmentsLang.title,
      component: <Departments handleNextStep={handleNext} handleBackStep={handleBack} />,
    },
    {
      heading: subDepartmentsLang.title,
      component: <SubDepartment handleNextStep={handleNext} handleBackStep={handleBack} handleSkipStep={handleSkip} />,
    },
    {
      heading: teamsLang.title,
      component: <Team handleNextStep={handleNext} handleBackStep={handleBack} handleSkipStep={handleSkip} />,
    },
    {
      heading: jobFamilyLang.title,
      component: <JobFamily handleNextStep={handleNext} handleBackStep={handleBack} handleSkipStep={handleSkip} />,
    },
    {
      heading: jobTitleLang.title,
      component: <JobTitle handleNextStep={handleNext} handleBackStep={handleBack} refetch={() => {}} />,
    },
    {
      heading: employeeIDConfigurationLang.title,
      component: <EmployeeIDConfiguration handleNextStep={onFinish} handleBackStep={handleBack} />,
    },
  ];

  const CustomConnector = styled(StepConnector)(() => ({
    [`&.${stepConnectorClasses.alternativeLabel}`]: {
      top: 20,
      left: "calc(-50% + 20px)",
      right: "calc(50% + 24px)",
    },
    [`&.${stepConnectorClasses.active}`]: {
      [`& .${stepConnectorClasses.line}`]: {
        borderColor: "#57ba57",
      },
    },
    [`&.${stepConnectorClasses.completed}`]: {
      [`& .${stepConnectorClasses.line}`]: {
        borderColor: "#57ba57",
      },
    },
    [`& .${stepConnectorClasses.line}`]: {
      borderColor: "#eaeaf0",
      borderTopWidth: 3,
      borderRadius: 1,
    },
  }));

  const commonIconStyles = {
    height: 45,
    width: 45,
  };

  const activeIconStyles = {
    backgroundColor: "white",
    borderRadius: "50%",
    display: "flex",
    justifyContent: "center",
    alignItems: "center",
    border: "#57ba57 3px solid",
    width: 45,
    height: 45,
    padding: "auto",
  };

  const iconTextStyle = {};

  const disableIconStyles = {
    backgroundColor: "#d2d2d2",
    width: "45px",
    height: "45px",
    borderRadius: "50%",
    display: "flex",
    justifyContent: "center",
    alignItems: "center",
  };

  const disableIconTextStyle = {
    color: "white",
  };

  const getStepIcon = (index: number, activeStep: number) => {
    return (
      <Box sx={commonIconStyles}>
        {index < activeStep && <TickIcon alt="tick" height={45} />}
        {index === activeStep && (
          <Box sx={activeIconStyles}>
            <Typography sx={iconTextStyle}>{index + 1}</Typography>
          </Box>
        )}
        {index > activeStep && (
          <Box sx={disableIconStyles}>
            <Typography sx={disableIconTextStyle}>{index + 1}</Typography>
          </Box>
        )}
      </Box>
    );
  };

  return (
    <Box
      sx={{
        display: "flex",
        flexDirection: "column",
        height: "100%",
        overflow: "hidden",
      }}
    >
      <Stepper activeStep={activeStep} alternativeLabel connector={<CustomConnector />}>
        {steps.map((step, index) => {
          return (
            <Step key={step.heading}>
              <StepLabel sx={{ width: "130px" }} icon={getStepIcon(index, activeStep)}>
                {step.heading}
              </StepLabel>
            </Step>
          );
        })}
      </Stepper>
      <Box sx={{ overflow: "scroll", padding: "40px 20px 0px 20px" }}>{steps[activeStep].component}</Box>
    </Box>
  );
}
