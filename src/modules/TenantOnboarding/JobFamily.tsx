import { CircularProgress } from "@mui/material";
import { useQuery } from "@tanstack/react-query";
import React, { useEffect, useMemo, useState } from "react";
import { BaseObject } from "src/app/global";
import languageConfig from "src/configs/language/en.lang";
import businessunitsService from "src/services/businessunits.service";
import departmentService from "src/services/department.service";
import { getCurrentTenantId } from "src/utils/authUtils";
import validators from "src/utils/validators";
import { CommonForm, FormConfig } from "./CommonForm";

const {
  jobFamilies: jobFamilyLang,
  businessUnits: businessUnitsLang,
  departments: departmentsLang,
} = languageConfig.tenants.tenantSettings;

const convertListToOptions = (list: BaseObject[], key = "id", value = "name") => {
  return list?.map((item) => ({ value: item[value], label: item[key] }));
};

const rowAdditionaInitialValues = [
  {
    businessUnits: "",
    department: "",
    jobFamily: "",
  },
];

export const JobFamily = ({
  handleNextStep,
  handleBackStep,
  handleSkipStep,
}: {
  handleNextStep: () => void;
  handleBackStep: () => void;
  handleSkipStep: () => void;
}) => {
  const tenantId = getCurrentTenantId();
  const [departmentList, setDepartmentList] = useState<BaseObject[]>([]);

  const { data: businessUnits, isLoading: businessUnitsLoading } = useQuery(
    ["get-business-unit-details"],
    async () => businessunitsService.getBusinessUnitDetails(),
    {
      enabled: !!tenantId,
      retryOnMount: false,
      refetchOnWindowFocus: false,
    },
  );

  const {
    data: jobFamilyList,
    isLoading: jobFamilyListLoading,
    refetch: jobFamilyListRefetch,
  } = useQuery(["get-all-job-families"], async () => departmentService.getAllJobFamilies(), {
    enabled: !!tenantId,
    retryOnMount: false,
    refetchOnWindowFocus: false,
  });

  const { data: allDepartments, isLoading: allDepartmentsLoading } = useQuery(
    ["get-all-departments"],
    async () => departmentService.getAllDepartments(),
    {
      enabled: !!tenantId,
      retryOnMount: false,
      refetchOnWindowFocus: false,
    },
  );

  useEffect(() => {
    const departmentList = jobFamilyList?.map((jobFamily) => {
      const departments = allDepartments?.filter((department) => department.business_unit === jobFamily.business_unit);
      return convertListToOptions(departments as BaseObject[], "name", "name");
    });
    setDepartmentList(departmentList as unknown as BaseObject[]);
  }, [allDepartments, jobFamilyList]);

  const handleNextClick = async (formDetails: BaseObject[]) => {
    const payload = formDetails.map((formDetail) => ({
      business_unit: formDetail.businessUnits,
      name: formDetail.jobFamily,
      department: formDetail.department,
    }));
    if (payload.length > defaultFormState.length || deafaultResponse.length === 0) {
      await departmentService.setJobFamilyDetails(payload);
      jobFamilyListRefetch();
    }

    handleNextStep();
  };
  const captureFormChange = async (ev: React.ChangeEvent<Record<string, never>>, index: number) => {
    const name = ev?.target?.id || ev?.target?.name;
    const value = (ev as unknown as Record<string, unknown>)?.value || ev?.target?.value;
    if (name === "businessUnits") {
      const response = allDepartments?.filter((department) => department.business_unit === value);
      const updatedList = [
        ...departmentList.slice(0, index),
        convertListToOptions(response as BaseObject[], "name", "name"),
        ...departmentList.slice(index + 1),
      ];
      setDepartmentList(updatedList as unknown as BaseObject[]);
    }
  };

  const businessUnitsOptions = convertListToOptions(businessUnits as [], "name", "name");
  const deafaultResponse = useMemo(() => {
    return (
      jobFamilyList?.map((jobFamily: BaseObject) => ({
        businessUnits: jobFamily.business_unit,
        department: jobFamily.department,
        jobFamily: jobFamily.name,
      })) || []
    );
  }, [jobFamilyList]);

  const defaultFormState: BaseObject[] = deafaultResponse.length ? deafaultResponse : rowAdditionaInitialValues;

  const formValidators = {
    businessUnits: [validators.validateInput],
    department: [validators.validateInput],
    jobFamily: [validators.validateInput],
  };
  const inputElements = [
    {
      name: "businessUnits",
      label: businessUnitsLang.inputTitle,
      type: "select",
      style: { flex: "30%" },
      placeholder: businessUnitsLang.selectBusinessUnit,
      isRequired: true,
    },
    {
      name: "department",
      label: departmentsLang.inputTitle,
      type: "select",
      style: { flex: "30%" },
      placeholder: departmentsLang.selectDepartment,
      isDynamicOptions: true,
      isRequired: true,
    },
    {
      name: "jobFamily",
      label: jobFamilyLang.inputTitle,
      type: "text",
      style: { flex: "30%" },
      placeholder: jobFamilyLang.enterJobFamily,
      isRequired: true,
    },
  ];
  const formConfig: FormConfig = {
    addButtonText: jobFamilyLang.addJobFamily,
    onNextClick: handleNextClick,
    formTitle: jobFamilyLang.addJobFamily,
    onBackClick: handleBackStep,
    onSkipClick: handleSkipStep,
  };
  const selectOptions: BaseObject = { businessUnits: businessUnitsOptions, department: departmentList };

  if (businessUnitsLoading || jobFamilyListLoading || allDepartmentsLoading) return <CircularProgress />;
  return (
    <CommonForm
      formConfig={formConfig}
      selectOptions={selectOptions}
      defaultFormState={defaultFormState}
      formValidators={formValidators}
      inputElements={inputElements}
      rowAdditionaInitialValues={rowAdditionaInitialValues}
      captureFormChange={captureFormChange}
    />
  );
};
