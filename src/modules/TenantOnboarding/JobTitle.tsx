import { ArrowBackIos, CloudDownload, Folder } from "@mui/icons-material";
import { Box, Button, Typography, styled } from "@mui/material";
import { useMutation, useQuery } from "@tanstack/react-query";
import React, { useState } from "react";
import languageConfig from "src/configs/language/en.lang";
import FileDropzone, { FileDropVariants } from "src/modules/Common/FileDropzone/FileDropzone";
import { FileUploadResponse } from "src/services/api_definitions/default.service";
import departmentService from "src/services/department.service";
import { getCurrentTenantId } from "src/utils/authUtils";
// import Modal from 'src/modules/Common/Modal/Modal';

interface ImportModalProps {
  refetch: () => void;
  handleBackStep: () => void;
  handleNextStep: () => void;
}

const CustomTypography = styled(Typography)(() => ({
  color: "#667085",
  fontSize: 14,
  fontFamily: "Poppins",
  fontWeight: "400",
  wordWrap: "break-word",
}));

const CustomFileDownloadContainer = styled(Box)(() => ({
  background: "#E6F2F1",
  display: "flex",
  alignItems: "center",
  justifyContent: "space-between",
  borderRadius: "15px",
}));

const CustomSampleIconContainer = styled(Box)(() => ({
  width: "52px",
  height: "52px",
  borderRadius: "50%",
  background: "white",
  display: "flex",
  justifyContent: "center",
  alignItems: "center",
  "&:hover": {
    opacity: 0.5,
    cursor: "pointer",
  },
}));
const backButtonStyle = {
  fontFamily: "Poppins",
  fontSize: "16px",
  fontWeight: 500,
  textTransform: "none",
  margin: 0,
  width: "172px",
  height: "48px",
  flexGrow: 0,
  display: "flex",
  flexDirection: "row",
  justifyContent: "center",
  alignItems: "center",
  padding: "0 20px",
  borderRadius: "50px",
  backgroundColor: "#eff4f8",
  border: "#eff4f8",
};

const nextButtonStyle = {
  fontFamily: "Poppins",
  fontSize: "16px",
  fontWeight: 500,
  textTransform: "none",
  margin: 0,

  width: "172px",
  height: "48px",
  flexGrow: 0,
  display: "flex",
  flexDirection: "row",
  justifyContent: "center",
  alignItems: "center",
  gap: "8px",
  padding: "0 20px",
  borderRadius: "50px",
  backgroundColor: "#007f6f",
  marginLeft: "auto",
};

const ImportModal: React.FC<ImportModalProps> = ({ handleBackStep, handleNextStep, refetch }) => {
  const tenantId = getCurrentTenantId();
  const [files, setFiles] = useState<File[] | null>(null);
  const [fileDropzoneVariant, setFileDropzoneVariant] = useState<FileDropVariants>("default");
  const [errorDetails, setErrorDetails] = useState<FileUploadResponse<string | string[]> | null>(null);
  const mutation = useMutation({
    mutationKey: ["download-job-title-template"],
    mutationFn: async () => departmentService.downloadSampleJobTitleTemplate(),
  });

  const { data: jobTitleList, refetch: refetchJobTitles } = useQuery(
    ["get-all-job-titles"],
    async () => departmentService.getAllJobTitles(),
    {
      enabled: !!tenantId,
      retryOnMount: false,
      refetchOnWindowFocus: false,
    },
  );

  const fileImportMutation = useMutation({
    mutationKey: ["import-job-titles", tenantId],
    mutationFn: async (file: File) => departmentService.uploadFile(file),
    onSuccess: (data) => {
      if (data.type === "success") {
        refetch();
        refetchJobTitles();
        handleNextStep();
      } else {
        setFileDropzoneVariant("error");
        setErrorDetails(data);
      }
    },
  });

  const onSkipClick = async () => {
    handleNextStep();
  };

  const onFileDrop = async <T extends File>(files: T[]) => {
    setFiles(files);
  };

  const onImportClick = async () => {
    if (files && files?.length > 0) {
      fileImportMutation.mutate(files[0]);
    }
  };

  // const onSkipClick = async () => {
  //   handleNextStep();
  // };

  const onDownloadSampleIconClick = () => {
    mutation.mutate();
  };

  return (
    <Box>
      <FileDropzone
        variant={fileDropzoneVariant}
        files={files}
        width="100%"
        onFileDrop={onFileDrop}
        acceptFileTypes={{
          "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet": [".xlsx"],
        }}
        message={typeof errorDetails?.message === "string" ? errorDetails.message : undefined}
      />
      <Box display="flex" justifyContent="space-between" alignItems="center" sx={{ margin: "15px 0px" }}>
        <CustomTypography>Supported Format: XLSX</CustomTypography>
        <CustomTypography>Maximum Size: 5mb</CustomTypography>
      </Box>
      <CustomFileDownloadContainer>
        <Box
          display="flex"
          justifyContent="space-between"
          alignItems="center"
          sx={{ width: "100%", padding: "14px 32px" }}
        >
          <Box display="flex" flexDirection="column" gap={1}>
            <Box display="flex" gap={1}>
              <Folder />
              <Typography>{languageConfig.tenants.workRoles.sampleFileTitle}</Typography>
            </Box>
            <Typography sx={{ color: "#667085", fontSize: 12, wordWrap: "break-word", width: 385 }}>
              {languageConfig.tenants.workRoles.sampleFileSubtitle}
            </Typography>
          </Box>
          <CustomSampleIconContainer onClick={onDownloadSampleIconClick}>
            <CloudDownload sx={{ fill: "black" }} />
          </CustomSampleIconContainer>
        </Box>
      </CustomFileDownloadContainer>
      <Box sx={{ padding: 1, marginTop: 8, display: "flex", justifyContent: "space-between" }}>
        <Button startIcon={<ArrowBackIos />} sx={backButtonStyle} onClick={handleBackStep} variant="outlined">
          Back
        </Button>
        <Box display="flex" gap={2}>
          {jobTitleList?.length !== 0 && (
            <Button sx={backButtonStyle} onClick={onSkipClick} variant="outlined">
              Skip
            </Button>
          )}
          <Button
            sx={nextButtonStyle}
            onClick={onImportClick}
            variant="contained"
            disabled={!files || files?.length === 0}
          >
            Import
          </Button>
        </Box>
      </Box>
    </Box>
  );
};

export default ImportModal;
