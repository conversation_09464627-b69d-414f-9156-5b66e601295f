import { Delete, Edit } from "@mui/icons-material";
import { Box, Button, IconButton } from "@mui/material";
import { useQuery } from "@tanstack/react-query";
import React, { useEffect, useMemo, useState } from "react";
import { BaseObject } from "src/app/global";
import languageConfig from "src/configs/language/en.lang";
import ContentHeader from "src/modules/Common/ContentHeader/ContentHeader";
import EffiDynamicForm from "src/modules/Common/Form/components/EffiDynamicForm";
import { useAppForm } from "src/modules/Common/Form/effiFormContext";
import Modal from "src/modules/Common/Modal/Modal";
import DataTable from "src/modules/Common/Table/DataTable";
import businessunitsService from "src/services/businessunits.service";
import tenantsService from "src/services/tenants.service";
import { getCurrentTenantId } from "src/utils/authUtils";
import { z } from "zod";
import DeleteConfirmationModal from "../../Common/DeleteConfirmationModal";

const { businessUnits: businessUnitsLang, descriptionLable } = languageConfig.tenants.tenantSettings;

const convertListToOptions = (list: BaseObject[], key = "id", value = "name") => {
  return list?.map((item) => ({ value: item[value], label: item[key] }));
};

const getRequestPayload = (formDetail: BaseObject) => {
  return {
    name: formDetail.name,
    cost_center: formDetail.costCenter,
    description: formDetail.description,
  };
};

export const BusinessUnit = () => {
  const tenantId = getCurrentTenantId();
  const { data: costCenters } = useQuery(["cost-center-details"], async () => tenantsService.getCostCenterDetails(), {
    enabled: !!tenantId,
    retryOnMount: false,
    refetchOnWindowFocus: false,
  });

  const { data: businessUnits, refetch: refetchBU } = useQuery(
    ["get-business-unit-details"],
    async () => businessunitsService.getBusinessUnitDetails(),
    {
      enabled: !!tenantId,
      retryOnMount: false,
      refetchOnWindowFocus: false,
    },
  );

  const centerCodesOptions = convertListToOptions(costCenters as [], "code", "code");

  const deafaultResponse =
    businessUnits?.map((businessUnit) => ({
      name: businessUnit.name,
      costCenter: businessUnit.cost_center,
      description: businessUnit.description,
    })) || [];

  const rowAdditionaInitialValues = {
    name: "",
    costCenter: "",
    description: "",
  };
  const defaultFormState: BaseObject[] = deafaultResponse;

  const postFormSubmit = () => {
    setIsAddModalOpen(false);
    setIsEditModalOpen(false);
    setIsDeleteModalOpen(false);
    refetchBU();
  };

  const handleEditDetailsClick = async (formDetails: BaseObject, selectedIndex: number) => {
    const parsedData = getRequestPayload(formDetails);
    const requestObject = {
      new_name: parsedData.name,
      name: businessUnits?.[selectedIndex]?.name,
      cost_center: parsedData?.cost_center,
      description: parsedData?.description,
    };
    const response = await businessunitsService.updateBusinessUnitDetails(requestObject);
    if (response) {
      postFormSubmit();
    }
  };

  const handleAddDetailsClick = async (formDetails: BaseObject) => {
    const payload = getRequestPayload(formDetails);
    const response = await businessunitsService.setBusinessUnitDetails([payload]);
    if (response) {
      postFormSubmit();
    }
  };

  const handleDeleteConfirmed = async (index: number) => {
    const response = await businessunitsService.deleteBusinessUnitDetails(businessUnits?.[index] || {});
    if (response) {
      postFormSubmit();
    }
  };

  const formValidators = z.object({
    name: z.string().nonempty({
      message: "Name is required",
    }),
    costCenter: z.string().nonempty({
      message: "Cost Center is required",
    }),
    description: z.string().optional().nullable(),
  });

  const [selectedRow, setSelectedRow] = useState<number | null>(null);

  const selectedRowData = useMemo(
    () => (selectedRow !== null ? deafaultResponse?.[selectedRow] : rowAdditionaInitialValues),
    [selectedRow],
  );

  const deleteText = `Are you sure you want to delete this ${deafaultResponse[selectedRow as number]?.name} ${businessUnitsLang.inputTitle}?`;

  const columns = [
    { accessorKey: "name", header: "Name" },
    { accessorKey: "costCenter", header: languageConfig.tenants.costCenter.title },
    { accessorKey: "description", header: descriptionLable },
    {
      accessorKey: "actions",
      header: "Actions",
      Cell: ({ row }: { row: any }) => (
        <Box display="flex" gap={1}>
          <IconButton
            onClick={() => {
              setSelectedRow(row.index);
              setIsEditModalOpen(true);
            }}
          >
            <Edit />
          </IconButton>
          <IconButton
            onClick={() => {
              setSelectedRow(row.index);
              setIsDeleteModalOpen(true);
            }}
          >
            <Delete />
          </IconButton>
        </Box>
      ),
    },
  ];

  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);

  useEffect(() => {
    if (!isAddModalOpen && !isEditModalOpen) {
      form.reset();
      setSelectedRow(null);
    }
  }, [isAddModalOpen, isEditModalOpen]);

  const form = useAppForm({
    defaultValues: selectedRowData,
    onSubmit: (values) => {
      if (selectedRow !== null) {
        handleEditDetailsClick(values?.value, selectedRow as number);
      } else {
        handleAddDetailsClick(values?.value);
      }
    },
    validators: {
      onChange: formValidators,
    },
  });

  const inputFields = [
    {
      fieldProps: {
        name: "name",
      },
      formProps: {
        label: businessUnitsLang.inputTitle,
        type: "text",
        required: true,
        options: centerCodesOptions,
        placeholder: "Enter Business Unit",
        // disabled: isFirstTime,
      },
      containerProps: {
        size: 4,
      },
    },
    {
      fieldProps: {
        name: "costCenter",
      },
      formProps: {
        label: languageConfig.tenants.costCenter.title,
        type: "select",
        required: true,
        options: centerCodesOptions,
        placeholder: "Select Cost Center",
        // disabled: selectedRow !== null,
      },
      containerProps: {
        size: 4,
      },
    },
    {
      fieldProps: {
        name: "description",
      },
      formProps: {
        label: descriptionLable,
        type: "text",
        placeholder: "Enter Description",
      },
      containerProps: {
        size: 4,
      },
    },
  ];

  const BusinessUnitForm = () => {
    return (
      <Box>
        <EffiDynamicForm form={form} inputFields={inputFields} />
        <form.Subscribe
          selector={(state) => [
            state.canSubmit,
            state.isSubmitting,
            state.isPristine,
            state.values,
            state.isDefaultValue,
          ]}
        >
          {([canSubmit, isSubmitting, isPristine, _, isDefaultValue]) => {
            return (
              <Box display="flex" p={2} gap={1} justifyContent="flex-end">
                <Button
                  variant="outlined"
                  onClick={() => {
                    setIsAddModalOpen(false);
                    setIsEditModalOpen(false);
                  }}
                >
                  Cancel
                </Button>
                <Button
                  variant="contained"
                  disabled={!canSubmit || isPristine || isSubmitting || isDefaultValue}
                  onClick={form.handleSubmit}
                >
                  Save
                </Button>
              </Box>
            );
          }}
        </form.Subscribe>
      </Box>
    );
  };

  return (
    <Box>
      <ContentHeader
        title={businessUnitsLang.title}
        subtitle={""}
        primaryAction={() => {
          setIsAddModalOpen(true);
        }}
        buttonTitle={businessUnitsLang.addBusinessUnit}
        allowAction={true}
      />
      <Box sx={{ margin: "20px 0px" }}>
        <DataTable data={defaultFormState} columns={columns} />
        <Modal
          isOpen={isAddModalOpen}
          onClose={() => setIsAddModalOpen(false)}
          title={businessUnitsLang.addBusinessUnit}
        >
          <BusinessUnitForm />
        </Modal>
        <Modal
          isOpen={isEditModalOpen}
          onClose={() => setIsEditModalOpen(false)}
          title={businessUnitsLang.editBusinessUnit}
        >
          <BusinessUnitForm />
        </Modal>
        <DeleteConfirmationModal
          isModalOpen={isDeleteModalOpen}
          onCancel={() => setIsDeleteModalOpen(false)}
          onDelete={() => handleDeleteConfirmed(selectedRow as number)}
          title={deleteText}
          selectedRole={deafaultResponse[selectedRow as number]?.name}
        />
      </Box>
    </Box>
  );
};
