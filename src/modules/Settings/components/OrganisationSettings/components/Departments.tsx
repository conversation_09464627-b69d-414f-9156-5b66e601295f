import { Delete, Edit } from "@mui/icons-material";
import { Box, Button, IconButton } from "@mui/material";
import { useQuery } from "@tanstack/react-query";
import React, { useEffect, useMemo, useState } from "react";
import { BaseObject } from "src/app/global";
import languageConfig from "src/configs/language/en.lang";
import ContentHeader from "src/modules/Common/ContentHeader/ContentHeader";
import EffiDynamicForm from "src/modules/Common/Form/components/EffiDynamicForm";
import { useAppForm } from "src/modules/Common/Form/effiFormContext";
import Modal from "src/modules/Common/Modal/Modal";
import DataTable from "src/modules/Common/Table/DataTable";
import businessunitsService from "src/services/businessunits.service";
import departmentService from "src/services/department.service";
import { getCurrentTenantId } from "src/utils/authUtils";
import { z } from "zod";
import DeleteConfirmationModal from "../../Common/DeleteConfirmationModal";

const { departments: departmentsLang, businessUnits: businessUnitsLang } = languageConfig.tenants.tenantSettings;

const convertListToOptions = (list: BaseObject[], key = "id", value = "name") => {
  return list?.map((item) => ({ value: item[value], label: item[key] }));
};

const getRequestPayload = (formDetail: BaseObject) => {
  return {
    business_unit: formDetail.businessUnits,
    name: formDetail.department,
  };
};

export const Departments = () => {
  const tenantId = getCurrentTenantId();
  const { data: departmentList, refetch: refetchDepartment } = useQuery(
    ["get-all-departments"],
    async () => departmentService.getAllDepartments(),
    {
      enabled: !!tenantId,
      retryOnMount: false,
      refetchOnWindowFocus: false,
    },
  );

  const { data: businessUnits } = useQuery(
    ["get-business-unit-details"],
    async () => businessunitsService.getBusinessUnitDetails(),
    {
      enabled: !!tenantId,
      retryOnMount: false,
      refetchOnWindowFocus: false,
    },
  );

  const businessUnitsOptions = convertListToOptions(businessUnits as [], "name", "name");
  const deafaultResponse =
    departmentList?.map((businessUnit: BaseObject) => ({
      businessUnits: businessUnit.business_unit,
      department: businessUnit.name,
    })) || [];

  const rowAdditionaInitialValues = {
    businessUnits: "",
    department: "",
  };
  const defaultFormState: BaseObject[] = deafaultResponse;

  const postFormSubmit = () => {
    refetchDepartment();
    setIsAddModalOpen(false);
    setIsEditModalOpen(false);
    setIsDeleteModalOpen(false);
  };

  const handleEditDetailsClick = async (formDetails: BaseObject, selectedIndex: number) => {
    const parsedData = getRequestPayload(formDetails);
    const requestObject = {
      new_name: parsedData.name,
      name: departmentList?.[selectedIndex]?.name,
      business_unit: parsedData?.business_unit,
    };
    const response = await departmentService.updateDepartmentDetails(requestObject);
    if (response) {
      postFormSubmit();
    }
  };

  const handleAddDetailsClick = async (formDetails: BaseObject) => {
    const payload = getRequestPayload(formDetails);
    const response = await departmentService.setDepartmentDetails([payload]);
    if (response) {
      postFormSubmit();
    }
  };

  const handleDeleteConfirmed = async (index: number) => {
    const response = await departmentService.deleteDepartmentDetails(departmentList?.[index] || {});
    if (response) {
      postFormSubmit();
    }
  };

  const formValidators = z.object({
    businessUnits: z.string().nonempty({
      message: "Business Unit is required",
    }),
    department: z.string().nonempty({
      message: "Department is required",
    }),
  });

  const [selectedRow, setSelectedRow] = useState<number | null>(null);

  const selectedRowData = useMemo(
    () => (selectedRow !== null ? deafaultResponse?.[selectedRow] : rowAdditionaInitialValues),
    [selectedRow],
  );

  const deleteText = `Are you sure you want to delete this ${deafaultResponse[selectedRow as number]?.department} ${departmentsLang.inputTitle}?`;

  const columns = [
    { accessorKey: "department", header: "Name" },
    { accessorKey: "businessUnits", header: businessUnitsLang.inputTitle },
    {
      accessorKey: "actions",
      header: "Actions",
      Cell: ({ row }: { row: any }) => (
        <Box display="flex" gap={1}>
          <IconButton
            onClick={() => {
              setSelectedRow(row.index);
              setIsEditModalOpen(true);
            }}
          >
            <Edit />
          </IconButton>
          <IconButton
            onClick={() => {
              setSelectedRow(row.index);
              setIsDeleteModalOpen(true);
            }}
          >
            <Delete />
          </IconButton>
        </Box>
      ),
    },
  ];

  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);

  useEffect(() => {
    if (!isAddModalOpen && !isEditModalOpen) {
      form.reset();
      setSelectedRow(null);
    }
  }, [isAddModalOpen, isEditModalOpen]);

  const form = useAppForm({
    defaultValues: selectedRowData,
    onSubmit: (values) => {
      console.log(values);
      if (selectedRow !== null) {
        handleEditDetailsClick(values?.value, selectedRow as number);
      } else {
        handleAddDetailsClick(values?.value);
      }
    },
    validators: {
      onChange: formValidators,
    },
  });

  const inputFields = [
    {
      fieldProps: {
        name: "businessUnits",
      },
      formProps: {
        label: businessUnitsLang.inputTitle,
        type: "select",
        required: true,
        options: businessUnitsOptions,
        placeholder: "Select Business Unit",
        disabled: selectedRow !== null,
      },
      containerProps: {
        size: 6,
      },
    },
    {
      fieldProps: {
        name: "department",
      },
      formProps: {
        label: departmentsLang.inputTitle,
        type: "text",
        required: true,
        placeholder: "Enter Department",
      },
      containerProps: {
        size: 6,
      },
    },
  ];

  const DepartmentForm = () => {
    return (
      <Box>
        <EffiDynamicForm form={form} inputFields={inputFields} />
        <form.Subscribe
          selector={(state: any) => [
            state.canSubmit,
            state.isSubmitting,
            state.isPristine,
            state.values,
            state.isDefaultValue,
          ]}
        >
          {([canSubmit, isSubmitting, isPristine, _, isDefaultValue]: any) => {
            return (
              <Box display="flex" p={2} gap={1} justifyContent="flex-end">
                <Button
                  variant="outlined"
                  onClick={() => {
                    setIsAddModalOpen(false);
                    setIsEditModalOpen(false);
                  }}
                >
                  Cancel
                </Button>
                <Button
                  variant="contained"
                  disabled={!canSubmit || isPristine || isSubmitting || isDefaultValue}
                  onClick={form.handleSubmit}
                >
                  Save
                </Button>
              </Box>
            );
          }}
        </form.Subscribe>
      </Box>
    );
  };

  return (
    <Box>
      <ContentHeader
        title={departmentsLang.title}
        subtitle={""}
        primaryAction={() => {
          setIsAddModalOpen(true);
        }}
        buttonTitle={departmentsLang.addDepartment}
        allowAction={true}
      />
      <Box sx={{ margin: "20px 0px" }}>
        <DataTable data={defaultFormState} columns={columns} />
        <Modal isOpen={isAddModalOpen} onClose={() => setIsAddModalOpen(false)} title={departmentsLang.addDepartment}>
          <DepartmentForm />
        </Modal>

        <Modal
          isOpen={isEditModalOpen}
          onClose={() => setIsEditModalOpen(false)}
          title={departmentsLang.editDepartment}
        >
          <DepartmentForm />
        </Modal>

        <DeleteConfirmationModal
          isModalOpen={isDeleteModalOpen}
          onCancel={() => setIsDeleteModalOpen(false)}
          onDelete={() => handleDeleteConfirmed(selectedRow as number)}
          title={deleteText}
          selectedRole={deafaultResponse[selectedRow as number]?.department as string}
        />
      </Box>
    </Box>
  );
};
