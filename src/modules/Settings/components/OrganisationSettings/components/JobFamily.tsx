import { Delete, Edit } from "@mui/icons-material";
import { Box, Button, IconButton } from "@mui/material";
import { useStore } from "@tanstack/react-form";
import { useQuery } from "@tanstack/react-query";
import React, { useEffect, useMemo, useState } from "react";
import { BaseObject } from "src/app/global";
import languageConfig from "src/configs/language/en.lang";
import ContentHeader from "src/modules/Common/ContentHeader/ContentHeader";
import EffiDynamicForm from "src/modules/Common/Form/components/EffiDynamicForm";
import { useAppForm } from "src/modules/Common/Form/effiFormContext";
import Modal from "src/modules/Common/Modal/Modal";
import DataTable from "src/modules/Common/Table/DataTable";
import businessunitsService from "src/services/businessunits.service";
import departmentService from "src/services/department.service";
import { getCurrentTenantId } from "src/utils/authUtils";
import { z } from "zod";
import DeleteConfirmationModal from "../../Common/DeleteConfirmationModal";

const {
  jobFamilies: jobFamilyLang,
  departments: departmentsLang,
  businessUnits: businessUnitsLang,
} = languageConfig.tenants.tenantSettings;

const convertListToOptions = (list: BaseObject[], key = "id", value = "name") => {
  return list?.map((item) => ({ value: item[value], label: item[key] }));
};

const getRequestPayload = (formDetail: BaseObject) => {
  return {
    business_unit: formDetail.businessUnits,
    name: formDetail.jobFamily,
    department: formDetail.department,
  };
};

const JobFamilyForm = ({
  form,
  inputFields,
  setIsAddModalOpen,
  setIsEditModalOpen,
}: {
  form: any;
  inputFields: any[];
  setIsAddModalOpen: (value: boolean) => void;
  setIsEditModalOpen: (value: boolean) => void;
}) => {
  return (
    <Box>
      <EffiDynamicForm form={form} inputFields={inputFields} />
      <form.Subscribe
        selector={(state: any) => [
          state.canSubmit,
          state.isSubmitting,
          state.isPristine,
          state.values,
          state.isDefaultValue,
        ]}
      >
        {([canSubmit, isSubmitting, isPristine, _, isDefaultValue]: any) => {
          return (
            <Box display="flex" p={2} gap={1} justifyContent="flex-end">
              <Button
                variant="outlined"
                onClick={() => {
                  setIsAddModalOpen(false);
                  setIsEditModalOpen(false);
                }}
              >
                Cancel
              </Button>
              <Button
                variant="contained"
                disabled={!canSubmit || isPristine || isSubmitting || isDefaultValue}
                onClick={form.handleSubmit}
              >
                Save
              </Button>
            </Box>
          );
        }}
      </form.Subscribe>
    </Box>
  );
};

export const JobFamily = () => {
  const tenantId = getCurrentTenantId();

  const { data: businessUnits } = useQuery(
    ["get-business-unit-details"],
    async () => businessunitsService.getBusinessUnitDetails(),
    {
      enabled: !!tenantId,
      retryOnMount: false,
      refetchOnWindowFocus: false,
    },
  );

  const { data: jobFamilyList, refetch: refetchJobFamilies } = useQuery(
    ["get-all-job-families"],
    async () => departmentService.getAllJobFamilies(),
    {
      enabled: !!tenantId,
      retryOnMount: false,
      refetchOnWindowFocus: false,
    },
  );

  const { data: allDepartments } = useQuery(
    ["get-all-departments"],
    async () => departmentService.getAllDepartments(),
    {
      enabled: !!tenantId,
      retryOnMount: false,
      refetchOnWindowFocus: false,
    },
  );

  const businessUnitsOptions = convertListToOptions(businessUnits as [], "name", "name");
  const deafaultResponse =
    jobFamilyList?.map((jobFamily: BaseObject) => ({
      businessUnits: jobFamily.business_unit,
      department: jobFamily.department,
      jobFamily: jobFamily.name,
    })) || [];

  const rowAdditionaInitialValues = {
    businessUnits: "",
    department: "",
    jobFamily: "",
  };
  const defaultFormState: BaseObject[] = deafaultResponse;

  const postFormSubmit = () => {
    refetchJobFamilies();
    setIsAddModalOpen(false);
    setIsEditModalOpen(false);
    setIsDeleteModalOpen(false);
  };

  const handleEditDetailsClick = async (formDetails: BaseObject, selectedIndex: number) => {
    const parsedData = getRequestPayload(formDetails);
    const requestObject = {
      new_name: parsedData.name,
      name: jobFamilyList?.[selectedIndex]?.name,
      business_unit: parsedData?.business_unit,
      department: parsedData?.department,
    };
    const response = await departmentService.updateJobFamilyDetails(requestObject);
    if (response) {
      postFormSubmit();
    }
  };

  const handleAddDetailsClick = async (formDetails: BaseObject) => {
    const payload = getRequestPayload(formDetails);
    const response = await departmentService.setJobFamilyDetails([payload]);
    if (response) {
      postFormSubmit();
    }
  };

  const handleDeleteConfirmed = async (index: number) => {
    const response = await departmentService.deleteJobFamilyDetails(jobFamilyList?.[index] || {});
    if (response) {
      postFormSubmit();
    }
  };

  const formValidators = z.object({
    businessUnits: z.string().nonempty({
      message: "Business Unit is required",
    }),
    department: z.string().nonempty({
      message: "Department is required",
    }),
    jobFamily: z.string().nonempty({
      message: "Job Family is required",
    }),
  });

  const [selectedRow, setSelectedRow] = useState<number | null>(null);

  const selectedRowData = useMemo(
    () => (selectedRow !== null ? deafaultResponse?.[selectedRow] : rowAdditionaInitialValues),
    [selectedRow],
  );

  const deleteText = `Are you sure you want to delete this ${deafaultResponse[selectedRow as number]?.jobFamily} ${jobFamilyLang.inputTitle}?`;

  const columns = [
    { accessorKey: "jobFamily", header: "Name" },
    { accessorKey: "department", header: "Department" },
    { accessorKey: "businessUnits", header: "Business Unit" },
    {
      accessorKey: "actions",
      header: "Actions",
      Cell: ({ row }: { row: any }) => (
        <Box display="flex" gap={1}>
          <IconButton
            onClick={() => {
              setSelectedRow(row.index);
              setIsEditModalOpen(true);
            }}
          >
            <Edit />
          </IconButton>
          <IconButton
            onClick={() => {
              setSelectedRow(row.index);
              setIsDeleteModalOpen(true);
            }}
          >
            <Delete />
          </IconButton>
        </Box>
      ),
    },
  ];

  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);

  useEffect(() => {
    if (!isAddModalOpen && !isEditModalOpen) {
      form.reset();
      setSelectedRow(null);
    }
  }, [isAddModalOpen, isEditModalOpen]);

  const form = useAppForm({
    defaultValues: selectedRowData,
    onSubmit: (values) => {
      if (selectedRow !== null) {
        handleEditDetailsClick(values?.value, selectedRow as number);
      } else {
        handleAddDetailsClick(values?.value);
      }
    },
    validators: {
      onChange: formValidators,
    },
  });

  const { businessUnits: selectedBusinessUnit } = useStore(form.store, (state: any) => state.values);

  const getDepartmentOptions = (businessUnit: string) => {
    const departments: BaseObject[] =
      allDepartments?.filter((department) => department.business_unit === businessUnit) || [];
    const depatmentOptions = convertListToOptions(departments, "name", "name");
    return depatmentOptions as unknown as BaseObject[];
  };

  const inputFields = [
    {
      fieldProps: {
        name: "businessUnits",
      },
      formProps: {
        label: businessUnitsLang.inputTitle,
        type: "select",
        required: true,
        options: businessUnitsOptions,
        placeholder: "Select Business Unit",
        disabled: selectedRow !== null,
      },
      containerProps: {
        size: 4,
      },
    },
    {
      fieldProps: {
        name: "department",
      },
      formProps: {
        label: departmentsLang.inputTitle,
        type: "select",
        required: true,
        options: getDepartmentOptions(selectedBusinessUnit),
        placeholder: "Select Department",
        disabled: selectedRow !== null,
      },
      containerProps: {
        size: 4,
      },
    },
    {
      fieldProps: {
        name: "jobFamily",
      },
      formProps: {
        label: jobFamilyLang.inputTitle,
        type: "text",
        required: true,
        placeholder: "Enter Job Family",
      },
      containerProps: {
        size: 4,
      },
    },
  ];

  return (
    <Box>
      <ContentHeader
        title={jobFamilyLang.title}
        subtitle={""}
        primaryAction={() => {
          setIsAddModalOpen(true);
        }}
        buttonTitle="Add Job Family"
        allowAction={true}
      />
      <Box sx={{ margin: "20px 0px" }}>
        <DataTable data={defaultFormState} columns={columns} />
        <Modal isOpen={isAddModalOpen} onClose={() => setIsAddModalOpen(false)} title="Add Job Family">
          <JobFamilyForm
            form={form}
            inputFields={inputFields}
            setIsAddModalOpen={setIsAddModalOpen}
            setIsEditModalOpen={setIsEditModalOpen}
          />
        </Modal>
        <Modal isOpen={isEditModalOpen} onClose={() => setIsEditModalOpen(false)} title="Edit Job Family">
          <JobFamilyForm
            form={form}
            inputFields={inputFields}
            setIsAddModalOpen={setIsAddModalOpen}
            setIsEditModalOpen={setIsEditModalOpen}
          />
        </Modal>
        <DeleteConfirmationModal
          isModalOpen={isDeleteModalOpen}
          onCancel={() => setIsDeleteModalOpen(false)}
          onDelete={() => handleDeleteConfirmed(selectedRow as number)}
          title={deleteText}
          selectedRole={deafaultResponse[selectedRow as number]?.jobFamily as string}
        />
      </Box>
    </Box>
  );
};
