import { Delete, Edit } from "@mui/icons-material";
import { Box, Button, IconButton } from "@mui/material";
import { useStore } from "@tanstack/react-form";
import { useQuery } from "@tanstack/react-query";
import React, { useEffect, useMemo, useState } from "react";
import { BaseObject } from "src/app/global";
import languageConfig from "src/configs/language/en.lang";
import ContentHeader from "src/modules/Common/ContentHeader/ContentHeader";
import EffiDynamicForm from "src/modules/Common/Form/components/EffiDynamicForm";
import { useAppForm } from "src/modules/Common/Form/effiFormContext";
import Modal from "src/modules/Common/Modal/Modal";
import DataTable from "src/modules/Common/Table/DataTable";
import businessunitsService from "src/services/businessunits.service";
import departmentService from "src/services/department.service";
import { getCurrentTenantId } from "src/utils/authUtils";
import { z } from "zod";
import DeleteConfirmationModal from "../../Common/DeleteConfirmationModal";

const {
  jobTitles: jobTitleLang,
  departments: departmentsLang,
  businessUnits: businessUnitsLang,
  workRole: workRoleLang,
} = languageConfig.tenants.tenantSettings;

const convertListToOptions = (list: BaseObject[], key = "id", value = "name") => {
  return list?.map((item) => ({ value: item[value], label: item[key] }));
};

const getRequestPayload = (formDetail: BaseObject) => {
  return {
    business_unit: formDetail.businessUnits,
    name: formDetail.jobTitle,
    department: formDetail.department,
    work_role: formDetail.workRole,
  };
};

const constructWorkRoleOptions = (workRoleList?: BaseObject[]) => {
  if (workRoleList && workRoleList?.length > 0) {
    const result = workRoleList.map((item) => {
      return {
        value: item.name,
        label: item.name,
      };
    });
    return result;
  }
  return [];
};

const JobTitleForm = ({
  form,
  inputFields,
  setIsAddModalOpen,
  setIsEditModalOpen,
}: {
  form: any;
  inputFields: any[];
  setIsAddModalOpen: (value: boolean) => void;
  setIsEditModalOpen: (value: boolean) => void;
}) => {
  return (
    <Box>
      <EffiDynamicForm form={form} inputFields={inputFields} />
      <form.Subscribe
        selector={(state: any) => [
          state.canSubmit,
          state.isSubmitting,
          state.isPristine,
          state.values,
          state.isDefaultValue,
        ]}
      >
        {([canSubmit, isSubmitting, isPristine, _, isDefaultValue]: any) => {
          return (
            <Box display="flex" p={2} gap={1} justifyContent="flex-end">
              <Button
                variant="outlined"
                onClick={() => {
                  setIsAddModalOpen(false);
                  setIsEditModalOpen(false);
                }}
              >
                Cancel
              </Button>
              <Button
                variant="contained"
                disabled={!canSubmit || isPristine || isSubmitting || isDefaultValue}
                onClick={form.handleSubmit}
              >
                Save
              </Button>
            </Box>
          );
        }}
      </form.Subscribe>
    </Box>
  );
};

export const JobTitle = () => {
  const tenantId = getCurrentTenantId();

  const { data: businessUnits } = useQuery(
    ["get-business-unit-details"],
    async () => businessunitsService.getBusinessUnitDetails(),
    {
      enabled: !!tenantId,
      retryOnMount: false,
      refetchOnWindowFocus: false,
    },
  );

  const { data: jobTitleList, refetch: refetchJobTitles } = useQuery(
    ["get-all-job-titles"],
    async () => departmentService.getAllJobTitles(),
    {
      enabled: !!tenantId,
      retryOnMount: false,
      refetchOnWindowFocus: false,
    },
  );

  const { data: allDepartments } = useQuery(
    ["get-all-departments"],
    async () => departmentService.getAllDepartments(),
    {
      enabled: !!tenantId,
      retryOnMount: false,
      refetchOnWindowFocus: false,
    },
  );

  const { data: workRoleList } = useQuery(
    ["get-all-work-roles"],
    async () => departmentService.getAllWorkRoles(tenantId),
    {
      enabled: !!tenantId,
      retryOnMount: false,
      refetchOnWindowFocus: false,
    },
  );

  const businessUnitsOptions = useMemo(
    () => convertListToOptions(businessUnits as [], "name", "name"),
    [businessUnits],
  );

  const deafaultResponse =
    jobTitleList?.map((jobTitle: BaseObject) => ({
      businessUnits: jobTitle.business_unit,
      department: jobTitle.department,
      jobTitle: jobTitle.name,
      workRole: jobTitle?.work_role_name,
    })) || [];

  const rowAdditionaInitialValues = {
    businessUnits: "",
    department: "",
    jobTitle: "",
    workRole: "",
  };
  const defaultFormState: BaseObject[] = deafaultResponse;

  const postFormSubmit = () => {
    refetchJobTitles();
    setIsAddModalOpen(false);
    setIsEditModalOpen(false);
    setIsDeleteModalOpen(false);
  };

  const handleEditDetailsClick = async (formDetails: BaseObject, selectedIndex: number) => {
    const parsedData = getRequestPayload(formDetails);
    const requestObject = {
      ...parsedData,
      new_name: parsedData.name,
      name: jobTitleList?.[selectedIndex]?.name,
    };
    const response = await departmentService.updateJobTitleDetails(requestObject);
    if (response) {
      postFormSubmit();
    }
  };

  const handleAddDetailsClick = async (formDetails: BaseObject) => {
    const payload = getRequestPayload(formDetails);
    const response = await departmentService.setJobTitleDetails([payload]);
    if (response) {
      postFormSubmit();
    }
  };

  const handleDeleteConfirmed = async (index: number) => {
    const response = await departmentService.deleteJobTitleDetails(jobTitleList?.[index] || {});
    if (response) {
      postFormSubmit();
    }
  };

  const formValidators = z.object({
    businessUnits: z.string().nonempty({
      message: "Business Unit is required",
    }),
    department: z.string().nonempty({
      message: "Department is required",
    }),
    jobTitle: z.string().nonempty({
      message: "Job Title is required",
    }),
    workRole: z.string().nonempty({
      message: "Work Role is required",
    }),
  });

  const [selectedRow, setSelectedRow] = useState<number | null>(null);

  const selectedRowData = useMemo(
    () => (selectedRow !== null ? deafaultResponse?.[selectedRow] : rowAdditionaInitialValues),
    [selectedRow],
  );

  const deleteText = `Are you sure you want to delete this ${deafaultResponse[selectedRow as number]?.jobTitle} ${jobTitleLang.inputTitle}?`;

  const columns = [
    { accessorKey: "jobTitle", header: "Name" },
    { accessorKey: "workRole", header: workRoleLang.inputTitle },
    { accessorKey: "department", header: departmentsLang.inputTitle },
    { accessorKey: "businessUnits", header: businessUnitsLang.inputTitle },
    {
      accessorKey: "actions",
      header: "Actions",
      Cell: ({ row }: { row: any }) => (
        <Box display="flex" gap={1}>
          <IconButton
            onClick={() => {
              setSelectedRow(row.index);
              setIsEditModalOpen(true);
            }}
          >
            <Edit />
          </IconButton>
          <IconButton
            onClick={() => {
              setSelectedRow(row.index);
              setIsDeleteModalOpen(true);
            }}
          >
            <Delete />
          </IconButton>
        </Box>
      ),
    },
  ];

  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);

  useEffect(() => {
    if (!isAddModalOpen && !isEditModalOpen) {
      form.reset();
      setSelectedRow(null);
    }
  }, [isAddModalOpen, isEditModalOpen]);

  const form = useAppForm({
    defaultValues: selectedRowData,
    onSubmit: (values) => {
      if (selectedRow !== null) {
        handleEditDetailsClick(values?.value, selectedRow as number);
      } else {
        handleAddDetailsClick(values?.value);
      }
    },
    validators: {
      onChange: formValidators,
    },
  });

  const { businessUnits: selectedBusinessUnit } = useStore(form.store, (state: any) => state.values);

  const getDepartmentOptions = (businessUnit: string) => {
    const departments: BaseObject[] =
      allDepartments?.filter((department) => department.business_unit === businessUnit) || [];
    const depatmentOptions = convertListToOptions(departments, "name", "name");
    return depatmentOptions as unknown as BaseObject[];
  };

  const inputFields = [
    {
      fieldProps: {
        name: "businessUnits",
      },
      formProps: {
        label: businessUnitsLang.inputTitle,
        type: "select",
        required: true,
        options: businessUnitsOptions,
        placeholder: "Select Business Unit",
        disabled: selectedRow !== null,
      },
      containerProps: {
        size: 4,
      },
    },
    {
      fieldProps: {
        name: "department",
      },
      formProps: {
        label: departmentsLang.inputTitle,
        type: "select",
        required: true,
        options: getDepartmentOptions(selectedBusinessUnit),
        placeholder: "Select Department",
        disabled: selectedRow !== null,
      },
      containerProps: {
        size: 4,
      },
    },
    {
      fieldProps: {
        name: "workRole",
      },
      formProps: {
        label: workRoleLang.inputTitle,
        type: "select",
        required: true,
        options: constructWorkRoleOptions(workRoleList),
        placeholder: "Select Work Role",
        disabled: selectedRow !== null,
      },
      containerProps: {
        size: 4,
      },
    },
    {
      fieldProps: {
        name: "jobTitle",
      },
      formProps: {
        label: jobTitleLang.inputTitle,
        type: "text",
        required: true,
        placeholder: "Enter Job Title",
      },
      containerProps: {
        size: 4,
      },
    },
  ];

  return (
    <Box>
      <ContentHeader
        title={jobTitleLang.title}
        subtitle={""}
        primaryAction={() => {
          setIsAddModalOpen(true);
        }}
        buttonTitle={jobTitleLang.addJobTitle}
        allowAction={true}
      />
      <Box sx={{ margin: "20px 0px" }}>
        <DataTable data={defaultFormState} columns={columns} />
        <Modal isOpen={isAddModalOpen} onClose={() => setIsAddModalOpen(false)} title={jobTitleLang.addJobTitle}>
          <JobTitleForm
            form={form}
            inputFields={inputFields}
            setIsAddModalOpen={setIsAddModalOpen}
            setIsEditModalOpen={setIsEditModalOpen}
          />
        </Modal>
        <Modal isOpen={isEditModalOpen} onClose={() => setIsEditModalOpen(false)} title={jobTitleLang.editJobTitle}>
          <JobTitleForm
            form={form}
            inputFields={inputFields}
            setIsAddModalOpen={setIsAddModalOpen}
            setIsEditModalOpen={setIsEditModalOpen}
          />
        </Modal>

        <DeleteConfirmationModal
          isModalOpen={isDeleteModalOpen}
          onCancel={() => setIsDeleteModalOpen(false)}
          onDelete={() => handleDeleteConfirmed(selectedRow as number)}
          title={deleteText}
          selectedRole={deafaultResponse[selectedRow as number]?.jobTitle as string}
        />
      </Box>
    </Box>
  );
};
