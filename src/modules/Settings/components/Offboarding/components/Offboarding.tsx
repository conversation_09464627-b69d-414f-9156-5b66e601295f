import { <PERSON>, But<PERSON>, Divider, Paper } from "@mui/material";
import { useMutation, useQuery } from "@tanstack/react-query";
import React, { useMemo } from "react";
import { useForm } from "src/customHooks/useForm";
import ContentHeader from "src/modules/Common/ContentHeader/ContentHeader";
import CustomTextField from "src/modules/Common/FormInputs/CustomTextField";
import offboardingService from "src/services/offboarding.service";
import validators from "src/utils/validators";

const Offboarding = () => {
  const { data, isFetched, refetch } = useQuery(
    ["get-notice-period-in-days"],
    async () => offboardingService.getNoticePeriodInDays(),
    {
      refetchOnMount: true,
      refetchOnWindowFocus: false,
    },
  );

  const defaultFormState = useMemo(
    () => ({
      noticePeriodInDays: data || 0,
    }),
    [data],
  );

  const { formDetails, formErrors, handleChange, areFormDetailsValid } = useForm({
    initialState: defaultFormState,
    isBulk: false,
    validations: {
      noticePeriodInDays: [validators.validateInput, validators.shouldBeNumeric],
    },
  });

  const typedFormDetails = formDetails as typeof defaultFormState;
  const typedFormErrors = formErrors as Record<keyof typeof defaultFormState, string>;

  const updateNoticePeriodInDays = useMutation({
    mutationKey: ["update-notice-period-in-days"],
    mutationFn: async () => offboardingService.updateNoticePeriodInDays(typedFormDetails.noticePeriodInDays),
    onSuccess: () => {
      refetch();
    },
  });

  const onSubmit = () => {
    updateNoticePeriodInDays.mutate();
  };

  return (
    <Box display="flex" flexDirection="column" gap={2} height="100%">
      <ContentHeader title="Employee Offboarding" subtitle="Configure offboarding settings for your organisation" />
      <Divider />
      <Box width={320}>
        <CustomTextField
          name="noticePeriodInDays"
          size="small"
          id="noticePeriodInDays"
          title="Notice Period (in days)"
          type="number"
          value={typedFormDetails.noticePeriodInDays}
          error={!!typedFormErrors.noticePeriodInDays}
          helperText={typedFormErrors.noticePeriodInDays}
          onChange={handleChange}
          disabled={!isFetched}
        />
      </Box>
      <Box alignSelf="flex-end" bottom={20} gap={2} zIndex={10}>
        <Button
          disabled={!areFormDetailsValid || typedFormDetails.noticePeriodInDays === data}
          onClick={onSubmit}
          variant="contained"
        >
          Save
        </Button>
      </Box>
    </Box>
  );
};

export default Offboarding;
