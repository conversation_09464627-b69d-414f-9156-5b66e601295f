import { Box, Button } from "@mui/material";
import React, { useState, useMemo } from "react";
import { queryClient } from "src/app/App";
import { useAppDispatch } from "src/customHooks/useAppDispatch";
import { LeavePolicyDetails } from "src/services/api_definitions/leaveManagement.service";
import { setFullviewMode } from "src/store/slices/app.slice";
import AddEditLeavePolicies from "./AddEditLeavePolicies";
import LeavePoliciesView from "./LeavePoliciesView";
import UploadLeavePolicies from "./UploadLeavePolicies";

export enum LeavePoliciyModes {
  VIEW_LEAVE_POLICIES = "VIEW_LEAVE_POLICIES",
  EDIT_LEAVE_POLICIES = "EDIT_LEAVE_POLICIES",
  ADD_LEAVE_POLICIES = "ADD_LEAVE_POLICIES",
  DUPLICATE_LEAVE_POLICIES = "DUPLICATE_LEAVE_POLICIES",
}

export enum LeavePolicyActionModes {
  ADD = "add",
  EDIT = "edit",
  DUPLICATE = "duplicate",
}

const LeavePolicies = () => {
  const [currentViewMode, setCurrentViewMode] = useState(LeavePoliciyModes.VIEW_LEAVE_POLICIES);
  const [isUploadLeavesModalOpen, setIsUploadLeavesModalOpen] = useState(false);
  const dispatch = useAppDispatch();
  const [selectedRow, setSelectedRow] = useState<LeavePolicyDetails | null>(null);

  const onAddLeavePolicyClick = () => {
    dispatch(setFullviewMode(true));
    setCurrentViewMode(LeavePoliciyModes.ADD_LEAVE_POLICIES);
  };

  const getActionMode = () => {
    switch (currentViewMode) {
      case LeavePoliciyModes.EDIT_LEAVE_POLICIES:
        return LeavePolicyActionModes.EDIT;
      case LeavePoliciyModes.DUPLICATE_LEAVE_POLICIES:
        return LeavePolicyActionModes.DUPLICATE;
      default:
        return LeavePolicyActionModes.ADD;
    }
  };

  const getComponent = useMemo(() => {
    if (currentViewMode === LeavePoliciyModes.VIEW_LEAVE_POLICIES) {
      return (
        <Box display="flex" flexDirection="column" width="100%" gap={2}>
          <Box display="flex" alignItems="center" sx={{ alignSelf: "flex-end" }} gap={1}>
            <Button fullWidth={false} variant="outlined" onClick={() => setIsUploadLeavesModalOpen(true)}>
              Upload Leaves
            </Button>
            <Button fullWidth={false} variant="contained" onClick={onAddLeavePolicyClick}>
              Add Leave Policy
            </Button>
          </Box>
          <LeavePoliciesView
            selectedRow={selectedRow}
            setCurrentViewMode={setCurrentViewMode}
            setSelectedRow={setSelectedRow}
          />
          {isUploadLeavesModalOpen && (
            <UploadLeavePolicies
              isModalOpen={isUploadLeavesModalOpen}
              onClose={() => setIsUploadLeavesModalOpen(false)}
              refetch={() => queryClient.invalidateQueries(["get-leave-policies"])}
            />
          )}
        </Box>
      );
    }
    return (
      <Box display="flex" flexDirection="column">
        <AddEditLeavePolicies
          actionMode={getActionMode()}
          setCurrentViewMode={setCurrentViewMode}
          selectedRow={selectedRow}
          setSelectedRow={setSelectedRow}
        />
      </Box>
    );
  }, [currentViewMode, selectedRow, isUploadLeavesModalOpen]);

  return <>{getComponent}</>;
};

export default LeavePolicies;
