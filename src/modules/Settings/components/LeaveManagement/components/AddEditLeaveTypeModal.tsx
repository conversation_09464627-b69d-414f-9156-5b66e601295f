import { <PERSON>, <PERSON>ton, <PERSON><PERSON>A<PERSON>, Grid2, Input<PERSON>abel, Switch } from "@mui/material";
import { useMutation, useQuery } from "@tanstack/react-query";
import React, { useMemo } from "react";
import languageConfig from "src/configs/language/en.lang";
import { useForm } from "src/customHooks/useForm";
import CustomSelect from "src/modules/Common/FormInputs/CustomSelect";
import CustomTextField from "src/modules/Common/FormInputs/CustomTextField";
import Modal from "src/modules/Common/Modal/Modal";
import holidayService from "src/services/leaveManagement.service";
import masterdataService from "src/services/masterdata.service";
import validators from "src/utils/validators";
import { ExtendedLeaveEntities } from "./LeaveTypes";

interface LeaveModalProps {
  readonly onClose: () => void;
  readonly isModalOpen: boolean;
  readonly refetch: () => void;
  readonly selectedRow: ExtendedLeaveEntities | null;
}

type LeaveTypeDefaultState = {
  name: string;
  shortName: string;
  description: string;
  isPaid: boolean;
  approver: string;
};

const defaultFormState: LeaveTypeDefaultState = {
  name: "",
  shortName: "",
  description: "",
  isPaid: false,
  approver: "",
};

const typography = {
  leaveType: "Leave Type",
  shortName: "Short Name",
  description: "Description",
  isPaid: "Paid Leave",
  approver: "Approver",
};

const AddEditLeaveTypeModal: React.FC<LeaveModalProps> = ({ isModalOpen, onClose, refetch, selectedRow }) => {
  const intialFormData = useMemo(() => {
    return selectedRow
      ? {
          ...defaultFormState,
          name: selectedRow?.type,
          shortName: selectedRow?.short_name,
          description: selectedRow?.description,
          isPaid: selectedRow?.paid,
          approver: selectedRow?.approver,
        }
      : defaultFormState;
  }, [selectedRow, defaultFormState]);

  const { formDetails, formErrors, handleChange, areFormDetailsValid, setFormDetail, handleSelectChange } = useForm({
    initialState: intialFormData,
    isBulk: false,
    validations: {
      name: [validators.validateInput],
      description: [validators.validateInput],
      isPaid: [],
      approver: [validators.shouldNotContainSpecialCharacters],
      shortName: [validators.validateInput],
    },
  });

  const { data } = useQuery(["get-approvers"], async () => masterdataService.getACLs("ApproverType"), {
    refetchOnMount: true,
    refetchOnReconnect: true,
    refetchOnWindowFocus: false,
  });

  const typedFormDetails = formDetails as unknown as LeaveTypeDefaultState;
  const typedFormErrors = formErrors as Record<keyof LeaveTypeDefaultState, string>;

  const createLeaveTypeMutation = useMutation({
    mutationKey: ["create-leave-type"],
    mutationFn: async () =>
      holidayService.createLeaveType({
        type: typedFormDetails?.name,
        short_name: typedFormDetails?.shortName,
        description: typedFormDetails?.description,
        isPaid: typedFormDetails?.isPaid,
        approver: typedFormDetails?.approver,
      }),
    onSuccess: () => {
      onClose();
      refetch();
    },
  });

  const updateLeaveTypeMutation = useMutation({
    mutationKey: ["update-leave-type"],
    mutationFn: async () =>
      holidayService.editLeaveType({
        type: selectedRow?.type as string,
        new_type: typedFormDetails?.name,
        short_name: typedFormDetails?.shortName,
        description: typedFormDetails?.description,
        isPaid: typedFormDetails?.isPaid,
        approver: typedFormDetails?.approver,
      }),
    onSuccess: () => {
      onClose();
      refetch();
    },
  });

  const hasErrors = useMemo(() => {
    const areAnyFormDetailsEmpty = Object.keys(typedFormDetails).some(
      (detailKey) => detailKey != "isPaid" && !typedFormDetails[detailKey as keyof typeof typedFormDetails],
    );
    return areAnyFormDetailsEmpty || !areFormDetailsValid;
  }, [typedFormDetails, areFormDetailsValid]);

  const onSaveClick = () => {
    if (selectedRow) {
      updateLeaveTypeMutation.mutate();
      return;
    }
    createLeaveTypeMutation.mutate();
  };

  const onCheckboxChange = (_event: React.ChangeEvent<HTMLInputElement>, checked: boolean) => {
    setFormDetail("isPaid", checked);
  };

  return (
    <Modal
      title={selectedRow ? "Edit Leave Types" : "Add Leave Types"}
      subtitle={selectedRow ? "Edit leave types accordingly" : "Add leave types accordingly"}
      showBackButton
      isOpen={isModalOpen}
      onClose={onClose}
      fullWidth
      actions={
        <DialogActions sx={{ margin: 2 }}>
          <Button disabled={hasErrors} size="large" variant="contained" onClick={onSaveClick}>
            {languageConfig.tenants.button.save}
          </Button>
        </DialogActions>
      }
    >
      <Grid2 container spacing={2}>
        <Grid2 size={4}>
          <CustomTextField
            title={typography.leaveType}
            id="name"
            size="small"
            fullWidth
            value={typedFormDetails.name}
            onChange={handleChange}
            placeholder="Leave type"
            required
            error={!!typedFormErrors.name}
            helperText={!!typedFormErrors?.name && typedFormErrors?.name}
          />
        </Grid2>
        <Grid2 size={4}>
          <CustomTextField
            title={typography.shortName}
            id="shortName"
            size="small"
            fullWidth
            value={typedFormDetails.shortName}
            onChange={handleChange}
            placeholder="Enter short name"
            required
            error={!!typedFormErrors.shortName}
            helperText={!!typedFormErrors?.shortName && typedFormErrors?.shortName}
          />
        </Grid2>
        <Grid2 size={4}>
          <CustomSelect
            label={typography.approver}
            id="approver"
            size="small"
            name="approver"
            select
            fullWidth
            value={typedFormDetails?.approver}
            onChange={(ev) => handleSelectChange(ev as never, "approver")}
            required
            options={data?.map((option) => ({ label: option as string, value: option as string })) || []}
            error={!!typedFormErrors?.approver}
            helperText={!!typedFormErrors?.approver && typedFormErrors?.approver}
          />
        </Grid2>
        <Grid2 size={12}>
          <CustomTextField
            title={typography.description}
            id="description"
            size="small"
            fullWidth
            value={typedFormDetails.description}
            onChange={handleChange}
            required
            error={!!typedFormErrors.description}
            helperText={!!typedFormErrors?.description && typedFormErrors?.description}
          />
        </Grid2>
        <Grid2 size={12}>
          <Box display="flex" alignItems="center">
            <InputLabel htmlFor="isPaid" sx={{ fontWeight: 600, color: "#000" }}>
              {typography.isPaid}
            </InputLabel>
            <Switch
              id="isPaid"
              value={typedFormDetails?.isPaid}
              checked={typedFormDetails?.isPaid}
              onChange={onCheckboxChange}
            />
          </Box>
        </Grid2>
      </Grid2>
    </Modal>
  );
};

export default AddEditLeaveTypeModal;
