import { Delete, Edit, PictureAsPdf } from "@mui/icons-material";
import { Box, IconButton, Tooltip } from "@mui/material";
import { useMutation, useQuery } from "@tanstack/react-query";
import { MRT_ColumnDef, MRT_Row } from "material-react-table";
import React, { useCallback, useMemo, useState } from "react";
import { useAppDispatch } from "src/customHooks/useAppDispatch";
import DataTable from "src/modules/Common/Table/DataTable";
import { PATH_CONFIG } from "src/modules/Routing/config";
import { OrganisationHolidays } from "src/services/api_definitions/leaveManagement.service";
import holidayService from "src/services/leaveManagement.service";
import { setFullviewMode } from "src/store/slices/app.slice";
import { getACLFromFeaturekey } from "src/utils/screenUtils";
import DeleteConfirmationModal from "./DeleteConfirmationModal";
import { HolidayOperations } from "./Holidays";

const OrganisationHolidayView: React.FC<{
  setCurrentViewMode: (currentViewMode: HolidayOperations) => void;
  setSelectedRow: React.Dispatch<React.SetStateAction<OrganisationHolidays | null>>;
  selectedRow: OrganisationHolidays | null;
}> = ({ setCurrentViewMode, setSelectedRow, selectedRow }) => {
  const dispatch = useAppDispatch();
  const [showDeleteConfirmation, setShowDeleteConfirmation] = useState<boolean>(false);
  const HOLIDAYS_ACL = getACLFromFeaturekey(PATH_CONFIG.LEAVE_MANAGEMENT_HOLIDAYS.key);
  const { data, isFetched, refetch } = useQuery(["get-org-holidays"], holidayService.getOrganisationHolidays, {
    refetchOnMount: true,
    refetchOnWindowFocus: false,
    refetchOnReconnect: true,
  });

  const deleteMutation = useMutation({
    mutationKey: ["delete-holiday"],
    mutationFn: async () =>
      await holidayService.deleteHoliday({
        country: selectedRow?.country as string,
        location: selectedRow?.location as string,
        year: selectedRow?.year as string,
      }),
    onSuccess: () => {
      refetch();
      setSelectedRow(null);
      setShowDeleteConfirmation(false);
    },
  });

  const getColumnDefs: MRT_ColumnDef<OrganisationHolidays>[] = useMemo(
    () => [
      {
        accessorKey: "country",
        header: "Country",
      },
      {
        accessorKey: "location",
        header: "Office Location",
      },
      {
        accessorKey: "year",
        header: "Year",
        size: 50,
      },
      {
        accessorKey: "mandatory_holidays",
        header: "Mandatory Holidays",
        Cell: ({ cell }) => {
          return cell.row.original.mandatory_holidays.length;
        },
      },
      {
        accessorKey: "restricted_holidays",
        header: "Restricted Holidays",
        Cell: ({ cell }) => {
          return cell.row.original.restricted_holidays.length;
        },
      },
    ],
    [data],
  );

  const onDeleteClick = (row: OrganisationHolidays) => {
    setSelectedRow(row);
    setShowDeleteConfirmation(true);
  };

  const onClose = () => {
    setSelectedRow(null);
    setShowDeleteConfirmation(false);
  };

  const onEditClick = (row: OrganisationHolidays) => {
    dispatch(setFullviewMode(true));
    setSelectedRow(row);
    setCurrentViewMode(HolidayOperations.EDIT_HOLIDAY_LIST);
  };

  const getEditRow = useCallback(
    (row: MRT_Row<OrganisationHolidays>) => (
      <Box width={200}>
        <IconButton disabled={!HOLIDAYS_ACL?.canWrite} onClick={() => onEditClick(row.original)}>
          <Tooltip title="Edit">
            <Edit />
          </Tooltip>
        </IconButton>
        <IconButton disabled={!HOLIDAYS_ACL?.canWrite} onClick={() => onDeleteClick(row?.original)}>
          <Tooltip title="Delete">
            <Delete />
          </Tooltip>
        </IconButton>
        <IconButton>
          <Tooltip
            title="Download PDF"
            onClick={() => holidayService.downloadPdf(row.original.country, row.original.location, row.original.year)}
          >
            <PictureAsPdf />
          </Tooltip>
        </IconButton>
      </Box>
    ),
    [HOLIDAYS_ACL?.canWrite],
  );

  return (
    <Box margin="16px 0px">
      <DataTable
        key={`orgdetails_${isFetched}`}
        data={data as OrganisationHolidays[]}
        enableColumnActions={false}
        enableCellActions={false}
        enableSorting={false}
        enableBottomToolbar={false}
        enableTopToolbar={false}
        columns={getColumnDefs}
        enableRowActions
        positionActionsColumn="last"
        renderRowActions={({ row }) => getEditRow(row)}
        initialState={{
          showSkeletons: !isFetched,
        }}
      />
      {showDeleteConfirmation && (
        <DeleteConfirmationModal
          onCancel={onClose}
          onDelete={() => deleteMutation.mutate()}
          selectedValue={selectedRow?.name as string}
          isModalOpen={showDeleteConfirmation && !!selectedRow}
        />
      )}
    </Box>
  );
};

export default OrganisationHolidayView;
