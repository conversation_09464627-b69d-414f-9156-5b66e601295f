/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable react/prop-types */
import { CopyAll, Delete, Edit } from "@mui/icons-material";
import { Box, IconButton, Tooltip } from "@mui/material";
import { useMutation, useQuery } from "@tanstack/react-query";
import { MRT_Row } from "material-react-table";
import React, { useCallback, useMemo, useState } from "react";
import { useAppDispatch } from "src/customHooks/useAppDispatch";
import DataTable from "src/modules/Common/Table/DataTable";
import { PATH_CONFIG } from "src/modules/Routing/config";
import { LeaveAttribute, LeavePolicyDetails } from "src/services/api_definitions/leaveManagement.service";
import holidayService from "src/services/leaveManagement.service";
import { setFullviewMode } from "src/store/slices/app.slice";
import { formatDateToDayMonthYear } from "src/utils/dateUtils";
import { getACLFromFeaturekey } from "src/utils/screenUtils";
import DeleteConfirmationModal from "./DeleteConfirmationModal";
import { LeavePoliciyModes } from "./LeavePolicies";

interface LeavePoliciesViewProps {
  selectedRow: LeavePolicyDetails | null;
  setCurrentViewMode: (currentViewMode: LeavePoliciyModes) => void;
  setSelectedRow: (selectedRow: LeavePolicyDetails | null) => void;
}

const LeavePoliciesView: React.FC<LeavePoliciesViewProps> = ({ selectedRow, setCurrentViewMode, setSelectedRow }) => {
  const LEAVE_POLICIES_ACL = getACLFromFeaturekey(PATH_CONFIG.LEAVE_MANAGEMENT_POLICIES.key);
  const dispatch = useAppDispatch();
  const [showDeleteModal, setShowDeleteModal] = useState(false);

  const onClose = () => {
    setSelectedRow(null);
    dispatch(setFullviewMode(false));
    setCurrentViewMode(LeavePoliciyModes.VIEW_LEAVE_POLICIES);
  };

  const { data, isFetched, isError, refetch } = useQuery(
    ["get-leave-policies"],
    async () => holidayService.getLeavePolicies(),
    {
      refetchOnMount: true,
      refetchOnWindowFocus: false,
    },
  );

  const deleteMutation = useMutation({
    mutationKey: ["delete-leave-policy"],
    mutationFn: async () =>
      holidayService.deleteLeavePolicy({
        employeeTypes: selectedRow?.employeeTypes as string[],
        endDate: selectedRow?.endDate as string,
        leaveAttributes: selectedRow?.leaveAttributes as LeaveAttribute[],
        name: selectedRow?.name as string,
        startDate: selectedRow?.startDate as string,
      }),
    onSuccess: () => {
      onClose();
      refetch();
    },
  });

  const getTableData = useMemo((): LeavePolicyDetails[] => {
    return data || [];
  }, [data]);

  const onEditClick = (row: LeavePolicyDetails) => {
    setCurrentViewMode(LeavePoliciyModes.EDIT_LEAVE_POLICIES);
    dispatch(setFullviewMode(true));
    setSelectedRow(row);
  };

  const onDuplicateClick = (row: LeavePolicyDetails) => {
    setCurrentViewMode(LeavePoliciyModes.DUPLICATE_LEAVE_POLICIES);
    dispatch(setFullviewMode(true));
    setSelectedRow(row);
  };

  const onDeleteClick = (row: LeavePolicyDetails) => {
    setShowDeleteModal(true);
    setSelectedRow(row);
  };

  const getEditRow = useCallback(
    (row: MRT_Row<LeavePolicyDetails>) => (
      <Box width={200}>
        <IconButton disabled={!LEAVE_POLICIES_ACL?.canWrite} onClick={() => onEditClick(row.original)}>
          <Tooltip title="Edit">
            <Edit />
          </Tooltip>
        </IconButton>
        <IconButton disabled={!LEAVE_POLICIES_ACL?.canWrite} onClick={() => onDuplicateClick(row.original)}>
          <Tooltip title="Duplicate">
            <CopyAll />
          </Tooltip>
        </IconButton>
        <IconButton disabled={!LEAVE_POLICIES_ACL?.canWrite} onClick={() => onDeleteClick(row?.original)}>
          <Tooltip title="Delete">
            <Delete />
          </Tooltip>
        </IconButton>
      </Box>
    ),
    [LEAVE_POLICIES_ACL?.canWrite],
  );

  if (isError) {
    return <Box>Something went wrong</Box>;
  }

  return (
    <Box display="flex" flexDirection="column" gap={2}>
      <DataTable
        data={getTableData}
        key={`leavePolicies-${isFetched}`}
        enableStickyHeader={true}
        enableRowActions
        positionActionsColumn="last"
        renderRowActions={({ row }) => getEditRow(row)}
        columns={[
          {
            accessorKey: "name",
            header: "Leave Policy",
          },
          {
            accessorKey: "employeeTypes",
            header: "Employee Types",
            Cell: ({ row }) => row?.original?.employeeTypes?.join(", "),
          },
          {
            accessorKey: "startDate",
            header: "Start Date",
            Cell: ({ cell }) => formatDateToDayMonthYear(cell.row.original?.startDate),
          },
          {
            accessorKey: "endDate",
            header: "End Date",
            Cell: ({ cell }) => formatDateToDayMonthYear(cell.row.original?.endDate),
          }
        ]}
        initialState={{
          showSkeletons: !isFetched,
        }}
      />
      {showDeleteModal && selectedRow && (
        <DeleteConfirmationModal
          onCancel={onClose}
          onDelete={() => deleteMutation.mutate()}
          selectedValue={selectedRow?.name as string}
          isModalOpen={showDeleteModal}
        />
      )}
    </Box>
  );
};

export default LeavePoliciesView;
