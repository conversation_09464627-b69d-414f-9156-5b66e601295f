import { Box, Paper, Tab, Tabs } from "@mui/material";
import React, { useMemo } from "react";
import { useAppSelector } from "src/customHooks/useAppSelector";
import useSubroutes from "src/customHooks/useSubroutes";
import ContentHeader from "src/modules/Common/ContentHeader/ContentHeader";
import NoAccessScreen from "src/modules/Common/NoAccess/NoAccess";
import { PATH_CONFIG } from "src/modules/Routing/config";
import Holidays from "./Holidays";
import LeavePolicies from "./LeavePolicies";
import LeaveTypes from "./LeaveTypes";

const leaveManagementTabs = [
  {
    key: PATH_CONFIG.LEAVE_MANAGEMENT_HOLIDAYS.key,
    label: "Holidays",
    header: "Leave Management",
    subtitle: "Manage & Create Holidays",
    buttonLabel: "Add Role",
    component: <Holidays />,
    id: 0,
  },
  {
    key: PATH_CONFIG.LEAVE_MANAGEMENT_LEAVE_TYPES.key,
    label: "Leave Types",
    header: "Leave Management",
    subtitle: "Configure leave options",
    component: <LeaveTypes />,
    id: 1,
  },
  {
    key: PATH_CONFIG.LEAVE_MANAGEMENT_POLICIES.key,
    label: "Leave Policies",
    header: "Leave Management",
    subtitle: "Manage leave policies for your company",
    component: <LeavePolicies />,
    id: 2,
  },
];

const LeaveManagement = () => {
  const { isFullView } = useAppSelector((state) => state.app);
  const subRoutes = useSubroutes(PATH_CONFIG.LEAVE_MANAGEMENT.key);
  const tabsToShow = useMemo(
    () =>
      leaveManagementTabs.filter((settings) => {
        return subRoutes.some((route) => route.key === settings.key && route.acl?.canRead);
      }),
    [subRoutes],
  );

  const [tabId, setTabId] = React.useState(tabsToShow?.[0]?.id);
  const selectedTab = useMemo(() => tabsToShow?.[tabId], [tabId]);

  const handleChange = (_event: React.SyntheticEvent, newValue: number) => {
    setTabId(newValue);
  };

  if (!tabsToShow || tabsToShow?.length === 0) {
    return (
      <Box>
        <NoAccessScreen />
      </Box>
    );
  }

  return (
    <Box display="flex" flexDirection="column" gap={2}>
      {!isFullView && <ContentHeader title={selectedTab?.header} />}
      {!isFullView && (
        <Tabs component={Paper} value={tabId} onChange={handleChange}>
          {tabsToShow?.map((tab) => (
            <Tab
              sx={{
                textTransform: "none",
              }}
              label={tab?.label}
              tabIndex={tab?.id}
              key={tab?.id}
            />
          ))}
        </Tabs>
      )}
      <Box display="flex" flexDirection="column" gap={2}>
        {selectedTab?.component && selectedTab?.component}
      </Box>
    </Box>
  );
};

export default LeaveManagement;
