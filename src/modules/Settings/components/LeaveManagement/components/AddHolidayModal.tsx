import { Box, Button, DialogActions } from "@mui/material";
import { useMutation } from "@tanstack/react-query";
import React, { useMemo } from "react";
import languageConfig from "src/configs/language/en.lang";
import { useForm } from "src/customHooks/useForm";
import CustomTextField from "src/modules/Common/FormInputs/CustomTextField";
import Modal from "src/modules/Common/Modal/Modal";
import holidayService from "src/services/leaveManagement.service";
import validators from "src/utils/validators";

interface HolidayModalProps {
  readonly onClose: () => void;
  readonly isModalOpen: boolean;
  readonly refetch: () => void;
  formDetails: {
    year: string | undefined;
    location: string | undefined;
    country: string | undefined;
  };
}

type HolidayModalFormState = {
  name: string;
  date: string;
};

const defaultFormState: HolidayModalFormState = {
  name: "",
  date: "",
};

const AddHolidayModal: React.FC<HolidayModalProps> = ({ isModalOpen, onClose, refetch, formDetails: parentForm }) => {
  const { formDetails, formErrors, handleChange, areFormDetailsValid } = useForm({
    initialState: defaultFormState,
    isBulk: false,
    validations: {
      name: [validators.validateInput, validators.shouldNotContainSpecialCharacters],
      date: [validators.validateInput],
    },
  });

  const typedFormDetails = formDetails as HolidayModalFormState;
  const typedFormErrors = formErrors as Record<keyof HolidayModalFormState, string>;

  const createHolidayMutation = useMutation({
    mutationKey: ["create-holiday"],
    mutationFn: async () =>
      holidayService.createHoliday({
        name: typedFormDetails.name,
        date: typedFormDetails.date,
        country: parentForm.country as string,
      }),
    onSuccess: () => {
      onClose();
      refetch();
    },
  });

  const hasErrors = useMemo(() => {
    const areAnyFormDetailsEmpty = Object.values(typedFormDetails).some((detail) => !detail);
    return areAnyFormDetailsEmpty || !areFormDetailsValid;
  }, [typedFormDetails, areFormDetailsValid]);

  const onSaveClick = () => {
    createHolidayMutation.mutate();
  };

  return (
    <Modal
      title="Add Holidays"
      subtitle="Add your own custom holiday"
      showBackButton
      isOpen={isModalOpen}
      onClose={onClose}
      fullWidth
      actions={
        <DialogActions sx={{ margin: 2 }}>
          <Button disabled={hasErrors} size="large" variant="contained" onClick={onSaveClick}>
            {languageConfig.tenants.button.create}
          </Button>
        </DialogActions>
      }
    >
      <Box display="flex" flexDirection="column" gap={2} sx={{ width: "100%" }}>
        <Box display="flex" flexDirection="column" gap={1}>
          <CustomTextField
            title="Holiday Name"
            id="name"
            size="small"
            fullWidth
            value={typedFormDetails.name}
            onChange={handleChange}
            placeholder="Please enter a valid holiday name"
            required
            error={!!typedFormErrors.name}
            helperText={!!typedFormErrors?.name && typedFormErrors?.name}
          />
        </Box>
        <Box display="flex" flexDirection="column" gap={1}>
          <CustomTextField
            title="Date"
            id="date"
            size="small"
            fullWidth
            type="date"
            value={typedFormDetails.date}
            onChange={handleChange}
            required
            error={!!typedFormErrors.date}
            helperText={!!typedFormErrors?.date && typedFormErrors?.date}
          />
        </Box>
      </Box>
    </Modal>
  );
};

export default AddHolidayModal;
