import { Box, Button, Container } from "@mui/material";
import React, { useMemo, useState } from "react";
import { useAppDispatch } from "src/customHooks/useAppDispatch";
import { OrganisationHolidays } from "src/services/api_definitions/leaveManagement.service";
import { setFullviewMode } from "src/store/slices/app.slice";
import AddEditHolidayList from "./AddEditHolidayList";
import OrganisationHolidayView from "./OrganisationHolidaysView";

export enum HolidayOperations {
  ADD_HOLIDAY_LIST = "ADD_HOLIDAY_LIST",
  EDIT_HOLIDAY_LIST = "EDIT_HOLIDAY_LIST",
  VIEW_HOLIDAY_LIST = "VIEW_HOLIDAY_LIST",
}

const Holidays = () => {
  const [currentViewMode, setCurrentViewMode] = useState(HolidayOperations.VIEW_HOLIDAY_LIST);
  const dispatch = useAppDispatch();
  const [selectedRow, setSelectedRow] = useState<OrganisationHolidays | null>(null);

  const onAddHolidayListClick = () => {
    dispatch(setFullviewMode(true));
    setCurrentViewMode(HolidayOperations.ADD_HOLIDAY_LIST);
  };

  const getComponent = useMemo(() => {
    if (currentViewMode === HolidayOperations.VIEW_HOLIDAY_LIST) {
      return (
        <Box display="flex" flexDirection="column" width="100%">
          <Button
            sx={{ alignSelf: "flex-end", textTransform: "none" }}
            fullWidth={false}
            variant="contained"
            onClick={onAddHolidayListClick}
          >
            Add Holiday List
          </Button>
          <Container disableGutters maxWidth="xl">
            <OrganisationHolidayView
              selectedRow={selectedRow}
              setCurrentViewMode={setCurrentViewMode}
              setSelectedRow={setSelectedRow}
            />
          </Container>
        </Box>
      );
    }
    return (
      <Box display="flex" flexDirection="column">
        <AddEditHolidayList
          isEdit={currentViewMode === HolidayOperations.EDIT_HOLIDAY_LIST}
          setCurrentViewMode={setCurrentViewMode}
          selectedRow={selectedRow}
        />
      </Box>
    );
  }, [currentViewMode, selectedRow]);

  return <>{getComponent}</>;
};

export default Holidays;
