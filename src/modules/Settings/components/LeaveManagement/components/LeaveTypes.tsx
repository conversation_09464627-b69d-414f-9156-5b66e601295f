/* eslint-disable react/prop-types */
import { Delete, Edit } from "@mui/icons-material";
import { Box, Button, IconButton, Tooltip, Typography } from "@mui/material";
import { useMutation, useQuery } from "@tanstack/react-query";
import { MRT_Row } from "material-react-table";
import React, { useCallback, useMemo, useState } from "react";
import DataTable from "src/modules/Common/Table/DataTable";
import { PATH_CONFIG } from "src/modules/Routing/config";
import { LeaveEntities } from "src/services/api_definitions/leaveManagement.service";
import holidayService from "src/services/leaveManagement.service";
import { getACLFromFeaturekey } from "src/utils/screenUtils";
import AddEditLeaveTypeModal from "./AddEditLeaveTypeModal";
import DeleteConfirmationModal from "./DeleteConfirmationModal";

export type ExtendedLeaveEntities = LeaveEntities & {
  isDefault: boolean;
};

const LeaveTypes = () => {
  const LEAVE_TYPES_ACL = getACLFromFeaturekey(PATH_CONFIG.LEAVE_MANAGEMENT_LEAVE_TYPES.key);
  const [selectedRow, setSelectedRow] = useState<ExtendedLeaveEntities | null>(null);
  const [isModalOpen, setModalOpen] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);

  const onClose = () => {
    setModalOpen(false);
    setShowDeleteModal(false);
    setSelectedRow(null);
  };

  const { data, isFetched, isError, refetch } = useQuery(
    ["get-leave-details"],
    async () => holidayService.getLeaveDetails(),
    {
      refetchOnMount: true,
      refetchOnWindowFocus: false,
    },
  );

  const deleteMutation = useMutation({
    mutationKey: ["delete-leave-type"],
    mutationFn: async () =>
      holidayService.deleteLeaveType({
        type: selectedRow?.type as string,
        approver: selectedRow?.approver as string,
        description: selectedRow?.description as string,
        isPaid: selectedRow?.paid as boolean,
        short_name: selectedRow?.short_name as string,
      }),
    onSuccess: () => {
      onClose();
      refetch();
    },
  });

  const getTableData = useMemo((): ExtendedLeaveEntities[] => {
    return [
      ...(data?.default_leaves || []).map((defaultLeaves) => ({
        ...defaultLeaves,
        isDefault: true,
      })),
      ...(data?.custom_leaves || []).map((customLeaves) => ({
        ...customLeaves,
        isDefault: false,
      })),
    ];
  }, [data]);

  const onEditClick = (row: ExtendedLeaveEntities) => {
    setModalOpen(true);
    setSelectedRow(row);
  };

  const onDeleteClick = (row: ExtendedLeaveEntities) => {
    setShowDeleteModal(true);
    setSelectedRow(row);
  };

  const getEditRow = useCallback(
    (row: MRT_Row<ExtendedLeaveEntities>) => (
      <Box width={200}>
        <IconButton disabled={!LEAVE_TYPES_ACL?.canWrite} onClick={() => onEditClick(row.original)}>
          <Tooltip title="Edit">
            <Edit />
          </Tooltip>
        </IconButton>
        <IconButton disabled={!LEAVE_TYPES_ACL?.canWrite} onClick={() => onDeleteClick(row?.original)}>
          <Tooltip title="Delete">
            <Delete />
          </Tooltip>
        </IconButton>
      </Box>
    ),
    [LEAVE_TYPES_ACL?.canWrite],
  );

  const onCreateLeaveTypeClick = () => {
    setModalOpen(true);
  };

  if (isError) {
    return <Box>Something went wrong</Box>;
  }

  return (
    <Box display="flex" flexDirection="column" gap={2}>
      <Button
        sx={{
          alignSelf: "flex-end",
          textTransform: "none",
        }}
        variant="contained"
        fullWidth={false}
        onClick={onCreateLeaveTypeClick}
      >
        Create Leave type
      </Button>
      <DataTable
        data={getTableData}
        key={`leaveTypes-${isFetched}`}
        enableStickyHeader={true}
        enableRowActions
        positionActionsColumn="last"
        renderRowActions={({ row }) => (row.original.isDefault ? null : getEditRow(row))}
        columns={[
          {
            accessorKey: "type",
            header: "Type",
          },
          {
            accessorKey: "short_name",
            header: "Short Name",
          },
          {
            accessorKey: "description",
            header: "Description",
          },
          {
            accessorKey: "paid",
            header: "Leave Pay Status",
            Cell: ({ cell }) => {
              const isPaidLeave = cell?.row?.original?.paid;
              return <Typography color={isPaidLeave ? "green" : "red"}>{isPaidLeave ? "Paid" : "Unpaid"}</Typography>;
            },
          },
          {
            accessorKey: "approver",
            header: "Approver",
          },
        ]}
        initialState={{
          showSkeletons: !isFetched,
        }}
      />
      {isModalOpen && (
        <AddEditLeaveTypeModal
          isModalOpen={isModalOpen}
          onClose={onClose}
          refetch={refetch}
          selectedRow={selectedRow}
        />
      )}
      {showDeleteModal && selectedRow && (
        <DeleteConfirmationModal
          onCancel={onClose}
          onDelete={() => deleteMutation.mutate()}
          selectedValue={selectedRow?.type as string}
          isModalOpen={showDeleteModal}
        />
      )}
    </Box>
  );
};

export default LeaveTypes;
