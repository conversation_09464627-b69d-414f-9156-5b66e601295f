import { Add, ArrowBack } from "@mui/icons-material";
import { Box, Button, Divider, Grid, IconButton, Button as MUIButton, Paper, Typography } from "@mui/material";
import { useMutation, useQuery } from "@tanstack/react-query";
import { format } from "date-fns";
import { MRT_Row } from "material-react-table";
import React, { useMemo, useState } from "react";
import { useAppDispatch } from "src/customHooks/useAppDispatch";
import { useAppSelector } from "src/customHooks/useAppSelector";
import { useForm } from "src/customHooks/useForm";
import CustomSelect from "src/modules/Common/FormInputs/CustomSelect";
import DataTable from "src/modules/Common/Table/DataTable";
import {
  HolidayResponse,
  OrganisationHolidays,
  UpdateOrganisationHoliday,
} from "src/services/api_definitions/leaveManagement.service";
import holidayService from "src/services/leaveManagement.service";
import masterdataService from "src/services/masterdata.service";
import { setFullviewMode } from "src/store/slices/app.slice";
import validators from "src/utils/validators";
import AddHolidayModal from "./AddHolidayModal";
import { HolidayOperations } from "./Holidays";

interface AddEditHolidayListProps {
  isEdit: boolean;
  setCurrentViewMode: (currentViewMode: HolidayOperations) => void;
  selectedRow: OrganisationHolidays | null;
}

const formatDate = (dateString: string) => {
  const date = new Date(dateString);
  const day = date.getDate().toString().padStart(2, "0");
  const month = date.toLocaleDateString("en-US", { month: "short" });
  const weekday = date.toLocaleDateString("en-US", { weekday: "short" });
  return `${month} ${day} (${weekday})`;
};

const mainHolidayListColumns = [
  {
    accessorKey: "name",
    header: "Name",
    size: 150,
  },
  {
    accessorKey: "type",
    header: "Type",
    size: 150,
  },
  {
    accessorKey: "date",
    header: "Date",
    size: 150,
    Cell: ({ cell }: { cell: { getValue: () => string } }) => formatDate(cell.getValue()),
  },
];

interface TableSectionHeaderProps {
  title: string;
  Actionable?: JSX.Element;
}
const TableSectionHeader: React.FC<TableSectionHeaderProps> = ({ title, Actionable }) => {
  return (
    <Box display="flex" justifyContent="space-between" alignItems="center">
      <Typography fontSize={14} fontWeight={600}>
        {title}
      </Typography>
      {Actionable}
    </Box>
  );
};

const AddEditHolidayList: React.FC<AddEditHolidayListProps> = ({ isEdit = false, setCurrentViewMode, selectedRow }) => {
  const dispatch = useAppDispatch();
  const { selectedOrganisationDetails } = useAppSelector((state) => state.userManagement);
  const [draggingRow, setDraggingRow] = useState<MRT_Row<HolidayResponse> | null>(null);
  const [hoveredTable, setHoveredTable] = useState<string | null>(null);
  const [allHolidays, setAllHolidays] = useState<HolidayResponse[]>([]);
  const [mandatoryHolidays, setMandatoryHolidays] = useState<HolidayResponse[]>(
    isEdit ? (selectedRow?.mandatory_holidays as HolidayResponse[]) : [],
  );
  const [optionalHolidays, setOptionalHolidays] = useState<HolidayResponse[]>(
    isEdit ? (selectedRow?.restricted_holidays as HolidayResponse[]) : [],
  );
  const [openAddHolidayModal, setOpenAddHolidayModal] = useState<boolean>(false);
  const { data, isFetched: isMasterDataFetched } = useQuery(
    ["get-years"],
    async () => {
      const response = await masterdataService.getACLs("CalendarYear");
      return response;
    },
    {
      refetchOnMount: true,
      refetchOnReconnect: true,
      refetchOnWindowFocus: false,
    },
  );

  const getDefaultYear = () => {
    if (isEdit) {
      return selectedRow?.year;
    }
    return isMasterDataFetched
      ? data?.find((eachDate) => eachDate === format(new Date(), "yyyy"))
      : new Date().getFullYear().toString();
  };

  const defaultOrgState = useMemo(() => {
    const countries = [...new Set(selectedOrganisationDetails?.addresses?.map((address) => address?.country))];
    return {
      countries,
      locationsBasedOnCountries: countries.reduce((locationsInCountries, currentCountry) => {
        locationsInCountries.set(
          currentCountry,
          selectedOrganisationDetails.addresses
            ?.filter((address) => address.country === currentCountry)
            ?.map((address) => address.state),
        );
        return locationsInCountries;
      }, new Map()),
    };
  }, [selectedOrganisationDetails]);

  const defaultFormState = useMemo(
    () => ({
      year: getDefaultYear(),
      location: isEdit ? selectedRow?.location : "",
      country: isEdit ? selectedRow?.country : defaultOrgState.countries?.[0],
    }),
    [isMasterDataFetched],
  );

  const { formDetails, formErrors, handleSelectChange } = useForm({
    initialState: defaultFormState as Record<string, string>,
    validations: {
      location: [validators.validateInput],
      year: [validators.validateInput],
      country: [validators.validateInput],
    },
  });

  const typedFormDetails = formDetails as typeof defaultFormState;
  const typedFormErrors = formErrors as Record<keyof typeof defaultFormState, string>;

  const isCreateDisabled = useMemo(() => {
    const areFormErrorsPresent = Object.values(typedFormErrors).some((error) => error);
    const areSomeFormDetailsEmpty = Object.values(typedFormDetails).some((detail) => !detail);

    const isFormDetailsUnchanged =
      isEdit &&
      selectedRow &&
      typedFormDetails.country === selectedRow?.country &&
      typedFormDetails.location === selectedRow.location &&
      typedFormDetails.year === selectedRow.year &&
      JSON.stringify(mandatoryHolidays) === JSON.stringify(selectedRow.mandatory_holidays) &&
      JSON.stringify(optionalHolidays) === JSON.stringify(selectedRow.restricted_holidays);

    return areFormErrorsPresent || areSomeFormDetailsEmpty || mandatoryHolidays.length === 0 || isFormDetailsUnchanged;
  }, [typedFormDetails, typedFormErrors, mandatoryHolidays, optionalHolidays, isEdit, selectedRow, allHolidays]);

  const { isFetched, refetch } = useQuery(
    ["get-holidays", typedFormDetails.year],
    async () => {
      const response = await holidayService.getHolidays(
        typedFormDetails?.year as string,
        typedFormDetails?.country as string,
      );
      if (isEdit && response?.length > 0) {
        const holidaysToFilter = new Set([
          ...mandatoryHolidays.map((holiday) => holiday.name),
          ...optionalHolidays.map((holiday) => holiday.name),
        ]);
        const filteredHolidays = response?.filter((holiday) => !holidaysToFilter.has(holiday.name));
        setAllHolidays(filteredHolidays);
      } else {
        setAllHolidays(JSON.parse(JSON.stringify(response)));
        setMandatoryHolidays([]);
        setOptionalHolidays([]);
      }
      return response;
    },
    {
      enabled: !!typedFormDetails.year && !!typedFormDetails.country,
      refetchOnMount: true,
      refetchOnReconnect: true,
      refetchOnWindowFocus: false,
    },
  );

  const createOrganisationHolidaysMutation = useMutation({
    mutationKey: ["create-organisation-holidays"],
    mutationFn: () => {
      return holidayService.createOrganisationHoliday({
        location: typedFormDetails.location as string,
        year: typedFormDetails.year as string,
        country: typedFormDetails?.country as string,
        mandatory_holidays: mandatoryHolidays,
        restricted_holidays: optionalHolidays,
      });
    },
    onSuccess: () => {
      setCurrentViewMode(HolidayOperations.VIEW_HOLIDAY_LIST);
      dispatch(setFullviewMode(false));
    },
  });

  const updateOrganisationHoliday = useMutation({
    mutationKey: ["update-organisation-holidays"],
    mutationFn: () => {
      return holidayService.updateOrganisationHoliday({
        country: typedFormDetails?.country as string,
        location: typedFormDetails.location,
        year: selectedRow?.year as string,
        mandatory_holidays: mandatoryHolidays,
        restricted_holidays: optionalHolidays,
      } as UpdateOrganisationHoliday);
    },
    onSuccess: () => {
      dispatch(setFullviewMode(false));
      setCurrentViewMode(HolidayOperations.VIEW_HOLIDAY_LIST);
    },
  });

  const onBackClick = () => {
    dispatch(setFullviewMode(false));
    setCurrentViewMode(HolidayOperations.VIEW_HOLIDAY_LIST);
  };

  const onAddClick = () => {
    setOpenAddHolidayModal(true);
  };

  const onActionClick = () => {
    if (isEdit) {
      updateOrganisationHoliday.mutate();
      return;
    }
    createOrganisationHolidaysMutation.mutate();
  };

  const locations = useMemo((): Array<string> => {
    return Array.from(new Set(defaultOrgState?.locationsBasedOnCountries.get(typedFormDetails?.country) || []));
  }, [defaultOrgState?.locationsBasedOnCountries, typedFormDetails?.country]);

  return (
    <Box position="relative">
      <Box id="section header" display="flex" gap={2} justifyContent="space-between" alignItems="center">
        <Box display="flex" flexDirection="column" sx={{ width: "100%" }} gap={2}>
          <Box display="flex" gap={1} alignItems="center">
            <IconButton onClick={onBackClick}>
              <ArrowBack sx={{ color: "#00000" }} />
            </IconButton>
            <Typography textTransform="none" fontWeight={600}>
              {isEdit ? "Edit Holiday List" : "Add Holidays"}
            </Typography>
          </Box>
          <Divider />
        </Box>
      </Box>
      <Grid id="form-elements" container spacing={2} marginY={1}>
        <Grid item xs={4}>
          <CustomSelect
            label="Country"
            onChange={(ev) => handleSelectChange(ev as never, "country" as never)}
            disabled={isEdit}
            fullWidth
            options={defaultOrgState?.countries?.map((val) => ({ value: val, label: val })) || []}
            name="country"
            value={typedFormDetails?.country as string}
            size="small"
            select
            required
          />
        </Grid>
        <Grid item xs={4}>
          <CustomSelect
            label="Year"
            onChange={(ev) => handleSelectChange(ev as never, "year" as never)}
            disabled={isEdit}
            options={data?.map((val) => ({ label: val as string, value: val as string })) || []}
            fullWidth
            name="year"
            key={`year_${isMasterDataFetched}_${typedFormDetails?.country}`}
            value={typedFormDetails?.year as string}
            size="small"
            select
            required
          />
        </Grid>
        <Grid item xs={4}>
          <CustomSelect
            label="Office Location"
            name="location"
            onChange={(ev) => handleSelectChange(ev as never, "location" as never)}
            options={locations?.map((eachLocation) => ({ value: eachLocation, label: eachLocation })) || []}
            value={typedFormDetails?.location || ""}
            fullWidth
            required
            disabled={isEdit || !typedFormDetails?.country}
            size="small"
          />
        </Grid>
      </Grid>
      <Box
        id="draggable-tables"
        gap={4}
        paddingY={2}
        sx={{ width: "100%", flex: 1 }}
        display="flex"
        alignItems="flex-start"
      >
        <Box sx={{ width: "50%" }}>
          <TableSectionHeader
            title="All Holidays"
            Actionable={
              <Box display="flex" gap={1} alignItems="center">
                <MUIButton onClick={onAddClick} disabled={!typedFormDetails.year} startIcon={<Add />}>
                  Add
                </MUIButton>
              </Box>
            }
          />
          <DataTable
            layoutMode="grid"
            data={allHolidays}
            columns={mainHolidayListColumns}
            key={`data_${isFetched}`}
            enableColumnActions={false}
            enableCellActions={false}
            enableSorting={false}
            enableBottomToolbar={false}
            enableTopToolbar={false}
            enableRowDragging={true}
            enableColumnFilters={false}
            enableGlobalFilter={true}
            enableStickyHeader={true}
            onDraggingRowChange={setDraggingRow}
            state={{ draggingRow }}
            muiTableContainerProps={{
              sx: {
                maxHeight: 450,
                minHeight: 450,
              },
            }}
            initialState={{
              showSkeletons: !isFetched,
              density: "comfortable",
              sorting: [
                {
                  desc: false,
                  id: "date",
                },
              ],
            }}
            getRowId={(originalRow) => `table-1-${originalRow.name}`}
            muiTablePaperProps={{
              onDragEnter: () => setHoveredTable("table-1"),
              sx: {
                outline: hoveredTable === "table-1" ? "2px dashed pink" : undefined,
              },
            }}
            muiRowDragHandleProps={{
              onDragEnd: () => {
                if (hoveredTable === "table-2") {
                  setMandatoryHolidays((data2) => [...data2, draggingRow!.original]);
                  setAllHolidays((data1) => data1.filter((d) => d !== draggingRow!.original));
                }

                if (hoveredTable === "table-3") {
                  setOptionalHolidays((data2) => [...data2, draggingRow!.original]);
                  setAllHolidays((data1) => data1.filter((d) => d !== draggingRow!.original));
                }
                setHoveredTable(null);
              },
            }}
          />
        </Box>
        <Box display="flex" flexDirection="column" gap={2} marginTop={2} sx={{ width: "50%" }}>
          <TableSectionHeader title="Mandatory Holidays" />
          <DataTable
            layoutMode="grid"
            data={mandatoryHolidays}
            columns={mainHolidayListColumns}
            enableColumnActions={false}
            enableCellActions={false}
            enableSorting={false}
            enableBottomToolbar={false}
            enableTopToolbar={false}
            enableRowDragging={true}
            enableFilters={false}
            enableGlobalFilter={true}
            enableStickyHeader={true}
            onDraggingRowChange={setDraggingRow}
            state={{ draggingRow }}
            getRowId={(originalRow) => `table-2-${originalRow.name}`}
            muiTablePaperProps={{
              onDragEnter: () => setHoveredTable("table-2"),
              sx: {
                outline: hoveredTable === "table-2" ? "2px dashed pink" : undefined,
              },
            }}
            muiRowDragHandleProps={{
              onDragEnd: () => {
                if (hoveredTable === "table-1") {
                  setAllHolidays((data2) => [...data2, draggingRow!.original]);
                  setMandatoryHolidays((data1) => data1.filter((d) => d !== draggingRow!.original));
                }

                if (hoveredTable === "table-3") {
                  setOptionalHolidays((data2) => [...data2, draggingRow!.original]);
                  setMandatoryHolidays((data1) => data1.filter((d) => d !== draggingRow!.original));
                }
                setHoveredTable(null);
              },
            }}
            muiTableContainerProps={{
              sx: {
                maxHeight: 200,
                minHeight: 200,
              },
            }}
          />
          <TableSectionHeader title="Restricted Holidays" />
          <DataTable
            layoutMode="grid"
            data={optionalHolidays}
            columns={mainHolidayListColumns}
            enableColumnActions={false}
            enableCellActions={false}
            enableSorting={false}
            enableBottomToolbar={false}
            enableTopToolbar={false}
            enableStickyHeader={true}
            enableFilters={false}
            enableGlobalFilter={true}
            enableRowDragging={true}
            onDraggingRowChange={setDraggingRow}
            state={{ draggingRow }}
            getRowId={(originalRow) => `table-3-${originalRow.name}`}
            muiTablePaperProps={{
              onDragEnter: () => setHoveredTable("table-3"),
              sx: {
                outline: hoveredTable === "table-3" ? "2px dashed pink" : undefined,
              },
            }}
            muiRowDragHandleProps={{
              onDragEnd: () => {
                if (hoveredTable === "table-1") {
                  setAllHolidays((data2) => [...data2, draggingRow!.original]);
                  setOptionalHolidays((data1) => data1.filter((d) => d !== draggingRow!.original));
                }

                if (hoveredTable === "table-2") {
                  setMandatoryHolidays((data2) => [...data2, draggingRow!.original]);
                  setOptionalHolidays((data1) => data1.filter((d) => d !== draggingRow!.original));
                }
                setHoveredTable(null);
              },
            }}
            muiTableContainerProps={{
              sx: {
                maxHeight: 200,
                minHeight: 200,
              },
            }}
          />
        </Box>
      </Box>
      {openAddHolidayModal && (
        <AddHolidayModal
          isModalOpen={openAddHolidayModal}
          onClose={() => setOpenAddHolidayModal(false)}
          refetch={refetch}
          key={`modalOpen_${openAddHolidayModal}`}
          formDetails={typedFormDetails}
        />
      )}
      <Box
        position="sticky"
        bottom={0}
        component={Paper}
        elevation={0}
        display="flex"
        justifyContent="flex-end"
        zIndex={2}
        sx={{ width: "100%" }}
      >
        <Button
          sx={{ alignSelf: "flex-end", padding: "10px 0px" }}
          variant="contained"
          onClick={onActionClick}
          disabled={!!isCreateDisabled}
        >
          Save
        </Button>
      </Box>
    </Box>
  );
};

export default AddEditHolidayList;
