import {
  Checkbox,
  CircularProgress,
  FormControl<PERSON>abel,
  Grid2,
  InputLabel,
  ListItemText,
  MenuItem,
  Select,
  Switch,
  Tooltip,
  Typography,
} from "@mui/material";
import { addYears, endOfMonth, endOfYear, startOfMonth, startOfYear, subYears } from "date-fns";
import React, { Key } from "react";
import { useMasterData } from "src/customHooks/useMasterData";
import CustomDateField from "src/modules/Common/FormInputs/CustomDateField";
import CustomTextField from "src/modules/Common/FormInputs/CustomTextField";

interface Props {
  employeeTypes: {
    data: string[];
    isFetched: boolean;
  };
  handleChange: (event: React.ChangeEvent<HTMLInputElement>) => void;
  setFormDetail: (name: string, value: unknown) => void;
  handleCheckboxChange: (event: React.ChangeEvent<HTMLInputElement>) => void;
  translations: {
    name: string;
    employeeType: string;
    startDate: string;
    endDate: string;
    encashmentEnabled: string;
    encashmentBasis: string;
    maxEncashmentDays: string;
  };
  typedFormErrors: Record<keyof any, string>;
  typedFormDetails: any;
}

const FixedLeavePolicyEditableFields: React.FC<Props> = ({
  employeeTypes,
  handleChange,
  setFormDetail,
  handleCheckboxChange,
  translations,
  typedFormErrors,
  typedFormDetails,
}) => {
  const { data: leaveEncashmentBasisOptions } = useMasterData("LeaveEncashmentBasis");

  return (
    <Grid2 container spacing={2}>
      <Grid2 size={3}>
        <CustomTextField
          title={translations.name}
          id="name"
          size="small"
          fullWidth
          value={typedFormDetails?.name}
          onChange={handleChange}
          placeholder={translations.name}
          required
          error={!!typedFormErrors.name}
          helperText={!!typedFormErrors?.name && typedFormErrors?.name}
        />
      </Grid2>
      <Grid2 size={3}>
        <InputLabel sx={{ marginBottom: "8px", fontSize: "14px" }} htmlFor="employeeType" required>
          {translations.employeeType}
        </InputLabel>
        <Tooltip title={typedFormDetails?.employeeType?.toString()} placement="top">
          <Select
            id="employeeType"
            size="small"
            fullWidth
            displayEmpty
            disabled={!employeeTypes.isFetched}
            multiple
            startAdornment={!employeeTypes.isFetched && <CircularProgress />}
            value={typedFormDetails?.employeeType}
            renderValue={(selected) => {
              if (!selected?.length) {
                return <Typography color="gray">Select Employee Type</Typography>;
              }
              return selected.join(", ");
            }}
            onChange={handleCheckboxChange}
            required
          >
            {employeeTypes?.data?.map((employeeType) => (
              <MenuItem key={employeeType as Key} value={employeeType as string}>
                <Checkbox checked={typedFormDetails?.employeeType.includes(employeeType as string)}></Checkbox>
                <ListItemText primary={employeeType as string} />
              </MenuItem>
            ))}
          </Select>
        </Tooltip>
      </Grid2>

      <Grid2 size={3}>
        <CustomDateField
          title={translations.startDate}
          required
          minDate={subYears(startOfYear(new Date()), 1)}
          maxDate={addYears(endOfYear(new Date()), 1)}
          format="MMM yyyy"
          views={["month", "year"]}
          slotProps={{
            textField: {
              id: "startDate",
              size: "small",
              fullWidth: true,
              error: !!typedFormErrors.startDate,
              helperText: !!typedFormErrors?.startDate && typedFormErrors?.startDate,
              placeholder: "Start Month",
            },
          }}
          value={typedFormDetails?.startDate as unknown as Date}
          onChange={(date: Date | null) => setFormDetail("startDate", startOfMonth(date as Date))}
        />
      </Grid2>
      <Grid2 size={3}>
        <CustomDateField
          title={translations.endDate}
          minDate={typedFormDetails?.startDate as unknown as Date}
          required
          format="MMM yyyy"
          views={["month", "year"]}
          disabled={!typedFormDetails?.startDate}
          maxDate={addYears(endOfYear(typedFormDetails?.startDate), 1)}
          slotProps={{
            textField: {
              id: "endDate",
              size: "small",
              fullWidth: true,
              error: !!typedFormErrors.endDate,
              helperText: !!typedFormErrors?.endDate && typedFormErrors?.endDate,
              placeholder: "End Month",
            },
          }}
          value={typedFormDetails?.endDate as unknown as Date}
          onChange={(date: Date | null) => setFormDetail("endDate", endOfMonth(date as Date))}
        />
      </Grid2>

      {/* Encashment Fields */}
      <Grid2 size={12} padding={1}>
        <FormControlLabel
          control={
            <Switch
              checked={typedFormDetails?.encashmentEnabled || false}
              onChange={(event) => {
                setFormDetail("encashmentEnabled", event.target.checked)
                if (!event.target.checked) {
                  setFormDetail("encashmentBasis", null);
                  setFormDetail("maxEncashmentDays", null);
                }
              }}
              size="medium"
            />
          }
          label={translations.encashmentEnabled}
          sx={{ marginTop: 0.5, paddingLeft: 0 }}
        />
      </Grid2>
        <>
          <Grid2 size={3}>
            <InputLabel sx={{ marginBottom: "8px", fontSize: "14px" }} htmlFor="encashmentBasis" required>
              {translations.encashmentBasis}
            </InputLabel>
            <Select
              id="encashmentBasis"
              size="small"
              fullWidth
              displayEmpty
              value={typedFormDetails?.encashmentBasis || ""}
              onChange={(event) => setFormDetail("encashmentBasis", event.target.value || null)}
              error={!!typedFormErrors.encashmentBasis}
              disabled={!typedFormDetails?.encashmentEnabled}
              renderValue={(selected) => {
                if (!selected?.length) {
                  return <Typography color="gray">Select Encashment Basis</Typography>;
                }
                return selected;
              }}
            >
              {leaveEncashmentBasisOptions?.map((option) => (
                <MenuItem key={option as Key} value={option as string}>
                  {option as string}
                </MenuItem>
              ))}
            </Select>
          </Grid2>

          <Grid2 size={3}>
            <CustomTextField
              title={translations.maxEncashmentDays}
              id="maxEncashmentDays"
              size="small"
              fullWidth
              type="number"
              disabled={!typedFormDetails?.encashmentEnabled}
              value={typedFormDetails?.maxEncashmentDays || ""}
              onChange={(event) =>
                setFormDetail("maxEncashmentDays", event.target.value ? Number(event.target.value) : null)
              }
              placeholder="Enter max days"
              error={!!typedFormErrors.maxEncashmentDays}
              helperText={!!typedFormErrors?.maxEncashmentDays && typedFormErrors?.maxEncashmentDays}
            />
          </Grid2>
        </>
    </Grid2>
  );
};

export default FixedLeavePolicyEditableFields;
