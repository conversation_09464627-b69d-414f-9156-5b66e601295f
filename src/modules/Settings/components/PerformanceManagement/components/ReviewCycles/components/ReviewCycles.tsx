import { Box, Button, Paper } from "@mui/material";
import { useQuery } from "@tanstack/react-query";
import React from "react";
import { useContentHeight } from "src/customHooks/useContentHeight";
import LoadingScreen from "src/modules/Common/LoadingScreen/LoadingScreen";
import { ReviewCycle } from "src/services/api_definitions/performanceManagement.service";
import performanceManagementService from "src/services/performanceManagement.service";
import ReviewCycleForm from "./ReviewCycleForm";

const defaultReviewCycles: ReviewCycle[] = [
  {
    name: "",
    start_date: "",
    end_date: "",
    goal_setting_start_date: "",
    goal_setting_end_date: "",
    performance_review_start_date: "",
    performance_review_end_date: "",
    goal_setting_enabled: false,
    performance_review_enabled: false,
  },
];

const ReviewCycles = () => {
  const [reviewCycles, setReviewCycles] = React.useState<ReviewCycle[]>([]);

  const { isFetched, refetch } = useQuery(
    ["reviewCycles"],
    async () => {
      const resp = await performanceManagementService.getReviewCyclesConfigDetails();

      if (resp?.length) {
        setReviewCycles(resp);
        return resp;
      }
      setReviewCycles(defaultReviewCycles);
      return resp;
    },
    {
      refetchOnWindowFocus: false,
      refetchOnMount: true,
    },
  );

  const onAddMoreClick = () => {
    setReviewCycles((prevState) => {
      return [...prevState, ...defaultReviewCycles];
    });
  };

  const onSuccess = () => {
    refetch();
  };

  const onDeleteClick = (index: number) => {
    setReviewCycles((prevState) => {
      return prevState.filter((_, i) => i !== index);
    });
  };

  return (
    <Box>
      {!isFetched && <LoadingScreen />}
      <Box display="flex" gap={2} flexDirection="column" height={useContentHeight() - 200}>
        {isFetched &&
          reviewCycles?.map((cycle, index) => (
            <Paper key={index} elevation={3} sx={{ p: 3 }}>
              <ReviewCycleForm
                key={cycle.name}
                selectedReviewCycle={cycle}
                onActionSuccess={onSuccess}
                onDelete={() => onDeleteClick(index)}
              />
            </Paper>
          ))}
        <Box padding={[2, 0]} margin={[2, 0]}>
          <Button onClick={onAddMoreClick} variant="contained" color="primary">
            Add Review Cycle
          </Button>
        </Box>
      </Box>
    </Box>
  );
};

export default ReviewCycles;
