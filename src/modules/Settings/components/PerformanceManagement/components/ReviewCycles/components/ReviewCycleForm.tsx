import { <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, FormControlLabel, Grid, RadioGroup, Switch, Tooltip, Typography } from "@mui/material";
import { useMutation } from "@tanstack/react-query";
import { format } from "date-fns";
import React, { useMemo } from "react";
import { useForm } from "src/customHooks/useForm";
import CustomDateField from "src/modules/Common/FormInputs/CustomDateField";
import CustomTextField from "src/modules/Common/FormInputs/CustomTextField";
import { ReviewCycle, UpdateReviewCycle } from "src/services/api_definitions/performanceManagement.service";
import performanceManagementService from "src/services/performanceManagement.service";
import validators from "src/utils/validators";

interface ReviewCycleFormProps {
  selectedReviewCycle?: ReviewCycle;
  onActionSuccess: () => void;
  onDelete?: () => void;
}

const defaultFormState: ReviewCycle = {
  name: "",
  start_date: "",
  end_date: "",
  goal_setting_start_date: "",
  goal_setting_end_date: "",
  performance_review_start_date: "",
  performance_review_end_date: "",
  goal_setting_enabled: false,
  performance_review_enabled: false,
};

const ReviewCycleForm: React.FC<ReviewCycleFormProps> = ({ selectedReviewCycle, onActionSuccess, onDelete }) => {
  const isOpen = selectedReviewCycle?.status === "OPEN";
  const isClosed = selectedReviewCycle?.status === "CLOSED";
  const isInit = selectedReviewCycle?.status === "INIT";
  const isGoalSettingEnabled = selectedReviewCycle?.goal_setting_enabled;
  const isPerformanceReviewEnabled = selectedReviewCycle?.performance_review_enabled;

  const getDefafultFormState = useMemo(() => {
    return {
      ...defaultFormState,
      ...selectedReviewCycle,
    };
  }, [selectedReviewCycle, defaultFormState]);

  const { formDetails, formErrors, handleChange, setFormDetail, areFormDetailsValid } = useForm({
    initialState: getDefafultFormState,
    isBulk: false,
    validations: {
      name: [validators.validateInput],
      start_date: [validators.validateInput],
      end_date: [validators.validateInput],
      status: [],
      goal_setting_enabled: [],
      performance_review_enabled: [],
      goal_setting_start_date: [validators.validateInput],
      goal_setting_end_date: [validators.validateInput],
      performance_review_start_date: [validators.validateInput],
      performance_review_end_date: [validators.validateInput],
    },
  });

  const typedFormDetails = formDetails as ReviewCycle;
  const typedFormErrors = formErrors as Record<keyof ReviewCycle, string>;

  const creationMutation = useMutation({
    mutationFn: async (payload: ReviewCycle) => performanceManagementService.createReviewCycle(payload),
    onSuccess: () => {
      onActionSuccess();
    },
    onError: (error) => {
      console.error({ error });
    },
  });

  const updationMutation = useMutation({
    mutationFn: async (payload: UpdateReviewCycle) => performanceManagementService.updateReviewCycle(payload),
    onSuccess: () => {
      onActionSuccess();
    },
    onError: (error) => {
      console.error({ error });
    },
  });

  const onCheckboxChange = (name: string, checked: boolean) => {
    if (name === "goal_setting_enabled" && checked) {
      setFormDetail("performance_review_enabled", false);
    }

    if (name === "performance_review_enabled" && checked) {
      setFormDetail("goal_setting_enabled", false);
    }
    setFormDetail(name, checked);
  };

  const onSave = () => {
    creationMutation.mutate(formDetails as ReviewCycle);
  };

  const onUpdate = () => {
    updationMutation.mutate({
      ...formDetails,
      name: selectedReviewCycle?.name,
      new_name: selectedReviewCycle?.name !== typedFormDetails?.name ? typedFormDetails?.name : undefined,
    } as UpdateReviewCycle);
  };
  const getButtonState = () => {
    if (isInit) {
      return "Update";
    }
    if (isOpen) {
      return "Update";
    }
    return "Save";
  };

  const areFormFieldsEnabled = () => {
    if (isInit || isOpen) {
      return (
        areFormDetailsValid &&
        (typedFormDetails.name !== selectedReviewCycle?.name ||
          typedFormDetails.start_date !== selectedReviewCycle?.start_date ||
          typedFormDetails.end_date !== selectedReviewCycle?.end_date ||
          typedFormDetails.goal_setting_start_date !== selectedReviewCycle?.goal_setting_start_date ||
          typedFormDetails.goal_setting_end_date !== selectedReviewCycle?.goal_setting_end_date ||
          typedFormDetails.performance_review_start_date !== selectedReviewCycle?.performance_review_start_date ||
          typedFormDetails.performance_review_end_date !== selectedReviewCycle?.performance_review_end_date ||
          typedFormDetails.goal_setting_enabled !== selectedReviewCycle?.goal_setting_enabled ||
          typedFormDetails.performance_review_enabled !== selectedReviewCycle?.performance_review_enabled)
      );
    }
    return areFormDetailsValid;
  };

  return (
    <Grid container spacing={2}>
      <Grid item sm={4}>
        <CustomTextField
          name="name"
          id="name"
          title="Name"
          value={typedFormDetails.name}
          onChange={handleChange}
          error={!!typedFormErrors.name}
          required
          helperText={typedFormErrors.name ?? typedFormErrors?.name}
          fullWidth
          disabled={isOpen || isClosed}
          size="small"
        />
      </Grid>
      <Grid item sm={4}>
        <CustomDateField
          name="start_date"
          title="Start Date"
          value={typedFormDetails?.start_date as unknown as Date}
          onChange={(date) => {
            setFormDetail("start_date", format(date as Date, "yyyy-MM-dd"));
          }}
          views={["year", "month", "day"]}
          disabled={isOpen || isClosed}
          slotProps={{
            textField: {
              fullWidth: true,
              size: "small",
              id: "start_date",
              error: !!typedFormErrors.start_date,
              helperText: typedFormErrors.start_date ?? typedFormErrors?.start_date,
              disabled: isOpen || isClosed,
              required: true,
            },
          }}
          required
        />
      </Grid>
      <Grid item sm={4}>
        <CustomDateField
          name="end_date"
          title="End Date"
          value={typedFormDetails.end_date as unknown as Date}
          views={["year", "month", "day"]}
          onChange={(date) => {
            setFormDetail("end_date", format(date as Date, "yyyy-MM-dd"));
          }}
          disabled={isOpen || isClosed}
          slotProps={{
            textField: {
              fullWidth: true,
              size: "small",
              id: "end_date",
              error: !!typedFormErrors.end_date,
              helperText: typedFormErrors.end_date ?? typedFormErrors?.end_date,
              disabled: isOpen || isClosed,
              required: true,
            },
          }}
          required
        />
      </Grid>
      <Grid item sm={12}>
        <Typography fontSize={14}>Goal Setting</Typography>
      </Grid>
      <Grid item sm={6}>
        <CustomDateField
          name="goal_setting_start_date"
          title="Start Date"
          value={typedFormDetails.goal_setting_start_date as unknown as Date}
          views={["year", "month", "day"]}
          onChange={(date) => {
            setFormDetail("goal_setting_start_date", format(date as Date, "yyyy-MM-dd"));
          }}
          disablePast
          minDate={new Date(new Date().setDate(new Date().getDate() + 1))}
          disabled={isClosed || isGoalSettingEnabled || isPerformanceReviewEnabled}
          slotProps={{
            textField: {
              fullWidth: true,
              size: "small",
              id: "goal_setting_start_date",
              error: !!typedFormErrors.goal_setting_start_date,
              helperText: typedFormErrors.goal_setting_start_date ?? typedFormErrors?.goal_setting_start_date,
              disabled: isClosed || isGoalSettingEnabled || isPerformanceReviewEnabled,
              required: true,
            },
          }}
          required
        />
      </Grid>
      <Grid item sm={6}>
        <CustomDateField
          name="goal_setting_end_date"
          title="End Date"
          value={typedFormDetails.goal_setting_end_date as unknown as Date}
          views={["year", "month", "day"]}
          onChange={(date) => {
            setFormDetail("goal_setting_end_date", format(date as Date, "yyyy-MM-dd"));
          }}
          disablePast
          minDate={new Date(new Date().setDate(new Date(typedFormDetails.goal_setting_start_date).getDate()))}
          disabled={isClosed || isPerformanceReviewEnabled}
          slotProps={{
            textField: {
              fullWidth: true,
              size: "small",
              id: "goal_setting_end_date",
              error: !!typedFormErrors.goal_setting_end_date,
              helperText: typedFormErrors.goal_setting_end_date ?? typedFormErrors?.goal_setting_end_date,
              disabled: isClosed || isPerformanceReviewEnabled,
              required: true,
            },
          }}
          required
        />
      </Grid>
      <Grid item sm={12}>
        <Typography fontSize={14}>Performance Review</Typography>
      </Grid>
      <Grid item sm={6}>
        <CustomDateField
          name="performance_review_start_date"
          title="Start Date"
          value={typedFormDetails.performance_review_start_date as unknown as Date}
          views={["year", "month", "day"]}
          onChange={(date) => {
            setFormDetail("performance_review_start_date", format(date as Date, "yyyy-MM-dd"));
          }}
          disablePast
          minDate={new Date(new Date().setDate(new Date().getDate() + 1))}
          disabled={isClosed || isPerformanceReviewEnabled}
          slotProps={{
            textField: {
              fullWidth: true,
              size: "small",
              id: "performance_review_start_date",
              error: !!typedFormErrors.performance_review_start_date,
              helperText:
                typedFormErrors.performance_review_start_date ?? typedFormErrors?.performance_review_start_date,
              required: true,
              disabled: isClosed || isPerformanceReviewEnabled,
            },
          }}
          required
        />
      </Grid>
      <Grid item sm={6}>
        <CustomDateField
          name="performance_review_end_date"
          title="End Date"
          value={typedFormDetails.performance_review_end_date as unknown as Date}
          views={["year", "month", "day"]}
          onChange={(date) => {
            setFormDetail("performance_review_end_date", format(date as Date, "yyyy-MM-dd"));
          }}
          disabled={isClosed}
          disablePast
          minDate={new Date(new Date().setDate(new Date(typedFormDetails.performance_review_start_date).getDate()))}
          slotProps={{
            textField: {
              fullWidth: true,
              size: "small",
              id: "performance_review_end_date",
              error: !!typedFormErrors.performance_review_end_date,
              helperText: typedFormErrors.performance_review_end_date ?? typedFormErrors?.performance_review_end_date,
              disabled: isClosed,
              required: true,
            },
          }}
          required
        />
      </Grid>
      <Grid item sm={6}>
        <RadioGroup aria-labelledby="demo-radio-buttons-group-label" defaultValue="female" name="radio-buttons-group">
          <FormControlLabel
            value="goal_setting_enabled"
            control={
              <Switch
                id="goal_setting_enabled"
                checked={typedFormDetails?.goal_setting_enabled}
                name="goal_setting_enabled"
                disabled
                onChange={(_ev, checked) => onCheckboxChange("goal_setting_enabled", checked)}
              />
            }
            label="Goals Setting"
          />
          <FormControlLabel
            value="performance_review_enabled"
            control={
              <Switch
                id="performance_review_enabled"
                name="performance_review_enabled"
                checked={typedFormDetails?.performance_review_enabled}
                disabled
                onChange={(_ev, checked) => onCheckboxChange("performance_review_enabled", checked)}
              />
            }
            label="Performance Review"
          />
        </RadioGroup>
      </Grid>
      <Grid item sm={12}>
        <Box display="flex" gap={1} justifyContent="flex-end">
          {isClosed && (
            <Tooltip title="Once the review cycle is closed, it cannot be modified" placement="left">
              <Alert variant="filled" color="error">
                Closed
              </Alert>
            </Tooltip>
          )}
          <Box display="flex" alignItems="center" gap={1}>
            {!isOpen && !isClosed && !isInit && (
              <Button size="small" color="error" onClick={onDelete} variant="outlined">
                Delete Row
              </Button>
            )}
            {!isClosed && (
              <Button
                onClick={isOpen || isInit ? onUpdate : onSave}
                disabled={!areFormFieldsEnabled()}
                variant="contained"
              >
                {getButtonState()}
              </Button>
            )}
          </Box>
        </Box>
      </Grid>
    </Grid>
  );
};

export default ReviewCycleForm;
