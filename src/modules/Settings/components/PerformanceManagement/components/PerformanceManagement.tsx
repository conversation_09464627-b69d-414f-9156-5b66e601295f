import { Box, Paper, Tab, Tabs } from "@mui/material";
import React, { useMemo } from "react";
import { useAppSelector } from "src/customHooks/useAppSelector";
import useSubroutes from "src/customHooks/useSubroutes";
import ContentHeader from "src/modules/Common/ContentHeader/ContentHeader";
import NoAccessScreen from "src/modules/Common/NoAccess/NoAccess";
import { PATH_CONFIG } from "src/modules/Routing/config";
import RatingsConfig from "./RatingsConfig/components/RatingsConfig";
import ReviewCycles from "./ReviewCycles/components/ReviewCycles";

const performanceManagementTabs = [
  {
    key: PATH_CONFIG.SETTINGS_PERFORMANCEMANAGEMENT_REVIEWCYCLES.key,
    label: "Review Cycles",
    header: "Review Cycles",
    subtitle: "Manage Employee Review Cycles",
    buttonLabel: "Review Cycles",
    component: <ReviewCycles />,
    id: 0,
  },
  {
    key: PATH_CONFIG.SETTINGS_PERFORMANCEMANAGEMENT_RATINGSCONFIG.key,
    label: "Ratings Configuration",
    header: "Ratings Configuration",
    subtitle: "Manage Employee Ratings Configuration",
    buttonLabel: "Ratings Configuration",
    component: <RatingsConfig />,
    id: 0,
  },
];

const PerformanceManagement = () => {
  const { isFullView } = useAppSelector((state) => state.app);
  const subRoutes = useSubroutes(PATH_CONFIG.SETTINGS_PERFORMANCEMANAGEMENT.key);
  const tabsToShow = useMemo(
    () =>
      performanceManagementTabs.filter((settings) => {
        return subRoutes.some((route) => route.key === settings.key && route.acl?.canRead);
      }),
    [subRoutes],
  );

  const [tabId, setTabId] = React.useState(tabsToShow?.[0]?.id);
  const selectedTab = useMemo(() => tabsToShow?.[tabId], [tabId]);

  const handleChange = (_event: React.SyntheticEvent, newValue: number) => {
    setTabId(newValue);
  };

  if (!tabsToShow || tabsToShow?.length === 0) {
    return (
      <Box>
        <NoAccessScreen />
      </Box>
    );
  }

  return (
    <Box display="flex" flexDirection="column" gap={2}>
      {!isFullView && <ContentHeader title={selectedTab?.header} />}
      {!isFullView && (
        <Tabs component={Paper} value={tabId} onChange={handleChange}>
          {tabsToShow?.map((tab) => (
            <Tab
              sx={{
                textTransform: "none",
              }}
              label={tab?.label}
              tabIndex={tab?.id}
              key={tab?.id}
            />
          ))}
        </Tabs>
      )}
      <Box display="flex" flexDirection="column" gap={2} p={1}>
        {selectedTab?.component && selectedTab?.component}
      </Box>
    </Box>
  );
};
export default PerformanceManagement;
