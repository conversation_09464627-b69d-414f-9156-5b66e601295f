import { Add, Delete } from "@mui/icons-material";
import { Box, Button, Grid, IconButton, Paper } from "@mui/material";
import { useMutation, useQuery } from "@tanstack/react-query";
import React, { useMemo } from "react";
import { useForm } from "src/customHooks/useForm";
import CustomTextField from "src/modules/Common/FormInputs/CustomTextField";
import { addButtonStyle } from "src/modules/Employees/components/CommonForm";
import { PATH_CONFIG } from "src/modules/Routing/config";
import { RatingsConfiguration } from "src/services/api_definitions/performanceManagement.service";
import performanceManagementService from "src/services/performanceManagement.service";
import { getACLFromFeaturekey } from "src/utils/screenUtils";
import validators from "src/utils/validators";

const initialFormState: RatingsConfiguration = {
  value: null as any,
  description: "",
};

const RatingsConfig = () => {
  const RATINGS_ACL = getACLFromFeaturekey(PATH_CONFIG.SETTINGS_PERFORMANCEMANAGEMENT_RATINGSCONFIG.key);
  const { data = [], refetch } = useQuery(
    ["RatingsConfig"],
    async () => {
      return performanceManagementService.getRatingsConfiguration();
    },
    {
      refetchOnWindowFocus: false,
      refetchOnMount: true,
    },
  );

  const defaultFormState = useMemo(() => {
    return data && data?.length > 0
      ? data?.map(({ value, description }) => ({
          value,
          description,
        }))
      : [initialFormState];
  }, [data]);

  const { formErrors, handleChange, deleteFormDetails, areFormDetailsValid, formDetails, addNewFormDetailRow } =
    useForm({
      initialState: defaultFormState,
      isBulk: true,
      validations: {
        value: [validators.validateInput],
        description: [validators.validateInput],
      },
    });

  const createRatingConfigMutation = useMutation({
    mutationFn: async (payload: RatingsConfiguration[]) =>
      performanceManagementService.createRatingsConfiguration(
        payload.map(({ value, description }) => ({
          value: Number(value),
          description,
        })),
      ),
    onSuccess: () => {
      refetch();
    },
    onError: (error) => {
      console.error({ error });
    },
  });

  const updateRatingMutation = useMutation({
    mutationFn: async (payload: RatingsConfiguration[]) =>
      performanceManagementService.updateRatingsConfiguration(
        payload.map(({ value, description }) => ({
          value: Number(value),
          description,
        })),
      ),
    onSuccess: () => {
      refetch();
    },
    onError: (error) => {
      console.error({ error });
    },
  });

  const typedFormDetails = formDetails as unknown as RatingsConfiguration[];
  const typedFormErrors = formErrors as Record<keyof RatingsConfiguration, string>[];

  const onCreate = () => {
    createRatingConfigMutation.mutate(typedFormDetails);
  };

  const onUpdate = () => {
    updateRatingMutation.mutate(typedFormDetails);
  };

  const onAddMoreClick = () => {
    addNewFormDetailRow([initialFormState]);
  };

  const isFormEnabled = useMemo(() => {
    if (!RATINGS_ACL.canWrite) return false;
    if (!areFormDetailsValid) return false;
    if (!typedFormDetails || typedFormDetails.length === 0) return false;
    if (!data) return true;
    return (
      typedFormDetails.some(
        (formDetail, index) =>
          Number(formDetail.value) !== Number(data[index]?.value) ||
          formDetail.description !== data[index]?.description,
      ) || data.length !== typedFormDetails.length
    );
  }, [data, typedFormDetails, RATINGS_ACL, areFormDetailsValid]);

  return (
    <>
      <Box display="flex" gap={2} flexDirection="column">
        {typedFormDetails.map(({ value, description }, index) => (
          <Paper key={index} elevation={2}>
            <Grid key={index} container spacing={2} p={3}>
              <Grid item xs={4}>
                <CustomTextField
                  name="value"
                  id="value"
                  title="Rating Value"
                  type="number"
                  size="small"
                  onChange={(ev) => handleChange(ev as React.ChangeEvent<HTMLInputElement>, index)}
                  placeholder="Add Rating Value"
                  fullWidth
                  required
                  disabled={!RATINGS_ACL.canWrite}
                  value={value}
                  error={!!typedFormErrors?.[index]?.value}
                  helperText={typedFormErrors?.[index]?.value || null}
                />
              </Grid>
              <Grid item xs={8}>
                <CustomTextField
                  name="description"
                  id="description"
                  title="Description"
                  size="small"
                  placeholder="Add Description"
                  required
                  disabled={!RATINGS_ACL.canWrite}
                  value={description}
                  onChange={(ev) => handleChange(ev as React.ChangeEvent<HTMLInputElement>, index)}
                  fullWidth
                  multiline
                  rows={1}
                  error={!!typedFormErrors?.[index]?.description}
                  helperText={typedFormErrors?.[index]?.description || null}
                />
              </Grid>
              {typedFormDetails?.length - 1 !== 0 && (
                <Grid item sm={12}>
                  <IconButton
                    color="error"
                    onClick={() => deleteFormDetails(index)}
                    sx={{
                      float: "right",
                    }}
                  >
                    <Delete />
                  </IconButton>
                </Grid>
              )}
              {index === typedFormDetails.length - 1 && (
                <Grid item>
                  <Button
                    variant="text"
                    sx={addButtonStyle}
                    onClick={onAddMoreClick}
                    startIcon={<Add fontSize="small" />}
                  >
                    Add more
                  </Button>
                </Grid>
              )}
            </Grid>
          </Paper>
        ))}
        {typedFormDetails?.length === 0 && (
          <Grid item>
            <Button variant="text" sx={addButtonStyle} onClick={onAddMoreClick} startIcon={<Add fontSize="small" />}>
              Add more
            </Button>
          </Grid>
        )}
        {typedFormDetails?.length > 0 && (
          <Box display="flex" width="100%" position="sticky" bottom={0} p={2} component={Paper} elevation={1}>
            <Button
              disabled={!isFormEnabled}
              variant="contained"
              onClick={data && data?.length > 0 ? onUpdate : onCreate}
            >
              Save
            </Button>
          </Box>
        )}
      </Box>
    </>
  );
};

export default RatingsConfig;
