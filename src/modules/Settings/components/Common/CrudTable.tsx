/* eslint-disable @typescript-eslint/no-explicit-any */
import React, { useState } from "react";

import { Delete, Download, Edit } from "@mui/icons-material";
import { Box, Button, CircularProgress, DialogActions, IconButton, Typography } from "@mui/material";
import { MRT_ColumnDef } from "material-react-table";
import { FormActions } from "src/modules/Common/CRUDTableV2";
import ContentHeader from "src/modules/Common/ContentHeader/ContentHeader";
import Modal from "src/modules/Common/Modal/Modal";
import DataTable from "src/modules/Common/Table/DataTable";
// import { CommonForm } from "./CommonForm";
import { CommonForm } from "src/modules/Employees/components/CommonForm";

/* A component that renders a table with below functionality:
1. Add
2. Edit
3. Delete */

interface FormConfig<T extends object> {
  isLoading: boolean;
  formConfig: {
    editFormConfig: {
      nextButtonText: string;
      onNextClick: (formDetails: any, rowIndex: number) => void;
      formTitle: string;
      customDayContent?: (day: any) => React.ReactNode;
    };
    addFormConfig: {
      nextButtonText: string;
      onNextClick: (formDetails: any) => void;
      formTitle: string;
      addButtonText: string;
      customDayContent?: (day: any) => React.ReactNode;
    };
    deleteFormConfig: {
      formTitle?: string;
      onNextClick: (rowIndex: number) => void;
      getQuestion?: (rowIndex: number) => string;
      nextButtonText?: string;
    };
    downloadFormConfig: {
      onNextClick: (rowIndex: number) => void;
      nextButtonText: string;
      formTitle: string;
    };
    tableHeaderTitle?: string;
    allowAdd?: boolean;
    allowDelete?: boolean;
    allowEdit?: boolean;
    allowDownload?: boolean;
  };
  selectOptions?: any;
  defaultFormState: any;
  inputElements: {
    name: string;
    label: string;
    type: string;
    options?: any;
    style?: any;
    placeholder?: string;
    isEditable?: boolean;
    isRequired?: boolean;
    allowEmpty?: boolean;
    rows?: number;
  }[];
  rowAdditionaInitialValues: any;
  captureFormChange?: () => void;
  columns?: MRT_ColumnDef<T>[];
  enableRowNumbers?: boolean;
  defaultOpenAddModal?: boolean;
  disabledInputFields?: Record<string, boolean>;
  useFormDetails: any;
  readOnlyFields?: Record<string, boolean>;
  setSelectedRow: (rowIndex: number) => void;
  selectedRow: number;
}

export const CrudTable = ({
  defaultOpenAddModal,
  isLoading,
  formConfig,
  selectOptions,
  defaultFormState,
  inputElements,
  columns,
  enableRowNumbers = true,
  disabledInputFields,
  useFormDetails,
  readOnlyFields,
  setSelectedRow,
  selectedRow,
  rowAdditionaInitialValues,
}: FormConfig) => {
  const [isAddModalOpen, setIsAddModalOpen] = useState(defaultOpenAddModal || false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [deleteModalOpen, setDeleteModalOpen] = useState(false);
  const {
    addFormConfig,
    editFormConfig,
    deleteFormConfig,
    downloadFormConfig,
    tableHeaderTitle,
    allowAdd = true,
    allowDelete = true,
    allowEdit = true,
    allowDownload = false,
  } = formConfig;
  const defaultDeleteText = "Are you sure you want to delete this entity?";

  const { formDetails, formErrors, areFormDetailsValid, setFormDetail, setFormDetails } = useFormDetails;

  const onAddButtonClick = () => {
    setIsAddModalOpen(true);
    setFormDetails(rowAdditionaInitialValues);
    setSelectedRow(null);
  };
  const onEditClicked = (row: any) => {
    setIsEditModalOpen(true);
    setSelectedRow(row?.index);
  };

  const onDeleteClick = (row: any) => {
    setDeleteModalOpen(true);
    setSelectedRow(row?.index);
  };

  const onDownloadClick = (row: any) => {
    downloadFormConfig.onNextClick(row?.index);
  };
  const onEditSubmit = async () => {
    try {
      await editFormConfig.onNextClick(formDetails, selectedRow);
      setIsEditModalOpen(false);
    } catch (_error) {
      // do noithing
    }
  };
  const onAddSubmit = async () => {
    try {
      await addFormConfig.onNextClick(formDetails);
      setIsAddModalOpen(false);
    } catch (_error) {
      // do noithing
    }
  };

  const onSecondaryAddSubmit = async () => {
    await addFormConfig.onSecondaryButtonSubmit?.(formDetails);
    setIsAddModalOpen(false);
  };

  const onSecondaryEditSubmit = async () => {
    await editFormConfig.onSecondaryButtonSubmit?.(formDetails);
    setIsEditModalOpen(false);
    setSelectedRow(null);
  };

  const onDeleteSubmit = () => {
    deleteFormConfig.onNextClick(selectedRow);
    setDeleteModalOpen(false);
  };

  const onEditModalClose = () => {
    setIsEditModalOpen(false);
    setSelectedRow(null);
  };

  const tableColumns = columns
    ? columns
    : inputElements.map((element: { name: string; label: string }) => ({
        accessorKey: element.name,
        header: element.label,
      }));

  return (
    <React.Fragment>
      <Box>
        <ContentHeader
          title={tableHeaderTitle}
          subtitle={""}
          primaryAction={onAddButtonClick}
          buttonTitle={addFormConfig.addButtonText}
          allowAction={allowAdd}
        />
        <Box sx={{ margin: "20px 0px" }}>
          {isLoading ? (
            <Box sx={{ display: "flex", justifyContent: "center", alignItems: "center", height: "50vh" }}>
              <CircularProgress sx={{ width: "100%", margin: "auto" }} />
            </Box>
          ) : (
            <DataTable
              columns={tableColumns}
              data={defaultFormState}
              enableRowActions
              enableStickyHeader
              enableEditing
              positionActionsColumn="last"
              enableRowNumbers={enableRowNumbers}
              renderBottomToolbar={false}
              renderRowActions={({ row }) => {
                const { hideEdit = false, hideDelete = false, hideDownload = false } = row.original;
                const allowRowEdit = allowEdit && !hideEdit;
                const allowRowDelete = allowDelete && !hideDelete;
                const allowRowDownload = allowDownload && !hideDownload;
                return (
                  <>
                    {row.original?.isDefaultRole || row.original?.hideActions ? null : (
                      <Box width={150}>
                        {allowRowEdit && (
                          <IconButton size="small" onClick={() => onEditClicked(row)}>
                            <Edit />
                          </IconButton>
                        )}
                        {allowRowDelete && (
                          <IconButton size="small" onClick={() => onDeleteClick(row)}>
                            <Delete />
                          </IconButton>
                        )}
                        {allowRowDownload && (
                          <IconButton size="small" onClick={() => onDownloadClick(row)}>
                            <Download />
                          </IconButton>
                        )}
                      </Box>
                    )}
                  </>
                );
              }}
            />
          )}
        </Box>
        {isAddModalOpen && (
          <Modal
            title={addFormConfig.formTitle}
            subtitle={""}
            showBackButton
            isOpen={isAddModalOpen}
            onClose={() => setIsAddModalOpen(false)}
            PaperProps={{ sx: { borderRadius: "20px" } }}
            actions={
              <FormActions
                onCancelClick={onSecondaryAddSubmit}
                onSubmitClick={onAddSubmit}
                submitButtonText={addFormConfig.nextButtonText}
                disabled={!areFormDetailsValid}
                // disabled todo:dhruva- how to get this variable, ans useForm not there
              />
            }
            // fullWidth
          >
            <CommonForm
              selectOptions={selectOptions}
              // initialState={rowAdditionaInitialValues}
              // validators={formValidators}
              inputElements={inputElements}
              // rowAdditionaInitialValues={rowAdditionaInitialValues}
              onChange={setFormDetail}
              formErrors={formErrors}
              formValues={formDetails}
              customDayContent={addFormConfig?.customDayContent}
            />
          </Modal>
        )}
        {isEditModalOpen && selectedRow !== null && (
          <Modal
            title={editFormConfig.formTitle}
            subtitle={""}
            showBackButton
            isOpen={isEditModalOpen}
            onClose={onEditModalClose}
            PaperProps={{ sx: { borderRadius: "20px" } }}
            actions={
              <FormActions
                onCancelClick={onSecondaryEditSubmit}
                onSubmitClick={onEditSubmit}
                submitButtonText={editFormConfig.nextButtonText}
                disabled={!areFormDetailsValid}
              />
            }
            // fullWidth
          >
            <CommonForm
              selectOptions={selectOptions}
              // initialState={defaultFormState[selectedRow]}
              // validators={formValidators}
              inputElements={inputElements}
              onChange={setFormDetail}
              formValues={formDetails}
              formErrors={formErrors}
              disabledInputFields={disabledInputFields}
              readOnlyFields={readOnlyFields}
              customDayContent={editFormConfig?.customDayContent}
            />
          </Modal>
        )}
        {deleteModalOpen && selectedRow !== null && (
          <Modal
            title={deleteFormConfig.formTitle}
            subtitle={""}
            isOpen={deleteModalOpen}
            showBackButton
            onClose={() => setDeleteModalOpen(false)}
            fullWidth
            actions={
              <DialogActions>
                <Button variant="outlined" onClick={() => setDeleteModalOpen(false)}>
                  Cancel
                </Button>
                <Button variant="contained" onClick={onDeleteSubmit}>
                  {deleteFormConfig.nextButtonText}
                </Button>
              </DialogActions>
            }
          >
            <Typography>{deleteFormConfig.getQuestion?.(selectedRow) || defaultDeleteText}</Typography>
          </Modal>
        )}
      </Box>
    </React.Fragment>
  );
};
