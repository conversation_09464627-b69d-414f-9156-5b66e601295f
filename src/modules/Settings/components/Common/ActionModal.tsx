import { Box, Button, DialogActions } from "@mui/material";
import React, { useMemo } from "react";
import { UseFormReturnType, useForm } from "src/customHooks/useForm";
import CustomTextField from "src/modules/Common/FormInputs/CustomTextField";
import Modal from "src/modules/Common/Modal/Modal";
import validators from "src/utils/validators";

export type DefaultRoleState = {
  role: string;
};
interface ActionModalProps {
  isEdit: boolean;
  isOpen: boolean;
  onClose: () => void;
  modalData: DefaultRoleState;
  onCreate?: (data: string) => void;
  onUpdate: (data: string) => void;
}

const defaultRoleValidations = {
  role: [validators.shouldNotContainSpecialCharacters, validators.validateInput],
};

const ActionModal: React.FC<ActionModalProps> = ({
  isEdit = false,
  isOpen,
  onClose,
  modalData,
  onCreate,
  onUpdate,
}) => {
  const defaultState: DefaultRoleState = useMemo(
    () => ({
      role: isEdit ? modalData?.role : "",
    }),
    [],
  );
  const { formDetails, formErrors, handleChange }: UseFormReturnType<DefaultRoleState> = useForm({
    initialState: defaultState,
    validations: defaultRoleValidations,
    isBulk: false,
  });

  const typedFormDetails = formDetails as DefaultRoleState;
  const typedFormErrors = formErrors as DefaultRoleState;

  return (
    <Modal
      showBackButton
      isOpen={isOpen}
      title={isEdit ? "Edit Role" : "Add Role"}
      subtitle="Update your business persona"
      onClose={onClose}
      fullWidth
      actions={
        <DialogActions sx={{ margin: 2 }}>
          <Button
            disabled={
              !typedFormDetails.role || !!formErrors?.role || (isEdit && modalData?.role === typedFormDetails.role)
            }
            onClick={() => (isEdit ? onUpdate(typedFormDetails.role) : onCreate && onCreate(typedFormDetails.role))}
            variant="contained"
          >
            {!isEdit ? "Create" : "Update"}
          </Button>
        </DialogActions>
      }
    >
      <Box sx={{ width: "100%", padding: "0px 10px" }}>
        <CustomTextField
          title="Role"
          required
          id="role"
          value={typedFormDetails.role}
          placeholder="Enter role"
          fullWidth
          size="small"
          error={!!typedFormErrors.role}
          helperText={!!typedFormErrors.role && typedFormErrors.role}
          onChange={handleChange}
        />
      </Box>
    </Modal>
  );
};

export default ActionModal;
