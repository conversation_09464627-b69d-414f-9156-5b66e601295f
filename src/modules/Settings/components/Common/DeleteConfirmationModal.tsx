import { Box, Button, DialogActions, Typography } from "@mui/material";
import React, { ReactNode } from "react";
import Modal from "src/modules/Common/Modal/Modal";

interface DeleteConfirmationModalProps {
  onCancel: () => void;
  isModalOpen?: boolean;
  onDelete: () => void;
  selectedRole: string;
  title?: string;
  children?: ReactNode | null;
  isSaveDisabled?: boolean;
  suffix?: string;
  saveButtonTitle?: string;
}

const DeleteConfirmationModal: React.FC<DeleteConfirmationModalProps> = ({
  onCancel,
  isModalOpen = false,
  onDelete,
  selectedRole,
  title = "Are you sure you want to delete this role?",
  children = null,
  isSaveDisabled = false,
  suffix = "deleted",
  saveButtonTitle = "Delete",
}) => {
  return (
    <Modal
      title={`${selectedRole} will be ${suffix}`}
      subtitle=""
      fullWidth
      showBackButtons
      onClose={onCancel}
      isOpen={isModalOpen}
      actions={
        <DialogActions>
          <Box display="flex" gap={2} padding={2}>
            <Button onClick={onCancel} variant="outlined">
              Cancel
            </Button>
            <Button disabled={isSaveDisabled} onClick={onDelete} variant="contained">
              {saveButtonTitle}
            </Button>
          </Box>
        </DialogActions>
      }
    >
      <Typography>{title}</Typography>
      {children}
    </Modal>
  );
};

export default DeleteConfirmationModal;
