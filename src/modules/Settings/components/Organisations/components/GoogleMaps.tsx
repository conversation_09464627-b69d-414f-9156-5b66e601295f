import { <PERSON>complete, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "@mui/material";
import { AdvancedMarker, Map as GoogleMap, MapMouseEvent, Pin, useMapsLibrary } from "@vis.gl/react-google-maps";
import React from "react";
import { Circle } from "./Circle";

export type AutocompleteMode = { id: string; label: string };
const MapPlacesSearch = ({ setCoords, onChange }) => {
  const placesApi = useMapsLibrary("places");
  const geocodeApi = useMapsLibrary("geocoding");
  const [predictions, setPredictions] = React.useState<google.maps.places.AutocompleteResponse["predictions"]>([]);

  return (
    <div className="p-2 bg-white rounded-lg shadow-lg">
      <Autocomplete
        security="strict"
        size="small"
        filterOptions={(predictions) => predictions}
        options={predictions}
        onChange={(_e, value) => {
          if (!geocodeApi || !value) return;

          new geocodeApi.Geocoder().geocode({ placeId: value.place_id }).then((geocodeResp) => {
            if (geocodeResp.results.length > 0) {
              const { lat, lng } = geocodeResp.results[0].geometry.location;

              const newCoords = {
                lat: lat(),
                lng: lng(),
              };

              // Update coordinates for the map, pin, and circle
              setCoords(newCoords);

              if (onChange) {
                onChange(newCoords);
              }
            }
          });
        }}
        autoComplete
        getOptionLabel={(option: google.maps.places.AutocompletePrediction) => option?.description || ""}
        isOptionEqualToValue={(option, value) => option.place_id === value.place_id} // Ensure equality check works
        id="combo-box-demo"
        renderInput={(params) => (
          <TextField
            placeholder="Search for a location"
            {...params}
            onChange={(e) => {
              if (!placesApi) return;

              new placesApi.AutocompleteService()
                .getPlacePredictions({ input: e.target.value })
                .then((resp) => setPredictions(resp.predictions));
            }}
            aria-label="Search"
          />
        )}
      />
    </div>
  );
};

interface Props {
  onChange?: (e: MapMouseEvent["detail"]["latLng"]) => void;
  radius?: number;
  defaultCoords?: { lat: number; lng: number };
}
const GoogleMaps: React.FC<Props> = ({ onChange, radius = 100, defaultCoords }) => {
  const [coords, setCoords] = React.useState({ lat: 0, lng: 0 });

  React.useEffect(() => {
    if (defaultCoords) {
      setCoords(defaultCoords);
      return;
    }
    navigator.geolocation.getCurrentPosition((position) => {
      setCoords({ lat: position.coords.latitude, lng: position.coords.longitude });
    });
  }, []);

  const onPinDragEnd = (e: google.maps.MapMouseEvent) => {
    const newCoords = {
      lat: e.latLng?.lat() ?? 0,
      lng: e.latLng?.lng() ?? 0,
    };

    setCoords(newCoords); // Update both pin and circle coordinates
    if (onChange) {
      onChange(newCoords); // Notify parent if needed
    }
  };

  const onMapClick = (e: MapMouseEvent) => {
    const newCoords = {
      lat: e.detail?.latLng?.lat ?? 0,
      lng: e?.detail?.latLng?.lng ?? 0,
    };

    setCoords(newCoords); // Update both pin and circle coordinates
    if (onChange) {
      onChange(newCoords); // Notify parent if needed
    }
  };

  return (
    <>
      <MapPlacesSearch setCoords={setCoords} onChange={onChange} />
      <GoogleMap
        defaultZoom={17}
        center={coords} // Dynamically update the map center
        draggableCursor={"pointer"}
        draggingCursor={"grabbing"}
        onClick={onMapClick}
        streetViewControl={false}
        mapTypeControl={false}
        fullscreenControl={false}
        onCameraChanged={(ev) => console.log({ ev })}
        mapId="DEMO_MAP_ID"
        style={{
          width: "100%",
          height: "40vh",
        }}
      >
        <Circle radius={Number(radius)} center={coords} />
        <Tooltip title="Drag me or Click anywhere to pin">
          <AdvancedMarker draggable position={coords} onClick={onPinDragEnd} onDragEnd={onPinDragEnd}>
            <Pin background={"#FBBC04"} glyphColor={"#000"} borderColor={"#000"} />
          </AdvancedMarker>
        </Tooltip>
      </GoogleMap>
    </>
  );
};

export default GoogleMaps;
