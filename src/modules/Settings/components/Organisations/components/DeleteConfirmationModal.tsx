import { Box, Button, DialogActions, Typography } from "@mui/material";
import React from "react";
import Modal from "src/modules/Common/Modal/Modal";

interface DeleteConfirmationModalProps {
  onCancel: () => void;
  isModalOpen?: boolean;
  onDelete: () => void;
  selectedValue: string;
}

const DeleteConfirmationModal: React.FC<DeleteConfirmationModalProps> = ({
  onCancel,
  isModalOpen = false,
  onDelete,
  selectedValue,
}) => {
  return (
    <Modal
      title={`${selectedValue} will be deleted`}
      subtitle=""
      fullWidth
      showBackButton
      onClose={onCancel}
      isOpen={isModalOpen}
      actions={
        <DialogActions>
          <Box display="flex" gap={2} padding={2}>
            <Button onClick={onCancel} variant="outlined">
              Cancel
            </Button>
            <Button onClick={onDelete} variant="contained">
              Delete
            </Button>
          </Box>
        </DialogActions>
      }
    >
      <Typography>Are you sure you want to delete this organisation?</Typography>
    </Modal>
  );
};

export default DeleteConfirmationModal;
