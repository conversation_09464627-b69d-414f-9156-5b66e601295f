import { OrganisationDetails } from "src/services/api_definitions/organisations.service";
import { z } from "zod";

const organisationProfileForm = [
  {
    fieldProps: {
      name: "name",
    },
    formProps: {
      label: "Organisation Name",
      type: "text",
      required: true,
    },
    containerProps: {
      size: 4,
    },
  },
  {
    fieldProps: {
      name: "hr_admin_name",
    },
    formProps: {
      label: "Primary Contact Name",
      type: "text",
      required: true,
    },
    containerProps: {
      size: 4,
    },
  },
  {
    fieldProps: {
      name: "hr_admin_email",
    },
    formProps: {
      label: "Primary Contact Email",
      type: "email",
      required: true,
    },
    containerProps: {
      size: 4,
    },
  },
];

const enrichAddressWithLatitudeLongitude = (addresses: any) => {
  return addresses.map((address: any) => {
    const isGeofenceEnabled = address?.geofence_enabled;
    return {
      ...address,
      latitude: !isGeofenceEnabled ? null : Number(address?.latitude),
      longitude: !isGeofenceEnabled ? null : Number(address?.longitude),
      geofence_radius_meters: !isGeofenceEnabled ? null : Number(address?.geofence_radius_meters),
    };
  });
};

const branchAddressForm = (index: number, getDetailsfromZipcode: any) => [
  {
    fieldProps: {
      name: `addresses[${index}].address_line1`,
    },
    formProps: {
      label: "Address Line 1",
      type: "text",
      required: true,
    },
    containerProps: {
      size: 4,
    },
  },
  {
    fieldProps: {
      name: `addresses[${index}].address_line2`,
    },
    formProps: {
      label: "Address Line 2",
      type: "text",
    },
    containerProps: {
      size: 4,
    },
  },
  {
    fieldProps: {
      name: `addresses[${index}].zip_code`,
      listeners: {
        onChange: async (value: any) => {
          getDetailsfromZipcode(value?.value, index);
        },
      },
    },
    formProps: {
      label: "Zip code",
      type: "text",
      required: true,
    },
    containerProps: {
      size: 4,
    },
  },
  {
    fieldProps: {
      name: `addresses[${index}].city`,
    },
    formProps: {
      label: "City",
      type: "text",
      required: true,
      disabled: true,
    },
    containerProps: {
      size: 4,
    },
  },
  {
    fieldProps: {
      name: `addresses[${index}].state`,
    },
    formProps: {
      label: "State",
      type: "text",
      required: true,
      disabled: true,
    },
    containerProps: {
      size: 4,
    },
  },
  {
    fieldProps: {
      name: `addresses[${index}].country`,
    },
    formProps: {
      label: "Country",
      type: "text",
      required: true,
      disabled: true,
    },
    containerProps: {
      size: 4,
    },
  },
  {
    fieldProps: {
      name: `addresses[${index}].geofence_enabled`,
    },
    formProps: {
      label: "Geofencing",
      type: "switch",
    },
    containerProps: {
      size: 12,
    },
  },
];

const branchGeoFenceForm = (index: number) => [
  {
    fieldProps: {
      name: `addresses[${index}].latitude`,
    },
    formProps: {
      label: "Latitude",
      type: "number",
      required: true,
      disabled: true,
    },
    containerProps: {
      size: 4,
    },
  },
  {
    fieldProps: {
      name: `addresses[${index}].longitude`,
    },
    formProps: {
      label: "Longitude",
      type: "number",
      required: true,
      disabled: true,
    },
    containerProps: {
      size: 4,
    },
  },
  {
    fieldProps: {
      name: `addresses[${index}].geofence_radius_meters`,
    },
    formProps: {
      label: "Geofencing Radius",
      type: "number",
      required: true,
    },
    containerProps: {
      size: 4,
    },
  },
];

const getInitialValues = (selectedRow: OrganisationDetails | null) => {
  if (selectedRow) {
    const modifiedData = {
      ...selectedRow,
      tax_deductor: null,
      addresses: selectedRow?.addresses?.map((address: any) => {
        return {
          ...address,
        };
      }),
    };
    return modifiedData;
  }
  return {
    name: "",
    logo: "",
    hr_admin_email: "",
    hr_admin_name: "",
    addresses: [
      {
        address_line1: "",
        address_line2: "",
        city: "",
        zip_code: "",
        state: "",
        country: "",
        geofence_enabled: false,
        head_office: true,
        // latitude: null,
        // longitude: null,
        // geofence_radius_meters: null,
      },
    ],
  };
};

const enrichPhoneNumber = (phone: any, isEmployee: boolean) => {
  if (isEmployee) {
    return null;
  }
  return phone?.countryCode + phone?.phone;
};

const addressSchema = z
  .object({
    address_line1: z.string().nonempty({
      message: "Address Line 1 is required",
    }),
    city: z.string().nonempty({
      message: "City is required",
    }),
    zip_code: z.string().nonempty({
      message: "Zip Code is required",
    }),
    country: z.string().nonempty({
      message: "Country is required",
    }),
    state: z.string().nonempty({
      message: "State is required",
    }),
    geofence_enabled: z.boolean(),
    latitude: z.number().nullable().optional(),
    longitude: z.number().nullable().optional(),
    geofence_radius_meters: z.number().nullable().optional(),
  })
  .refine(
    (data) => {
      if (!data.geofence_enabled) return true;
      return (
        typeof data.latitude === "number" &&
        typeof data.longitude === "number" &&
        typeof data.geofence_radius_meters === "number"
      );
    },
    {
      message: "Latitude, Longitude, and Radius are required when geofence is enabled.",
      path: ["latitude"], // attaches error to `geofence_enabled`
    },
  );

const formSchema = z.object({
  name: z.string().nonempty({
    message: "Name is required",
  }),
  logo: z.string().nonempty({
    message: "Logo is required",
  }),
  hr_admin_email: z.string().nonempty({
    message: "Primary Contact Email is required",
  }),
  hr_admin_name: z.string().nonempty({
    message: "Primary Contact Name is required",
  }),
  addresses: z.array(addressSchema),
});

export {
  enrichPhoneNumber,
  getInitialValues,
  branchAddressForm,
  branchGeoFenceForm,
  enrichAddressWithLatitudeLongitude,
  organisationProfileForm,
  formSchema,
};
