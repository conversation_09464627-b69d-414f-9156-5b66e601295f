import { Box, Typography } from "@mui/material";
import React from "react";
import Modal from "src/modules/Common/Modal/Modal";
import DataTable from "src/modules/Common/Table/DataTable";
import { Address } from "src/services/api_definitions/organisations.service";

interface AddressModalProps {
  isModalOpen: boolean;
  onClose: () => void;
  addresses: Address[];
}

const AddressesModal: React.FC<AddressModalProps> = ({ isModalOpen, onClose, addresses }) => {
  return (
    <Modal title="Addresses" isOpen={isModalOpen} onClose={onClose} showBackButton>
      <DataTable
        data={addresses}
        layoutMode="grid-no-grow"
        columns={[
          {
            accessorKey: "address_line1",
            header: "Address",
            Cell: ({ row }) => {
              return (
                <Box>
                  {row?.original?.address_line1}, {row?.original?.address_line2}
                  {row?.original?.head_office && (
                    <Typography variant="caption" sx={{ fontSize: 14, fontWeight: 400, color: "#007F6F" }}>
                      {" "}
                      (Head Office)
                    </Typography>
                  )}
                </Box>
              );
            },
          },
          {
            accessorKey: "city",
            header: "City",
            size: 150,
          },
          {
            accessorKey: "state",
            header: "State",
            size: 150,
          },
          {
            accessorKey: "country",
            header: "Country",
            size: 150,
          },
          {
            accessorKey: "zip_code",
            header: "Zip Code",
            size: 150,
          },
        ]}
      />
    </Modal>
  );
};

export default AddressesModal;
