import { Box, Chip, Stack, Typography } from "@mui/material";
import React from "react";
import languageConfig from "src/configs/language/en.lang";
import { CustomInputLabel } from "src/modules/Common/FormInputs/CustomInputLabel";

interface WeekOffPropsType {
  readonly onClick: (event: { label: string; value: string }) => void;
  selectedDays: Array<string>;
  title: string;
  required: boolean;
  error: string;
  isDisabled?: boolean;
}

export const WeekOff: React.FC<WeekOffPropsType> = ({
  onClick,
  title,
  required,
  selectedDays,
  error,
  isDisabled = false,
}) => {
  const { attendanceConfig: attendanceConfigLang } = languageConfig.tenants.tenantSettings;
  return (
    <Box>
      <CustomInputLabel title={title} required={required} />
      <Stack direction="row" spacing={2}>
        {attendanceConfigLang.days.map((day) => (
          <Chip
            label={day.label}
            variant="outlined"
            key={day.label}
            disabled={isDisabled}
            clickable={true}
            onClick={() => onClick(day)}
            sx={{
              height: "25px",
              borderColor: selectedDays.includes(day.value) ? "#B6EACD" : "#F2F3F3",
              backgroundColor: selectedDays.includes(day.value) ? "#B6EACD" : "",
              color: selectedDays.includes(day.value) ? "#000000" : "#667085",
              "& .MuiChip-label": {
                display: "block",
                whiteSpace: "normal",
                padding: "3px 14px",
              },
            }}
          />
        ))}
      </Stack>
      {error && (
        <Typography variant="body2" sx={{ fontSize: "10px", color: "#FF4D4D", marginTop: "10px" }}>
          <Box component="span" sx={{ backgroundColor: "#FFDEDE", borderRadius: "50%", padding: "0 5px" }}>
            !
          </Box>{" "}
          {error}
        </Typography>
      )}
    </Box>
  );
};
