import React from "react";

import { CircularProgress } from "@mui/material";
import { useQuery } from "@tanstack/react-query";
import { BaseObject } from "src/app/global";
import languageConfig from "src/configs/language/en.lang";
import { FormActions } from "src/modules/Common/CRUDTableV2";
import { CommonFormWithState } from "src/modules/Employees/components/CommonForm";
import { FormInputType } from "src/modules/Employees/types/FormDataTypes";
import departmentService from "src/services/department.service";
import { getCurrentTenantId } from "src/utils/authUtils";
import validators from "src/utils/validators";

const { employeeConfig: employeeIdConfigLang } = languageConfig.tenants.tenantSettings;

export const EmployeeIDConfiguration = () => {
  const tenantId = getCurrentTenantId();

  const { data: idConfigDetails, isLoading: idConfigDetailsLoading } = useQuery(
    ["get-employee-id-config-details"],
    async () => departmentService.getEmployeeIDConfig(),
    {
      enabled: !!tenantId,
      retryOnMount: false,
      refetchOnWindowFocus: false,
    },
  );

  const onSubmit = async (formDetails: any) => {
    const payload = {
      prefix: formDetails.prefix,
      sequence: formDetails.sequence,
      length: formDetails.idLength,
    };
    await departmentService.updateEmployeeIDConfig(payload);
  };

  const deafaultResponse: BaseObject = {
    prefix: idConfigDetails?.prefix || "",
    sequence: idConfigDetails?.sequence || "",
    idLength: idConfigDetails?.length || "",
  };

  const defaultFormState: BaseObject = deafaultResponse;

  const formValidators = {
    prefix: [validators.prefixShouldBeLessThenEmployeeIdConfigLength],
    sequence: [validators.validateInput, validators.sequenceShouldBeLessThenEmployeeIdConfigLength],
    idLength: [validators.shouldBeOfMinLength],
  };

  const inputElements: FormInputType[] = [
    {
      name: "prefix",
      label: employeeIdConfigLang.prefixTitle,
      variant: "text",
      placeholder: employeeIdConfigLang.prefixTitle,
      isRequired: false,
    },
    {
      name: "sequence",
      label: employeeIdConfigLang.nextEmployeeNumber,
      variant: "text",
      placeholder: employeeIdConfigLang.nextEmployeeNumber,
      isRequired: true,
    },
    {
      name: "idLength",
      label: employeeIdConfigLang.lengthTitle,
      variant: "text",
      placeholder: employeeIdConfigLang.lengthTitle,
      isRequired: true,
    },
  ];

  if (idConfigDetailsLoading) return <CircularProgress />;
  return (
    <CommonFormWithState
      initialState={defaultFormState}
      validators={formValidators as any}
      inputElements={inputElements}
      gridStyles={{ rowSpacing: 4 }}
      onFormSubmit={onSubmit}
      ActionComponent={(props) => (
        <FormActions
          hideCancelButton
          onSubmitClick={props.onSubmit}
          onCancelClick={() => {}}
          submitButtonText="Save"
          disabled={!props.areFormDetailsValid}
        />
      )}
    />
  );
};
