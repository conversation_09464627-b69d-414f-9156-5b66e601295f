import { Add } from "@mui/icons-material";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>rid2, SelectChange<PERSON><PERSON>, Typography } from "@mui/material";
import { CircularProgress } from "@mui/material";
import { useMutation, useQuery } from "@tanstack/react-query";
import React, { useMemo } from "react";
import { NUMBER } from "src/app/constants";
import languageConfig from "src/configs/language/en.lang";
import { useForm } from "src/customHooks/useForm";
import { addButtonStyle } from "src/modules/Employees/components/CommonForm";
import departmentService from "src/services/department.service";
import { isAllCustomEmpty } from "src/utils/dataUtils";
import validators from "src/utils/validators";
import AttendanceConfigListItem from "./AttendanceConfigListItem";

export type LeaveTypeDefaultState = {
  numberOfWorkingDays: string;
  workStartTime: string;
  workEndTime: string;
  minHoursHalfDay: string | null;
  minHoursFullDay: string | null;
  enforceWorkingHours: boolean;
  weekOff: string[];
  enforcementType: string;
};

export type CustomLeaveTypeDefaultState = LeaveTypeDefaultState & {
  business: string;
  departments: string[];
};

const saveButtonStyle = {
  position: "fixed",
  bottom: "40px",
  right: "70px",
  fontFamily: "Poppins",
  fontSize: "16px",
  fontWeight: 500,
  textTransform: "none",
  margin: 0,
  width: "172px",
  height: "48px",
  flexGrow: 0,
  display: "flex",
  flexDirection: "row",
  justifyContent: "center",
  alignItems: "center",
  gap: "8px",
  padding: "0 20px",
  borderRadius: "50px",
  backgroundColor: "#007f6f",
  marginLeft: "auto",
};

const { attendanceConfig: attendanceConfigLang } = languageConfig.tenants.tenantSettings;

const defaultFormState: LeaveTypeDefaultState = {
  numberOfWorkingDays: "",
  workStartTime: "",
  workEndTime: "",
  minHoursHalfDay: null,
  minHoursFullDay: null,
  enforceWorkingHours: false,
  weekOff: [],
  enforcementType: attendanceConfigLang.enforceWorking.enforceLop.value,
};

const defaultCustomFormState: CustomLeaveTypeDefaultState = {
  numberOfWorkingDays: "",
  workStartTime: "",
  workEndTime: "",
  minHoursHalfDay: null,
  minHoursFullDay: null,
  enforceWorkingHours: false,
  weekOff: [],
  enforcementType: attendanceConfigLang.enforceWorking.enforceLop.value,
  business: "",
  departments: [],
};

export const AttendanceConfiguration: React.FC = () => {
  const {
    data: attendanceConfigDetails,
    isLoading: isLoadingConfig,
    refetch: refetchAttendanceConfig,
  } = useQuery(["get-all-attendance-config"], async () => await departmentService.getAllAttendanceConfigs(), {
    retryOnMount: false,
    refetchInterval: false,
    refetchOnWindowFocus: false,
  });

  const defaultConfigResponse = useMemo(() => {
    return {
      numberOfWorkingDays: attendanceConfigDetails?.default_config.num_working_days,
      workStartTime: attendanceConfigDetails?.default_config?.work_start_time,
      workEndTime: attendanceConfigDetails?.default_config?.work_end_time,
      weekOff: attendanceConfigDetails?.default_config?.week_offs,
      minHoursHalfDay: attendanceConfigDetails?.default_config?.min_hours_half_day,
      minHoursFullDay: attendanceConfigDetails?.default_config?.min_hours_full_day,
      enforceWorkingHours: attendanceConfigDetails?.default_config?.enforce_working_hours,
      enforcementType:
        attendanceConfigDetails?.default_config?.working_hours_lapse_enforcement ||
        attendanceConfigLang.enforceWorking.enforceLop.value,
    };
  }, [attendanceConfigDetails]);

  const customConfigResponse = useMemo(() => {
    return (
      attendanceConfigDetails?.custom_configs?.map((customConfig) => ({
        numberOfWorkingDays: customConfig?.num_working_days,
        workStartTime: customConfig?.work_start_time,
        workEndTime: customConfig?.work_end_time,
        weekOff: customConfig?.week_offs || [],
        minHoursHalfDay: customConfig?.min_hours_half_day,
        minHoursFullDay: customConfig?.min_hours_full_day,
        enforceWorkingHours: customConfig?.enforce_working_hours,
        business: customConfig?.business_unit_name,
        departments: customConfig?.department_names,
        enforcementType:
          customConfig?.working_hours_lapse_enforcement || attendanceConfigLang.enforceWorking.enforceLop.value,
      })) || []
    );
  }, [attendanceConfigDetails]);

  const validation = {
    weekOff: [validators.validateWeekOff],
    numberOfWorkingDays: [validators.validateWorkingDays],
    workStartTime: [validators.validateTimeOnly],
    workEndTime: [validators.validateTimeOnly],
    minHoursHalfDay: defaultConfigResponse.enforceWorkingHours ? [validators.validateMinHalfDayTime] : [],
    minHoursFullDay: defaultConfigResponse.enforceWorkingHours ? [validators.validateMinFullDayTime] : [],
    enforceWorkingHours: [],
    enforcementType: [],
  };

  const customConfigValidation = {
    weekOff: [validators.validateWeekOff],
    numberOfWorkingDays: [validators.validateWorkingDays],
    workStartTime: [validators.validateTimeOnly],
    workEndTime: [validators.validateTimeOnly],
    minHoursHalfDay: [],
    minHoursFullDay: [],
    enforceWorkingHours: [],
    enforcementType: [],
    business: [],
    departments: [],
  };

  const { formDetails, formErrors, handleChange, setFormDetail } = useForm({
    initialState: defaultConfigResponse.numberOfWorkingDays ? defaultConfigResponse : defaultFormState,
    isBulk: false,
    validations: validation,
  });

  const {
    formDetails: customConfigFormDetails,
    formErrors: customConfigFormErrors,
    handleChange: onCustomConfigChange,
    setFormDetail: setCustomConfigFormDetail,
    addNewFormDetailRow,
    deleteFormDetails,
    handleSelectChange,
  } = useForm({
    initialState: customConfigResponse,
    isBulk: true,
    validations: customConfigValidation,
  });

  const typedFormDetails = formDetails as unknown as LeaveTypeDefaultState;
  const typedFormErrors = formErrors as Record<keyof LeaveTypeDefaultState, string>;

  const createPayload = () => {
    return {
      default_config: {
        num_working_days: typedFormDetails.numberOfWorkingDays,
        work_start_time: typedFormDetails.workStartTime,
        work_end_time: typedFormDetails.workEndTime,
        week_offs: typedFormDetails.weekOff,
        enforce_working_hours: typedFormDetails.enforceWorkingHours,
        min_hours_half_day: typedFormDetails.minHoursHalfDay || null,
        min_hours_full_day: typedFormDetails.minHoursFullDay || null,
        working_hours_lapse_enforcement: typedFormDetails.enforcementType,
      },
      custom_configs: ((customConfigFormDetails as unknown as CustomLeaveTypeDefaultState[]) || [])?.map(
        (configDetail) => ({
          num_working_days: configDetail.numberOfWorkingDays,
          work_start_time: configDetail.workStartTime,
          work_end_time: configDetail.workEndTime,
          week_offs: configDetail.weekOff,
          enforce_working_hours: configDetail.enforceWorkingHours,
          min_hours_half_day: configDetail.minHoursHalfDay || null,
          min_hours_full_day: configDetail.minHoursFullDay || null,
          working_hours_lapse_enforcement: configDetail.enforcementType,
          business_unit_name: configDetail.business,
          department_names: configDetail?.departments,
        }),
      ),
    };
  };

  const createLeaveTypeMutation = useMutation({
    mutationKey: ["create-attendance-config"],
    mutationFn: async () => {
      await departmentService.createAttendanceConfig(createPayload());
      refetchAttendanceConfig();
    },
  });

  const updateLeaveTypeMutation = useMutation({
    mutationKey: ["update-attendance-config"],
    mutationFn: async () => {
      await departmentService.updateAttendanceConfig(createPayload());
      refetchAttendanceConfig();
    },
  });

  const onSaveClickHandler = async () => {
    if (defaultConfigResponse.numberOfWorkingDays) {
      updateLeaveTypeMutation.mutate();
      return;
    }
    createLeaveTypeMutation.mutate();
  };

  const hasErrorInFormFelds = (formDetail: Record<string, any>, isCustom = false) => {
    const newFormDetails = { ...formDetail };

    // Default checks for basic form details
    const numberOfWorkingDays = Number(newFormDetails.numberOfWorkingDays || 0);
    const weekOff = newFormDetails.weekOff || []; // Default to an empty array if undefined

    const defaultChecks =
      !numberOfWorkingDays ||
      !newFormDetails.workStartTime ||
      !newFormDetails.workEndTime ||
      weekOff.length + numberOfWorkingDays !== NUMBER.SEVEN;

    if (defaultChecks) {
      return defaultChecks;
    }

    if (newFormDetails.enforceWorkingHours) {
      return !newFormDetails.minHoursFullDay || !newFormDetails.minHoursHalfDay;
    }

    if (!isCustom) {
      return defaultChecks;
    }

    return (
      defaultChecks ||
      !newFormDetails.business ||
      !newFormDetails?.departments ||
      newFormDetails?.departments?.length === 0
    );
  };

  const hasDefaultConfigErrors = useMemo(() => hasErrorInFormFelds(typedFormDetails), [typedFormDetails]);
  const hasCustomConfigErrors = useMemo(
    () => (customConfigFormDetails as any[])?.some((formDetail) => hasErrorInFormFelds(formDetail, true)),
    [customConfigFormDetails],
  );
  const onMultiSelectInputChange = (event: SelectChangeEvent, name: string, index: number) => {
    const {
      target: { value },
    } = event;
    const valueArray = typeof value === "string" ? value.split(",") : value;
    setCustomConfigFormDetail(name, valueArray, index);
  };

  const onAddMoreClick = () => {
    // biome-ignore lint/suspicious/noExplicitAny: <explanation>
    addNewFormDetailRow([{ ...(defaultCustomFormState as any) }]);
  };

  if (isLoadingConfig) return <CircularProgress />;
  return (
    <>
      <Typography fontSize={24} fontWeight={500}>
        Default Config
      </Typography>
      <Grid2 container spacing={3}>
        <AttendanceConfigListItem
          typedFormDetails={typedFormDetails}
          typedFormErrors={typedFormErrors}
          setFormDetail={setFormDetail}
          handleChange={handleChange}
        />
      </Grid2>
      <Divider />
      <Typography fontSize={24} fontWeight={500}>
        Custom Config
      </Typography>
      {(customConfigFormDetails as any[])?.length === 0 && (
        <Grid2 alignSelf="baseline">
          <Button variant="text" sx={addButtonStyle} onClick={onAddMoreClick} startIcon={<Add fontSize="small" />}>
            Add more
          </Button>
        </Grid2>
      )}
      {(customConfigFormDetails as any[]).map((eachformDetails, index) => (
        <>
          <Grid2 key={index} container spacing={3}>
            <AttendanceConfigListItem
              key={index}
              typedFormDetails={eachformDetails}
              typedFormErrors={customConfigFormErrors[index as keyof typeof customConfigFormErrors]}
              setFormDetail={(name, value) => setCustomConfigFormDetail(name, value, index)}
              handleChange={(ev) => onCustomConfigChange(ev, index)}
              onDeleteRow={() => deleteFormDetails(index)}
              handleSelectChange={(ev, name) => handleSelectChange(ev, name as any, index)}
              handleMultiSelectChange={(ev, name) => onMultiSelectInputChange(ev, name, index)}
              isCustom
            />
          </Grid2>
          <Divider key={index} />
          {index === (customConfigFormDetails as any[])?.length - 1 && (
            <Grid2 alignSelf="baseline">
              <Button variant="text" sx={addButtonStyle} onClick={onAddMoreClick} startIcon={<Add fontSize="small" />}>
                Add more
              </Button>
            </Grid2>
          )}
        </>
      ))}
      <Grid2 size={12}>
        <Button
          disabled={hasDefaultConfigErrors || hasCustomConfigErrors || !isAllCustomEmpty(typedFormErrors)}
          size="large"
          variant="contained"
          sx={saveButtonStyle}
          onClick={onSaveClickHandler}
        >
          {attendanceConfigLang.save}
        </Button>
      </Grid2>
    </>
  );
};
