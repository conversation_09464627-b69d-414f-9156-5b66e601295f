import { Bedtime, Delete, WbSunny } from "@mui/icons-material";
import {
  Box,
  FormControl,
  FormControlLabel,
  Grid2,
  IconButton,
  InputLabel,
  Radio,
  RadioGroup,
  Switch,
  Tooltip,
} from "@mui/material";
import { useQuery } from "@tanstack/react-query";
import { format, parse } from "date-fns";
import React, { useMemo } from "react";
import { NUMBER } from "src/app/constants";
import languageConfig from "src/configs/language/en.lang";
import CustomMultiSelect from "src/modules/Common/FormInputs/CustomMultiSelect";
import CustomSelect from "src/modules/Common/FormInputs/CustomSelect";
import CustomTextField from "src/modules/Common/FormInputs/CustomTextField";
import CustomTimeField from "src/modules/Common/FormInputs/CustomTimeField";
import businessunitsService from "src/services/businessunits.service";
import departmentService from "src/services/department.service";
import { getMinutesDifference, getTimeDifference } from "src/utils/dateUtils";
import { INPUT_FIELDS_NAME } from "../constants";
import { CustomLeaveTypeDefaultState, LeaveTypeDefaultState } from "./AttendanceConfiguration";
import { WeekOff } from "./WeekOff";

type AttendanceDisabledStates = {
  numberOfWorkingDays?: boolean;
  workStartTime?: boolean;
  workEndTime?: boolean;
  minHoursHalfDay?: boolean;
  minHoursFullDay?: boolean;
  enforceWorkingHours?: boolean;
  enforcementType?: boolean;
};

interface AttendanceConfigListItemProps {
  typedFormDetails: LeaveTypeDefaultState | CustomLeaveTypeDefaultState;
  setFormDetail: (name: string, value: unknown) => void;
  // biome-ignore lint/suspicious/noExplicitAny: <explanation>
  handleChange: (changeEvent: any) => void;
  handleSelectChange?: (changeEvent: any, name: string) => void;
  handleMultiSelectChange?: (changeEvent: any, name: string) => void;
  typedFormErrors: Record<keyof LeaveTypeDefaultState, string>;
  onDeleteRow?: () => void;
  isCustom?: boolean;
  disabledStates?: AttendanceDisabledStates;
}

const inputLabelStyle = {
  fontSize: "16px",
  color: "#000",
};

const radioInputStyles = {
  "&  .MuiFormControlLabel-label": {
    fontSize: "14px",
  },
  "& .MuiButtonBase-root": {
    padding: "3px 10px",
  },
};

const AttendanceConfigListItem: React.FC<AttendanceConfigListItemProps> = ({
  typedFormDetails,
  setFormDetail,
  handleChange,
  typedFormErrors,
  onDeleteRow,
  handleSelectChange,
  handleMultiSelectChange,
  isCustom = false,
  disabledStates = {
    minHoursFullDay: false,
    minHoursHalfDay: false,
    numberOfWorkingDays: false,
    workEndTime: false,
    workStartTime: false,
  },
}) => {
  const { attendanceConfig: attendanceConfigLang } = languageConfig.tenants.tenantSettings;

  const { data: businessUnits = [], isLoading: businessUnitsLoading } = useQuery(
    ["get-business-unit-details"],
    async () => businessunitsService.getBusinessUnitDetails(),
    {
      retryOnMount: false,
      refetchOnWindowFocus: false,
      enabled: isCustom,
    },
  );

  const { data: allDepartments = [], isLoading: allDepartmentsLoading } = useQuery(
    ["get-all-business-departments"],
    async () => departmentService.getAllDepartments(),
    {
      retryOnMount: false,
      refetchOnWindowFocus: false,
      enabled: isCustom,
    },
  );

  const keyMap = {
    [INPUT_FIELDS_NAME.WORK_STAR_TIME]: INPUT_FIELDS_NAME.WORK_END_TIME,
    [INPUT_FIELDS_NAME.MIN_HOURS_HALF_DAY]: INPUT_FIELDS_NAME.MIN_HOURS_FULL_DAY,
  };

  const diffTime = useMemo(
    () => getTimeDifference(typedFormDetails?.workStartTime, typedFormDetails?.workEndTime),
    [typedFormDetails?.workEndTime, typedFormDetails?.workStartTime],
  );

  const onChangeTimeHandler = (date: Date, fieldName: string) => {
    if (date && date.toString() !== "Invalid Date") {
      const keyName =
        fieldName === INPUT_FIELDS_NAME.WORK_STAR_TIME
          ? INPUT_FIELDS_NAME.WORK_END_TIME
          : INPUT_FIELDS_NAME.MIN_HOURS_FULL_DAY;
      const fieldValue: any = typedFormDetails[keyName as keyof typeof typedFormDetails];
      if (
        [INPUT_FIELDS_NAME.WORK_STAR_TIME, INPUT_FIELDS_NAME.MIN_HOURS_HALF_DAY].includes(fieldName) &&
        getMinutesDifference(fieldValue, format(date, "HH:mm")) < 0
      ) {
        if (fieldName === INPUT_FIELDS_NAME.WORK_STAR_TIME) {
          setFormDetail(INPUT_FIELDS_NAME.MIN_HOURS_HALF_DAY, "");
          setFormDetail(INPUT_FIELDS_NAME.MIN_HOURS_FULL_DAY, "");
        }
        setFormDetail(keyMap[fieldName], "");
      }
      if (fieldName === INPUT_FIELDS_NAME.WORK_END_TIME) {
        setFormDetail(INPUT_FIELDS_NAME.MIN_HOURS_HALF_DAY, "");
        setFormDetail(INPUT_FIELDS_NAME.MIN_HOURS_FULL_DAY, "");
      }
      setFormDetail(fieldName, format(date, "HH:mm"));
      return;
    }
    setFormDetail(fieldName, "");
  };

  const numberOfWorkingDaysHandler = (event: React.ChangeEvent<HTMLInputElement>) => {
    handleChange(event);
    setFormDetail("weekOff", []);
  };

  const weeOffkDaysHandler = (event: { value: string }) => {
    const selectedDay = event.value;
    const { weekOff, numberOfWorkingDays } = typedFormDetails;
    const weekOffList = [...weekOff];
    if (!weekOffList.includes(selectedDay)) {
      if (+numberOfWorkingDays + weekOffList.length >= NUMBER.SEVEN) {
        weekOffList.pop();
      }
      weekOffList.push(selectedDay);
    } else {
      weekOffList.splice(weekOffList.indexOf(selectedDay), 1);
    }
    setFormDetail("weekOff", weekOffList);
  };

  const getTime = (time?: string | null) => {
    return time ? parse(time, "HH:mm", 1) : undefined;
  };

  const onChangeEnforceWorking = () => {
    setFormDetail(INPUT_FIELDS_NAME.ENFORCE_WORKING, !typedFormDetails?.enforceWorkingHours);
    setFormDetail(INPUT_FIELDS_NAME.MIN_HOURS_HALF_DAY, "");
    setFormDetail(INPUT_FIELDS_NAME.MIN_HOURS_FULL_DAY, "");
  };

  const onChangeHanlder = (event: React.ChangeEvent<HTMLInputElement>) => {
    setFormDetail("enforcementType", event.target.value);
  };

  const onBusinessUnitChange = (event: any) => {
    handleSelectChange?.(event, INPUT_FIELDS_NAME.BUSINESS_UNIT);
    setFormDetail(INPUT_FIELDS_NAME.DEPARTMENT, []);
  };

  const isNightShift = getMinutesDifference(typedFormDetails?.workStartTime, typedFormDetails?.workEndTime) > 0;

  const getEndAdornmentForInputs = () => {
    if (!typedFormDetails?.workStartTime || !typedFormDetails?.workEndTime) {
      return null;
    }

    if (isNightShift) {
      return (
        <Tooltip title="+1 day (Night Shift)">
          <Bedtime color="secondary" />
        </Tooltip>
      );
    }
    return (
      <Tooltip title="Day Shift">
        <WbSunny color="warning" />
      </Tooltip>
    );
  };

  const getHelperText = () => {
    return isNightShift ? "+1 day (Night Shift)" : "";
  };

  return (
    <>
      {isCustom && (
        <>
          <Grid2 size={6}>
            <CustomSelect
              sx={{
                width: "100%",
              }}
              size="small"
              required={true}
              name={INPUT_FIELDS_NAME.BUSINESS_UNIT}
              label={attendanceConfigLang.businessUnit.label}
              value={(typedFormDetails as CustomLeaveTypeDefaultState)?.business as string}
              placeholder="Select business unit"
              isLoading={businessUnitsLoading}
              disabled={businessUnitsLoading}
              options={(businessUnits as any[])?.map((businessUnit) => ({
                value: businessUnit.name as string,
                label: businessUnit.name as string,
              }))}
              onChange={(event: any) => onBusinessUnitChange(event)}
            />
          </Grid2>
          <Grid2 size={6}>
            <CustomMultiSelect
              sx={{
                width: "100%",
              }}
              size="small"
              required={true}
              name={INPUT_FIELDS_NAME.DEPARTMENT}
              isLoading={allDepartmentsLoading}
              placeholder="Select multiple departments"
              disabled={!(typedFormDetails as CustomLeaveTypeDefaultState)?.business}
              multiple
              label={attendanceConfigLang.department.label}
              options={((allDepartments as any[]) || [])
                ?.filter(
                  (eachDepartment) =>
                    eachDepartment.business_unit === (typedFormDetails as CustomLeaveTypeDefaultState)?.business,
                )
                ?.map((businessUnit) => ({
                  value: businessUnit.name as string,
                  label: businessUnit.name as string,
                }))}
              value={(typedFormDetails as CustomLeaveTypeDefaultState)?.departments}
              onChange={(event: any) => handleMultiSelectChange?.(event, INPUT_FIELDS_NAME.DEPARTMENT)}
            />
          </Grid2>
        </>
      )}
      <Grid2 size={4}>
        <CustomTextField
          title={attendanceConfigLang.numberOfWorkingDays.label}
          id={INPUT_FIELDS_NAME.WORKING_DAYS}
          size="small"
          fullWidth
          disabled={disabledStates.numberOfWorkingDays}
          value={typedFormDetails?.numberOfWorkingDays}
          onChange={numberOfWorkingDaysHandler}
          placeholder={attendanceConfigLang.numberOfWorkingDays.placeholder}
          required={true}
          error={!!typedFormErrors?.numberOfWorkingDays}
          helperText={!!typedFormErrors?.numberOfWorkingDays && typedFormErrors?.numberOfWorkingDays}
        />
      </Grid2>
      <Grid2 size={4}>
        <CustomTimeField
          required={true}
          name={INPUT_FIELDS_NAME.WORK_STAR_TIME}
          timeSteps={{ minutes: 1 }}
          title={attendanceConfigLang.workStartTime.label}
          disabled={disabledStates.workStartTime}
          // slotProps={{
          //   textField: {
          //     helperText: typedFormErrors?.workStartTime,
          //   },
          // }}
          value={getTime(typedFormDetails.workStartTime)}
          onChange={(event: any) => onChangeTimeHandler(event, INPUT_FIELDS_NAME.WORK_STAR_TIME)}
        />
      </Grid2>
      <Grid2 size={4}>
        <CustomTimeField
          required={true}
          name={INPUT_FIELDS_NAME.WORK_END_TIME}
          timeSteps={{ minutes: 1 }}
          disabled={!getTime(typedFormDetails.workStartTime) || disabledStates.workEndTime}
          // minTime={getTime(typedFormDetails.workStartTime)}
          title={attendanceConfigLang.workEndTime.label}
          value={getTime(typedFormDetails?.workEndTime)}
          slotProps={{
            textField: {
              helperText: typedFormErrors?.workEndTime || getHelperText(),
              InputProps: {
                startAdornment: getEndAdornmentForInputs(),
              },
            },
          }}
          onChange={(event: any) => onChangeTimeHandler(event, INPUT_FIELDS_NAME.WORK_END_TIME)}
        />
      </Grid2>
      <Grid2 size={12}>
        <WeekOff
          title={attendanceConfigLang.weekOffLabel}
          required={true}
          selectedDays={typedFormDetails?.weekOff}
          onClick={weeOffkDaysHandler}
          error={typedFormErrors?.weekOff}
          isDisabled={disabledStates.numberOfWorkingDays}
        />
      </Grid2>
      <Grid2 size={12}>
        <Box display="flex" justifyContent="space-between" alignItems="center">
          <Box display="flex" alignItems="center" width="100%">
            <InputLabel sx={inputLabelStyle}>{attendanceConfigLang.enforceWorking.title}</InputLabel>
            <Switch
              id={INPUT_FIELDS_NAME.ENFORCE_WORKING}
              value={typedFormDetails?.enforceWorkingHours}
              checked={typedFormDetails?.enforceWorkingHours}
              onChange={onChangeEnforceWorking}
              disabled={disabledStates.enforceWorkingHours}
            />
          </Box>
          {onDeleteRow && (
            <Box>
              <IconButton color="error" onClick={onDeleteRow}>
                <Delete />
              </IconButton>
            </Box>
          )}
        </Box>
      </Grid2>
      {typedFormDetails.enforceWorkingHours && (
        <>
          <Grid2 size={4}>
            <CustomTimeField
              required={true}
              name={INPUT_FIELDS_NAME.MIN_HOURS_HALF_DAY}
              timeSteps={{ minutes: 1 }}
              title={attendanceConfigLang.minHoursHalfDay.label}
              maxTime={getTime(diffTime)}
              disabled={!getTime(typedFormDetails.workEndTime) || disabledStates.minHoursHalfDay}
              slotProps={{
                textField: {
                  helperText: typedFormErrors?.minHoursHalfDay,
                },
              }}
              value={getTime(typedFormDetails?.minHoursHalfDay)}
              onChange={(event: any) => onChangeTimeHandler(event, INPUT_FIELDS_NAME.MIN_HOURS_HALF_DAY)}
            />
          </Grid2>
          <Grid2 size={4}>
            <CustomTimeField
              required={true}
              name={INPUT_FIELDS_NAME.MIN_HOURS_FULL_DAY}
              timeSteps={{ minutes: 1 }}
              minTime={getTime(typedFormDetails?.minHoursHalfDay)}
              maxTime={getTime(diffTime)}
              disabled={!getTime(typedFormDetails?.minHoursHalfDay) || disabledStates.minHoursFullDay}
              title={attendanceConfigLang.minHoursFullDay.label}
              value={getTime(typedFormDetails?.minHoursFullDay)}
              slotProps={{
                textField: {
                  helperText: typedFormErrors?.minHoursFullDay,
                },
              }}
              onChange={(event: Date | any) => onChangeTimeHandler(event, INPUT_FIELDS_NAME.MIN_HOURS_FULL_DAY)}
            />
          </Grid2>
          <Grid2 size={12}>
            <Box display="flex" alignItems="center">
              <InputLabel sx={{ ...inputLabelStyle, fontSize: "14px" }}>
                {attendanceConfigLang.enforceWorking.subTitle}
              </InputLabel>
            </Box>
            <Box display="flex" alignItems="center">
              <FormControl disabled={disabledStates.enforcementType}>
                <RadioGroup
                  aria-labelledby="demo-radio-buttons-group-label"
                  value={typedFormDetails.enforcementType}
                  name="radio-buttons-group"
                  onChange={onChangeHanlder}
                  sx={{ marginTop: "5px" }}
                >
                  <FormControlLabel
                    sx={radioInputStyles}
                    value={attendanceConfigLang.enforceWorking.enforceLop.value}
                    control={<Radio />}
                    label={attendanceConfigLang.enforceWorking.enforceLop.title}
                  />
                  <FormControlLabel
                    sx={radioInputStyles}
                    value={attendanceConfigLang.enforceWorking.normalization.value}
                    control={<Radio />}
                    label={attendanceConfigLang.enforceWorking.normalization.title}
                  />
                </RadioGroup>
              </FormControl>
            </Box>
          </Grid2>
        </>
      )}
    </>
  );
};

export default AttendanceConfigListItem;
