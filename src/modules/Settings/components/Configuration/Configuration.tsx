import { Box, Paper, Tab, Tabs } from "@mui/material";
import React, { useMemo } from "react";
import { useAppSelector } from "src/customHooks/useAppSelector";
import useSubroutes from "src/customHooks/useSubroutes";
import ContentHeader from "src/modules/Common/ContentHeader/ContentHeader";
import NoAccessScreen from "src/modules/Common/NoAccess/NoAccess";
import { PATH_CONFIG } from "src/modules/Routing/config";
import { AttendanceConfiguration } from "src/modules/Settings/components/Configuration/components/AttendanceConfiguration";
import { EmployeeIDConfiguration } from "src/modules/Settings/components/Configuration/components/EmployeeIDConfiguration";

const configManagementTabs = [
  {
    key: PATH_CONFIG.EMPLOYEE_ID_CONFIGURATION.key,
    label: "Employee",
    header: "Configuration",
    component: <EmployeeIDConfiguration />,
    id: 0,
  },
  {
    key: PATH_CONFIG.ATTENDANCE_CONFIGURATION.key,
    label: "Attendance",
    header: "Configuration",
    component: <AttendanceConfiguration />,
    id: 1,
  },
];

const Configuration = () => {
  const { isFullView } = useAppSelector((state) => state.app);
  const subRoutes = useSubroutes(PATH_CONFIG.CONFIGURATION.key);
  const tabsToShow = useMemo(
    () =>
      configManagementTabs.filter((settings) => {
        return subRoutes.some((route) => route.key === settings.key && route.acl?.canRead);
      }),
    [subRoutes],
  );

  const [tabId, setTabId] = React.useState(tabsToShow?.[0]?.id);
  const selectedTab = useMemo(() => tabsToShow?.[tabId], [tabId]);

  const handleChange = (_event: React.SyntheticEvent, newValue: number) => {
    setTabId(newValue);
  };

  if (!tabsToShow || tabsToShow?.length === 0) {
    return (
      <Box>
        <NoAccessScreen />
      </Box>
    );
  }

  return (
    <Box display="flex" flexDirection="column" gap={3} padding="0px 16px">
      {!isFullView && <ContentHeader title={selectedTab?.header} />}
      {!isFullView && (
        <Tabs component={Paper} value={tabId} onChange={handleChange}>
          {tabsToShow?.map((tab) => (
            <Tab
              sx={{
                textTransform: "none",
              }}
              label={tab?.label}
              tabIndex={tab?.id}
              key={tab?.id}
            />
          ))}
        </Tabs>
      )}
      <Box display="flex" flexDirection="column" gap={2}>
        {selectedTab?.component && selectedTab?.component}
      </Box>
    </Box>
  );
};

export default Configuration;
