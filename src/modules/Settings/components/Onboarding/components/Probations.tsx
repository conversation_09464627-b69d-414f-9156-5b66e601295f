import { <PERSON>, But<PERSON>, Divider } from "@mui/material";
import { useMutation, useQuery } from "@tanstack/react-query";
import React, { useMemo } from "react";
import { useForm } from "src/customHooks/useForm";
import ContentHeader from "src/modules/Common/ContentHeader/ContentHeader";
import CustomTextField from "src/modules/Common/FormInputs/CustomTextField";
import offboardingService from "src/services/offboarding.service";
import validators from "src/utils/validators";

const Probations = () => {
  const { data, isFetched, refetch } = useQuery(
    ["get-probation-period-in-days"],
    async () => offboardingService.getProbationPeriodInDays(),
    {
      refetchOnMount: true,
      refetchOnWindowFocus: false,
    },
  );

  const defaultFormState = useMemo(
    () => ({
      probationPeriodInDays: data || 0,
    }),
    [data],
  );

  const { formDetails, formErrors, handleChange, areFormDetailsValid } = useForm({
    initialState: defaultFormState,
    isBulk: false,
    validations: {
      probationPeriodInDays: [validators.validateInput, validators.shouldBeNumeric],
    },
  });

  const typedFormDetails = formDetails as typeof defaultFormState;
  const typedFormErrors = formErrors as Record<keyof typeof defaultFormState, string>;

  const updateProbationPeriodInDays = useMutation({
    mutationKey: ["update-probation-period-in-days"],
    mutationFn: async () => offboardingService.updateProbationPeriodInDays(typedFormDetails.probationPeriodInDays),
    onSuccess: () => {
      refetch();
    },
  });

  const onSubmit = () => {
    updateProbationPeriodInDays.mutate();
  };

  return (
    <Box display="flex" flexDirection="column" gap={2} height="100%">
      <ContentHeader title="Employee Onboarding" subtitle="Configure onboarding settings for your organisation" />
      <Divider />
      <Box width={320}>
        <CustomTextField
          name="probationPeriodInDays"
          size="small"
          id="probationPeriodInDays"
          title="Probation Period (in days)"
          type="number"
          value={typedFormDetails.probationPeriodInDays}
          error={!!typedFormErrors.probationPeriodInDays}
          helperText={typedFormErrors.probationPeriodInDays}
          onChange={handleChange}
          disabled={!isFetched}
        />
      </Box>
      <Box alignSelf="flex-end" bottom={20} gap={2} zIndex={10}>
        <Button
          disabled={!areFormDetailsValid || typedFormDetails.probationPeriodInDays === data}
          onClick={onSubmit}
          variant="contained"
        >
          Save
        </Button>
      </Box>
    </Box>
  );
};

export default Probations;
