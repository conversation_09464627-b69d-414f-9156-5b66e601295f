/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable react/prop-types */
import { Box, Button, CircularProgress, MenuItem, Skeleton, Tooltip, Typography } from "@mui/material";
import { MutationObserverSuccessResult, useMutation, useQuery } from "@tanstack/react-query";
import { MRT_ColumnDef } from "material-react-table";
import React, { Suspense, useEffect, useMemo, useState, lazy } from "react";
import { BaseObject } from "src/app/global";
import CustomTextField from "src/modules/Common/FormInputs/CustomTextField";
import masterdataService from "src/services/masterdata.service";
import roleManagementService from "src/services/roleManagement.service";

const LazyDataTable = lazy(() => import("src/modules/Common/Table/DataTable"));

const createDefaultFormState = (screens: BaseObject[]) => {
  return screens.map((data) => {
    return Object.keys(data).reduce((acc, key) => {
      return {
        ...acc,
        [key]: (data as any)[key].acl ? (data as any)[key].acl : (data as any)[key],
      };
    }, {});
  });
};

function areArraysEqual<T extends BaseObject>(arr1: T[], arr2: T[]): boolean {
  if (arr1 === arr2) return true;
  if (arr1.length !== arr2.length) return false;

  for (let i = 0; i < arr1.length; i++) {
    if (Array.isArray(arr1[i]) && Array.isArray(arr2[i])) {
      if (!areArraysEqual((arr1 as any)[i], (arr2 as any)[i])) return false;
    } else if (typeof arr1[i] === "object" && typeof arr2[i] === "object") {
      if (!areObjectsEqual(arr1[i], arr2[i])) return false;
    } else if (arr1[i] !== arr2[i]) {
      return false;
    }
  }

  return true;
}

function areObjectsEqual<T extends BaseObject>(obj1: T, obj2: T): boolean {
  if (obj1 === obj2) return true;

  const keys1 = Object.keys(obj1);
  const keys2 = Object.keys(obj2);

  if (keys1.length !== keys2.length) return false;

  for (const key of keys1) {
    if (Array.isArray(obj1[key]) && Array.isArray(obj2[key])) {
      if (!areArraysEqual(obj1[key], obj2[key])) return false;
    } else if (typeof obj1[key] === "object" && typeof obj2[key] === "object") {
      if (!areObjectsEqual((obj1 as any)[key], obj2[key] as object)) return false;
    } else if (obj1[key] !== obj2[key]) {
      return false;
    }
  }

  return true;
}

const convertToRequestObject = (data: any[]) => {
  return data.reduce((accum, curr) => {
    Object.keys(curr).forEach((key) => {
      if (key !== "screenName" && key !== "screenDescription") {
        accum.push({
          screen: curr.screenName,
          role: key,
          acl: curr[key],
        });
      }
    });

    return accum;
  }, []);
};

const ManageRoles: React.FC<{
  createMutation?: MutationObserverSuccessResult;
}> = ({ createMutation }) => {
  const [formState, setFormState] = useState([]);
  const { data: masterData, isLoading: isMasterDataLoading } = useQuery(
    ["get-acl"],
    async () => masterdataService.getACLs("ACL"),
    {
      refetchOnMount: true,
      refetchOnReconnect: true,
      refetchOnWindowFocus: false,
    },
  );
  const { data, isLoading, isFetching, refetch } = useQuery(
    ["get-all-roles", createMutation?.isSuccess],
    roleManagementService.getAllRoles,
    {
      refetchOnMount: true,
      refetchOnWindowFocus: false,
      refetchOnReconnect: true,
    },
  );

  const upsertACLRoleScreens = useMutation({
    mutationKey: ["upsert-acl-role-screens"],
    mutationFn: async (data: any) => roleManagementService.upsertACLForRoleScreens(data),
    onSuccess: () => {
      refetch();
    },
  });

  const defaultFormState = useMemo(() => createDefaultFormState(data ? [...data] : []), [data]);

  useEffect(() => {
    if (defaultFormState && defaultFormState?.length > 0) {
      setFormState(JSON.parse(JSON.stringify([...defaultFormState])));
    }
  }, [defaultFormState, data]);

  const getAccessorKey = (key: string) => {
    if (key === "screenDescription") {
      return "screenDescription";
    }

    if (key === "screenName") {
      return "screenName";
    }

    return `${key}.acl`;
  };

  const handleChange = (key: string, index: number, value: any) => {
    const newData = [...formState];
    (newData as any)[index][key] = value;
    setFormState(newData);
  };

  const onSave = () => {
    const requestObject = convertToRequestObject(formState);
    if (requestObject.length > 0) {
      upsertACLRoleScreens.mutate(requestObject);
    }
  };

  const columnDefs = useMemo(
    (): MRT_ColumnDef<any, never>[] =>
      !data || data?.length === 0
        ? []
        : Object.keys(data[0]).map((val) => ({
            accessorKey: getAccessorKey(val),
            header: val === "screenDescription" ? "Action" : val,
            Header: () => (
              <Tooltip title={val === "screenDescription" ? "Action" : val}>
                <Typography>{val === "screenDescription" ? "Action" : val}</Typography>
              </Tooltip>
            ),
            visibleInShowHideMenu: val !== "screenName",
            enableHiding: val !== "screenDescription",
            size: 200,
            Cell: ({ cell }) => {
              if (cell.column.id === "screenName") {
                return cell.row.original.screenName;
              }
              if (cell.column.id === "screenDescription") {
                return cell.row.original.screenDescription;
              }

              if (formState?.length === 0 || (isLoading && isFetching)) {
                return <Skeleton width={100} height={50} />;
              }

              return (
                <CustomTextField
                  sx={{ width: 150 }}
                  disabled={cell.row.original[cell.column.columnDef.header]?.isDefault}
                  key={cell.column.id}
                  id={cell.column.header}
                  value={(formState as any)?.[cell.row.index]?.[cell.column.columnDef.header] || "None"}
                  onChange={(ev) => handleChange(cell.column.columnDef.header, cell.row.index, ev.target.value)}
                  size="small"
                  select
                >
                  {(masterData as string[])?.map((data) => (
                    <MenuItem value={data} id={data} key={data}>
                      {data}
                    </MenuItem>
                  ))}
                </CustomTextField>
              );
            },
          })),
    [isMasterDataLoading, formState, isLoading, isFetching],
  );

  return (
    <Suspense fallback={<CircularProgress />}>
      <LazyDataTable
        layoutMode="grid"
        enableColumnPinning
        enableStickyHeader
        enableGlobalFilter
        enableBottomToolbar
        enableTopToolbar
        renderBottomToolbarCustomActions={() => (
          <Box sx={{ display: "flex", alignItems: "center", justifyContent: "flex-end", width: "100%", padding: 1 }}>
            <Button
              variant="contained"
              disabled={areArraysEqual(defaultFormState, formState) || isMasterDataLoading || (isFetching && isLoading)}
              onClick={onSave}
              sx={{ textTransform: "none" }}
              size="large"
            >
              Save
            </Button>
          </Box>
        )}
        initialState={{
          columnVisibility: {
            screenName: false,
          },
          columnPinning: {
            left: ["screenDescription"],
          },
        }}
        data={data || []}
        state={{
          showLoadingOverlay: isLoading && isFetching,
        }}
        // biome-ignore lint/suspicious/noExplicitAny: <explanation>
        columns={columnDefs as any}
      />
    </Suspense>
  );
};

export default ManageRoles;
