import { Delete, Edit } from "@mui/icons-material";
import { Box, CircularProgress, IconButton } from "@mui/material";
import { useMutation } from "@tanstack/react-query";
import {
  MRT_GlobalFilterTextField,
  MRT_Row,
  MRT_ShowHideColumnsButton,
  MRT_ToggleDensePaddingButton,
  MRT_ToggleFullScreenButton,
} from "material-react-table";
import React, { Suspense, lazy, useCallback, useState } from "react";
import { PATH_CONFIG } from "src/modules/Routing/config";
import { DefaultRoleScreen } from "src/services/api_definitions/roleManagement";
import roleManagementService from "src/services/roleManagement.service";
import { getACLFromFeaturekey } from "src/utils/screenUtils";

const LazyDataTable = lazy(() => import("src/modules/Common/Table/DataTable"));
const LazyDeleteConfirmationModal = lazy(
  () => import("src/modules/Settings/components/Common/DeleteConfirmationModal"),
);

interface AllRolesProps {
  onSuccess?: () => void;
  roles?: DefaultRoleScreen[];
  setIsModalOpen?: (isModalOpen: boolean) => void;
  areRolesFetching?: boolean;
  getRoles?: () => void;
  selectedRow?: unknown;
  setSelectedRow?: (selectedRow: unknown) => void;
}

const AllRoles: React.FC<AllRolesProps> = ({
  roles,
  setIsModalOpen,
  areRolesFetching,
  getRoles,
  selectedRow,
  setSelectedRow,
}) => {
  const ALL_ROLES_ACL = getACLFromFeaturekey(PATH_CONFIG.ALL_ROLES.key);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);

  const deleteMutation = useMutation({
    mutationKey: ["delete-user-role"],
    mutationFn: roleManagementService.deleteUserRoles,
    onSuccess: () => {
      getRoles?.();
      if (setSelectedRow) {
        setSelectedRow(null);
      }
      setIsDeleteModalOpen(false);
    },
  });

  const onEditClicked = (row: MRT_Row<Record<string, unknown>>) => {
    if (setSelectedRow && setIsModalOpen) {
      setSelectedRow({
        role: row.original.name,
      });
      setIsModalOpen(true);
    }
  };

  const onDeleteClick = useCallback((row: string) => {
    if (setSelectedRow) {
      setSelectedRow(row);
    }
    setIsDeleteModalOpen(true);
  }, []);

  const onDeleteRow = useCallback(() => {
    deleteMutation.mutate(selectedRow as string);
  }, [selectedRow]);

  const onDeleteModalClose = () => {
    setIsDeleteModalOpen(false);
    if (setSelectedRow) {
      setSelectedRow(null);
    }
  };

  const getEditRow = useCallback(
    (row: MRT_Row<Record<string, string>>) => (
      <>
        <IconButton disabled={!ALL_ROLES_ACL?.canWrite} onClick={() => onEditClicked(row)}>
          <Edit />
        </IconButton>
        <IconButton disabled={!ALL_ROLES_ACL?.canWrite} onClick={() => onDeleteClick(row?.original?.name)}>
          <Delete />
        </IconButton>
      </>
    ),
    [ALL_ROLES_ACL.canWrite],
  );

  return (
    <Box sx={{ width: "100%" }}>
      <Suspense fallback={<CircularProgress />}>
        <LazyDataTable
          renderTopToolbar={({ table }) => {
            return (
              <Box display="flex" justifyContent="space-between" padding={1} alignItems="center">
                <Box display="flex" alignItems="center" gap={2}>
                  <MRT_GlobalFilterTextField table={table} />
                </Box>
                <Box display="flex">
                  <MRT_ShowHideColumnsButton table={table} />
                  <MRT_ToggleFullScreenButton table={table} />
                  <MRT_ToggleDensePaddingButton table={table} />
                </Box>
              </Box>
            );
          }}
          layoutMode="grid"
          sortDescFirst
          data={roles || []}
          state={{
            showSkeletons: areRolesFetching,
          }}
          displayColumnDefOptions={{
            "mrt-row-actions": {
              header: "Actions",
              size: 150,
              grow: false,
            },
          }}
          enableRowActions={ALL_ROLES_ACL?.canWrite || false}
          enableStickyHeader
          enableEditing={ALL_ROLES_ACL?.canWrite || false}
          positionActionsColumn="last"
          enableRowNumbers
          renderBottomToolbar={false}
          renderRowActions={({ row }) => <>{row.original?.isDefaultRole ? null : getEditRow(row)}</>}
          columns={[
            {
              accessorKey: "name",
              header: "Role",
              size: 300,
              muiEditTextFieldProps: {
                error: false,
              },
              enableSorting: true,
            },
          ]}
        />
      </Suspense>
      <Suspense fallback={<CircularProgress />}>
        {isDeleteModalOpen && (
          <LazyDeleteConfirmationModal
            onCancel={onDeleteModalClose}
            onDelete={onDeleteRow}
            isModalOpen={isDeleteModalOpen}
            selectedRole={selectedRow as string}
          />
        )}
      </Suspense>
    </Box>
  );
};

export default AllRoles;
