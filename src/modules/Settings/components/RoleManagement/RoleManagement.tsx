import { Box, CircularProgress, Paper, Tab, Tabs } from "@mui/material";
import { useMutation, useQuery } from "@tanstack/react-query";
import React, { lazy, Suspense, useCallback, useMemo, useState } from "react";
import useSubroutes from "src/customHooks/useSubroutes";
import ContentHeader from "src/modules/Common/ContentHeader/ContentHeader";
import NoAccessScreen from "src/modules/Common/NoAccess/NoAccess";
import { PATH_CONFIG } from "src/modules/Routing/config";
import roleManagementService from "src/services/roleManagement.service";
import { getACLFromFeaturekey } from "src/utils/screenUtils";
import { DefaultRoleState } from "../Common/ActionModal";
import AllRoles from "./AllRoles";
import NewRolesManagement from "./NewRoleManagement";

const roleManagementTabs = [
  {
    key: PATH_CONFIG.ALL_ROLES.key,
    label: "All Roles",
    header: "Role Management",
    subtitle: "Operate on all roles under your organisation",
    buttonLabel: "Add Role",
    component: <AllRoles />,
    id: 0,
  },
  {
    key: PATH_CONFIG.MANAGE_HIERARCHIAL_ROLES.key,
    label: "Manage Roles",
    header: "Role Management",
    subtitle: "Add access control permissions to different roles under your organisation",
    component: <NewRolesManagement />,
    id: 1,
  },
];
const LazyActionModal = lazy(() => import("../Common/ActionModal"));

const RoleManagement = () => {
  const subRoutes = useSubroutes(PATH_CONFIG.ROLE_MANAGEMENT.key);
  const ROLE_MANAGEMENT_ACL = getACLFromFeaturekey(PATH_CONFIG.ROLE_MANAGEMENT.key);

  const { data, isLoading, isFetching, refetch } = useQuery(["user-roles"], roleManagementService.getUserRoles, {
    enabled: true,
    refetchOnMount: true,
    refetchOnWindowFocus: false,
  });
  const tabsToShow = useMemo(
    () =>
      roleManagementTabs.filter((settings) =>
        subRoutes.some((route) => route.key === settings.key && route.acl?.canRead),
      ),
    [subRoutes],
  );

  const [tabId, setTabId] = React.useState(tabsToShow?.[0]?.id);
  const selectedTab = useMemo(() => tabsToShow?.[tabId], [tabId]);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedRow, setSelectedRow] = useState<DefaultRoleState | null>(null);

  const onSuccess = () => {
    refetch();
    if (selectedRow) {
      setSelectedRow(null);
    }
    if (setIsModalOpen) {
      setIsModalOpen(false);
    }
  };

  const createMutation = useMutation({
    mutationKey: ["create-user-role"],
    mutationFn: roleManagementService.createUserRoles,
    onSuccess: () => onSuccess(),
  });

  const updateMutation = useMutation({
    mutationKey: ["update-user-role"],
    mutationFn: ({ oldRole, newRole }: any) => roleManagementService.updateUserRoles(oldRole, newRole),
    onSuccess: () => onSuccess && onSuccess(),
  });

  const onModalClose = useCallback(() => {
    if (setSelectedRow && setIsModalOpen) {
      setSelectedRow(null);
      setIsModalOpen(false);
    }
  }, []);

  const onUpdate = useCallback(
    (data: string) => {
      updateMutation.mutate({
        oldRole: (selectedRow as any)?.role as string,
        newRole: data,
      });
    },
    [selectedRow],
  );

  const handleChange = (_event: React.SyntheticEvent, newValue: number) => {
    setTabId(newValue);
  };

  const onCreate = useCallback((data: string) => {
    createMutation.mutate(data);
  }, []);

  if (!tabsToShow || tabsToShow?.length === 0) {
    return (
      <Box>
        <NoAccessScreen />
      </Box>
    );
  }

  return (
    <Box display="flex" flexDirection="column" gap={2}>
      <ContentHeader
        title={selectedTab?.header}
        subtitle={selectedTab?.subtitle}
        buttonTitle={ROLE_MANAGEMENT_ACL?.canWrite ? "Add Role" : ""}
        primaryAction={() => setIsModalOpen(true)}
      />
      <Tabs component={Paper} value={tabId} onChange={handleChange}>
        {tabsToShow?.map((tab) => (
          <Tab
            sx={{
              textTransform: "none",
            }}
            label={tab?.label}
            tabIndex={tab?.id}
            key={tab?.id}
          />
        ))}
      </Tabs>
      <Box margin="16px 0px" display="flex" flexDirection="column" gap={2}>
        {selectedTab?.component &&
          React.cloneElement(selectedTab?.component, {
            roles: data,
            onSuccess: onSuccess,
            setIsModalOpen: setIsModalOpen,
            areRolesFetching: isLoading && isFetching,
            getRoles: refetch,
            selectedRow: selectedRow,
            setSelectedRow: setSelectedRow,
            createMutation: createMutation,
          })}
      </Box>
      <Suspense fallback={<CircularProgress />}>
        {isModalOpen && (
          <LazyActionModal
            isEdit={!!selectedRow}
            isOpen={isModalOpen}
            modalData={selectedRow as DefaultRoleState}
            onClose={() => onModalClose()}
            onCreate={onCreate}
            onUpdate={onUpdate}
            key="modal"
          />
        )}
      </Suspense>
    </Box>
  );
};

export default RoleManagement;
