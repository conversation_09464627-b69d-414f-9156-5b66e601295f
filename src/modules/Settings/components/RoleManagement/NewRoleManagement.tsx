/* eslint-disable react/prop-types */
/* eslint-disable @typescript-eslint/no-unused-vars */
import { Box, Button, CircularProgress, MenuItem, Select } from "@mui/material";
import { useMutation, useQuery } from "@tanstack/react-query";
import { MRT_ColumnDef } from "material-react-table";
import React, { useEffect, useMemo, useState } from "react";
import { BaseObject } from "src/app/global";
import DataTable from "src/modules/Common/Table/DataTable";
import { RoleScreensResponse } from "src/services/api_definitions/roleManagement";
import masterdataService from "src/services/masterdata.service";
import roleManagementService from "src/services/roleManagement.service";

interface ScreenNode extends BaseObject {
  screen: string;
  screen_description: string;
  subRows: ScreenNode[];
}

interface RoleACL {
  screen: string;
  role: string;
  acl: string;
}

const NewRolesManagement = () => {
  const [nestedData, setNestedData] = useState<ScreenNode[]>([]);
  const upsertACLRoleScreens = useMutation({
    mutationKey: ["upsert-acl-role-screens"],
    mutationFn: async (data: RoleACL[]) =>
      roleManagementService.upsertACLForRoleScreens(data as unknown as Record<string, string>[]),
    onSuccess: () => {
      refetch();
    },
  });

  const { data: masterData, isLoading: isMasterDataLoading } = useQuery(
    ["get-acl"],
    async () => masterdataService.getACLs("ACL"),
    {
      refetchOnMount: true,
      refetchOnReconnect: true,
      refetchOnWindowFocus: false,
    },
  );

  // Create a nested structure from the screen data
  function createNestedStructure(data: RoleScreensResponse): ScreenNode[] {
    const screens: ScreenNode[] = [];
    for (const item of [...(data?.default_role_screens || []), ...(data?.custom_role_screens || [])]) {
      const screenPath = item.screen.split("_");
      let currentScreen: ScreenNode = null as unknown as ScreenNode;
      let parentScreen: ScreenNode = null as unknown as ScreenNode;

      for (let i = 0; i < screenPath.length; i++) {
        const screen = screenPath[i];
        const existingScreen = parentScreen
          ? parentScreen.subRows.find((s) => s.screen === screen)
          : screens.find((s) => s.screen === screen);
        if (!existingScreen) {
          const newScreen = {
            screen,
            screen_description: "",
            subRows: [],
          } as unknown as ScreenNode;
          if (parentScreen) {
            parentScreen.subRows.push(newScreen);
          } else {
            screens.push(newScreen);
          }
          currentScreen = newScreen;
        } else {
          currentScreen = existingScreen;
        }
        parentScreen = currentScreen;
      }

      (currentScreen as ScreenNode).screen_description = item.screen_description;
      (currentScreen as ScreenNode)[item.role] = item.acl;
    }
    return screens;
  }

  const { data, isLoading, isFetching, refetch } = useQuery(["get-all-roles"], roleManagementService.getAllRoles1, {
    refetchOnMount: true,
    refetchOnWindowFocus: false,
    refetchOnReconnect: true,
  });

  useEffect(() => {
    setNestedData(createNestedStructure(data as RoleScreensResponse));
  }, [data]);

  const columns = useMemo(
    () => [
      {
        accessorKey: "screen",
        header: "Screen",
      },
      {
        accessorKey: "screen_description",
        header: "Screen Description",
      },
    ],
    [],
  );

  const roleColumns = useMemo(() => {
    const roles = Array.from(
      new Set(
        nestedData?.flatMap((item) =>
          Object.keys(item).filter((key) => key !== "screen" && key !== "screen_description" && key !== "subRows"),
        ),
      ),
    );

    const disabledRoles = new Set(
      (data as RoleScreensResponse)?.default_role_screens?.map((roleScreen) => roleScreen.role),
    );

    return roles.map(
      (role) =>
        ({
          accessorKey: role,
          header: role,
          Cell: ({ cell }) => {
            const parentRow = cell.row.original;
            const parentPermission = parentRow[role as keyof RoleACL];
            const value =
              cell.column.id !== "screen" && cell.column.id !== "screen_description" && cell.column.id !== "subRows"
                ? parentPermission !== undefined
                  ? parentPermission
                  : cell?.row?.original[cell.column.id as keyof RoleACL]
                : null;

            return (
              <Select
                value={value || "None"}
                onChange={(e) => handleRoleChange(nestedData, cell.row.original.screen, cell.column.id, e.target.value)}
                sx={{ width: 150 }}
                size="small"
                disabled={disabledRoles.has(cell.column.id)}
              >
                {(masterData as string[])?.map((acl: string) => (
                  <MenuItem key={acl} value={acl}>
                    {acl}
                  </MenuItem>
                ))}
              </Select>
            );
          },
        }) as MRT_ColumnDef<RoleACL>,
    );
  }, [nestedData, masterData]);

  // Handle role change and update the nested data structure
  function handleRoleChange(orgData: ScreenNode[], screen: string, role: string, value: string) {
    // Deep copy the orgData to avoid mutating the original data
    const data = JSON.parse(JSON.stringify(orgData));

    // Function to update roles for a node and its subRows
    function updateRoles(node: ScreenNode, role: string, value: string) {
      node[role] = value;

      if (node.subRows && node.subRows.length > 0) {
        node.subRows.forEach((child) => {
          updateRoles(child, role, value);
        });
      }
    }

    // Function to find the screen node
    function findScreen(node: ScreenNode, targetScreen: string): boolean {
      if (node.screen === targetScreen) {
        updateRoles(node, role, value);
        return true;
      } else if (node.subRows && node.subRows.length > 0) {
        let updated = false;
        node.subRows.forEach((child) => {
          if (findScreen(child, targetScreen)) {
            updated = true;
          }
        });
        if (updated && node[role] === "None") {
          node[role] = value;
        }
        return updated;
      }
      return false;
    }

    // Start by finding the screen and updating roles
    data.forEach((node: ScreenNode) => findScreen(node, screen));

    // Set the updated nested data
    setNestedData(data);
    return data;
  }

  // Flatten the nested data structure into an array of RoleACL objects
  function flattenData(data: ScreenNode[]): RoleACL[] {
    const result: RoleACL[] = [];

    function traverse(node: ScreenNode, path: string[] = []) {
      if (node.screen) {
        Object.keys(node).forEach((key) => {
          if (!["screen", "screen_description", "subRows"].includes(key)) {
            result.push({
              screen: path.concat(node.screen).join("_"),
              role: key,
              acl: (node[key] as string) || "",
            });
          }
        });
      }

      if (node.subRows && node.subRows.length > 0) {
        node.subRows.forEach((subRow) => traverse(subRow, path.concat(node.screen)));
      }
    }

    data.forEach((node) => traverse(node));
    return result;
  }

  // Handle save event and upsert the role-screen ACLs
  const handleSave = () => {
    const requestData = flattenData(nestedData);
    if (requestData.length > 0) {
      upsertACLRoleScreens.mutate(requestData);
    }
  };

  if (isLoading && isFetching && isMasterDataLoading) {
    return <CircularProgress />;
  }

  return (
    <DataTable
      columns={[...columns, ...roleColumns]}
      data={nestedData as unknown as RoleACL[]}
      enableExpanding
      enableBottomToolbar
      enableTopToolbar
      initialState={{
        expanded: {},
        columnVisibility: {
          screen: false,
        },
        columnPinning: {
          left: ["screen_description"],
        },
      }}
      enableStickyHeader
      enableColumnPinning
      renderBottomToolbarCustomActions={() => (
        <Box sx={{ display: "flex", alignItems: "center", justifyContent: "flex-end", width: "100%", padding: 1 }}>
          <Button variant="contained" onClick={handleSave} sx={{ textTransform: "none" }} size="large">
            Save
          </Button>
        </Box>
      )}
    />
  );
};

export default NewRolesManagement;
