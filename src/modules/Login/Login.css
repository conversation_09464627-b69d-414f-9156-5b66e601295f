.login-container {
  background: white;
}

.login-content {
  display: flex;
  height: 100vh;
  justify-content: space-between;
}

.login-box {
  display: flex;
  flex-direction: column;
  width: 450px;
  justify-content: space-between;
  height: 80vh;
}

.right-section img {
  height: 120vh;
  width: auto;
  display: block;
}

.left-section {
  margin: auto;
}
.company-logo-container {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.company-logo {
  width: auto;
  max-height: 80px;
  margin: 20px 0 100px;
}

/* General styles for desktop and mobile */

/* Styles specific to desktop */
@media (min-width: 768px) {
}

/* Styles specific to mobile */
@media (max-width: 767px) {
  @media (max-width: 767px) {
    .right-section {
      display: none;
    }
    /* .left-section {
      padding: 0 30px;
    } */
  }
}

.form-wrapper {
  display: flex;
  flex-direction: column;
  row-gap: 29px;
}
.forgot-password-wrapper {
  display: flex;
  column-gap: 15px;
  margin-bottom: 60px;
  margin-top: 14px;
}

.social-container {
  margin-top: 34px;
  margin-bottom: 130px;
  width: 400px;
  margin-left: auto;
  margin-right: auto;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 20px;
}

.terms-container {
  display: flex;
  justify-content: space-around;
  margin-bottom: 27px;
}
.form-label {
  color: #344054;
  font-family: Poppins;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 140%;
  margin-bottom: 8px;
}
.form-input {
  padding: 10px 14px !important;
  border-radius: 5px;
  border: 1px solid #d5d7d8;
  background: #fff;
  box-shadow: 0px 1px 2px 0px rgba(16, 24, 40, 0.05);
  color: #667085;

  /* H6 */
  font-family: Poppins;
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: 140%; /* 22.4px */
}
.login-button {
  display: flex;
  width: 189px;
  height: 48px;
  padding: 0px 20px;
  justify-content: center;
  align-items: center;
  gap: 8px;
  flex-shrink: 0;
  border-radius: 50px;
  border: 1.5px solid #007f6f;
  background: #007f6f;
}

.subHeading {
  color: #6d6b6b;
  font-family: Lexend;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 26px;
}

.heading {
  color: #000000;
  font-family: Inter;
  font-size: 40px;
  font-weight: 500;
  line-height: 48px;
}
.reset-password {
  color: #5395e2;
  font-family: Lexend;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 26px;
}
.footer-text {
  color: #777;
  font-family: Poppins;
  font-size: 15px;
  font-style: normal;
  font-weight: 400;
  line-height: 16px;
  text-align: center;
}

.or-divider {
  width: 156.967px;
  height: 1px;
  background: #b7b7b7;
}
.or-wrapper {
  display: flex;
  column-gap: 10px;
  align-items: center;
}
.loader-container {
  position: absolute;
  top: 50%;
  left: 50%;
}
