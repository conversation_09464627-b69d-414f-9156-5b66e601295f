import { useQueryClient } from "@tanstack/react-query";
import { useEffect, useState } from "react";
import { useAppSelector } from "src/customHooks/useAppSelector";
import authService from "src/services/auth.service";
import { isTokenExpired } from "src/utils/authUtils";
import { showToast } from "src/utils/toast";
import { PATH_CONFIG } from "../Routing/config";

export const getSessionToken = () => {
  return localStorage.getItem("accessToken") || "";
};

export const getUriPathname = () => {
  return window.location.host;
};

export const getJwtRoles = (sessionToken: string, tenantId?: string) => {
  if (!tenantId) return [];
  const decodedToken = JSON.parse(atob(sessionToken.split(".")[1]));
  return decodedToken.tenants[tenantId]?.roles;
};

// Handling the case of skewed system time
export const getExpiryBuffer = (token: string) => {
  const decodedToken = JSON.parse(atob(token.split(".")[1]));
  const systemTime = Math.floor(Date.now() / 1000);
  const expiryBuffer = Math.floor(decodedToken.iat) - systemTime;
  if (expiryBuffer < 0) {
    return 6; //in seconds
  }
  return expiryBuffer; //in seconds
};

const expiryBuffer = parseInt(localStorage.getItem("expiryBuffer") || "0");

export const checkAndRefreshToken = async () => {
  const accessToken = localStorage.getItem("accessToken");

  if (!accessToken) return null;

  if (isTokenExpired(accessToken, expiryBuffer)) {
    return await forceRefreshToken();
  }
  return accessToken;
};

export const forceRefreshToken = async () => {
  try {
    // Get refresh token from HttpOnly cookie (handled by backend)
    const response = await authService.refreshToken();
    localStorage.setItem("accessToken", response.response.token);
    const expiryBuffer = getExpiryBuffer(response.response.token);
    localStorage.setItem("expiryBuffer", expiryBuffer.toString());
    return response.response.token;
  } catch (_error) {
    logoutAndReload();
    return null;
  }
};

const logoutAndReload = () => {
  localStorage.clear();
  document.cookie = "refreshToken=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;";
  authService.signOut().finally(() => {
    window.location.href = PATH_CONFIG.LOGIN.path;
  });
};

export const useAuth = () => {
  const storedAccessToken = localStorage.getItem("accessToken");
  const queryClient = useQueryClient();
  const [isAuthenticated, setIsAuthenticated] = useState<boolean>(storedAccessToken ? true : false);
  // const [isSessionLoading, setIsSessionLoading] = useState<boolean>(true);
  const { tenantDetails } = useAppSelector((state) => state.userManagement);

  const getTokenFromUrl = () => {
    const urlParams = new URLSearchParams(window.location.search);
    const token = urlParams.get("access_token");
    const error = urlParams.get("error");
    if (error) {
      setIsAuthenticated(false);
      showToast(error, { type: "error" });
    } else if (token) {
      handleLoginSuccess(token).then(async () => {
        window.history.replaceState({}, document.title, window.location.pathname);
        checkAndRefreshToken();
      });
    } else if (!storedAccessToken) {
      // setIsSessionLoading(false);
      setIsAuthenticated(false);
    }
  };

  useEffect(() => {
    if (storedAccessToken) {
      setIsAuthenticated(true);
    }
  }, [storedAccessToken]);

  // useEffect(() => {
  //   if (isAuthenticated) {
  //     setIsSessionLoading(false);
  //   }
  // }, [isAuthenticated]);

  const getLoginUrl = (provider: string) =>
    `${process.env.LOGIN_SERVER_URL}/oauth2/authorization/${provider}?redirect_uri=${window.location.origin}/login`;

  const handleLoginSuccess = async (token: string) => {
    if (!token) return;
    localStorage.setItem("accessToken", token);
    const expiryBuffer = getExpiryBuffer(token);
    localStorage.setItem("expiryBuffer", expiryBuffer.toString());
    localStorage.setItem("showRoleSelectionModal", "true");
    // setIsSessionLoading(false);
    setIsAuthenticated(true);
  };

  const logout = () => {
    setIsAuthenticated(false);
    localStorage.clear();
    document.cookie = "refreshToken=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;";
    authService.signOut().then(() => {
      queryClient.invalidateQueries(["user"]);
      window.location.href = PATH_CONFIG.LOGIN.path;
    });
  };

  const passwordLogin = async (username: string, password: string) => {
    const response = await authService.passwordLogin({ username, password, tenant_id: tenantDetails?.tenant_id });
    await handleLoginSuccess(response.response.token);
    window.location.href = PATH_CONFIG.HOME.path;
  };

  const generateOtp = async (username: string) => {
    const response = await authService.generateOtp({ username, tenant_id: tenantDetails?.tenant_id });
    return response;
  };

  const verifyOtp = async (username: string, otp: string, password: string) => {
    const response = await authService.verifyOtp({ username, otp, password, tenant_id: tenantDetails?.tenant_id });
    return response;
  };

  const getValidAccessToken = async () => {
    return await checkAndRefreshToken();
  };

  return {
    logout,
    getValidAccessToken,
    getLoginUrl,
    passwordLogin,
    isAuthenticated,
    generateOtp,
    verifyOtp,
    // isSessionLoading,
    getTokenFromUrl,
  };
};
