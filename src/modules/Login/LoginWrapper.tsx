import { Box, <PERSON>, Typography } from "@mui/material";
import loginHero from "assets/loginHero.png";
import React from "react";

const PageWrapper = ({ children, tenantDetails }: { children: React.ReactNode; tenantDetails: any }) => {
  return (
    <Box display="flex" minHeight="100vh" bgcolor={"white"}>
      {/* Left Section */}
      <Box
        flex={1}
        px={5}
        // py={6}
        display="flex"
        flexDirection="column"
        justifyContent="space-between"
        maxHeight={"fit-content"}
        height={"100%"}
        margin={"auto"}
        maxWidth={"640px"}
      >
        {/* Logo */}
        <Box mb={2} sx={{ margin: "auto" }}>
          <img src={tenantDetails?.logo} alt="Company Logo" style={{ height: 80, objectFit: "contain" }} />
        </Box>

        {/* Children (Form) */}
        <Box
          mt={4}
          flex={1}
          display="flex"
          flexDirection="column"
          justifyContent="center"
          maxWidth={"455px"}
          margin={"auto"}
          width={"100%"}
        >
          {children}
        </Box>

        {/* Terms and Privacy */}
        <Box pt={4}>
          <Typography variant="body2" color="text.secondary" align="center" fontSize={"12px"}>
            <Link target="_blank" href="https://effihr.com/terms-of-use/" style={{ color: "#1976d2" }}>
              Terms of service
            </Link>{" "}
            &nbsp; | &nbsp;{" "}
            <Link target="_blank" href="https://effihr.com/privacy-policy/" style={{ color: "#1976d2" }}>
              Privacy Policy
            </Link>
          </Typography>
          <Typography variant="caption" color="text.secondary" align="center" display="block" mt={1}>
            For details on how we use your personal data, please see our privacy and data processing statement.
          </Typography>
        </Box>
      </Box>

      {/* Right Section (Image) */}
      <Box flex={1} position="relative">
        <img
          src={loginHero as string}
          alt="HRMS visual"
          style={{
            width: "100%",
            height: "100vh",
            objectFit: "cover",
            // borderTopLeftRadius: '40px',
            // borderBottomLeftRadius: '40px',
          }}
        />
      </Box>
    </Box>
  );
};

export default PageWrapper;
