import { Box, Typography } from "@mui/material";
import React, { useEffect, useState } from "react";
import { useAuth } from "src/modules/Login/LoginHook";
import LoginWrapper from "./LoginWrapper";
import "./NativeLogin.css";
import { useAppSelector } from "src/customHooks/useAppSelector";
import { setCurrentTenantId } from "src/utils/authUtils";
import { PasswordLogin } from "./PasswordLoginForm";
import googleLogo from "./google-logo.svg";
import microsoftLogo from "./ms-logo.svg";
export const getUriPathname = () => {
  return window.location.pathname;
};
const effiHRLogo = "https://effihr.com/wp-content/uploads/2024/07/Png_updated-logo-icon.png";

type Step = "social" | "password" | "otp-generate" | "otp-verify";

const LoginHeader = ({ step, username }: { step: Step; username: string }) => {
  if (step === "password" || step === "social") {
    return (
      <div className="login-header">
        <Typography fontWeight="bold" align="center" variant="h4" mb={1}>
          Welcome to effiHR
        </Typography>
        <Typography align="center" variant="h6" color={"#696767"}>
          Sign in, to get started
        </Typography>
      </div>
    );
  }
  if (step === "otp-generate") {
    return (
      <div className="login-header">
        <Typography fontWeight="bold" align="center" variant="h4" mb={2}>
          Password Reset
        </Typography>
        <Typography align="center" variant="h6" fontSize={"18px"}>
          Enter your <span style={{ fontWeight: "bold" }}>work email address</span> that is registered with us. We'll
          send you an email with OTP to reset your password.
        </Typography>
      </div>
    );
  }
  if (step === "otp-verify") {
    return (
      <div className="login-header">
        <Typography fontWeight="bold" align="center" variant="h4" mb={1}>
          Verify OTP
        </Typography>
        <Typography align="center" variant="h6" fontSize={"18px"}>
          We have sent an OTP to <span style={{ fontWeight: "bold" }}>{username}</span>.
          <br />
          Enter it below to continue.
        </Typography>
      </div>
    );
  }
};

export const NativeLoginPage = () => {
  const { getLoginUrl } = useAuth();
  const { tenantDetails } = useAppSelector((state) => state.userManagement);
  const [step, setStep] = useState<Step>("social");
  const [username, setUsername] = useState<string>("");
  const ssoTypes = tenantDetails?.sso_types?.split(", ");
  const isGoogleEnabled = ssoTypes?.includes("Google");
  const isMicrosoftEnabled = ssoTypes?.includes("Microsoft");
  const isPasswordEnabled = ssoTypes?.includes("Password");

  const handleGoogleLogin = () => {
    window.location.href = getLoginUrl("google");
  };

  const handleMicrosoftLogin = () => {
    window.location.href = getLoginUrl("microsoft");
  };

  useEffect(() => {
    if (tenantDetails?.tenant_id) {
      setCurrentTenantId(tenantDetails?.tenant_id);
    }
  }, [tenantDetails]);

  if (step !== "social" && step !== "password") {
    return (
      <LoginWrapper tenantDetails={tenantDetails}>
        <Box>
          <LoginHeader step={step} username={username} />
          <Box className="social-container">
            <PasswordLogin setStep={setStep} step={step} username={username} setUsername={setUsername} />
          </Box>
        </Box>
      </LoginWrapper>
    );
  }

  return (
    <LoginWrapper tenantDetails={tenantDetails}>
      <Box>
        <LoginHeader step={step} username={username} />
        <Box display="flex" flexDirection="column" alignItems="center" gap={2}>
          {isGoogleEnabled && (
            <button type="button" onClick={handleGoogleLogin} className="login-with-google-btn">
              <img height={20} className="social-logo" src={googleLogo as unknown as string} alt="Google" /> Sign in
              with Google
            </button>
          )}
          {isMicrosoftEnabled && (
            <button type="button" onClick={handleMicrosoftLogin} className="login-with-microsoft-btn">
              <img height={23} className="social-logo" src={microsoftLogo as unknown as string} alt="Microsoft" /> Sign
              in with Microsoft
            </button>
          )}
          {isPasswordEnabled && ssoTypes?.length > 1 && (
            <Box display="flex" flexDirection="row" alignItems="center" width="100%" gap={2}>
              <Box
                sx={{ width: "100%", borderTop: "1px dotted rgb(105, 103, 103)", margin: "10px 0px", display: "flex" }}
              ></Box>
              <Typography fontWeight="bold" align="center" variant="body2" color={"#696767"}>
                Or
              </Typography>
              <Box
                sx={{ width: "100%", borderTop: "1px dotted rgb(105, 103, 103)", margin: "10px 0px", display: "flex" }}
              ></Box>
            </Box>
          )}
          {isPasswordEnabled && step === "social" && (
            <button type="button" onClick={() => setStep("password")} className="login-with-microsoft-btn">
              <img height={20} className="social-logo" src={effiHRLogo} alt="Google" /> Sign in with credentials
            </button>
          )}
          {step === "password" && (
            <Box className="social-container">
              <PasswordLogin setStep={setStep} step={step} username={username} setUsername={setUsername} />
            </Box>
          )}
        </Box>
      </Box>
    </LoginWrapper>
  );
};

export default NativeLoginPage;
