import { <PERSON><PERSON>, <PERSON>, Typo<PERSON>, styled } from "@mui/material";
import React, { useEffect, useState } from "react";
import { showToast } from "src/utils/toast";
import CustomTextField from "../Common/FormInputs/CustomTextField";
import OTPInput from "../Common/FormInputs/OTPInput";
import { useAuth } from "./LoginHook";
import "./NativeLogin.css";
import { AxiosError } from "axios";
import { CustomPasswordField } from "../Common/FormInputs/CustomPasswordField";

type Step = "password" | "otp-generate" | "otp-verify" | "social";

const CustomInput = styled(CustomTextField)({
  marginBottom: "16px",
});

const CustomPasswordInput = styled(CustomPasswordField)({
  marginBottom: "16px",
});

const CustomButton = styled(Button)({
  borderRadius: "10px",
  fontWeight: "bold",
});

export const PasswordLogin = ({
  setStep,
  step,
  username,
  setUsername,
}: { setStep: (step: Step) => void; step: Step; username: string; setUsername: (username: string) => void }) => {
  const { generateOtp, verifyOtp, passwordLogin } = useAuth();
  const [otp, setOtp] = useState<string>("");
  const [errorMessage, setErrorMessage] = useState<any>("");

  useEffect(() => {
    setErrorMessage("");
  }, [step]);

  const handlePasswordLogin = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    const formData = new FormData(e.target as HTMLFormElement);
    const username = formData.get("username") as string;
    const password = formData.get("password") as string;
    try {
      await passwordLogin(username, password);
      (e.target as HTMLFormElement).reset();
    } catch (error) {
      const errorResponse = error as AxiosError;
      setErrorMessage(errorResponse.response?.data || errorResponse.message);
    }
  };

  const handleOtpGenerate = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    const formData = new FormData(e.target as HTMLFormElement);
    const username = formData.get("username") as string;

    setUsername(username);
    try {
      await generateOtp(username);
      setStep("otp-verify");
      (e.target as HTMLFormElement).reset();
    } catch (error) {
      const errorResponse = error as AxiosError;
      setErrorMessage(errorResponse.response?.data || errorResponse.message);
    }
  };

  const handleOtpVerify = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    const formData = new FormData(e.target as HTMLFormElement);
    const password = formData.get("password") as string;
    const confirmPassword = formData.get("confirm_password") as string;
    if (password !== confirmPassword) {
      setErrorMessage("Passwords do not match");
      return;
    }
    try {
      await verifyOtp(username, otp, password);
      showToast("Password updated successfully, login again", {
        type: "success",
      });
      setStep("password");
      (e.target as HTMLFormElement).reset();
    } catch (error) {
      const errorResponse = error as AxiosError;
      setErrorMessage(errorResponse.response?.data || errorResponse.message);
    }
  };

  if (step === "otp-generate") {
    return (
      <form onSubmit={handleOtpGenerate}>
        <div style={{ display: "flex", flexDirection: "column", width: "100%" }}>
          <CustomInput
            size="small"
            fullWidth
            title="Email address"
            type="text"
            placeholder="Enter your work email"
            required
            name="username"
            labelProps={{ fontSize: "16px" }}
          />
          <CustomButton type="submit" variant="contained" color="primary">
            Send OTP
          </CustomButton>
        </div>
        {errorMessage && <Typography color="error">{errorMessage}</Typography>}
      </form>
    );
  }
  if (step === "otp-verify") {
    return (
      <form onSubmit={handleOtpVerify}>
        <div style={{ display: "flex", flexDirection: "column", width: "100%" }}>
          <div style={{ display: "flex", flexDirection: "column", margin: "16px 0px", gap: "5px" }}>
            <OTPInput separator={<span />} value={otp} onChange={setOtp} length={6} />
            <div>
              <Link
                onClick={() => generateOtp(username)}
                sx={{ cursor: "pointer", fontSize: "14px", textDecoration: "none" }}
              >
                Resend OTP
              </Link>
            </div>
          </div>
          <CustomPasswordInput
            size="small"
            fullWidth
            title="New Password"
            placeholder="New Password"
            required
            name="password"
            labelProps={{ fontSize: "16px" }}
          />
          <CustomPasswordInput
            size="small"
            fullWidth
            title="Confirm New Password"
            placeholder="Confirm New Password"
            required
            name="confirm_password"
            labelProps={{ fontSize: "16px" }}
          />
          <CustomButton type="submit" variant="contained" color="primary">
            Reset Password
          </CustomButton>
          {errorMessage && <Typography color="error">{errorMessage}</Typography>}
        </div>
      </form>
    );
  }
  return (
    <div style={{ width: "100%" }}>
      <form onSubmit={handlePasswordLogin}>
        <div style={{ display: "flex", flexDirection: "column", width: "100%" }}>
          <CustomInput
            labelProps={{ fontSize: "16px" }}
            title="Email address"
            type="email"
            fullWidth
            required
            name="username"
            size="small"
            placeholder="Enter email"
          />
          <CustomPasswordInput
            labelProps={{ fontSize: "16px" }}
            title="Password"
            required
            name="password"
            size="small"
            placeholder="Enter password"
          />
          <div className="forgot-password-button">
            <Link onClick={() => setStep("otp-generate")} sx={{ cursor: "pointer", fontSize: "14px" }}>
              Reset Password?
            </Link>
          </div>
          <CustomButton type="submit" variant="contained" color="primary">
            Sign In
          </CustomButton>
        </div>
        {errorMessage && <Typography color="error">{errorMessage}</Typography>}
      </form>
    </div>
  );
};
