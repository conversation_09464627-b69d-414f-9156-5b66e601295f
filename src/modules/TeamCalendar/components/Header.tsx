import { Box } from "@mui/material";
import React from "react";
import EffiMonthDatePicker from "src/modules/Common/FormInputs/EffiMonthDatePicker";

interface CalendarHeaderProps {
  selectedDate: string;
  setSelectedDate: (selectedDate: string) => void;
}

const Header: React.FC<CalendarHeaderProps> = ({ selectedDate, setSelectedDate }) => (
  <Box display="flex" alignItems="center" justifyContent="flex-end" width="100%">
    {selectedDate && (
      <EffiMonthDatePicker
        onChange={(date) => setSelectedDate(date as string)}
        value={selectedDate}
        slotProps={{
          textField: {
            size: "small",
            sx: { width: 153, height: 42 },
          },
        }}
      />
    )}
  </Box>
);

export default Header;
