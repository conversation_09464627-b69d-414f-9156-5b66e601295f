import { FormControlLabel, Switch } from "@mui/material";
import { useMutation } from "@tanstack/react-query";
import React from "react";
import DeleteConfirmationModal from "src/modules/Settings/components/Common/DeleteConfirmationModal";
import attendanceService from "src/services/attendance.service";
import { DataTableActionProps } from "./TeamCalendarView";

const DeleteAttendanceConfig: React.FC<
  Pick<DataTableActionProps, "onClose" | "teamCalendarDetails"> & {
    displayName: string;
    employeeCode: string;
    canReplicateForAll?: boolean;
  }
> = ({ onClose, displayName, employeeCode, canReplicateForAll = true }) => {
  const ref = React.useRef<HTMLInputElement | null>(null);
  const deleteMutation = useMutation({
    mutationKey: ["delete-attendance-config"],
    mutationFn: async (replicate: boolean) => attendanceService.deleteEmployeeAttendanceConfig(employeeCode, replicate),
    onSuccess: () => {
      onClose();
    },
  });

  const onDelete = () => {
    deleteMutation.mutate(ref?.current?.checked || false);
  };

  return (
    <DeleteConfirmationModal
      onDelete={onDelete}
      onCancel={onClose}
      selectedRole={`Attendance config for ${displayName}`}
      isModalOpen
      title=""
    >
      {canReplicateForAll ? (
        <FormControlLabel control={<Switch inputRef={ref} />} label="Delete for all reportees" />
      ) : null}
    </DeleteConfirmationModal>
  );
};
export default DeleteAttendanceConfig;
