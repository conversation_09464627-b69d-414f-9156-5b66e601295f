import { Box } from "@mui/material";
import { format, parse } from "date-fns";
import React, { useMemo, useState } from "react";
import Header from "src/modules/TeamCalendar/components/Header";
import TeamCalendarView from "src/modules/TeamCalendar/components/TeamCalendarView";

const DEFAULT_SELECTED_MONTH: string = format(new Date(), "MM-yyyy");

const TeamCalendar = () => {
  const [selectedDate, setSelectedDate] = useState(DEFAULT_SELECTED_MONTH);

  const selectedDateToRender = useMemo(
    () => format(parse(selectedDate, "MM-yyyy", new Date()), "MMM yyyy"),
    [selectedDate],
  );

  return (
    <Box display="flex" flexDirection="column" gap={3}>
      <Header setSelectedDate={setSelectedDate} selectedDate={selectedDate} />
      <TeamCalendarView selectedDate={selectedDateToRender} key={selectedDateToRender} />
    </Box>
  );
};

export default TeamCalendar;
