import { Box, Button } from "@mui/material";
import { useMutation } from "@tanstack/react-query";
import { MRT_RowSelectionState } from "material-react-table";
import React, { useEffect, useMemo, useState } from "react";
import Modal from "src/modules/Common/Modal/Modal";
import DataTable from "src/modules/Common/Table/DataTable";
import timesheetsService from "src/services/timesheets.service";
import { DataTableActionProps } from "./TeamCalendarView";
// import { useMutation } from "@tanstack/react-query";

const COLUMN_DEFS = [
  {
    header: "Project Code",
    accessorKey: "project_code",
  },
  {
    header: "Project Name",
    accessorKey: "project_name",
  },
  {
    header: "Start Date",
    accessorKey: "start_date",
  },
  {
    header: "End Date",
    accessorKey: "end_date",
  },
];

const AssignProjectsModal: React.FC<DataTableActionProps> = ({
  onClose,
  selectedRow,
  teamCalendarDetails,
  projectAssignments,
  refetchProjectAssignments,
}) => {
  const [selectedRows, setSelectedRows] = useState<MRT_RowSelectionState>({});

  const initialSelectedRows = {} as MRT_RowSelectionState;
  projectAssignments?.forEach((row, index) => {
    if (row?.assigned_employee_codes?.includes(selectedRow?.reportee?.employee_code)) {
      initialSelectedRows[index] = true;
    } else {
      initialSelectedRows[index] = false;
    }
  });

  const isDirty = useMemo(() => {
    return JSON.stringify(selectedRows) !== JSON.stringify(initialSelectedRows);
  }, [selectedRows, initialSelectedRows]);

  useEffect(() => {
    //convert this to {0:true, 1:true} format
    setSelectedRows(initialSelectedRows);
  }, [teamCalendarDetails]);

  const taskAssignMutation = useMutation({
    mutationFn: async () => {
      const projectCodes = Object.keys(selectedRows)
        .map((project) => {
          if (selectedRows[project]) {
            return projectAssignments[project as unknown as number]?.project_id;
          }
        })
        .filter((project) => project !== null && project !== undefined);
      return await timesheetsService.assignProjects(selectedRow?.reportee?.employee_code || "", projectCodes);
    },
    onSuccess: (isSuccessfullyAssigned: boolean) => {
      if (isSuccessfullyAssigned) {
        refetchProjectAssignments();
        onClose();
      }
    },
  });

  const onSaveClick = async () => {
    taskAssignMutation.mutate();
  };

  return (
    <Modal
      isOpen
      onClose={onClose}
      title={`Assign Projects to ${selectedRow?.reportee?.display_name}`}
      showBackButton
      showDivider
    >
      <Box display="flex" flexDirection="column" mb={2}>
        <DataTable
          layoutMode="semantic"
          enableRowSelection={() => true}
          onRowSelectionChange={setSelectedRows}
          data={projectAssignments || []}
          state={{
            showSkeletons: false,
            rowSelection: selectedRows || [],
          }}
          columns={COLUMN_DEFS}
        />
      </Box>
      <Box display="flex" justifyContent="flex-end">
        <Button
          sx={{ alignSelf: "flex-end", marginBottom: 2 }}
          onClick={onSaveClick}
          variant="contained"
          disabled={!isDirty}
        >
          Save
        </Button>
      </Box>
    </Modal>
  );
};

export default AssignProjectsModal;
