import { Add, Delete } from "@mui/icons-material";
import {
  <PERSON>ton,
  DialogActions,
  FormControlLabel,
  Grid2,
  IconButton,
  Paper,
  Switch,
  Tooltip,
  Typography,
} from "@mui/material";
import { useMutation, useQuery } from "@tanstack/react-query";
import { format } from "date-fns-tz";
import React, { useMemo, useRef, useState } from "react";
import { NUMBER } from "src/app/constants";
import languageConfig from "src/configs/language/en.lang";
import { useForm } from "src/customHooks/useForm";
import CustomDateField from "src/modules/Common/FormInputs/CustomDateField";
import Modal from "src/modules/Common/Modal/Modal";
import AttendanceConfigListItem from "src/modules/Settings/components/Configuration/components/AttendanceConfigListItem";
import { LeaveTypeDefaultState } from "src/modules/Settings/components/Configuration/components/AttendanceConfiguration";
import { EmployeeAttendanceConfiguration } from "src/services/api_definitions/employeeAttendance.service";
import attendanceService from "src/services/attendance.service";
import validators from "src/utils/validators";
import { DataTableActionProps } from "./TeamCalendarView";

const { attendanceConfig: attendanceConfigLang } = languageConfig.tenants.tenantSettings;

type CurrentLeaveTypeDefaultState = LeaveTypeDefaultState & {
  currentStartDate: string | null;
  currentEndDate: string | null;
};

type FutureLeaveTypeDefaultState = LeaveTypeDefaultState & {
  futureStartDate: string | null;
  futureEndDate: string | null;
};

const defaultCurrentFormState: CurrentLeaveTypeDefaultState = {
  numberOfWorkingDays: "",
  workStartTime: "",
  workEndTime: "",
  minHoursHalfDay: null,
  minHoursFullDay: null,
  enforceWorkingHours: false,
  currentStartDate: null,
  currentEndDate: null,
  weekOff: [],
  enforcementType: attendanceConfigLang.enforceWorking.enforceLop.value,
};

const defaultFutureFormState: FutureLeaveTypeDefaultState = {
  numberOfWorkingDays: "",
  workStartTime: "",
  workEndTime: "",
  minHoursHalfDay: null,
  minHoursFullDay: null,
  enforceWorkingHours: false,
  weekOff: [],
  futureEndDate: null,
  futureStartDate: null,
  enforcementType: attendanceConfigLang.enforceWorking.enforceLop.value,
};

const CURRENT_CONFIG_DISABLED_STATES = {
  enforceWorkingHours: true,
  numberOfWorkingDays: true,
  workStartTime: true,
  minHoursFullDay: true,
  minHoursHalfDay: true,
  enforcementType: true,
  workEndTime: true,
};

type ConfigStates = "add-future" | "delete-future";

const EditAttendanceConfig: React.FC<
  Pick<DataTableActionProps, "onClose" | "teamCalendarDetails"> & {
    displayName: string;
    employeeCode: string;
    canReplicateForAll?: boolean;
  }
> = ({ onClose, employeeCode, displayName, canReplicateForAll = true }) => {
  const ref = useRef<HTMLInputElement | null>(null);
  const [configState, setConfigState] = useState<ConfigStates>("add-future");
  const { data: attendanceConfigDetails, refetch: refetchAttendanceConfig } = useQuery(
    ["get-employee-attendance-config", employeeCode],
    async () => {
      const details = await attendanceService.getEmployeeAttendanceDetails(employeeCode);
      setConfigState(details?.future ? "delete-future" : "add-future");
      return details;
    },
    {
      retryOnMount: false,
      refetchInterval: false,
      refetchOnWindowFocus: false,
    },
  );
  const isDefaultOrgConfig = useMemo(() => {
    return !attendanceConfigDetails?.current?.start_date && !attendanceConfigDetails?.current?.end_date;
  }, [attendanceConfigDetails]);

  const defaultCurrentConfigResponse = useMemo(() => {
    return {
      numberOfWorkingDays: attendanceConfigDetails?.current?.num_working_days,
      workStartTime: attendanceConfigDetails?.current?.work_start_time,
      workEndTime: attendanceConfigDetails?.current?.work_end_time,
      weekOff: attendanceConfigDetails?.current?.week_offs,
      minHoursHalfDay: attendanceConfigDetails?.current?.min_hours_half_day,
      minHoursFullDay: attendanceConfigDetails?.current?.min_hours_full_day,
      enforceWorkingHours: attendanceConfigDetails?.current?.enforce_working_hours,
      currentStartDate: attendanceConfigDetails?.current?.start_date
        ? new Date(attendanceConfigDetails?.current?.start_date)
        : null,
      currentEndDate: attendanceConfigDetails?.current?.end_date
        ? new Date(attendanceConfigDetails?.current?.end_date)
        : null,
      enforcementType:
        attendanceConfigDetails?.current?.working_hours_lapse_enforcement ||
        attendanceConfigLang.enforceWorking.enforceLop.value,
    };
  }, [attendanceConfigDetails?.current]);

  const defaultFutureConfigResponse = useMemo(() => {
    return {
      futureStartDate: attendanceConfigDetails?.future?.start_date
        ? new Date(attendanceConfigDetails?.future?.start_date)
        : null,
      futureEndDate: attendanceConfigDetails?.future?.end_date
        ? new Date(attendanceConfigDetails?.future?.end_date)
        : null,
      numberOfWorkingDays: attendanceConfigDetails?.future?.num_working_days,
      workStartTime: attendanceConfigDetails?.future?.work_start_time,
      workEndTime: attendanceConfigDetails?.future?.work_end_time,
      weekOff: attendanceConfigDetails?.future?.week_offs,
      minHoursHalfDay: attendanceConfigDetails?.future?.min_hours_half_day,
      minHoursFullDay: attendanceConfigDetails?.future?.min_hours_full_day,
      enforceWorkingHours: attendanceConfigDetails?.future?.enforce_working_hours,
      enforcementType:
        attendanceConfigDetails?.future?.working_hours_lapse_enforcement ||
        attendanceConfigLang.enforceWorking.enforceLop.value,
    };
  }, [attendanceConfigDetails?.future]);

  const { formDetails, formErrors, handleChange, setFormDetail } = useForm({
    initialState: defaultCurrentConfigResponse.numberOfWorkingDays
      ? defaultCurrentConfigResponse
      : defaultCurrentFormState,
    isBulk: false,
    validations: {
      weekOff: [validators.validateWeekOff],
      numberOfWorkingDays: [validators.validateWorkingDays],
      workStartTime: [validators.validateTimeOnly],
      workEndTime: [],
      minHoursHalfDay: defaultCurrentConfigResponse.enforceWorkingHours ? [validators.validateMinHalfDayTime] : [],
      minHoursFullDay: defaultCurrentConfigResponse.enforceWorkingHours ? [validators.validateMinFullDayTime] : [],
      currentStartDate: [validators.validateInput],
      currentEndDate: [validators.validateDate],
      enforceWorkingHours: [],
      enforcementType: [],
    },
  });

  const {
    formDetails: futureFormDetails,
    formErrors: futureFormErrors,
    handleChange: handleFutureFormChange,
    setFormDetail: setFutureFormDetail,
  } = useForm({
    initialState: defaultFutureConfigResponse.numberOfWorkingDays
      ? defaultFutureConfigResponse
      : defaultFutureFormState,
    isBulk: false,
    validations: {
      weekOff: [validators.validateWeekOff],
      numberOfWorkingDays: [validators.validateWorkingDays],
      workStartTime: [validators.validateTimeOnly],
      workEndTime: [validators.validateTimeOnly],
      minHoursHalfDay: defaultFutureConfigResponse.enforceWorkingHours ? [validators.validateMinHalfDayTime] : [],
      minHoursFullDay: defaultFutureConfigResponse.enforceWorkingHours ? [validators.validateMinFullDayTime] : [],
      futureStartDate: [validators.validateDate],
      futureEndDate: [validators.validateDate],
      enforceWorkingHours: [],
      enforcementType: [],
    },
  });

  const typedCurrentFormDetails = formDetails as unknown as CurrentLeaveTypeDefaultState;
  const typedCurrentFormErrors = formErrors as Record<keyof CurrentLeaveTypeDefaultState, string>;

  const typedFutureFormDetails = futureFormDetails as unknown as FutureLeaveTypeDefaultState;
  const typedFutureFormErrors = futureFormErrors as Record<keyof FutureLeaveTypeDefaultState, string>;

  const hasErrorInFormFelds = (formDetail: Record<string, any>) => {
    if (configState === "add-future") {
      return false;
    }
    const newFormDetails = { ...formDetail };
    if (newFormDetails.enforceWorkingHours) {
      const hasError = validators.validateMinFullDayTime(newFormDetails.minHoursFullDay, newFormDetails);
      return !newFormDetails.minHoursFullDay || !newFormDetails.minHoursHalfDay || hasError !== null;
    }

    const defaultChecks =
      !newFormDetails.numberOfWorkingDays ||
      !newFormDetails.workStartTime ||
      !newFormDetails.workEndTime ||
      newFormDetails.weekOff.length + +newFormDetails.numberOfWorkingDays !== NUMBER.SEVEN;

    return defaultChecks;
  };

  const hasDefaultConfigErrors = useMemo(
    () => hasErrorInFormFelds(typedFutureFormDetails),
    [typedFutureFormDetails, configState],
  );

  const createPayload = (): EmployeeAttendanceConfiguration => {
    const current = {
      num_working_days: Number(typedCurrentFormDetails.numberOfWorkingDays),
      work_start_time: typedCurrentFormDetails.workStartTime,
      work_end_time: typedCurrentFormDetails.workEndTime,
      week_offs: typedCurrentFormDetails.weekOff,
      enforce_working_hours: typedCurrentFormDetails.enforceWorkingHours,
      min_hours_half_day: typedCurrentFormDetails.minHoursHalfDay || null,
      min_hours_full_day: typedCurrentFormDetails.minHoursFullDay || null,
      working_hours_lapse_enforcement: typedCurrentFormDetails.enforcementType,
      start_date: typedCurrentFormDetails?.currentStartDate
        ? format(typedCurrentFormDetails?.currentStartDate, "yyyy-MM-dd")
        : null,
      end_date: typedCurrentFormDetails?.currentEndDate
        ? format(typedCurrentFormDetails?.currentEndDate, "yyyy-MM-dd")
        : null,
    };
    const future = {
      num_working_days: Number(typedFutureFormDetails.numberOfWorkingDays),
      work_start_time: typedFutureFormDetails.workStartTime,
      work_end_time: typedFutureFormDetails.workEndTime,
      week_offs: typedFutureFormDetails.weekOff,
      enforce_working_hours: typedFutureFormDetails.enforceWorkingHours,
      min_hours_half_day: typedFutureFormDetails.minHoursHalfDay || null,
      min_hours_full_day: typedFutureFormDetails.minHoursFullDay || null,
      working_hours_lapse_enforcement: typedFutureFormDetails.enforcementType,
      start_date: typedFutureFormDetails?.futureStartDate
        ? format(typedFutureFormDetails?.futureStartDate, "yyyy-MM-dd")
        : null,
      end_date: typedFutureFormDetails?.futureEndDate
        ? format(typedFutureFormDetails?.futureEndDate, "yyyy-MM-dd")
        : null,
    };

    if (isDefaultOrgConfig && configState === "delete-future") {
      return {
        current: null,
        future,
      };
    }

    if (isDefaultOrgConfig && configState === "add-future") {
      return {
        current: null,
        future: null,
      };
    }
    if (configState === "add-future") {
      return {
        current,
        future: null,
      };
    }
    return {
      current,
      future,
    };
  };

  const upsertEmployeeAttendanceConfig = useMutation({
    mutationFn: async (replicate: boolean) =>
      attendanceService.upsertEmployeeAttendanceConfig(createPayload(), employeeCode || "", replicate),
    onSuccess: () => {
      onClose();
      refetchAttendanceConfig();
    },
  });

  const onSave = () => {
    const duplicateConfigForAll = ref?.current?.checked || false;
    upsertEmployeeAttendanceConfig.mutate(duplicateConfigForAll);
  };

  const onAddMoreClick = () => {
    setConfigState("delete-future");
  };

  const onDeleteClick = () => {
    setConfigState("add-future");
  };

  return (
    <Modal
      isOpen
      onClose={onClose}
      title={`Edit attendance configuration for ${displayName}`}
      fullWidth
      showBackButton
      showDivider
      actions={
        <DialogActions>
          <Button variant="outlined" onClick={onClose}>
            Cancel
          </Button>
          <Button disabled={hasDefaultConfigErrors} onClick={onSave} variant="contained">
            Save
          </Button>
        </DialogActions>
      }
    >
      <Grid2 container spacing={2} sx={{ mt: 2 }}>
        <Grid2 size={12}>
          <Paper elevation={2}>
            <Grid2 container spacing={3} p={2} pt={0}>
              <Grid2 size={12}>
                <Typography variant="h6">Current Config</Typography>
                <Typography variant="subtitle2">Current applied configuration</Typography>
              </Grid2>
              <Grid2 size={6} display={isDefaultOrgConfig ? "none" : "visible"}>
                <CustomDateField
                  name="currentStartDate"
                  title="Start Date"
                  value={typedCurrentFormDetails?.currentStartDate as unknown as Date}
                  disabled
                  onChange={(value) => setFormDetail("currentStartDate", value)}
                  slotProps={{
                    textField: {
                      size: "small",
                      fullWidth: true,
                    },
                  }}
                />
              </Grid2>
              <Grid2 size={6} display={isDefaultOrgConfig ? "none" : "visible"}>
                <CustomDateField
                  name="currentEndDate"
                  title="End Date"
                  disabled={isDefaultOrgConfig}
                  value={typedCurrentFormDetails?.currentEndDate as unknown as Date}
                  onChange={(value) => setFormDetail("currentEndDate", value)}
                  minDate={new Date()}
                  slotProps={{
                    textField: {
                      size: "small",
                      fullWidth: true,
                    },
                  }}
                />
              </Grid2>
              <AttendanceConfigListItem
                typedFormDetails={typedCurrentFormDetails}
                typedFormErrors={typedCurrentFormErrors}
                setFormDetail={setFormDetail}
                handleChange={handleChange}
                disabledStates={CURRENT_CONFIG_DISABLED_STATES}
              />
            </Grid2>
          </Paper>
        </Grid2>
        {configState === "add-future" && (
          <Grid2 size={12}>
            <Button variant="text" onClick={onAddMoreClick} startIcon={<Add fontSize="small" />}>
              Future Config
            </Button>
          </Grid2>
        )}
        {configState === "delete-future" && (
          <Grid2 size={12} mt={4}>
            <Paper elevation={2}>
              <Grid2 container spacing={3} p={2} pt={0}>
                <Grid2 size={11}>
                  <Typography variant="h6">Future Config</Typography>
                  <Typography variant="subtitle2">Set configuration for future</Typography>
                </Grid2>
                <Grid2 size={1}>
                  <Tooltip title="Delete future config">
                    <IconButton onClick={onDeleteClick}>
                      <Delete color="error" />
                    </IconButton>
                  </Tooltip>
                </Grid2>
                <Grid2 size={6}>
                  <CustomDateField
                    name="futureStartDate"
                    title="Start Date"
                    minDate={new Date(new Date().setDate(new Date().getDate() + 1))}
                    value={typedFutureFormDetails?.futureStartDate as unknown as Date}
                    onChange={(value) => setFutureFormDetail("futureStartDate", value)}
                    required
                    slotProps={{
                      textField: {
                        size: "small",
                        fullWidth: true,
                        required: true,
                      },
                    }}
                  />
                </Grid2>
                <Grid2 size={6}>
                  <CustomDateField
                    name="futureEndDate"
                    title="End Date"
                    minDate={(typedFutureFormDetails?.futureStartDate as unknown as Date) || new Date()} // Ensures end date is >= start date
                    value={typedFutureFormDetails?.futureEndDate as unknown as Date}
                    onChange={(value) => setFutureFormDetail("futureEndDate", value)}
                    required
                    slotProps={{
                      textField: {
                        size: "small",
                        fullWidth: true,
                        required: true,
                      },
                    }}
                  />
                </Grid2>
                <AttendanceConfigListItem
                  typedFormDetails={typedFutureFormDetails}
                  typedFormErrors={typedFutureFormErrors}
                  setFormDetail={setFutureFormDetail}
                  handleChange={handleFutureFormChange}
                />
              </Grid2>
            </Paper>
          </Grid2>
        )}
        {canReplicateForAll && (
          <Grid2 size={12} mt={1}>
            <FormControlLabel control={<Switch inputRef={ref} />} label="Replicate for all reportees" />
          </Grid2>
        )}
      </Grid2>
    </Modal>
  );
};
export default EditAttendanceConfig;
