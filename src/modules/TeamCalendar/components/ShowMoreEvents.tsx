import { Box, Menu, Typography } from "@mui/material";
import React, { useState, MouseEvent, useMemo } from "react";
import CustomEvent from "src/modules/Calendar/components/CustomEvents/CustomEvent";
import { CalendarEventProps } from "src/services/api_definitions/calendar";

interface ShowMoreEventsProps {
  events: CalendarEventProps[];
}

const showMoreStyle = {
  root: {
    display: "flex",
    justifyContent: "center",
    alignItem: "center",
    margin: "-10px 0",
  },
  typography: {
    fontSize: "12px",
    height: "20px",
    color: "#00776f",
    cursor: "pointer",
    fontWeight: "600",
  },
};

const ShowMoreEvents: React.FC<ShowMoreEventsProps> = ({ events }) => {
  if (!events?.length) {
    return null;
  }
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const open = Boolean(anchorEl);
  const handleClick = (event: MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget);
  };
  const handleClose = () => {
    setAnchorEl(null);
  };

  const eventLenth = events?.length;
  const isMoreItems = eventLenth > 1;

  const remaninglist = useMemo(() => {
    return events?.slice(1)?.map((event, index) => (
      <Box key={index} sx={{ marginBottom: "10px", cursor: "pointer" }}>
        <CustomEvent event={event} />
      </Box>
    ));
  }, [events]);

  return (
    <>
      <Box sx={{ marginBottom: "10px", cursor: "pointer" }}>
        <CustomEvent event={events[0]} />
      </Box>
      {isMoreItems && (
        <Box sx={showMoreStyle.root}>
          <Typography sx={showMoreStyle.typography} onClick={handleClick}>
            {`${eventLenth - 1} More`}
          </Typography>
        </Box>
      )}
      <Menu
        id="basic-menu"
        anchorEl={anchorEl}
        open={open}
        onClose={handleClose}
        MenuListProps={{
          "aria-labelledby": "basic-button",
        }}
      >
        <Box sx={{ width: "150px", margin: "10px 10px" }}>{remaninglist as React.ReactNode}</Box>
      </Menu>
    </>
  );
};

export default ShowMoreEvents;
