export const DATE_FORMAT = "yyyy-MM-dd";

const EVENT_TYPES = {
  Leave: "Leave",
  Holiday: "Holiday",
  Attendance: "Attendance",
  Anniversary: "Anniversary",
  Birthday: "Birthday",
};

const LEAVE_STATUS = {
  Rejected: "Rejected",
  Approved: "Approve",
  Pending: "Pending",
  Canceled: "Canceled",
  CancellationRequested: "Cancellation Requested",
};

const TOOLBAR_VIEW = {
  MONTH: "month",
  WEEK: "week",
  DAY: "day",
};

const ATTENDACE_TYPES = {
  PRESENT: "Present",
  ABSENT: "Absent",
  HALF_DAY: "Half Day",
  REGULARISED: "Regularised",
  REGULARISATION_PENDING: "Regularisation Pending",
  ON_LEAVE: "On Leave",
  WEEK_OFF: "Week Off",
  REGULARISATION_REQUESTED: "Regularisation Requested",
  HOLIDAY: "Holiday",
  LOP_FULL: "Loss of Pay (Full)",
  LOP_HALF: "Loss of Pay (Half)",
  ON_LEAVE_WITHOUT_PAY: "On Leave (w/o pay)",
};

const DAYS_NAMES_LIST = ["SUNDAY", "MONDAY", "TUESDAY", "WEDNESDAY", "THURSDAY", "FRIDAY", "SATURDAY"];

const ATTENDACE_TITLE_TYPES = [
  ATTENDACE_TYPES.ABSENT,
  ATTENDACE_TYPES.HALF_DAY,
  ATTENDACE_TYPES.REGULARISED,
  ATTENDACE_TYPES.REGULARISATION_PENDING,
  ATTENDACE_TYPES.REGULARISATION_REQUESTED,
  ATTENDACE_TYPES.LOP_FULL,
  ATTENDACE_TYPES.LOP_HALF,
];

export { EVENT_TYPES, LEAVE_STATUS, TOOLBAR_VIEW, ATTENDACE_TYPES, ATTENDACE_TITLE_TYPES, DAYS_NAMES_LIST };
