import { Box } from "@mui/material";
import { useQuery } from "@tanstack/react-query";
import { format, getDay, lastDayOfMonth, parse, startOfMonth, startOfWeek } from "date-fns";
import React, { useMemo, useState } from "react";
import { Calendar, DateCellWrapperProps, ToolbarProps, View, dateFnsLocalizer } from "react-big-calendar";
import { useAppDispatch } from "src/customHooks/useAppDispatch";
import { useAppSelector } from "src/customHooks/useAppSelector";
import "react-big-calendar/lib/css/react-big-calendar.css";
import { BaseObject } from "src/app/global";
import CustomDateHeader from "src/modules/Calendar/components/CustomDateHeader";
import CustomDayHeader from "src/modules/Calendar/components/CustomDayHeader";
import CustomEvent from "src/modules/Calendar/components/CustomEvents/CustomEvent";
import CustomToolbar from "src/modules/Calendar/components/Toolbar/CustomeToolbar";
import { CalendarDateRangeProps, CalendarEventPayloadProps } from "src/services/api_definitions/calendar";
import { EventResponseProps } from "src/services/api_definitions/calendar";
import calendarServiceAPI from "src/services/calendar.service";
import { setMyCalendarEvents } from "src/store/slices/calendar.slice";
import DateCellWrapper from "./components/DateCellWrapper";
import TimeGutterHeader from "./components/TimeGutterHeader";
import WeekHeader from "./components/WeekHeader";
import { DATE_FORMAT, EVENT_TYPES, TOOLBAR_VIEW } from "./constants";
const localeType = Intl.DateTimeFormat().resolvedOptions().locale;
const ModuleName = require(`date-fns/locale`);

const locales = {
  [localeType]: ModuleName[localeType.split("-").join("")],
};

const localizer = dateFnsLocalizer({
  format,
  parse,
  startOfWeek,
  getDay,
  locales,
});

const myCalendarStyle = {
  "& .rbc-calendar .rbc-date-cell": {
    padding: "10px 0 0 10px",
    textAlign: "left",
    fontSize: " 14px",
  },
  "&  .rbc-calendar .rbc-event ": {
    backgroundColor: "#ffffff",
  },
  "& .rbc-row-segment": {
    padding: "0 5px",
  },
  "& .rbc-day-bg": {
    position: "relative",
  },
  "& .rbc-time-view .rbc-row": {
    minHeight: "40px",
  },
  "& .rbc-month-view, .rbc-time-view": {
    borderRadius: "5px",
  },
};

export const createCalendarPayload = (date: Date = new Date()) => {
  return {
    start_date: format(startOfMonth(date), DATE_FORMAT),
    end_date: format(lastDayOfMonth(date), DATE_FORMAT),
  };
};

const DefaultDateCellWrapper = (props: DateCellWrapperProps) => <span className={`rbc-day-bg`}>{props.children}</span>;

export default function MyCalendar() {
  const dispatch = useAppDispatch();
  const [calendarPayload, setCalendarPayload] = useState<CalendarEventPayloadProps>(createCalendarPayload());
  const [selectedView, setSelectedView] = useState<View>("month");

  const { myCalendarEvents } = useAppSelector((state) => state.calendar);
  useQuery({
    queryKey: ["my-calendar", calendarPayload],
    queryFn: async () => {
      const response = await calendarServiceAPI.getCalendarEvents(calendarPayload as CalendarEventPayloadProps);
      dispatch(setMyCalendarEvents(response as EventResponseProps));
      return response;
    },
    retryOnMount: false,
    refetchInterval: false,
    refetchOnWindowFocus: false,
  });

  const { components, defaultDate } = useMemo(
    () => ({
      components: {
        toolbar: (props: ToolbarProps) => <CustomToolbar selectedView={selectedView} {...props} />,
        event: CustomEvent,
        timeGutterHeader: TimeGutterHeader,
        dateCellWrapper: selectedView === TOOLBAR_VIEW.MONTH ? DateCellWrapper : DefaultDateCellWrapper,
        month: {
          header: CustomDayHeader,
          dateHeader: CustomDateHeader,
        },
        week: {
          header: WeekHeader,
        },
      },
      defaultDate: new Date(),
    }),
    [selectedView],
  );

  const handleRangeChange = (dateRange: Date[] | CalendarDateRangeProps) => {
    const range: CalendarEventPayloadProps = { start_date: "", end_date: "" };
    if (Array.isArray(dateRange)) {
      (range.start_date = format(dateRange[0], DATE_FORMAT)),
        (range.end_date = format(dateRange[dateRange?.length - 1], DATE_FORMAT));
    } else {
      (range.start_date = format(dateRange.start, DATE_FORMAT)), (range.end_date = format(dateRange.end, DATE_FORMAT));
    }

    setCalendarPayload(range);
  };

  const onViewChangeHandler = (view: View) => {
    setSelectedView(view);
  };

  return (
    <Box sx={myCalendarStyle}>
      <Calendar
        dayLayoutAlgorithm="no-overlap"
        localizer={localizer}
        events={myCalendarEvents.filter((event) => event.type !== EVENT_TYPES.Attendance)}
        startAccessor="start"
        endAccessor="end"
        view={selectedView}
        defaultDate={defaultDate}
        style={{ height: 850, width: "100%", overflow: "hidden" }}
        popup={true}
        onView={onViewChangeHandler}
        onRangeChange={handleRangeChange}
        components={components as BaseObject}
      />
    </Box>
  );
}
