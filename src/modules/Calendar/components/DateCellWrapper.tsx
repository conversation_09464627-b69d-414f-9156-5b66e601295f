import { Box, Tooltip, Typography } from "@mui/material";
import { isAfter } from "date-fns";
import React from "react";
import { DateCellWrapperProps } from "react-big-calendar";
import { NUMBER } from "src/app/constants";
import { useAppSelector } from "src/customHooks/useAppSelector";
import { CalendarEventProps } from "src/services/api_definitions/calendar";
import { truncatedString } from "src/utils/dataUtils";
import { convertTimeToAMPMWithZonedTime } from "src/utils/dateUtils";
import { ATTENDACE_TITLE_TYPES, ATTENDACE_TYPES, EVENT_TYPES } from "../constants";

interface MyProps extends DateCellWrapperProps {
  events: any;
}

interface FilterEventTypes {
  type: string;
  start: Date;
}

const eventColor = {
  [EVENT_TYPES.Holiday]: {
    backgroundColor: "#E6F8F4",
  },
  [ATTENDACE_TYPES.ABSENT]: {
    backgroundColor: "#FFF1F1",
    color: "#DC0B11",
    fontSize: "10px",
  },
  [ATTENDACE_TYPES.REGULARISED]: {
    backgroundColor: "#FFFFFF",
    color: "#007F6F",
    fontSize: "10px",
  },
  [ATTENDACE_TYPES.REGULARISATION_PENDING]: {
    backgroundColor: "#FFFFFF",
    color: "#DA6B00",
    fontSize: "10px",
  },
  [ATTENDACE_TYPES.REGULARISATION_REQUESTED]: {
    backgroundColor: "#FFFFFF",
    color: "#DA6B00",
    fontSize: "10px",
  },
  [ATTENDACE_TYPES.HALF_DAY]: {
    backgroundColor: "#FFF6EA",
    color: "#DA6B00",
    fontSize: "10px",
  },
  [ATTENDACE_TYPES.LOP_FULL]: {
    backgroundColor: "#FFF1F1",
    color: "#DC0B11",
    fontSize: "10px",
  },
  [ATTENDACE_TYPES.LOP_HALF]: {
    backgroundColor: "#FFF1F1",
    color: "#DC0B11",
    fontSize: "10px",
  },
};

const timeStyle = {
  display: "flex",
  justifyContent: "center",
  position: "absolute",
  left: "50%",
  bottom: "5px",
  transform: "translateX(-50%)",
  margin: "0 auto",
  fontWeight: "400",
  color: "#000000",
  width: "100%",
};

const titleStyle = {
  display: "flex",
  justifyContent: "flex-end",
  fontSize: "10px",
  fontWeight: "400",
  margin: "12px 5px 0 0",
  position: "relative",
  zIndex: 5,
};

const DateCellWrapper: React.FC<MyProps> = ({ value, children }) => {
  const { myCalendarEvents } = useAppSelector((state) => state.calendar);

  const getTimeString = (event: CalendarEventProps) => {
    if (!isAfter(new Date(), new Date(event.start))) {
      return null;
    }
    if (
      event?.title === ATTENDACE_TYPES.ABSENT ||
      (![ATTENDACE_TYPES.WEEK_OFF, ATTENDACE_TYPES.HOLIDAY].includes(event?.title) &&
        !event?.checkinTime &&
        !event?.checkoutTime)
    ) {
      return "-- : --";
    } else if ([ATTENDACE_TYPES.WEEK_OFF, ATTENDACE_TYPES.HOLIDAY].includes(event?.title) && !event?.checkinTime) {
      return null;
    } else {
      return `${convertTimeToAMPMWithZonedTime(event?.checkinTime as string, "")} - ${convertTimeToAMPMWithZonedTime(event?.checkoutTime as string, "")}`;
    }
  };

  const filteredEvents = myCalendarEvents?.filter(
    ({ type, start }: FilterEventTypes) =>
      [EVENT_TYPES.Holiday, EVENT_TYPES.Attendance].includes(type) && value.toDateString() === start.toDateString(),
  )[0];

  const currentType = filteredEvents?.type === EVENT_TYPES.Attendance ? filteredEvents?.title : filteredEvents?.type;

  const colorStyle = filteredEvents ? eventColor[currentType as keyof typeof eventColor] : null;
  return (
    <Box className={`rbc-day-bg`} sx={colorStyle}>
      {filteredEvents && ATTENDACE_TITLE_TYPES.includes(filteredEvents.title) && (
        <Tooltip title={filteredEvents.title} placement="top" key={filteredEvents.title}>
          <Box sx={titleStyle}>{truncatedString(filteredEvents.title, NUMBER.EIGHTEEN)}</Box>
        </Tooltip>
      )}
      {children}
      {filteredEvents && [EVENT_TYPES.Attendance].includes(filteredEvents.type) && (
        <Tooltip title={filteredEvents.duration} placement="top" key={filteredEvents.duration}>
          <Box sx={timeStyle}>
            <Typography fontSize={10}>{getTimeString(filteredEvents)}</Typography>
          </Box>
        </Tooltip>
      )}
    </Box>
  );
};

export default DateCellWrapper;
