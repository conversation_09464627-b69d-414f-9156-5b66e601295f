import { Box, Typography } from "@mui/material";
import React from "react";
import { DateLocalizer } from "react-big-calendar";

const weekHeaderStyles = {
  display: "flex",
  justifyContent: "center",
  alignItem: "center",
  padding: "10px",
};

const WeekHeader = ({ date, localizer }: { date: Date; localizer: DateLocalizer }) => {
  return (
    <Box sx={weekHeaderStyles}>
      <Typography fontSize={14}>{localizer.format(date, "d LLL")}</Typography>
    </Box>
  );
};

export default WeekHeader;
