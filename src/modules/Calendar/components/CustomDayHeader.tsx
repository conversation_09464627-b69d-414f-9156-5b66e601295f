import { Box } from "@mui/material";
import React from "react";
import { DAYS } from "src/app/constants";

interface CustomDayHeaderProps {
  label: string;
}

const headerStyle = {
  padding: "10px",
  color: "#667085",
  fontSize: "10px",
  textAlign: "left",
};

const CustomDayHeader: React.FC<CustomDayHeaderProps> = ({ label }) => {
  return <Box sx={headerStyle}>{DAYS[label as keyof typeof DAYS]}</Box>;
};

export default CustomDayHeader;
