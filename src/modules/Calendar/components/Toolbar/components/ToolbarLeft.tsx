import ArrowBackIosIcon from "@mui/icons-material/ArrowBackIos";
import ArrowForwardIosIcon from "@mui/icons-material/ArrowForwardIos";
import { Box, Button, IconButton } from "@mui/material";
import React from "react";
import { NavigateAction } from "react-big-calendar";
import languageConfig from "src/configs/language/en.lang";
import { styles as calendarStyles } from "../../styles";

interface CustomToolbarProps {
  onNavigate: (action: NavigateAction) => void;
}

const ToolbarLeftSection: React.FC<CustomToolbarProps> = ({ onNavigate }) => {
  const onClickPrevious = () => {
    onNavigate("PREV");
  };

  const onClickNext = () => {
    onNavigate("NEXT");
  };

  const onClickToday = () => {
    onNavigate("TODAY");
  };

  return (
    <Box>
      <IconButton sx={calendarStyles.toolbar.customIconButtonStyle} onClick={onClickPrevious}>
        <ArrowBackIosIcon sx={{ fontSize: 16 }} />
      </IconButton>
      <IconButton sx={calendarStyles.toolbar.customIconButtonStyle} onClick={onClickNext}>
        <ArrowForwardIosIcon sx={{ fontSize: 16 }} />
      </IconButton>
      <Button sx={calendarStyles.toolbar.todayButtonStyle} onClick={onClickToday}>
        {languageConfig.calendar.today}
      </Button>
    </Box>
  );
};

export default ToolbarLeftSection;
