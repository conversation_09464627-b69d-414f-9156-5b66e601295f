import { Button, ButtonGroup } from "@mui/material";
import React from "react";
import { View } from "react-big-calendar";
import languageConfig from "src/configs/language/en.lang";
import { TOOLBAR_VIEW } from "../../../constants";
import { styles as calendarStyles } from "../../styles";

interface CustomButtonGroupProps {
  onView: (view: View) => void;
  selectedView: View;
}

const ToolbarRightSection: React.FC<CustomButtonGroupProps> = ({ selectedView, onView }) => {
  const handleButtonClick = (view: View) => {
    onView(view);
  };

  return (
    <ButtonGroup>
      <Button
        sx={calendarStyles.toolbar.customButtonStyle}
        className={selectedView === TOOLBAR_VIEW.MONTH ? "Mui-selected" : ""}
        onClick={() => handleButtonClick(TOOLBAR_VIEW.MONTH as View)}
      >
        {languageConfig.calendar.month}
      </Button>
      <Button
        sx={calendarStyles.toolbar.customButtonStyle}
        className={selectedView === TOOLBAR_VIEW.WEEK ? "Mui-selected" : ""}
        onClick={() => handleButtonClick(TOOLBAR_VIEW.WEEK as View)}
      >
        {languageConfig.calendar.week}
      </Button>
      <Button
        sx={calendarStyles.toolbar.customButtonStyle}
        className={selectedView === TOOLBAR_VIEW.DAY ? "Mui-selected" : ""}
        onClick={() => handleButtonClick(TOOLBAR_VIEW.DAY as View)}
      >
        {languageConfig.calendar.day}
      </Button>
    </ButtonGroup>
  );
};

export default ToolbarRightSection;
