import { Box, InputLabel } from "@mui/material";
import React from "react";
import { ToolbarProps, View } from "react-big-calendar";
import { styles as calendarStyles } from "../styles";
import ToolbarLeftSection from "./components/ToolbarLeft";
import ToolbarRightSection from "./components/ToolbarRight";

interface CustomToolbarProps extends ToolbarProps {
  selectedView: View;
}

const CustomToolbar: React.FC<CustomToolbarProps> = ({ selectedView, label, onNavigate, onView }) => {
  return (
    <Box sx={calendarStyles.toolbar.root}>
      <Box>
        <ToolbarLeftSection onNavigate={onNavigate} />
      </Box>
      <InputLabel sx={{ color: "#000000" }}>{label}</InputLabel>
      <Box>
        <ToolbarRightSection selectedView={selectedView} onView={onView} />
      </Box>
    </Box>
  );
};

export default CustomToolbar;
