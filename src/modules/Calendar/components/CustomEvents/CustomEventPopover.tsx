import CloseOutlinedIcon from "@mui/icons-material/CloseOutlined";
import { Box, Grid, IconButton } from "@mui/material";
import Popover from "@mui/material/Popover";
import { format } from "date-fns";
import React from "react";
import {
  AnniversaryIcon,
  BirthdayIcon,
  ColorCalendar,
  HolidayIcon,
  InfoIcon,
  StatusIcon,
  TitleIcon,
} from "src/assets/icons.svg";
import { EVENT_TYPES, LEAVE_STATUS } from "../../constants";
import { CustomEventProps } from "./CustomEvent";

interface EventPopoverProps extends CustomEventProps {
  id: string | null;
  anchorEl: null | HTMLElement;
  open: boolean;
  setAnchorEl: (value: null) => void;
}

const eventIconList = {
  Approved: {
    titleIcon: TitleIcon,
    calendarIcon: ColorCalendar,
    infoIcon: InfoIcon,
    statusIcon: StatusIcon,
  },
  Pending: {
    titleIcon: TitleIcon,
    calendarIcon: ColorCalendar,
    infoIcon: InfoIcon,
    statusIcon: StatusIcon,
  },
  Rejected: {
    titleIcon: TitleIcon,
    calendarIcon: ColorCalendar,
    infoIcon: InfoIcon,
    statusIcon: StatusIcon,
  },
  Holiday: {
    titleIcon: TitleIcon,
    calendarIcon: ColorCalendar,
    infoIcon: HolidayIcon,
    statusIcon: StatusIcon,
  },
  Birthday: {
    titleIcon: TitleIcon,
    calendarIcon: ColorCalendar,
    infoIcon: BirthdayIcon,
    statusIcon: StatusIcon,
  },
  Anniversary: {
    titleIcon: TitleIcon,
    calendarIcon: ColorCalendar,
    infoIcon: AnniversaryIcon,
    statusIcon: StatusIcon,
  },
  [LEAVE_STATUS.CancellationRequested]: {
    titleIcon: TitleIcon,
    calendarIcon: ColorCalendar,
    infoIcon: InfoIcon,
    statusIcon: StatusIcon,
  },
  [LEAVE_STATUS.Canceled]: {
    titleIcon: TitleIcon,
    calendarIcon: ColorCalendar,
    infoIcon: InfoIcon,
    statusIcon: StatusIcon,
  },
};

const popoverStyle = {
  root: {
    padding: "10px 10px 20px 20px",
  },
  title: {
    fontSize: "16px",
    fontWeight: "600",
    marginLeft: "10px",
  },
  subTitle: {
    fontSize: "12px",
    marginLeft: "15px",
  },
  Approved: {
    backgroundColor: "#E6F8F4",
  },
  Pending: {
    backgroundColor: "#FFF6EA",
  },
  Rejected: {
    backgroundColor: "#FFF1F1",
  },
  Birthday: {
    backgroundColor: "#F5F3FF",
  },
  Holiday: {
    backgroundColor: "#F6FFFE",
  },
  Anniversary: {
    backgroundColor: "#F5F3FF",
  },
  [LEAVE_STATUS.CancellationRequested]: {
    backgroundColor: "#FFF6EA",
  },
  [LEAVE_STATUS.Canceled]: {
    backgroundColor: "#FFF1F1",
  },
};

const fillIconColor = {
  title: {
    Approved: "#008877",
    Rejected: "#FF3C41",
    Pending: "#DA6B00",
    Birthday: "#FF5F8A",
    Anniversary: "#7D69FF",
    Holiday: "#007F6F",
    [LEAVE_STATUS.CancellationRequested]: "#DA6B00",
  },
  status: {
    Approved: "#008877",
    Rejected: "#FF3C41",
    Pending: "#DA6B00",
    [LEAVE_STATUS.CancellationRequested]: "#DA6B00",
  },
};

const iconWrapperStyle = {
  display: "flex",
  justifyContent: "flex-start",
  alignItems: "center",
  marginBottom: "15px",
};

const CustomEventPopover: React.FC<EventPopoverProps> = ({ event, id, open = false, anchorEl, setAnchorEl }) => {
  const keyType = event.type === EVENT_TYPES.Leave ? event.subTitle : event.type;
  const onClose = () => setAnchorEl(null);
  let DisplayIcon = eventIconList[keyType as keyof typeof eventIconList];

  const leaveSubTitle = event.subTitle === LEAVE_STATUS.Rejected ? event.comment : event.reason || event.subTitle;

  const durationType = event.durationType && event.type === EVENT_TYPES.Leave ? `| ${event.durationType}` : "";
  let rootPopoverStyle = popoverStyle[keyType as keyof typeof popoverStyle];
  let titleFillIconColor = fillIconColor.title[keyType as keyof typeof fillIconColor.title];
  let statusFillIconColor = fillIconColor.status[keyType as keyof typeof fillIconColor.status];
  if (
    event?.type === EVENT_TYPES.Leave &&
    ![LEAVE_STATUS.Pending, LEAVE_STATUS.CancellationRequested].includes(event?.subTitle as string) &&
    !event?.isPaid
  ) {
    // Its an unpaid leave. Set the styling same as rejected
    DisplayIcon = eventIconList.Rejected;
    rootPopoverStyle = popoverStyle.Rejected;
    titleFillIconColor = fillIconColor.title.Rejected;
    statusFillIconColor = fillIconColor.status.Rejected;
  }

  return (
    <Popover
      id={id || ""}
      open={open}
      anchorEl={anchorEl}
      anchorOrigin={{
        vertical: "bottom",
        horizontal: "left",
      }}
      onClose={onClose}
      slotProps={{
        paper: {
          style: { width: "350px" },
        },
      }}
    >
      <Box sx={{ ...popoverStyle.root, ...rootPopoverStyle }}>
        <Grid container sx={{ gap: "16px", justifyContent: "flex-end", width: "auto" }}>
          <IconButton onClick={onClose} sx={{ backgroundColor: "#EDEDED" }} aria-label="close">
            <CloseOutlinedIcon fontSize="small" sx={{ borderRadius: "50%", color: "#667085" }} />
          </IconButton>
        </Grid>
        <Box sx={iconWrapperStyle}>
          {DisplayIcon?.titleIcon && <DisplayIcon.titleIcon width={20} height={20} fill={titleFillIconColor} />}
          <Box sx={popoverStyle.title}>{event.title}</Box>
        </Box>
        <Box sx={iconWrapperStyle}>
          {DisplayIcon?.calendarIcon && <DisplayIcon.calendarIcon width={20} height={20} fill={titleFillIconColor} />}
          <Box sx={popoverStyle.subTitle}>
            {format(event.start, "EEEE, MMMM dd")} {durationType}
          </Box>
        </Box>
        <Box sx={iconWrapperStyle}>
          {DisplayIcon?.infoIcon && <DisplayIcon.infoIcon width={20} height={20} />}
          <Box sx={popoverStyle.subTitle}>{leaveSubTitle}</Box>
        </Box>
        {![EVENT_TYPES.Birthday, EVENT_TYPES.Anniversary, EVENT_TYPES.Holiday].includes(keyType as string) && (
          <Box sx={iconWrapperStyle}>
            {DisplayIcon?.statusIcon && <DisplayIcon.statusIcon width={20} height={20} fill={statusFillIconColor} />}
            <Box sx={popoverStyle.subTitle}>{event.subTitle}</Box>
          </Box>
        )}
      </Box>
    </Popover>
  );
};

export default CustomEventPopover;
