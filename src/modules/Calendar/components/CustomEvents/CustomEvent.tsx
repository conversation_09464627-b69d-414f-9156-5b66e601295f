import React from "react";
import { Event as BigCalendarEvent } from "react-big-calendar";
import withAnchorElement from "src/HOC/withAnchorElement";
import { CalendarEventProps } from "src/services/api_definitions/calendar";
import HolidayCustomEvent from "./HolidayCustomEvent";
import LeaveCustomEvent from "./LeaveCustomEvent";

const customEventComponents = {
  Holiday: HolidayCustomEvent,
  Leave: LeaveCustomEvent,
  Anniversary: LeaveCustomEvent,
  Birthday: LeaveCustomEvent,
};

export interface CustomEventProps extends BigCalendarEvent {
  event: CalendarEventProps;
}
export interface LeaveCustomEventPropsType extends CustomEventProps {
  anchorEl?: HTMLElement | null;
  open?: boolean;
  poperId?: string | undefined;
  setAnchorEl?: (value: null) => void;
  openPoperHandler?: (event: React.MouseEvent<HTMLElement>) => void;
}

const CustomEvent: React.FC<LeaveCustomEventPropsType> = ({
  event,
  anchorEl,
  setAnchorEl,
  openPoperHandler,
  open,
  poperId,
}) => {
  const Event = customEventComponents[event.type as keyof typeof customEventComponents];
  return (
    <Event
      event={event}
      poperId={poperId}
      open={open}
      anchorEl={anchorEl}
      setAnchorEl={setAnchorEl}
      openPoperHandler={openPoperHandler}
    />
  );
};

export default withAnchorElement(CustomEvent);
