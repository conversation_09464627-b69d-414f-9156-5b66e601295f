import { Box } from "@mui/material";
import React from "react";
import { EVENT_TYPES, LEAVE_STATUS } from "../../constants";
import { styles as calendarStyles } from "../styles";
import { LeaveCustomEventPropsType } from "./CustomEvent";
import CustomEventPopover from "./CustomEventPopover";

const { leaveStyles, rootLeaveStyle, rootContainerStyle, titleStyle, subTitleStyle } = calendarStyles.customEventStyle;

const LeaveCustomEvent: React.FC<LeaveCustomEventPropsType> = ({
  event,
  anchorEl = null,
  setAnchorEl = (_value) => {},
  openPoperHandler = () => {},
  open = false,
  poperId = null,
}) => {
  const statusKey = event?.subTitle;
  let myStyle = leaveStyles[statusKey as keyof typeof leaveStyles];
  let rootStyle = rootLeaveStyle[statusKey as keyof typeof rootLeaveStyle];
  if (
    event?.type === EVENT_TYPES.Leave &&
    ![LEAVE_STATUS.Pending, LEAVE_STATUS.CancellationRequested].includes(event?.subTitle as string) &&
    !event?.isPaid
  ) {
    // Its an unpaid leave. Set the styling same as rejected
    myStyle = leaveStyles.Rejected;
    rootStyle = rootLeaveStyle.Rejected;
  }
  const combinedStyle = {
    minHeight: "40px",
    ...rootStyle,
    ...rootContainerStyle,
  };

  return (
    <>
      <Box sx={combinedStyle} onClick={openPoperHandler}>
        <Box sx={myStyle}>
          <Box sx={titleStyle}>{event.title}</Box>
          <Box sx={subTitleStyle}>{event.subTitle}</Box>
        </Box>
      </Box>
      <CustomEventPopover id={poperId} open={open} event={event} anchorEl={anchorEl} setAnchorEl={setAnchorEl} />
    </>
  );
};

export default LeaveCustomEvent;
