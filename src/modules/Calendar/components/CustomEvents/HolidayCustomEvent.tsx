import { Box } from "@mui/material";
import React from "react";
import { LeaveCustomEventPropsType } from "./CustomEvent";
import CustomEventPopover from "./CustomEventPopover";

const holidayStyle = {
  backgroundColor: "#007F6F",
  color: "#FFFFFF",
  fontSize: "12px",
  borderRadius: "5px",
  padding: "5px",
  textAlign: "center",
  minHeight: "40px",
};

const titleStyle = {
  fontSize: "10px",
  fontWeight: "600",
};

const subTitleStyle = {
  fontSize: "10px",
};

const HolidayCustomEvent: React.FC<LeaveCustomEventPropsType> = ({
  event,
  anchorEl = null,
  setAnchorEl = () => {},
  openPoperHandler = () => {},
  open = false,
  poperId = "",
}) => {
  return (
    <>
      <Box sx={holidayStyle} onClick={openPoperHandler}>
        <Box sx={titleStyle}>{event.title}</Box>
        <Box sx={subTitleStyle}>{event.subTitle}</Box>
      </Box>
      <CustomEventPopover id={poperId} open={open} event={event} anchorEl={anchorEl} setAnchorEl={setAnchorEl} />
    </>
  );
};

export default HolidayCustomEvent;
