import { InputLabel } from "@mui/material";
import { getDay } from "date-fns";
import React from "react";
import { useAppSelector } from "src/customHooks/useAppSelector";
import { compareTwoDatesWithoutTime } from "src/utils/dateUtils";
import { DAYS_NAMES_LIST } from "../constants";

const highlightDateStyle = {
  backgroundColor: "#007F6F",
  color: "#ffffff",
  borderRadius: "50%",
  height: "25px",
  width: "25px",
  justifyContent: "center",
  display: "flex",
  alignItems: "center",
  margin: "-5px",
  fontSize: "14px",
};

interface CustomDateHeaderPropsType {
  date: Date;
  label: string;
}

const CustomDateHeader: React.FC<CustomDateHeaderPropsType> = ({ date, label }) => {
  const { weekOffs } = useAppSelector((state) => state.calendar);
  const isWeekEnd = weekOffs?.includes(DAYS_NAMES_LIST[getDay(date)]);

  const highlightDate = compareTwoDatesWithoutTime(new Date(), date);
  if (!(highlightDate || isWeekEnd)) {
    return label;
  }

  return (
    <InputLabel
      sx={highlightDate ? highlightDateStyle : { fontSize: "14px", color: isWeekEnd ? "#999999" : "#000000" }}
    >
      {label}
    </InputLabel>
  );
};

export default CustomDateHeader;
