import { LEAVE_STATUS } from "../constants";

export const styles = {
  toolbar: {
    root: {
      display: "flex",
      justifyContent: "space-between",
      alignItems: "center",
      borderBottom: "1px solid #EDEDED",
      margin: "0 -24px 20px",
      padding: "0 24px",
    },
    customIconButtonStyle: {
      backgroundColor: "#E6F2F1",
      color: "#6b6b6b",
      height: "36px",
      margin: "10px",
      "&:hover": {
        backgroundColor: "#dce8e6",
      },
      borderRadius: "5px",
      "&:first-of-type": {
        marginLeft: 0,
      },
    },
    customButtonStyle: {
      borderRadius: "5px",
      height: "36px",
      border: "none",
      "&.Mui-selected": {
        backgroundColor: "#007F6F",
        color: "#fff",
        border: "none",
      },
      "&:not(.Mui-selected)": {
        backgroundColor: "#f9f9f9",
        color: "#667085",
      },
      "&:not(.Mui-selected):hover": {
        backgroundColor: "#e0e0e0",
        border: "none",
      },
      "&:first-of-type": {
        borderTopLeftRadius: "5px",
        borderBottomLeftRadius: "5px",
        // borderRight: "2px solid #EDEDED",
      },
      "&:last-of-type": {
        borderTopRightRadius: "5px",
        borderBottomRightRadius: "5px",
        // borderLeft: "1px solid #EDEDED",
      },
      fontWeight: "400",
    },
    todayButtonStyle: {
      backgroundColor: "#E6F2F1",
      color: "#000",
      height: "36px",
      margin: "0px 15px",
      padding: "5px 24px",
      "&:hover": {
        backgroundColor: "#dce8e6",
      },
      borderRadius: "5px",
      textTransform: "none",
      fontWeight: "400",
    },
  },
  customEventStyle: {
    "& .rbc-calendar .rbc-day-bg": {
      backgroundColor: "red",
    },
    rootContainerStyle: {
      display: "flex",
      justifyContent: "space-between",
      alignItems: "center",
    },
    rootLeaveStyle: {
      Approved: {
        padding: "5px",
        backgroundColor: "#E6F8F4",
        borderRadius: "5px",
      },
      Pending: {
        padding: "5px",
        borderRadius: "5px",
        backgroundColor: "#FFF1DF",
      },
      Rejected: {
        padding: "5px",
        borderRadius: "5px",
        backgroundColor: "#FEE2E1",
      },
      Birthday: {
        padding: "5px",
        borderRadius: "5px",
        backgroundColor: "#ECE9FF",
      },
      Anniversary: {
        padding: "5px",
        borderRadius: "5px",
        backgroundColor: "#EAF5FF",
      },
      [LEAVE_STATUS.CancellationRequested]: {
        padding: "5px",
        borderRadius: "5px",
        backgroundColor: "#FFF1DF",
      },
      [LEAVE_STATUS.Canceled]: {
        padding: "5px",
        borderRadius: "5px",
        backgroundColor: "#FEE2E1",
      },
    },
    leaveStyles: {
      Approved: {
        color: "#008877",
        borderLeft: "2px solid #008877",
      },
      Pending: {
        color: "#DA6B00",
        borderLeft: "2px solid #DA6B00",
      },
      Rejected: {
        color: "#DC0B11",
        borderLeft: "2px solid #DC0B11",
      },
      Birthday: {
        color: "#FF5F8A",
        borderLeft: "2px solid #FF5F8A",
      },
      Anniversary: {
        color: "#7D69FF",
        borderLeft: "2px solid #7D69FF",
      },
      [LEAVE_STATUS.CancellationRequested]: {
        color: "#DA6B00",
        borderLeft: "2px solid #DA6B00",
      },
      [LEAVE_STATUS.Canceled]: {
        color: "#DC0B11",
        borderLeft: "2px solid #DC0B11",
      },
    },
    titleStyle: {
      fontSize: "12px",
      marginLeft: "5px",
    },
    subTitleStyle: {
      fontSize: "10px",
      color: "#42526B",
      marginLeft: "5px",
    },
  },
};
