import { SxProps, Theme } from "@mui/material";
import { isDueSoon, isDueToday, isOverdue } from "src/utils/dateUtils";

export interface DueStatusStyling {
  cardSx: SxProps<Theme>;
  dueDateColor: string;
  dueDateBgColor?: string;
  borderColor?: string;
  glowEffect?: boolean;
}

export const getDueStatusStyling = (dueDate: string): DueStatusStyling => {
  if (!dueDate) {
    return {
      cardSx: {},
      dueDateColor: "text.secondary",
    };
  }

  if (isOverdue(dueDate)) {
    return {
      cardSx: {
        borderLeft: "3px solid #E57373",
        backgroundColor: "#FAFAFA",
        "&:hover": {
          boxShadow: "0 4px 16px rgba(0, 0, 0, 0.1)",
          transform: "translateY(-2px)",
        },
        transition: "all 0.3s ease-in-out",
      },
      dueDateColor: "#C62828",
      dueDateBgColor: "#FFEBEE",
      borderColor: "#E57373",
    };
  }

  if (isDueToday(dueDate)) {
    return {
      cardSx: {
        borderLeft: "4px solid #F49025",
        backgroundColor: "#FFF6DA",
        boxShadow: "0 2px 8px rgba(244, 144, 37, 0.15)",
        "&:hover": {
          boxShadow: "0 4px 16px rgba(244, 144, 37, 0.25)",
          transform: "translateY(-2px)",
        },
        transition: "all 0.3s ease-in-out",
        animation: "pulse 2s infinite",
        "@keyframes pulse": {
          "0%": {
            boxShadow: "0 2px 8px rgba(244, 144, 37, 0.15)",
          },
          "50%": {
            boxShadow: "0 4px 16px rgba(244, 144, 37, 0.3)",
          },
          "100%": {
            boxShadow: "0 2px 8px rgba(244, 144, 37, 0.15)",
          },
        },
      },
      dueDateColor: "#F49025",
      dueDateBgColor: "#FFF6DA",
      borderColor: "#F49025",
      glowEffect: true,
    };
  }

  if (isDueSoon(dueDate)) {
    return {
      cardSx: {
        borderLeft: "4px solid #619FFC",
        backgroundColor: "#F0F7FF",
        boxShadow: "0 2px 8px rgba(97, 159, 252, 0.15)",
        "&:hover": {
          boxShadow: "0 4px 16px rgba(97, 159, 252, 0.25)",
          transform: "translateY(-2px)",
        },
        transition: "all 0.3s ease-in-out",
      },
      dueDateColor: "#619FFC",
      dueDateBgColor: "#E3F2FD",
      borderColor: "#619FFC",
    };
  }

  return {
    cardSx: {
      "&:hover": {
        boxShadow: "0 4px 16px rgba(0, 0, 0, 0.1)",
        transform: "translateY(-2px)",
      },
      transition: "all 0.3s ease-in-out",
    },
    dueDateColor: "text.secondary",
  };
};

export const getPeriodDisplayStyling = (): SxProps<Theme> => ({
  display: "flex",
  alignItems: "center",
  gap: 0.5,
  color: "text.secondary",
});

export const getDueDateChipStyling = (dueDate: string): SxProps<Theme> => {
  const styling = getDueStatusStyling(dueDate);

  return {
    display: "inline-flex",
    alignItems: "center",
    gap: 0.5,
    padding: "4px 8px",
    borderRadius: "6px",
    fontSize: "0.75rem",
    fontWeight: 500,
    backgroundColor: styling.dueDateBgColor || "#F9F9F9",
    color: styling.dueDateColor,
    border: styling.borderColor ? `1px solid ${styling.borderColor}` : "1px solid #E5E5E5",
    minWidth: "fit-content",
  };
};

export const getOverdueChipStyling = (): SxProps<Theme> => ({
  display: "flex",
  flexDirection: "column",
  gap: 0.25,
  padding: "4px 8px",
  borderRadius: "6px",
  backgroundColor: "#FFF8F5",
  border: "1px solid #FFCCBC",
  minWidth: "fit-content",
});
