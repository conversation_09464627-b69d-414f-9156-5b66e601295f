import { Box, IconButton } from "@mui/material";
import { Background, BackgroundVariant, Controls, Edge, ReactFlow, useEdgesState, useNodesState } from "@xyflow/react";
import React, { useState, useCallback, useEffect } from "react";
import "@xyflow/react/dist/style.css";
import { ArrowBack } from "@mui/icons-material";
import FullscreenIcon from "@mui/icons-material/Fullscreen";
import FullscreenExitIcon from "@mui/icons-material/FullscreenExit";
import { useAppDispatch } from "src/customHooks/useAppDispatch";
import useToggle from "src/customHooks/useToggle";
import { setFullviewMode } from "src/store/slices/app.slice";
import { calculateNodePositions } from "src/utils/nodePositionUtils";
import {
  ColorTheme,
  CustomNodeRA,
  MappedResource,
  OBJECTIVE_COLORS,
  ResourceAllocationProps,
  defaultEdgeStyle,
} from "../ResourceAllocationTypes";
import ResourceAllocationNode from "./ResourceAllocationNode";

const LAYOUT_CONFIG = {
  horizontalSpacing: 400,
  verticalSpacing: 250,
};

function ResourceAllocation({ resourceData, onBack }: ResourceAllocationProps) {
  const [nodes, setNodes, onNodesChange] = useNodesState<CustomNodeRA>([]);
  const [edges, setEdges, onEdgesChange] = useEdgesState<Edge>([]);
  const [expandedNodes, setExpandedNodes] = useState<Set<string>>(new Set());

  const { toggle: isFullScreen, toggleState: toggleExpandFullScreen } = useToggle();

  const dispatch = useAppDispatch();

  useEffect(() => {
    dispatch(setFullviewMode(true));
    return () => {
      dispatch(setFullviewMode(false));
    };
  }, [dispatch]);

  const totalWeightage = resourceData.objective_resource_map.reduce((sum, obj) => sum + obj.estimated_weightage, 0);

  // Initialize nodes with objectives
  useEffect(() => {
    const objectiveNodes: CustomNodeRA[] = resourceData.objective_resource_map.map((objective, index) => ({
      id: `objective-${objective.goal_objective_id}`,
      type: "objectiveNode",
      data: {
        goal_objective_title: objective.goal_objective_title,
        estimated_weightage: objective.estimated_weightage,
        totalWeightage: totalWeightage,
        objectives: objective.mapped_resources.map((resource) => ({
          goal_objective_title: resource.employee.display_name,
          estimated_weightage: resource.estimated_weightage,
          employee: {
            display_pic: resource.employee.display_pic,
            display_name: resource.employee.display_name,
            employee_code: resource.employee.employee_code,
          },
        })),
        hasChildren: objective.mapped_resources.length > 0,
        colorTheme: OBJECTIVE_COLORS[index % OBJECTIVE_COLORS.length],
      },
      position: { x: 250 + index * 450, y: 100 },
      draggable: true,
    }));

    setNodes(objectiveNodes);
  }, [resourceData, setNodes]);

  // Get color theme for a node based on its root objective
  const getNodeColorTheme = useCallback(
    (nodeId: string): ColorTheme => {
      let currentId = nodeId;

      while (currentId) {
        if (currentId.startsWith("objective-")) {
          const index = resourceData.objective_resource_map.findIndex(
            (obj) => `objective-${obj.goal_objective_id}` === currentId,
          );
          return OBJECTIVE_COLORS[index % OBJECTIVE_COLORS.length];
        }
        currentId = edges.find((e) => e.target === currentId)?.source || "";
      }

      return OBJECTIVE_COLORS[0];
    },
    [edges, resourceData.objective_resource_map],
  );

  // Get all descendant nodes
  const getDescendants = useCallback(
    (nodeId: string): string[] => {
      const descendants: string[] = [];
      const queue = edges.filter((edge) => edge.source === nodeId).map((edge) => edge.target);

      while (queue.length > 0) {
        const currentId = queue.shift();
        if (currentId) {
          descendants.push(currentId);
          const childEdges = edges.filter((edge) => edge.source === currentId).map((edge) => edge.target);
          queue.push(...childEdges);
        }
      }

      return descendants;
    },
    [edges],
  );

  // Get mapped resources for a node
  const getMappedResources = useCallback(
    (clickedNode: CustomNodeRA): MappedResource[] => {
      // For objective nodes
      if (clickedNode.id.startsWith("objective-")) {
        const objectiveId = clickedNode.id.replace("objective-", "");
        return (
          resourceData.objective_resource_map.find((obj) => obj.goal_objective_id === objectiveId)?.mapped_resources ||
          []
        );
      }
      const pathToRoot = getPathToRoot(clickedNode.id);
      const objectiveId = pathToRoot[0]?.replace("objective-", "");
      if (!objectiveId) return [];

      const objective = resourceData.objective_resource_map.find((obj) => obj.goal_objective_id === objectiveId);
      if (!objective) return [];
      return followResourcePath(objective.mapped_resources, pathToRoot.slice(1));
    },
    [resourceData.objective_resource_map, edges],
  );

  const getPathToRoot = useCallback(
    (nodeId: string): string[] => {
      const path: string[] = [nodeId];
      let currentId = nodeId;

      while (currentId && !currentId.startsWith("objective-")) {
        const parentEdge = edges.find((e) => e.target === currentId);
        if (!parentEdge) break;
        currentId = parentEdge.source;
        path.unshift(currentId);
      }
      return path;
    },
    [edges],
  );

  const followResourcePath = (resources: MappedResource[], path: string[]): MappedResource[] => {
    if (path.length === 0) return resources;

    const currentCode = path[0].replace("resource-", "");
    const resource = resources.find((r) => r.goal_objective_id === currentCode);

    if (!resource) return [];

    return path.length === 1 ? resource.mapped_resources : followResourcePath(resource.mapped_resources, path.slice(1));
  };

  // Handle node collapse
  const collapseNode = useCallback(
    (nodeId: string) => {
      const descendantIds = getDescendants(nodeId);
      setNodes((nodes) => nodes.filter((node) => !descendantIds.includes(node.id)));
      setEdges((edges) => edges.filter((edge) => !descendantIds.includes(edge.target)));
      setExpandedNodes((prev) => {
        const next = new Set(prev);
        next.delete(nodeId);
        return next;
      });
    },
    [getDescendants, setNodes, setEdges],
  );

  // Handle node expansion
  const expandNode = useCallback(
    (clickedNode: CustomNodeRA) => {
      const mappedResources = getMappedResources(clickedNode);
      if (mappedResources.length === 0) return;

      const colorTheme = getNodeColorTheme(clickedNode.id);
      const positions = calculateNodePositions(clickedNode, mappedResources, nodes, LAYOUT_CONFIG);

      const newNodes: CustomNodeRA[] = mappedResources.map((resource, index) => ({
        id: `resource-${resource.goal_objective_id}`,
        type: "resourceNode",
        data: {
          display_name: resource.employee.display_name,
          job_title: resource.employee.job_title,
          display_pic: resource.employee.display_pic,
          estimated_weightage: resource.estimated_weightage,
          totalWeightage: clickedNode.data.totalWeightage,
          objectives: resource.mapped_resources.map((subResource) => ({
            goal_objective_title: subResource.employee.display_name,
            estimated_weightage: subResource.estimated_weightage,
            employee: {
              display_pic: subResource.employee.display_pic,
              display_name: subResource.employee.display_name,
              employee_code: subResource.employee.employee_code,
            },
          })),
          hasChildren: resource.mapped_resources.length > 0,
          colorTheme: colorTheme,
        },
        position: positions[index],
      }));

      const newEdges: Edge[] = newNodes.map((node) => ({
        id: `e-${clickedNode.id}-${node.id}`,
        source: clickedNode.id,
        target: node.id,
        sourceHandle: "bottom",
        targetHandle: "top",
        style: {
          ...defaultEdgeStyle,
          stroke: colorTheme.primary,
        },
      }));

      setNodes((nodes) => [...nodes, ...newNodes]);
      setEdges((edges) => [...edges, ...newEdges]);
      setExpandedNodes((prev) => new Set(prev).add(clickedNode.id));
    },
    [getMappedResources, getNodeColorTheme, nodes, setNodes, setEdges],
  );

  // Handle node click
  const onNodeClick = useCallback(
    (_event: any, clickedNode: CustomNodeRA) => {
      // Toggle node expansion/collapse
      if (expandedNodes.has(clickedNode.id)) {
        collapseNode(clickedNode.id);
      } else {
        expandNode(clickedNode);
      }
    },
    [expandedNodes, collapseNode, expandNode],
  );

  return (
    <Box
      style={{
        position: isFullScreen ? "fixed" : "relative",
        top: isFullScreen ? 0 : "auto",
        left: isFullScreen ? 0 : "auto",
        width: isFullScreen ? "100vw" : "100%",
        height: isFullScreen ? "100vh" : "80vh",
        background: "white",
        zIndex: isFullScreen ? 99999 : "auto",
      }}
    >
      <ReactFlow
        nodes={nodes}
        edges={edges}
        onNodesChange={onNodesChange}
        onEdgesChange={onEdgesChange}
        onNodeClick={onNodeClick}
        nodeTypes={{
          resourceNode: ResourceAllocationNode,
          objectiveNode: ResourceAllocationNode,
        }}
        fitView
        className="bg-teal-50"
        minZoom={0.1}
        maxZoom={2}
      >
        <Controls position="top-right" showZoom={true} showFitView={false} showInteractive={false} />
        {isFullScreen && (
          <IconButton
            onClick={onBack}
            sx={{
              position: "absolute",
              top: "10px",
              left: "10px",
              padding: "4px",
              zIndex: 9999,
              color: "grey",
              backgroundColor: "white",
            }}
          >
            <ArrowBack />
          </IconButton>
        )}
        <IconButton
          onClick={toggleExpandFullScreen}
          sx={{
            position: "absolute",
            top: "70px",
            right: "12px",
            padding: "4px",
            zIndex: 9999,
            color: "grey",
            backgroundColor: "white",
          }}
        >
          {isFullScreen ? <FullscreenExitIcon /> : <FullscreenIcon />}
        </IconButton>
        <Background variant={BackgroundVariant.Cross} gap={600} />
      </ReactFlow>
    </Box>
  );
}

export default ResourceAllocation;
