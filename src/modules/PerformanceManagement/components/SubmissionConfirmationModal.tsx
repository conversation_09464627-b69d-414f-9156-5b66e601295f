import { CrisisAlertOutlined } from "@mui/icons-material";
import { Box, Button, Typography } from "@mui/material";
import React from "react";
import Modal from "src/modules/Common/Modal/Modal";

interface SubmissionConfirmationModalProps {
  isModalOpen: boolean;
  title: string;
  description: string;
  onClose: () => void;
  onSubmit: () => void;
}

const SubmissionConfirmationModal: React.FC<SubmissionConfirmationModalProps> = ({
  isModalOpen,
  title,
  description,
  onClose,
  onSubmit,
}) => {
  return (
    <Modal
      isOpen={isModalOpen}
      title={
        <Box display="flex" gap={1}>
          <CrisisAlertOutlined />
          <Typography>{title}</Typography>
        </Box>
      }
      showBackButton
      onClose={onClose}
      showDivider={false}
      actions={
        <Box display={"flex"} gap={2} p={3} alignSelf="flex-end">
          <Button variant="outlined" onClick={onClose}>
            Cancel
          </Button>
          <Button variant="contained" onClick={onSubmit}>
            Submit
          </Button>
        </Box>
      }
    >
      <Typography>{description}</Typography>
    </Modal>
  );
};

export default SubmissionConfirmationModal;
