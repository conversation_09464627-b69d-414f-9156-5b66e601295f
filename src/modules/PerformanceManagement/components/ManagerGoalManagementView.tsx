import React from "react";
import { useAppSelector } from "src/customHooks/useAppSelector";
import CustomTabs, { TabType } from "src/modules/Common/CustomTabs/CustomTabs";
import GoalSettings from "src/modules/PerformanceManagement/components/GoalSettings";
import GoalRequests from "./GoalRequests";
import ResourceAllocationWrapper from "./ResourceAllocationWrapper";

const tabs: TabType[] = [
  {
    id: 0,
    label: "Goals Setting",
    component: <GoalSettings />,
  },
  {
    id: 1 as unknown as string,
    label: "Requests",
    component: <GoalRequests />,
  },
  {
    id: 2,
    label: "Resource Allocation",
    component: <ResourceAllocationWrapper />,
  },
];

const ManagerGoalManagementView = () => {
  const { isFullView } = useAppSelector((state) => state.app);
  const { userDetails } = useAppSelector((state) => state.userManagement);

  return (
    <CustomTabs
      tabs={userDetails?.is_manager ? tabs : tabs}
      currentTabIndex={tabs[0].id as unknown as number}
      hideTabBar={isFullView}
    />
  );
};

export default ManagerGoalManagementView;
