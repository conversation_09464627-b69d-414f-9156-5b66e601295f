import { <PERSON><PERSON><PERSON>, Percent } from "@mui/icons-material";
import {
  Box,
  Button,
  Divider,
  Grid2,
  IconButton,
  InputAdornment,
  Paper,
  SelectChangeEvent,
  Typography,
} from "@mui/material";
import { useMutation } from "@tanstack/react-query";
import React, { useEffect, useMemo, useState } from "react";
import CustomSelect from "src/modules/Common/FormInputs/CustomSelect";
import CustomTextField from "src/modules/Common/FormInputs/CustomTextField";
import { Objective, Requests } from "src/services/api_definitions/performanceManagement.service";
import performanceManagementService from "src/services/performanceManagement.service";
import { DD_MM_YYYY, formatDateToDayMonthYear, getIntlTimeToSpecifiedFormat } from "src/utils/dateUtils";
import DetailListItem from "./DetailListItem";
import { GoalRequestStates } from "./GoalRequests";
import SubmissionConfirmationModal from "./SubmissionConfirmationModal";

interface EmployeeGoalRequestActionableViewProps {
  selectedRequest: Requests;
  setCurrentMode: (state: GoalRequestStates) => void;
}

interface HeaderProps {
  name: string;
  title: string;
  goal: string;
  appliedDate?: string;
  submittedAt?: string;
}

interface EmployeeGoalDetailsProps {
  objective: Objective;
  onWeightageChange?: (ev: React.ChangeEvent<HTMLInputElement>) => void;
  areWeightagesValid?: boolean;
  weightage?: number;
  hideWeightage?: boolean;
  isApproved?: boolean;
}

interface ManagerActionProps {
  setCurrentSelectedAction: (state: string) => void;
  currentSelectedAction: string;
  onChange: (ev: SelectChangeEvent<string>) => void;
  setReason: (reason: string) => void;
  reason: string | null;
}

interface GoalRequestProps {
  setCurrentSelectedMode: (state: GoalRequestStates) => void;
}

export const EmployeeDetailHeader: React.FC<HeaderProps> = ({ appliedDate, goal, name, title, submittedAt }) => {
  const propertiesToDisplay = [
    {
      title: "Name",
      value: name,
    },
    {
      title: "Designation",
      value: title,
    },
    {
      title: "Goal Request",
      value: goal,
    },
    {
      title: "Applied Date",
      value: appliedDate,
    },
    {
      title: "Submitted On",
      value: submittedAt ? getIntlTimeToSpecifiedFormat(submittedAt || "", DD_MM_YYYY).formattedDate : null,
    },
  ];

  return (
    <Box
      bgcolor="#F2F3F3"
      borderRadius="10px"
      display="flex"
      alignItems="center"
      justifyContent="space-between"
      padding="20px 30px"
    >
      {propertiesToDisplay?.map(({ title, value }) => (
        <React.Fragment key={title}>
          {value && (
            <Box key={title} display="flex" gap={1}>
              <Typography color="#667085">{title}:</Typography>
              <Typography color="#000">{value}</Typography>
            </Box>
          )}
        </React.Fragment>
      ))}
    </Box>
  );
};
export const EmployeeGoalDetails: React.FC<EmployeeGoalDetailsProps> = ({
  objective,
  onWeightageChange,
  areWeightagesValid,
  weightage,
  hideWeightage = false,
  isApproved = false,
}) => {
  const showWeightageInReadOnly = !hideWeightage && isApproved && weightage;
  const preventScroll = (event: any) => {
    if (event.target.type === "number") {
      event.preventDefault();
    }
  };

  useEffect(() => {
    window.addEventListener("wheel", preventScroll, { passive: false });
    return () => window.removeEventListener("wheel", preventScroll);
  }, []);

  return (
    <Grid2 container spacing={2} component={Paper} elevation={2} p={2} m={1} borderRadius={4}>
      <Grid2 container spacing={2} alignItems="center" justifyContent="space-between" width="100%">
        <Grid2 size={12}>
          <DetailListItem title="Objective" value={objective?.title} />
        </Grid2>
        <Grid2 size={12}>
          <DetailListItem title="Description" value={objective?.description} />
        </Grid2>
        <Grid2 container spacing={2} display="flex" alignItems="center" justifyContent="space-between" width="100%">
          <Grid2 size={showWeightageInReadOnly ? 4 : 6}>
            <DetailListItem
              title="Planned Completion Date"
              value={formatDateToDayMonthYear(objective?.target_completion_date)}
            />
          </Grid2>
          <Grid2 size={showWeightageInReadOnly ? 4 : 6}>
            <DetailListItem
              title="Status"
              value={objective?.status}
              color={objective?.status !== "Deprioritised" ? "#000" : "red"}
              info={objective?.deprioritisation_reason}
            />
          </Grid2>
          {showWeightageInReadOnly && (
            <Grid2 size={4}>
              <DetailListItem title="Weightage" value={`${(weightage * 100).toFixed(0)}%`} />
            </Grid2>
          )}
        </Grid2>
      </Grid2>
      {objective?.linked_goal_objective && (
        <Paper elevation={3} sx={{ p: 2, width: "100%" }}>
          <Grid2 container spacing={2} alignItems="center">
            <Grid2 size={12}>
              <DetailListItem
                title={`${objective?.linked_goal_objective?.owner_name}'s Objective`}
                value={objective?.linked_goal_objective?.title}
              />
            </Grid2>
            <Grid2 size={6}>
              <DetailListItem
                title="Planned Completion Date"
                value={formatDateToDayMonthYear(objective?.linked_goal_objective?.target_completion_date)}
              />
            </Grid2>
            <Grid2 size={6}>
              <DetailListItem
                title="Status"
                value={objective?.linked_goal_objective?.status}
                color={objective?.linked_goal_objective?.status !== "Deprioritised" ? "#000" : "red"}
                info={objective?.linked_goal_objective?.deprioritisation_reason}
              />
            </Grid2>
          </Grid2>
        </Paper>
      )}
      {!hideWeightage && !isApproved && (
        <>
          <Grid2 size={12}>
            <Divider />
          </Grid2>
          <Grid2 size={12}>
            <CustomTextField
              size="small"
              type="number"
              inputMode="numeric"
              title="Weightage"
              placeholder="Weightage"
              onChange={onWeightageChange}
              error={!areWeightagesValid}
              defaultValue={weightage ? Math.floor(weightage * 100) : 0}
              helperText={areWeightagesValid ? null : "Weightage should be between 0 and 100 & total should be 100%"}
              InputProps={{
                endAdornment: (
                  <InputAdornment position="end">
                    <IconButton>
                      <Percent fontSize="small" />
                    </IconButton>
                  </InputAdornment>
                ),
              }}
            />
          </Grid2>
        </>
      )}
    </Grid2>
  );
};

export const MANAGER_ACTIONS = [
  {
    label: "Approve",
    value: "Approve",
  },
  {
    label: "Send Back",
    value: "Send Back",
  },
];

const Footer: React.FC<
  Omit<
    GoalRequestProps & {
      onSubmit: () => void;
      isSubmitDisabled: boolean;
    },
    "selectedGoalDetails"
  >
> = ({ setCurrentSelectedMode, isSubmitDisabled, onSubmit }) => (
  <Box alignSelf="flex-end" bottom={20} gap={2} component={Paper} elevation={0} display="flex" p={[2, 0]}>
    <Box display="flex" alignItems="center" gap={2}>
      <Button variant="outlined" onClick={() => setCurrentSelectedMode(GoalRequestStates.VIEW_REQUESTS)}>
        Cancel
      </Button>
      <Button disabled={isSubmitDisabled} variant="contained" onClick={onSubmit}>
        Submit
      </Button>
    </Box>
  </Box>
);

const ManagerAction: React.FC<ManagerActionProps> = ({ currentSelectedAction, onChange, setReason, reason }) => {
  return (
    <Box display="flex" flexDirection="column" component={Paper} gap={2} elevation={2} p={2} borderRadius={4}>
      <Box display="flex" flexDirection="column" gap={2}>
        <Typography color="#000" fontSize={18} fontWeight={500}>
          Manager Goal Review
        </Typography>
        <Divider />
      </Box>
      <Box width={320}>
        <CustomSelect
          options={MANAGER_ACTIONS}
          name="action"
          sx={{ width: 320 }}
          // biome-ignore lint/suspicious/noExplicitAny: <explanation>
          onChange={onChange as any}
          value={currentSelectedAction}
          size="small"
        />
      </Box>
      <Box>
        {currentSelectedAction === "Send Back" && (
          <CustomTextField
            name="reason"
            id="reason"
            title="Reason"
            placeholder="Add Reason"
            multiline
            value={reason}
            rows={3}
            required
            fullWidth
            color="primary"
            onChange={(ev: React.ChangeEvent<HTMLInputElement>) => setReason(ev.target.value)}
          />
        )}
      </Box>
    </Box>
  );
};

const Header: React.FC<
  Omit<
    EmployeeGoalRequestActionableViewProps & {
      title?: string;
    },
    "selectedRequest"
  >
> = ({ setCurrentMode, title = "Review Goals" }) => (
  <React.Fragment>
    <Box display="flex" gap={2} alignItems="center">
      <IconButton onClick={() => setCurrentMode(GoalRequestStates.VIEW_REQUESTS)}>
        <ArrowBack />
      </IconButton>
      <Typography>{title}</Typography>
    </Box>
    <Divider />
  </React.Fragment>
);

const EmployeeGoalRequestActionableView: React.FC<EmployeeGoalRequestActionableViewProps> = ({
  selectedRequest,
  setCurrentMode,
}) => {
  const [currentSelectedAction, setCurrentSelectedAction] = useState(MANAGER_ACTIONS[0].label);
  const [reasonForSendingBack, setReasonForSendingBack] = useState<string | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [objectiveWeightages, setObjectiveWeightages] = useState<Record<string, number>>({});

  useEffect(() => {
    const weightages = selectedRequest.goal.objectives.reduce((acc, objective) => {
      return {
        ...acc,
        [objective.id as string]: objective?.estimated_weightage ? objective?.estimated_weightage * 100 : 0,
      };
    }, {});
    setObjectiveWeightages(weightages);
  }, []);

  const approveRequest = useMutation({
    mutationFn: async (goalId: string) => {
      const modifiedObjectives = selectedRequest.goal.objectives.map((objective) => ({
        ...objective,
        estimated_weightage: Number(objectiveWeightages[objective.id as string]) / 100,
      }));
      return performanceManagementService.approveGoalRequest(goalId, modifiedObjectives);
    },
    onSuccess: () => {
      setCurrentMode(GoalRequestStates.VIEW_REQUESTS);
    },
    onError: () => {
      setIsModalOpen(false);
    },
  });

  const sendBackRequest = useMutation({
    mutationFn: async (payload: {
      goalId: string;
      reason: string;
    }) => {
      return performanceManagementService.sendBackGoalRequest(payload.goalId, payload.reason);
    },
    onSuccess: () => {
      setCurrentMode(GoalRequestStates.VIEW_REQUESTS);
    },
    onError: () => {
      setIsModalOpen(false);
    },
  });

  const onChange = (ev: SelectChangeEvent<string>) => {
    if (ev?.target?.value) {
      setCurrentSelectedAction(ev?.target?.value);
    }
  };

  const isSubmitDisabled = useMemo(() => {
    if (currentSelectedAction === "Send Back" && !reasonForSendingBack) {
      return true;
    }
    return false;
  }, [currentSelectedAction, reasonForSendingBack]);

  const onSubmit = () => {
    setIsModalOpen(true);
  };

  const onAction = () => {
    if (currentSelectedAction === "Approve") {
      approveRequest.mutate(selectedRequest.goal.goal_id);
      return;
    }

    sendBackRequest.mutate({
      goalId: selectedRequest.goal.goal_id,
      reason: reasonForSendingBack || "",
    });
  };

  const onWeightageChange = (ev: React.ChangeEvent<HTMLInputElement>, id: string) => {
    setObjectiveWeightages((prevWeightages) => ({
      ...prevWeightages,
      [id]: Number(ev.target.value),
    }));
  };

  const areWeightagesValid = useMemo(() => {
    if (currentSelectedAction === "Send Back") {
      return true;
    }
    const areInRage = Object.values(objectiveWeightages).every((weightage) => weightage > 0 && weightage <= 100);
    const areSumEqualTo100 = Object.values(objectiveWeightages).reduce((acc, curr) => acc + curr, 0) === 100;
    return areInRage && areSumEqualTo100;
  }, [objectiveWeightages, currentSelectedAction]);

  return (
    <Box display="flex" gap={2} flexDirection="column" padding={2}>
      <Header setCurrentMode={setCurrentMode} />
      <EmployeeDetailHeader
        title={selectedRequest.job_title}
        appliedDate=""
        name={selectedRequest.employee_name}
        goal={selectedRequest?.goal?.performance_review_cycle?.name || ""}
      />
      {selectedRequest?.goal?.objectives.map((objective, index) => (
        <EmployeeGoalDetails
          key={index}
          objective={objective}
          onWeightageChange={(ev: React.ChangeEvent<HTMLInputElement>) => onWeightageChange(ev, objective.id as string)}
          areWeightagesValid={areWeightagesValid}
          weightage={objective?.estimated_weightage as number}
          hideWeightage={currentSelectedAction === "Send Back"}
          isApproved={selectedRequest?.goal?.status === "Approved"}
        />
      ))}
      {selectedRequest?.goal?.status !== "Approved" && (
        <ManagerAction
          setCurrentSelectedAction={setCurrentSelectedAction}
          currentSelectedAction={currentSelectedAction}
          onChange={onChange}
          setReason={setReasonForSendingBack}
          reason={reasonForSendingBack}
        />
      )}
      {selectedRequest?.goal?.status !== "Approved" && (
        <Footer
          setCurrentSelectedMode={setCurrentMode}
          isSubmitDisabled={isSubmitDisabled || !areWeightagesValid}
          onSubmit={onSubmit}
        />
      )}
      {isModalOpen && (
        <SubmissionConfirmationModal
          title={`${currentSelectedAction} Goal Request`}
          description={`Are you sure you want to ${currentSelectedAction.toLowerCase()} this goal request?`}
          isModalOpen={isModalOpen}
          onClose={() => setIsModalOpen(false)}
          onSubmit={onAction}
        />
      )}
    </Box>
  );
};

export default EmployeeGoalRequestActionableView;
