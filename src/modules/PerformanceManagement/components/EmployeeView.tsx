import React, { useMemo } from "react";
import { useAppSelector } from "src/customHooks/useAppSelector";
import CustomTabs, { TabType } from "src/modules/Common/CustomTabs/CustomTabs";
import GoalSettings from "src/modules/PerformanceManagement/components/GoalSettings";
import PerformanceReview from "src/modules/PerformanceManagement/components/PerformanceReviews/components/PerformanceReview";
import PeerNominations from "./PerformanceReviews/components/PeerNominations";
import PeerPerformanceRequests from "./PerformanceReviews/components/PeerPerformanceRequests";

const tabs: TabType[] = [
  {
    id: 0,
    label: "Goals Setting",
    component: <GoalSettings />,
  },
  {
    id: 1,
    label: "Self Review",
    component: <PerformanceReview />,
  },
  {
    id: 2,
    label: "Peer Review",
    component: <PeerPerformanceRequests />,
  },
  {
    id: 3,
    label: "Peer Nominations",
    component: <PeerNominations />,
  },
];

const managerTabs = tabs;

const EmployeeView = () => {
  const { isFullView } = useAppSelector((state) => state.app);
  const { userDetails } = useAppSelector((state) => state.userManagement);
  const tabsToDisplay = useMemo(() => (userDetails?.is_manager ? managerTabs : tabs), [userDetails?.is_manager]);

  return (
    <CustomTabs
      tabs={userDetails?.is_manager ? managerTabs : tabs}
      currentTabIndex={tabsToDisplay[0].id as unknown as number}
      hideTabBar={isFullView}
    />
  );
};

export default EmployeeView;
