import { Add, <PERSON><PERSON><PERSON>, Delete } from "@mui/icons-material";
import { Box, Button, Divider, Grid2, IconButton, Paper, Typography } from "@mui/material";
import { useMutation, useQuery } from "@tanstack/react-query";
import { format } from "date-fns";
import React, { useCallback, useMemo } from "react";
import { queryClient } from "src/app/App";
import { useForm } from "src/customHooks/useForm";
import { useMasterData } from "src/customHooks/useMasterData";
import CustomDateField from "src/modules/Common/FormInputs/CustomDateField";
import CustomSelect from "src/modules/Common/FormInputs/CustomSelect";
import CustomTextField from "src/modules/Common/FormInputs/CustomTextField";
import { Goal, LinkedGoalObjective, Objective } from "src/services/api_definitions/performanceManagement.service";
import performanceManagementService from "src/services/performanceManagement.service";
import validators from "src/utils/validators";
import DetailListItem from "./DetailListItem";
import { EmployeeGoalDetails } from "./EmployeeGoalRequestActionableView";
import { GoalSettingStates } from "./GoalSettings";
import SubmissionConfirmationModal from "./SubmissionConfirmationModal";

interface GoalFormProps {
  setCurrentMode: (currentMode: GoalSettingStates) => void;
  selectedGoalDetails: Goal;
  isReadOnly?: boolean;
}

const Header: React.FC<Omit<GoalFormProps, "selectedGoalDetails">> = ({ setCurrentMode }) => (
  <Box display="flex" flexDirection="column" gap={1} sx={{ margin: ["0px 0px 12px 0px"] }}>
    <Box display="flex" gap={2} alignItems="center">
      <IconButton onClick={() => setCurrentMode(GoalSettingStates.VIEW_GOALS)}>
        <ArrowBack />
      </IconButton>
      <Typography>Review Goal Objectives</Typography>
    </Box>
    <Divider />
  </Box>
);

const Footer: React.FC<
  Omit<
    GoalFormProps & {
      onSaveToDrafts: () => void;
      onSubmit: () => void;
      isSubmitDisabled: boolean;
      isSaveToDraftsDisabled?: boolean;
    },
    "selectedGoalDetails"
  >
> = ({ setCurrentMode, isSubmitDisabled, onSaveToDrafts, onSubmit, isSaveToDraftsDisabled = false }) => (
  <Box
    alignSelf="flex-end"
    // position="fixed"
    // bottom={20}
    gap={2}
    component={Paper}
    elevation={0}
    sx={{
      padding: "16px 4px",
    }}
  >
    <Box display="flex" alignItems="center" gap={2}>
      <Button variant="outlined" onClick={() => setCurrentMode(GoalSettingStates.VIEW_GOALS)}>
        Cancel
      </Button>
      <Button disabled={isSaveToDraftsDisabled} variant="outlined" onClick={onSaveToDrafts}>
        Save Draft
      </Button>
      <Button disabled={isSubmitDisabled} variant="contained" onClick={onSubmit}>
        Submit
      </Button>
    </Box>
  </Box>
);

const initialFormState = [
  {
    description: "",
    title: "",
    target_completion_date: "",
    linked_goal_objective_id: null,
    status: "Not Started",
    deprioritisation_reason: "",
  },
];

const GoalForm: React.FC<GoalFormProps> = ({ setCurrentMode, selectedGoalDetails, isReadOnly }) => {
  const { data: goalStatuses = [] } = useMasterData<string>("GoalObjectiveStatus", {
    enabled: !isReadOnly,
  });

  const goalStatusOptions = useMemo(
    () => goalStatuses?.map((status: string) => ({ label: status, value: status })) || [],
    [goalStatuses],
  );

  const [isModalOpen, setIsModalOpen] = React.useState(false);
  const { data: managerGoalObjectives = [] } = useQuery(
    ["manager-goal-objectives"],
    async () => performanceManagementService.getManagerGoalObjectives(selectedGoalDetails.goal_id),
    {
      enabled: !isReadOnly,
      refetchOnMount: true,
      refetchOnWindowFocus: false,
    },
  );

  const defaultFormState: Omit<Objective, "linked_goal_objective">[] = useMemo(() => {
    if (selectedGoalDetails?.objectives?.length > 0) {
      return selectedGoalDetails?.objectives?.map((objective) => ({
        description: objective.description,
        title: objective?.title,
        status: objective?.status,
        deprioritisation_reason: objective?.deprioritisation_reason,
        target_completion_date: format(new Date(objective?.target_completion_date), "yyyy-MM-dd"),
        linked_goal_objective_id:
          [...(managerGoalObjectives || [])]?.find(
            (eachObjective) => eachObjective?.goal_objective_id === objective?.linked_goal_objective?.goal_objective_id,
          )?.goal_objective_id ||
          objective?.linked_goal_objective?.goal_objective_id ||
          null,
        id: objective?.id,
      }));
    }

    return [...initialFormState];
  }, [managerGoalObjectives]);

  const { formDetails, formErrors, addNewFormDetailRow, deleteFormDetails, setFormDetail, areFormDetailsValid } =
    useForm({
      initialState: defaultFormState,
      isBulk: true,
      validations: {
        description: [validators.validateInput, validators.shouldBeOfMinLength],
        deprioritisation_reason: [validators.validateDeprioritationReason],
        id: [],
        title: [validators.validateInput],
        target_completion_date: [validators.validateInput],
        linked_goal_objective_id: managerGoalObjectives?.length > 0 ? [validators.validateInput] : [],
        status: [],
        actual_completion_date: [],
        estimated_weightage: [],
        final_weightage: [],
      },
    });

  const updateGoalMutation = useMutation({
    mutationFn: async (payload: Goal) => {
      return performanceManagementService.updateGoal(payload);
    },
    onSuccess: () => {
      queryClient.refetchQueries(["goal"]);
      setCurrentMode(GoalSettingStates.VIEW_GOALS);
    },
    onError: (error) => {
      console.error({ error });
    },
  });

  const typedFormDetails = formDetails as Objective[];
  const typedFormErrors = formErrors as { [key: string]: string }[];

  const onAddMoreClick = () => {
    addNewFormDetailRow(initialFormState);
  };

  const onChange = (index: number, fieldName: string, value: unknown) => {
    setFormDetail(fieldName, value, index);
  };

  const onSaveToDrafts = () => {
    updateGoalMutation.mutate({
      goal_id: selectedGoalDetails.goal_id,
      status: "Draft",
      objectives: [...typedFormDetails],
      enabled: selectedGoalDetails?.enabled,
      approved_at: selectedGoalDetails?.approved_at,
    });
  };

  const onSubmit = () => {
    setIsModalOpen(true);
  };

  const onSubmitConfirmation = () => {
    updateGoalMutation.mutate({
      goal_id: selectedGoalDetails.goal_id,
      status: "Submitted",
      enabled: selectedGoalDetails?.enabled,
      approved_at: selectedGoalDetails?.approved_at,
      objectives: [...typedFormDetails],
    });
  };

  const selectedManagerGoalObjective = useCallback(
    (goalObjectiveId?: string | null): LinkedGoalObjective | null | undefined => {
      if (!goalObjectiveId) {
        return null;
      }
      const objectives = managerGoalObjectives?.find(
        (goalObjective) => goalObjective?.goal_objective_id === goalObjectiveId,
      );
      return objectives;
    },
    [managerGoalObjectives],
  );

  const isObjectivePartOfManagerGoal = useCallback(
    (linkedObjectiveId: string | null) => {
      if (!linkedObjectiveId) {
        return true;
      }
      return managerGoalObjectives?.some((goalObjective) => goalObjective?.goal_objective_id === linkedObjectiveId);
    },
    [managerGoalObjectives, formDetails],
  );

  const getViews = (formDetail: Objective, index: number) => {
    if (isReadOnly) {
      const estimatedWeightage = selectedGoalDetails?.objectives?.find(
        (objective) => objective.id === formDetail.id,
      )?.estimated_weightage;
      const managerGoalObjective = selectedGoalDetails?.objectives?.find(
        (eachObjective) => eachObjective.id === formDetail.id,
      )?.linked_goal_objective;
      return (
        <EmployeeGoalDetails
          objective={{ ...formDetail, linked_goal_objective: managerGoalObjective as LinkedGoalObjective }}
          weightage={estimatedWeightage}
          hideWeightage={!estimatedWeightage || selectedGoalDetails.status !== "Approved"}
          isApproved={selectedGoalDetails.status === "Approved"}
        />
      );
    }

    const managerObjective = selectedManagerGoalObjective(formDetail?.linked_goal_objective_id)?.goal_objective_id
      ? selectedManagerGoalObjective(formDetail?.linked_goal_objective_id)
      : selectedGoalDetails?.objectives?.[index]?.linked_goal_objective;

    return (
      <>
        <Grid2 key={index} container spacing={2} component={Paper} elevation={4} margin={1} p={2}>
          {managerGoalObjectives?.length > 0 && (
            <Grid2 container component={Paper} elevation={2} sx={{ width: "100%", p: 2, borderRadius: 4 }}>
              {isObjectivePartOfManagerGoal(formDetail?.linked_goal_objective_id) ? (
                <Grid2 size={4}>
                  <CustomSelect
                    name="linked_goal_objective_id"
                    id="linked_goal_objective_id"
                    label="Linked Objectives"
                    placeholder="Select Linked Objective"
                    size="small"
                    fullWidth
                    required
                    value={managerObjective?.title || ""}
                    onChange={(ev) => onChange(index, "linked_goal_objective_id", ev.target.value)}
                    options={
                      managerGoalObjectives?.map((eachObjective) => ({
                        label: eachObjective?.title,
                        value: eachObjective?.goal_objective_id,
                      })) || []
                    }
                    error={!!typedFormErrors?.[index]?.linked_goal_objective_id}
                    helperText={typedFormErrors?.[index]?.linked_goal_objective_id}
                    inputProps={{
                      readOnly: isReadOnly,
                    }}
                  />
                </Grid2>
              ) : (
                <Grid2 size={4}>
                  <DetailListItem title="Linked Objective" value={managerObjective?.title} />
                </Grid2>
              )}
              <Grid2 size={4}>
                <DetailListItem
                  title={"Planned Completion Date"}
                  value={managerObjective?.target_completion_date as unknown as string}
                />
              </Grid2>
              <Grid2 size={4}>
                <DetailListItem
                  title={"Status"}
                  value={managerObjective?.status as unknown as string}
                  color={managerObjective?.status === "Deprioritised" ? "red" : "black"}
                  info={managerObjective?.deprioritisation_reason}
                />
              </Grid2>
              {managerObjective?.description && (
                <Grid2 size={12} gap={3}>
                  <Typography variant="subtitle1" fontSize={14} color="textSecondary">
                    Description
                  </Typography>
                  <Typography>{managerObjective?.description || ""}</Typography>
                </Grid2>
              )}
            </Grid2>
          )}
          <Grid2 size={6}>
            <CustomTextField
              title={`Objective ${index + 1}`}
              id="title"
              name="title"
              inputProps={{ readOnly: isReadOnly }}
              value={formDetail.title}
              fullWidth
              onChange={(ev) => onChange(index, "title", ev.target.value)}
              size="small"
              required
            />
          </Grid2>
          <Grid2 size={6}>
            <CustomDateField
              name="target_completion_date"
              title="Planned Completion Date"
              value={formDetail?.target_completion_date as unknown as Date}
              onChange={(date) => {
                onChange(index, "target_completion_date", format(date as Date, "yyyy-MM-dd"));
              }}
              views={["year", "month", "day"]}
              slotProps={{
                textField: {
                  fullWidth: true,
                  size: "small",
                  id: "target_completion_date",
                  error: !!typedFormErrors?.[index].plannedCompletionDate,
                  helperText:
                    typedFormErrors?.[index].plannedCompletionDate ?? typedFormErrors?.[index].plannedCompletionDate,
                  required: true,
                  inputProps: {
                    readOnly: isReadOnly,
                  },
                  // variant: isReadOnly ? "filled" : "outlined",
                },
              }}
              required
            />
          </Grid2>
          <Grid2 size={12}>
            <CustomTextField
              name="description"
              id="description"
              title="Description"
              placeholder="Add Description"
              required
              value={formDetail?.description}
              inputProps={{
                maxLength: 2000,
                readOnly: isReadOnly,
              }}
              onChange={(ev) => onChange(index, "description", ev.target.value)}
              fullWidth
              multiline
              rows={4}
              error={!!typedFormErrors?.[index]?.description}
              helperText={typedFormErrors?.[index]?.description || (!isReadOnly && "max 2000 character(s)")}
            />
          </Grid2>
          <Grid2 size={4}>
            <CustomSelect
              title="Status"
              name="status"
              id="status"
              label="Status"
              placeholder="Select Status"
              value={formDetail.status || "Not Started"}
              fullWidth
              size="small"
              options={goalStatusOptions}
              onChange={(ev) => {
                if (ev.target.value !== "Deprioritised") {
                  onChange(index, "deprioritisation_reason", null);
                }
                onChange(index, "status", ev.target.value);
              }}
              error={!!typedFormErrors?.[index]?.status}
              helperText={typedFormErrors?.[index]?.status}
            />
          </Grid2>
          {formDetail?.status === "Deprioritised" && (
            <Grid2 size={12}>
              <CustomTextField
                name="deprioritisation_reason"
                id="deprioritisation_reason"
                title="Reason"
                placeholder="Add Reason"
                required
                value={formDetail?.deprioritisation_reason}
                inputProps={{
                  maxLength: 200,
                  readOnly: isReadOnly,
                }}
                onChange={(ev) => onChange(index, "deprioritisation_reason", ev.target.value)}
                fullWidth
                multiline
                rows={4}
                error={!!typedFormErrors?.[index]?.deprioritisation_reason}
                helperText={
                  typedFormErrors?.[index]?.deprioritisation_reason || (!isReadOnly && "max 200 character(s)")
                }
              />
            </Grid2>
          )}
          {typedFormDetails?.length !== 1 &&
            !isReadOnly &&
            !(
              selectedGoalDetails?.approved_at &&
              formDetail?.id &&
              selectedGoalDetails?.objectives?.[index]?.estimated_weightage
            ) && (
              <Grid2 size={12}>
                <IconButton sx={{ float: "right" }} onClick={() => deleteFormDetails(index)}>
                  <Delete color="error" />
                </IconButton>
              </Grid2>
            )}
        </Grid2>
        {index === typedFormDetails?.length - 1 && !isReadOnly && (
          <Grid2 size={12}>
            <Button variant="text" onClick={onAddMoreClick} startIcon={<Add fontSize="small" />}>
              Add more
            </Button>
          </Grid2>
        )}
      </>
    );
  };

  return (
    <Box display="flex" flexDirection="column" height="100%">
      <Header setCurrentMode={setCurrentMode} />
      <Box display={"flex"} flexDirection="column" gap={4}>
        {typedFormDetails?.map((formDetail, index) => getViews(formDetail, index))}
      </Box>
      {!isReadOnly && (
        <Footer
          setCurrentMode={setCurrentMode}
          onSaveToDrafts={onSaveToDrafts}
          onSubmit={onSubmit}
          isSubmitDisabled={!areFormDetailsValid}
          isSaveToDraftsDisabled={!areFormDetailsValid}
        />
      )}
      {isModalOpen && (
        <SubmissionConfirmationModal
          title="Submit Goal Objectives"
          description="Ready to submit your goal objectives?"
          isModalOpen={isModalOpen}
          onClose={() => setIsModalOpen(false)}
          onSubmit={onSubmitConfirmation}
        />
      )}
    </Box>
  );
};

export default GoalForm;
