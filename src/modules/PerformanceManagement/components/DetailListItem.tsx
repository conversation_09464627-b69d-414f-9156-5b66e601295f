import { InfoOutlined } from "@mui/icons-material";
import { Box, Tooltip, Typography } from "@mui/material";
import React from "react";

interface DetailListItemProps {
  title: string;
  value?: string | number | React.ReactNode | null;
  color?: string;
  info?: string | null;
}
const DetailListItem: React.FC<DetailListItemProps> = ({ title, value, color = "#000", info = "" }) => {
  return (
    <Box display="flex" flexDirection="column" gap={1}>
      <Box display={info ? "flex" : "block"} alignItems="center" gap={1}>
        <Typography color="#667085" fontWeight={500}>
          {title}
        </Typography>
        {info && (
          <Tooltip title={info}>
            <InfoOutlined fontSize="small" />
          </Tooltip>
        )}
      </Box>
      {typeof value === "object" ? (
        value
      ) : (
        <Typography color={color} fontWeight={500} textOverflow="ellipsis" overflow="hidden" whiteSpace="pretty">
          {value}
        </Typography>
      )}
    </Box>
  );
};

export default DetailListItem;
