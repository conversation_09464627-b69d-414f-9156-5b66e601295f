import { Box, Button, Switch, Typography } from "@mui/material";
import { useQuery } from "@tanstack/react-query";
import { MRT_GlobalFilterTextField } from "material-react-table";
import React from "react";
import { EmployeeCellInfo } from "src/modules/Common/EmployeeViews/EmployeeCellInfo";
import DataTable from "src/modules/Common/Table/DataTable";
import EnableGoalSettings from "src/modules/Employees/components/EnableGoalSettings";
import { EmployeeGoalSettingDetail } from "src/services/api_definitions/performanceManagement.service";
import performanceManagementService from "src/services/performanceManagement.service";
import { getStatusColors } from "src/utils/typographyUtils";

const PERFORMANCE_CYCLE_OPEN = "OPEN";

const typography = {
  employee: "Employee",
  goalSubmissionStatus: "Goal Submission Status",
  goalSettingStatus: "Goal Setting Status",
  goalSettingEnabled: "Goal Setting Enabled",
  goalSettingDisabled: "Goal Setting Disabled",
  enable: "Enable",
  disabled: "Disabled",
  enabled: "Enabled",
};

const isGoalSettingVisible = (row: EmployeeGoalSettingDetail) => {
  return (
    !row?.goal?.enabled &&
    row?.goal?.performance_review_cycle?.status === PERFORMANCE_CYCLE_OPEN &&
    !row?.goal?.performance_review_cycle?.performance_review_enabled
  );
};

const EmployeeGoalSettingStatus: React.FC = () => {
  const [employeeCode, setEmployeeCode] = React.useState<string | null>(null);

  const {
    data: employeeGoalSettingStatus = [],
    isLoading,
    isFetching,
    isError,
    refetch,
  } = useQuery({
    queryKey: ["get-employee-goal-setting-status"],
    queryFn: async () => performanceManagementService.getEmployeeGoalSettingStatus(),
    refetchOnWindowFocus: false,
  });

  return (
    <>
      <DataTable
        enableGlobalFilter
        enableTopToolbar
        renderTopToolbar={({ ...props }) => (
          <Box p={1}>
            <MRT_GlobalFilterTextField {...props} aria-label="Search" aria-roledescription="search" />
          </Box>
        )}
        state={{
          showSkeletons: isLoading || isFetching,
          showAlertBanner: isError,
        }}
        data={employeeGoalSettingStatus}
        columns={[
          {
            accessorKey: "employee_name",
            header: typography.employee,
            enableColumnFilter: true,
            Cell: ({ row: { original } }) => (
              <EmployeeCellInfo
                name={original?.employee_name}
                jobTitle={original?.job_title}
                displayPic={original?.display_pic}
              />
            ),
          },
          {
            header: typography.goalSubmissionStatus,
            accessorKey: "goal.status",
            Cell: ({ row: { original } }) => (
              <Typography color={getStatusColors(original?.goal?.status)}>{original?.goal?.status}</Typography>
            ),
          },
          {
            header: typography.goalSettingStatus,
            Cell: ({ row: { original } }) => {
              const isGoalSettingEnabled = original?.goal?.enabled;
              return isGoalSettingEnabled ? (
                <Box display="flex" alignItems="center" gap={2}>
                  <Typography color={getStatusColors(typography?.enabled)}>{typography?.enabled}</Typography>
                  {isGoalSettingVisible(original) ? (
                    <Button
                      onClick={(_ev) => setEmployeeCode(original?.employee_code)}
                      variant="contained"
                      size="small"
                    >
                      {typography.enable}
                    </Button>
                  ) : null}
                </Box>
              ) : (
                <Box display="flex" alignItems="center" gap={2}>
                  <Typography color={getStatusColors(typography.disabled)}>{typography.disabled}</Typography>
                  {isGoalSettingVisible(original) ? (
                    <Button
                      variant="contained"
                      size="small"
                      onClick={(_ev) => setEmployeeCode(original?.employee_code)}
                    >
                      {typography.enable}
                    </Button>
                  ) : null}
                </Box>
              );
            },
          },
        ]}
      />
      {employeeCode && (
        <EnableGoalSettings
          employee={{ employee_code: employeeCode }}
          onClose={() => {
            setEmployeeCode(null);
            refetch();
          }}
          isOpen={!!employeeCode}
        />
      )}
    </>
  );
};

export default EmployeeGoalSettingStatus;
