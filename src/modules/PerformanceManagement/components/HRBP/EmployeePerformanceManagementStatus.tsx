import { Box, Typography } from "@mui/material";
import { useQuery } from "@tanstack/react-query";
import { MRT_GlobalFilterTextField } from "material-react-table";
import React from "react";
import { EmployeeCellInfo } from "src/modules/Common/EmployeeViews/EmployeeCellInfo";
import DataTable from "src/modules/Common/Table/DataTable";
import performanceManagementService from "src/services/performanceManagement.service";
import { getStatusColors } from "src/utils/typographyUtils";

const typography = {
  employee: "Employee",
  reviewStatus: "Review Status",
  performanceReviewEnabled: "Performance Review Enabled",
  performanceReviewDisabled: "Performance Review Disabled",
};

const EmployeePerformanceManagementStatus: React.FC = () => {
  const {
    data: employeePerformanceManagementStatus = [],
    isLoading,
    isFetching,
    isError,
  } = useQuery({
    queryKey: ["get-employee-performance-review-status"],
    queryFn: async () => performanceManagementService.getEmployeePerformanceReviewstatus(),
    refetchOnWindowFocus: false,
  });

  return (
    <DataTable
      enableGlobalFilter
      enableTopToolbar
      renderTopToolbar={({ ...props }) => (
        <Box p={1}>
          <MRT_GlobalFilterTextField {...props} aria-label="Search" aria-roledescription="search" />
        </Box>
      )}
      state={{
        showSkeletons: isLoading || isFetching,
        showAlertBanner: isError,
      }}
      data={employeePerformanceManagementStatus}
      columns={[
        {
          accessorKey: "employee_name",
          header: typography.employee,
          enableColumnFilter: true,
          Cell: ({ row: { original } }) => (
            <EmployeeCellInfo
              name={original?.employee_name}
              jobTitle={original?.job_title}
              displayPic={original?.display_pic}
            />
          ),
        },
        {
          header: typography.reviewStatus,
          accessorKey: "review.status",
          Cell: ({ row: { original } }) => (
            <Typography color={getStatusColors(original?.review?.status || "")}>{original?.review?.status}</Typography>
          ),
        },
      ]}
    />
  );
};

export default EmployeePerformanceManagementStatus;
