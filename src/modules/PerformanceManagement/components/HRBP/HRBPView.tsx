import { Box, Tab, Tabs } from "@mui/material";
import React, { useEffect, useMemo } from "react";
import { useNavigate, useSearchParams } from "react-router-dom";
import { useAppDispatch } from "src/customHooks/useAppDispatch";
import { useAppSelector } from "src/customHooks/useAppSelector";
import NoAccessScreen from "src/modules/Common/NoAccess/NoAccess";
import { PATH_CONFIG } from "src/modules/Routing/config";
import { addBreadcrumb, removeBreadcrumb } from "src/store/slices/breadcrumbs.slice";
import HRBPGoalManagement from "./GoalManagementView";
import HRBPPerformanceReviewView from "./PerformanceReviewView";

const performanceManagementTabs = [
  {
    key: "goalManagement",
    path: "/goal-management",
    label: "Goal Management",
    component: <HRBPGoalManagement />,
    id: 0,
  },
  {
    key: "performanceReview",
    path: "/perforanceReview",
    label: "Performance Review",
    component: <HRBPPerformanceReviewView />,
    id: 1,
  },
];

const HRBPView = () => {
  const { isFullView } = useAppSelector((state) => state.app);
  const dispatch = useAppDispatch();
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();

  const [value, setValue] = React.useState(performanceManagementTabs?.[0]?.key);

  // This is needed when we are changing roles or orgs & the url remains the same, a fallback check if we may presume
  useEffect(() => {
    const previousSelectedTab = performanceManagementTabs?.find((eachTab) => eachTab.key === searchParams.get("tabId"));

    if (!previousSelectedTab) {
      dispatch(
        addBreadcrumb({
          isActive: true,
          isDisabled: false,
          label: performanceManagementTabs?.[0]?.label,
          path: performanceManagementTabs?.[0]?.key,
        }),
      );
      setValue(performanceManagementTabs?.[0]?.key);
      navigate(PATH_CONFIG.PEFRORMANCE_MANAGEMENT.path);
      return;
    }
    dispatch(
      addBreadcrumb({
        isActive: true,
        isDisabled: false,
        label: previousSelectedTab.label,
        path: previousSelectedTab.key,
      }),
    );
    setValue(previousSelectedTab.key);
    navigate(`${PATH_CONFIG.PEFRORMANCE_MANAGEMENT.path}?tabId=${previousSelectedTab.key}`);
  }, []);

  const Component = useMemo(
    () => performanceManagementTabs.find((eachTab) => eachTab.key === value)?.component,
    [value],
  );

  const handleChange = (_event: React.SyntheticEvent, newValue: string) => {
    const tab = performanceManagementTabs.find((eachTab) => eachTab.key === newValue);
    if (tab) {
      dispatch(removeBreadcrumb(performanceManagementTabs.find((eachTab) => eachTab.key === value)?.key || ""));
      dispatch(
        addBreadcrumb({
          isActive: true,
          isDisabled: false,
          label: tab?.label || "",
          path: tab?.key || "",
        }),
      );
      setValue(tab.key);
      navigate(`${PATH_CONFIG.PEFRORMANCE_MANAGEMENT.path}?tabId=${tab.key}`);
    }
  };

  if (!performanceManagementTabs || performanceManagementTabs?.length === 0) {
    return (
      <Box>
        <NoAccessScreen />
      </Box>
    );
  }

  return (
    <Box
      sx={{
        height: "100%",
        display: "flex",
        justifyContent: "flex-start",
      }}
    >
      {!isFullView && (
        <Tabs
          orientation="vertical"
          variant="scrollable"
          value={value}
          onChange={handleChange}
          aria-label="Vertical tabs example"
          sx={{
            borderRight: 1,
            borderColor: "divider",
            minWidth: "fit-content",
            padding: 0,
          }}
        >
          {performanceManagementTabs.map((tab) => (
            <Tab
              sx={{
                textTransform: "none",
                wordBreak: "break-word",
                width: "100%",
                alignItems: "flex-start",
                fontWeight: 500,
                color: "#667085",
              }}
              id={tab.key}
              value={tab.key}
              key={tab.key}
              label={tab.label}
            />
          ))}
        </Tabs>
      )}
      <Box sx={{ overflow: "auto", width: "100%", padding: isFullView ? 0 : "0px 16px" }}>{Component ?? null}</Box>
    </Box>
  );
};

export default HRBPView;
