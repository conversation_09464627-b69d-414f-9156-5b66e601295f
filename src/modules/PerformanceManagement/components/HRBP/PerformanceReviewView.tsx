import React from "react";
import CustomTabs, { TabType } from "src/modules/Common/CustomTabs/CustomTabs";
import HRBPPerformanceRequests from "../PerformanceReviews/components/HRBPPerformanceRequests";
import EmployeePerformanceManagementStatus from "./EmployeePerformanceManagementStatus";

const tabs: TabType[] = [
  {
    id: 1,
    label: "Performance Review Status",
    component: <EmployeePerformanceManagementStatus />,
  },
  {
    id: 2,
    label: "Requests",
    component: <HRBPPerformanceRequests />,
  },
];

const HRBPPerformanceReviewView = () => {
  return <CustomTabs tabs={tabs} />;
};

export default HRBPPerformanceReviewView;
