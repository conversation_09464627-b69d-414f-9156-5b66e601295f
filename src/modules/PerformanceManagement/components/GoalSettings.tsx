import { Box } from "@mui/material";
import React, { useEffect, useMemo } from "react";
import { useAppDispatch } from "src/customHooks/useAppDispatch";
import { Goal } from "src/services/api_definitions/performanceManagement.service";
import { setFullviewMode } from "src/store/slices/app.slice";
import GoalForm from "./GoalForm";
import ViewGoalSettings from "./ViewGoalSettings";

export enum GoalSettingStates {
  VIEW_GOALS,
  ADD_GOAL,
  VIEW_ADDED_GOALS,
}

const GoalSettings = () => {
  const dispatch = useAppDispatch();
  const [currentMode, setCurrentMode] = React.useState(GoalSettingStates.VIEW_GOALS);
  const [selectedGoalDetails, setSelectedGoalDetails] = React.useState<Goal | null>(null);
  const readOnlyStatusModes = new Set(["Submitted", "Approved"]);

  useEffect(() => {
    if (currentMode === GoalSettingStates.ADD_GOAL || currentMode === GoalSettingStates.VIEW_ADDED_GOALS) {
      dispatch(setFullviewMode(true));
      return;
    }
    dispatch(setFullviewMode(false));
  }, [currentMode]);

  const renderGoalSettings = useMemo(() => {
    switch (currentMode) {
      case GoalSettingStates.VIEW_GOALS:
        return (
          <Box m={1}>
            <ViewGoalSettings setCurrentMode={setCurrentMode} setSelectedGoalDetails={setSelectedGoalDetails} />
          </Box>
        );
      case GoalSettingStates.ADD_GOAL:
        return (
          selectedGoalDetails && <GoalForm setCurrentMode={setCurrentMode} selectedGoalDetails={selectedGoalDetails} />
        );
      case GoalSettingStates.VIEW_ADDED_GOALS:
        return (
          selectedGoalDetails && (
            <GoalForm
              setCurrentMode={setCurrentMode}
              selectedGoalDetails={selectedGoalDetails}
              isReadOnly={readOnlyStatusModes.has(selectedGoalDetails?.status)}
            />
          )
        );
    }
  }, [currentMode]);

  return <>{renderGoalSettings}</>;
};

export default GoalSettings;
