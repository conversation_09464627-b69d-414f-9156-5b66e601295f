import { Box } from "@mui/material";
import React, { useEffect } from "react";
import { useAppDispatch } from "src/customHooks/useAppDispatch";
import { Requests } from "src/services/api_definitions/performanceManagement.service";
import { setFullviewMode } from "src/store/slices/app.slice";
import EmployeeGoalRequestActionableView from "./EmployeeGoalRequestActionableView";
import EmployeeGoalRequestsView from "./EmployeeGoalRequestsView";

export enum GoalRequestStates {
  VIEW_REQUESTS,
  TAKE_ACTION_ON_REQUESTS,
}

const GoalRequests = () => {
  const dispatch = useAppDispatch();
  const [currentMode, setCurrentMode] = React.useState(GoalRequestStates.VIEW_REQUESTS);
  const [selectedRequest, setSelectedRequest] = React.useState<Requests | null>(null);

  useEffect(() => {
    if (currentMode === GoalRequestStates.TAKE_ACTION_ON_REQUESTS) {
      dispatch(setFullviewMode(true));
      return;
    }
    dispatch(setFullviewMode(false));
  }, [currentMode]);

  return (
    <Box>
      {currentMode === GoalRequestStates.VIEW_REQUESTS && (
        <EmployeeGoalRequestsView setCurrentMode={setCurrentMode} setSelectedRequest={setSelectedRequest} />
      )}
      {currentMode === GoalRequestStates.TAKE_ACTION_ON_REQUESTS && selectedRequest && (
        <EmployeeGoalRequestActionableView selectedRequest={selectedRequest} setCurrentMode={setCurrentMode} />
      )}
    </Box>
  );
};

export default GoalRequests;
