import { VisibilityOutlined } from "@mui/icons-material";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>lt<PERSON>, Typography } from "@mui/material";
import { useQuery } from "@tanstack/react-query";
import React from "react";
import { EmployeeCellInfo } from "src/modules/Common/EmployeeViews/EmployeeCellInfo";
import DataTable from "src/modules/Common/Table/DataTable";
import { Requests } from "src/services/api_definitions/performanceManagement.service";
import performanceManagementService from "src/services/performanceManagement.service";
import { getStatusColors } from "src/utils/typographyUtils";
import { GoalRequestStates } from "./GoalRequests";

interface EmployeeGoalRequestViewProps {
  setCurrentMode: React.Dispatch<React.SetStateAction<number>>;
  setSelectedRequest: React.Dispatch<React.SetStateAction<Requests | null>>;
}

const EmployeeGoalRequestsView: React.FC<EmployeeGoalRequestViewProps> = ({ setCurrentMode, setSelectedRequest }) => {
  const { data: goalRequests, isLoading } = useQuery(
    ["get-goal-setting-requests"],
    async () => {
      return performanceManagementService.getGoalRequests();
    },
    {
      refetchOnWindowFocus: false,
      refetchOnMount: true,
    },
  );

  const onViewClick = (row: Requests) => {
    setSelectedRequest(row);
    setCurrentMode(GoalRequestStates.TAKE_ACTION_ON_REQUESTS);
  };

  return (
    <DataTable
      state={{
        showSkeletons: isLoading,
      }}
      data={goalRequests || []}
      columns={[
        {
          header: "Employee",
          accessorFn: (row: Requests) => {
            return (
              <EmployeeCellInfo name={row?.employee_name} jobTitle={row?.job_title} displayPic={row?.display_pic} />
            );
          },
        },
        {
          accessorKey: "goal.performance_review_cycle.name",
          header: "Goal Duration",
        },
        {
          header: "Status",
          accessorFn: (row: Requests) => (
            <Typography color={getStatusColors(row.goal.status)}>{row?.goal?.status}</Typography>
          ),
        },
        {
          accessorFn: (row: Requests) => (
            <Tooltip title="View Details">
              <IconButton color="primary" onClick={() => onViewClick(row)}>
                <VisibilityOutlined />
              </IconButton>
            </Tooltip>
          ),
          header: "Action",
        },
      ]}
    />
  );
};

export default EmployeeGoalRequestsView;
