import { Box, Grid, Paper, SelectChangeEvent } from "@mui/material";
import React, { ChangeEvent } from "react";
import CustomSelect from "src/modules/Common/FormInputs/CustomSelect";
import CustomTextField from "src/modules/Common/FormInputs/CustomTextField";
import { MANAGER_ACTIONS } from "../../EmployeeGoalRequestActionableView";

interface FeedbackVerdictProps {
  currentSelectedAction: Record<string, string | null>;
  setCurrentSelectedAction: React.Dispatch<
    React.SetStateAction<{
      status: string | null;
      reason: string | null;
    }>
  >;
  status?: string;
}

const typography = {
  reviewStatus: "Review Status",
  reason: "Reason",
  addReasonPlaceholder: "Add Reason",
};

const FeedbackVerdicts: React.FC<FeedbackVerdictProps> = ({
  currentSelectedAction,
  setCurrentSelectedAction,
  status,
}) => {
  const onChange = (ev: SelectChangeEvent<string | boolean | number>) => {
    const { value } = ev.target;
    setCurrentSelectedAction((prev) => ({
      ...prev,
      status: value as string | null,
    }));
  };

  const onReasonChange = (ev: ChangeEvent<HTMLInputElement>) => {
    const { value } = ev.target;
    setCurrentSelectedAction((prev) => ({
      ...prev,
      reason: value,
    }));
  };

  return (
    <Box display="flex" flexDirection="column" gap={2} component={Paper} elevation={2} padding={3}>
      <Grid item sm={6}>
        <CustomSelect
          options={status === "Sent Back" ? [MANAGER_ACTIONS[0]] : MANAGER_ACTIONS}
          label={typography.reviewStatus}
          name="action"
          sx={{ width: 320 }}
          onChange={onChange}
          required
          value={currentSelectedAction.status || ""}
          size="small"
        />
      </Grid>
      <Grid item sm={12}>
        {currentSelectedAction.status === "Send Back" && (
          <CustomTextField
            name="reason"
            id="reason"
            title={typography.reason}
            placeholder={typography.addReasonPlaceholder}
            multiline
            value={currentSelectedAction.reason}
            rows={3}
            required
            fullWidth
            color="primary"
            onChange={(ev: React.ChangeEvent<HTMLInputElement>) => onReasonChange(ev)}
          />
        )}
      </Grid>
    </Box>
  );
};

export default FeedbackVerdicts;
