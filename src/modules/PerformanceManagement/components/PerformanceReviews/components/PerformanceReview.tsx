import { Box } from "@mui/material";
import React, { useEffect, useMemo } from "react";
import { useAppDispatch } from "src/customHooks/useAppDispatch";
import { PerformanceReview } from "src/services/api_definitions/performanceManagement.service";
import { setFullviewMode } from "src/store/slices/app.slice";
import PerformanceReviewForm from "./PerformanceReviewForm";
import ReadOnlyPerformanceReviewForm from "./ReadOnlyPerformanceReviewFormView";
import ViewPerformanceReviews from "./ViewPerformanceReviews";

export enum PerformanceReviewFormStates {
  VIEW_REVIEWS,
  ADD_REVIEW,
  VIEW_ADDED_REVIEWS,
}

const PerformanceReview = () => {
  const dispatch = useAppDispatch();
  const [currentMode, setCurrentMode] = React.useState(PerformanceReviewFormStates.VIEW_REVIEWS);
  const [selectedPerformanceReview, setSelectedPerformanceReview] = React.useState<PerformanceReview | null>(null);

  useEffect(() => {
    if (
      currentMode === PerformanceReviewFormStates.ADD_REVIEW ||
      currentMode === PerformanceReviewFormStates.VIEW_ADDED_REVIEWS
    ) {
      dispatch(setFullviewMode(true));
      return;
    }
    dispatch(setFullviewMode(false));
  }, [currentMode]);

  const renderPerformanceReviews = useMemo(() => {
    switch (currentMode) {
      case PerformanceReviewFormStates.VIEW_REVIEWS:
        return (
          <ViewPerformanceReviews
            setCurrentMode={setCurrentMode}
            setSelectedPerformanceReview={setSelectedPerformanceReview}
          />
        );
      case PerformanceReviewFormStates.ADD_REVIEW:
        return (
          selectedPerformanceReview && (
            <PerformanceReviewForm
              setCurrentMode={setCurrentMode}
              selectedPerformanceReview={selectedPerformanceReview}
            />
          )
        );
      case PerformanceReviewFormStates.VIEW_ADDED_REVIEWS:
        return (
          selectedPerformanceReview && (
            <ReadOnlyPerformanceReviewForm
              setCurrentMode={setCurrentMode as any}
              selectedPerformanceReview={selectedPerformanceReview}
            />
          )
        );
    }
  }, [currentMode]);

  return <Box m={1}>{renderPerformanceReviews}</Box>;
};

export default PerformanceReview;
