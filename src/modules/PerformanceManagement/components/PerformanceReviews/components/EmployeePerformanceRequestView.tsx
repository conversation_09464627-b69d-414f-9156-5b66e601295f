import { InfoOutlined, VisibilityOutlined } from "@mui/icons-material";
import { Box, IconButton, Tooltip, Typography } from "@mui/material";
import { useQuery } from "@tanstack/react-query";
import React, { useMemo } from "react";
import { EmployeeCellInfo } from "src/modules/Common/EmployeeViews/EmployeeCellInfo";
import DataTable from "src/modules/Common/Table/DataTable";
import { PerformanceReviewRequest, ReviewerTypes } from "src/services/api_definitions/performanceManagement.service";
import performanceManagementService from "src/services/performanceManagement.service";
import { getStatusColors } from "src/utils/typographyUtils";
import { PerformanceRequestStates } from "./PerformanceRequests";

interface EmployeePerformanceReviewActionableViewProps {
  setCurrentMode: (state: PerformanceRequestStates) => void;
  setSelectedRequest: React.Dispatch<React.SetStateAction<PerformanceReviewRequest | null>>;
  requester?: ReviewerTypes;
}

const EmployeePerformanceRequestView: React.FC<EmployeePerformanceReviewActionableViewProps> = ({
  setCurrentMode,
  setSelectedRequest,
  requester = "manager",
}) => {
  const { data: performanceReviewRequests, isLoading } = useQuery(
    ["performance-request-view"],
    async () => {
      return performanceManagementService.getPerformanceManagementReviewRequests(requester);
    },
    {
      refetchOnWindowFocus: false,
      refetchOnMount: true,
    },
  );

  const onViewClick = (row: PerformanceReviewRequest) => {
    setSelectedRequest(row);
    if (requester === "hrbp" && row.review.status === "Approved") {
      setCurrentMode(PerformanceRequestStates.TAKE_ACTION_ON_REQUESTS);
      return;
    }
    if (row.review.status === "Approved" || row.review.status === "Completed") {
      setCurrentMode(PerformanceRequestStates.VIEW_ACTION_ON_REQUESTS);
      return;
    }
    setCurrentMode(PerformanceRequestStates.TAKE_ACTION_ON_REQUESTS);
  };

  const getColumnDefs = useMemo(() => {
    const commonColumnDefs = [
      {
        header: "Employee",
        accessorFn: (row: PerformanceReviewRequest) => (
          <EmployeeCellInfo name={row?.employee_name} jobTitle={row?.job_title} displayPic={row?.display_pic} />
        ),
      },
      {
        accessorKey: "review.performance_review_cycle.name",
        header: "Review Cycle",
      },
    ];

    if (requester === "peer") {
      return [
        ...commonColumnDefs,
        {
          accessorFn: (row: PerformanceReviewRequest) => (
            <Tooltip title="View Details">
              <IconButton color="primary" onClick={() => onViewClick(row)}>
                <VisibilityOutlined />
              </IconButton>
            </Tooltip>
          ),
          header: "Action",
        },
      ];
    }
    return [
      ...commonColumnDefs,
      {
        header: "Status",
        accessorFn: (row: PerformanceReviewRequest) => (
          <Box display="flex" alignItems="center" gap={1}>
            <Typography color={getStatusColors(row.review.status || "")}>{row?.review?.status}</Typography>
            {row?.review?.status === "Sent Back" && (
              <Tooltip title={row?.review?.comment}>
                <IconButton>
                  <InfoOutlined fontSize="small" />
                </IconButton>
              </Tooltip>
            )}
          </Box>
        ),
      },
      {
        accessorFn: (row: PerformanceReviewRequest) => (
          <Tooltip title="View Details">
            <IconButton color="primary" onClick={() => onViewClick(row)}>
              <VisibilityOutlined />
            </IconButton>
          </Tooltip>
        ),
        header: "Action",
      },
    ];
  }, [requester]);

  return (
    <DataTable
      state={{
        showSkeletons: isLoading,
      }}
      data={performanceReviewRequests || []}
      columns={getColumnDefs}
    />
  );
};

export default EmployeePerformanceRequestView;
