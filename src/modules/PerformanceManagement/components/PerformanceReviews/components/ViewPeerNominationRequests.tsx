import { VisibilityOutlined } from "@mui/icons-material";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Typography } from "@mui/material";
import { useQuery } from "@tanstack/react-query";
import React from "react";
import { useAppDispatch } from "src/customHooks/useAppDispatch";
import { EmployeeCellInfo } from "src/modules/Common/EmployeeViews/EmployeeCellInfo";
import DataTable from "src/modules/Common/Table/DataTable";
import { PeerNominationRequest } from "src/services/api_definitions/performanceManagement.service";
import performanceManagementService from "src/services/performanceManagement.service";
import { setFullviewMode } from "src/store/slices/app.slice";
import { getStatusColors } from "src/utils/typographyUtils";
import { PeerNominationModes } from "./PeerNominations";

interface PeerNominationProps {
  setSelectedGoalDetails: (goal: PeerNominationRequest) => void;
  setCurrentMode: (currentMode: PeerNominationModes) => void;
}

const ViewPeerNominationRequests: React.FC<PeerNominationProps> = ({ setCurrentMode, setSelectedGoalDetails }) => {
  const dispatch = useAppDispatch();

  const { data: peerNominations = [], isFetching } = useQuery({
    queryKey: ["peer-nomination-requests"],
    queryFn: async () => {
      return performanceManagementService.getPeerNominationRequests();
    },
    refetchOnMount: true,
    refetchOnWindowFocus: false,
  });

  const onAddPeerNominationClick = (goal: PeerNominationRequest) => {
    setSelectedGoalDetails(goal);
    dispatch(setFullviewMode(true));
    setCurrentMode(PeerNominationModes.ADD_PEER_NOMINATION);
  };

  return (
    <DataTable
      state={{
        showSkeletons: isFetching,
      }}
      data={peerNominations || []}
      columns={[
        {
          header: "Employee",
          accessorFn: (row: PeerNominationRequest) => (
            <EmployeeCellInfo name={row?.employee_name} jobTitle={row?.job_title} />
          ),
        },
        {
          accessorKey: "peer_nomination.performance_review_cycle.name",
          header: "Review Cycle",
        },
        {
          header: "Status",
          accessorFn: (row: PeerNominationRequest) => (
            <Typography color={getStatusColors(row?.peer_nomination?.status)}>
              {row?.peer_nomination?.status}
            </Typography>
          ),
        },
        {
          accessorFn: (row: PeerNominationRequest) => (
            <Tooltip title="View Details">
              <IconButton color="primary" onClick={() => onAddPeerNominationClick(row)}>
                <VisibilityOutlined />
              </IconButton>
            </Tooltip>
          ),
          header: "Action",
        },
      ]}
    />
  );
};

export default ViewPeerNominationRequests;
