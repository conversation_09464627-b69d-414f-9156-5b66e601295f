import React, { useEffect } from "react";
import { useAppDispatch } from "src/customHooks/useAppDispatch";
import { PeerNominations } from "src/services/api_definitions/performanceManagement.service";
import { setFullviewMode } from "src/store/slices/app.slice";
import NominatePeer from "./NominatePeers";
import ReadOnlyPeerNominations from "./ReadOnlyPeerNominations";
import ViewPeerNominationGoals from "./ViewPeerNominationGoals";

export enum PeerNominationModes {
  VIEW_GOALS = "View Goals",
  ADD_PEER_NOMINATION = "Add Peer Nomination",
  READ_ONY_PEER_NOMINATIONS = "Read Only Peer Nominations",
}

const PeerNominations: React.FC = () => {
  const dispatch = useAppDispatch();
  const [selectedGoalDetails, setSelectedGoalDetails] = React.useState<PeerNominations | null>(null);
  const [currentMode, setCurrentMode] = React.useState(PeerNominationModes.VIEW_GOALS);

  useEffect(() => {
    if (currentMode === PeerNominationModes.VIEW_GOALS) {
      dispatch(setFullviewMode(false));
    }
  }, [currentMode]);

  const renderViewsAccordingToModes = () => {
    switch (currentMode) {
      case PeerNominationModes.VIEW_GOALS:
        return (
          <ViewPeerNominationGoals setSelectedGoalDetails={setSelectedGoalDetails} setCurrentMode={setCurrentMode} />
        );
      case PeerNominationModes.ADD_PEER_NOMINATION:
        return (
          <NominatePeer selectedNomination={selectedGoalDetails as PeerNominations} setCurrentMode={setCurrentMode} />
        );
      case PeerNominationModes.READ_ONY_PEER_NOMINATIONS:
        return (
          <ReadOnlyPeerNominations
            selectedNomination={selectedGoalDetails as PeerNominations}
            setCurrentMode={setCurrentMode}
          />
        );
    }
  };

  return <>{renderViewsAccordingToModes()}</>;
};

export default PeerNominations;
