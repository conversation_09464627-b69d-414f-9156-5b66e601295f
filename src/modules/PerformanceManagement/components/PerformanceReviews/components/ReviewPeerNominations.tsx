import { Add, Delete } from "@mui/icons-material";
import { Autocomplete, AutocompleteInputChangeReason, Box, Button, Grid, IconButton } from "@mui/material";
import { useMutation } from "@tanstack/react-query";
import React, { useMemo, useState } from "react";
import { useForm } from "src/customHooks/useForm";
import CustomTextField from "src/modules/Common/FormInputs/CustomTextField";
import DataTable from "src/modules/Common/Table/DataTable";
import { PeerNominationRequest } from "src/services/api_definitions/performanceManagement.service";
import performanceManagementService from "src/services/performanceManagement.service";
import searchService from "src/services/search.service";
import validators from "src/utils/validators";
import { EmployeeDetailHeader } from "../../EmployeeGoalRequestActionableView";
import { PeerNominationModes } from "./PeerNominations";
import { <PERSON><PERSON>, <PERSON><PERSON> } from "./PerformanceReviewForm";

interface NominatePeerProps {
  selectedNomination: PeerNominationRequest;
  setCurrentMode: (currentMode: PeerNominationModes) => void;
}

const defaultFormState = {
  peerEmail: "",
};

const ReviewPeerNominations: React.FC<NominatePeerProps> = ({ selectedNomination, setCurrentMode }) => {
  const [searchOptions, setSearchOptions] = useState<string[]>([]);
  const prefilledNomineeEmails = new Set(
    selectedNomination?.peer_nomination?.peer_nominations?.map((nomination) => nomination.nominee_email) || [],
  );

  const readOnlyPeerNominations =
    selectedNomination?.peer_nomination.status === "Approved"
      ? selectedNomination?.peer_nomination?.peer_nominations
      : [];

  const getDefaultState = useMemo(() => {
    if (selectedNomination?.peer_nomination.status === "Approved") {
      return [];
    }
    return (
      selectedNomination?.peer_nomination?.peer_nominations?.map((nomination) => ({
        peerEmail: nomination.nominee_email,
      })) || [defaultFormState]
    );
  }, [selectedNomination?.peer_nomination?.peer_nominations]);

  const {
    formDetails,
    formErrors,
    setFormDetail,
    addNewFormDetailRow,
    deleteFormDetails,
    setFormErrors,
    areFormDetailsValid,
  } = useForm({
    initialState: getDefaultState,
    validations: {
      peerEmail: [validators.validateInput],
    },
    isBulk: true,
  });

  const typedFormDetails = formDetails as (typeof defaultFormState)[];
  const typedFormErrors = formErrors as Record<keyof typeof defaultFormState, string>[];

  const updatePeerNominationMutation = useMutation({
    mutationKey: ["update-peer-nomination"],
    mutationFn: async (emails: string[]) => {
      return performanceManagementService.approvePeerNominations(
        selectedNomination?.peer_nomination?.review_id,
        emails,
      );
    },
    onSuccess: () => {
      setCurrentMode(PeerNominationModes.VIEW_GOALS);
    },
  });

  const onAddMoreClick = () => {
    addNewFormDetailRow([defaultFormState]);
  };

  const onDeleteClick = (index: number) => {
    deleteFormDetails(index);
  };

  const onInputChange = (value: string, reason: AutocompleteInputChangeReason, index: number) => {
    if (reason === "reset") {
      setFormDetail("peerEmail", value, index);
      return;
    }
    searchService
      .searchValue(value)
      .then((resp) => {
        if (resp) {
          setSearchOptions(resp?.map((option) => option.email) || []);
        }
      })
      .catch(() => {
        setFormErrors((prevErrors) => {
          const newErrors = [...(prevErrors as Record<keyof typeof defaultFormState, string>[])];
          newErrors[index] = {
            ...newErrors[index],
            peerEmail: "Email not found",
          };
          return newErrors;
        });
      });
    setFormDetail("peerEmail", value, index);
  };

  const onSubmit = () => {
    updatePeerNominationMutation.mutate([
      ...readOnlyPeerNominations.map((nomination) => nomination.nominee_email),
      ...(typedFormDetails?.map((form) => form.peerEmail) || []),
    ]);
  };

  const isSubmitDisabled = () => {
    if (selectedNomination?.peer_nomination?.status === "Approved") {
      return typedFormDetails.some((form) => !form.peerEmail) || !areFormDetailsValid;
    }
    return typedFormDetails.some((form) => !form.peerEmail) || !areFormDetailsValid;
  };

  const getSearchOptions = useMemo(() => searchOptions || [], [searchOptions]);

  return (
    <Box display="flex" flexDirection="column" gap={2}>
      <Header title="Nominate Peers" setCurrentMode={() => setCurrentMode(PeerNominationModes.VIEW_GOALS)} />
      <EmployeeDetailHeader
        name={selectedNomination?.employee_name}
        title={selectedNomination?.job_title}
        goal={selectedNomination?.peer_nomination?.performance_review_cycle?.name}
        appliedDate=""
      />
      {readOnlyPeerNominations.length !== 0 && (
        <DataTable
          data={readOnlyPeerNominations}
          muiTableContainerProps={{
            sx: {
              height: "100%",
              maxHeight: 400,
            },
          }}
          columns={[
            {
              accessorKey: "nominee_email",
              header: "Nominee Email",
            },
            {
              accessorKey: "review_status",
              header: "Review Status",
            },
          ]}
        />
      )}
      {typedFormDetails?.map((form, index) => (
        <Grid key={index} container spacing={2} width="100%">
          <Grid key={index} item sm={8}>
            <Autocomplete
              options={getSearchOptions}
              id="peerEmail"
              size="small"
              disabled={
                selectedNomination?.peer_nomination?.status !== "Approved" &&
                prefilledNomineeEmails.has(form?.peerEmail)
              }
              onInputChange={(_ev, value, reason) => onInputChange(value, reason, index)}
              inputValue={form?.peerEmail}
              disableClearable
              autoComplete
              includeInputInList
              filterOptions={(x) => x}
              freeSolo
              renderInput={(params) => (
                <CustomTextField
                  {...params}
                  placeholder="Search employee by name or email"
                  required
                  error={!!typedFormErrors?.[index]?.peerEmail}
                  helperText={typedFormErrors?.[index]?.peerEmail}
                />
              )}
              value={form.peerEmail}
            />
          </Grid>
          <Grid item sm={4} alignSelf="center">
            <IconButton disabled={typedFormDetails?.length === 0} onClick={() => onDeleteClick(index)}>
              <Delete color="error" />
            </IconButton>
          </Grid>
        </Grid>
      ))}
      <Grid item sm={12}>
        <Button variant="text" onClick={onAddMoreClick} startIcon={<Add fontSize="small" />}>
          Add more
        </Button>
      </Grid>
      <Footer
        onSubmit={onSubmit}
        onCancel={() => setCurrentMode(PeerNominationModes.VIEW_GOALS)}
        onSaveToDrafts={function (): void {
          throw new Error("Function not implemented.");
        }}
        isSubmitDisabled={isSubmitDisabled()}
        hideDrafts
        title="Approve Nominations"
      />
    </Box>
  );
};

export default ReviewPeerNominations;
