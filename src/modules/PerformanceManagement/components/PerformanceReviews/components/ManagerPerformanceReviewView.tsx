import React from "react";
import { useAppSelector } from "src/customHooks/useAppSelector";
import CustomTabs, { TabType } from "src/modules/Common/CustomTabs/CustomTabs";
import PeerNominationRequests from "./PeerNominationRequests";
import PeerNominations from "./PeerNominations";
import PeerPerformanceRequests from "./PeerPerformanceRequests";
import PerformanceRequests from "./PerformanceRequests";
import PerformanceReview from "./PerformanceReview";

const tabs: TabType[] = [
  {
    id: 0,
    label: "Self Review",
    component: <PerformanceReview />,
  },
  {
    id: 1,
    label: "Peer Review",
    component: <PeerPerformanceRequests />,
  },
  {
    id: 2,
    label: "Peer Nominations",
    component: <PeerNominations />,
  },
  {
    id: 3,
    label: "Requests",
    component: <PerformanceRequests />,
  },
  {
    id: 4,
    label: "Peer Nomination Requests",
    component: <PeerNominationRequests />,
  },
];

const ManagerGoalManagementView = () => {
  const { isFullView } = useAppSelector((state) => state.app);
  const { userDetails } = useAppSelector((state) => state.userManagement);

  return (
    <CustomTabs
      tabs={userDetails?.is_manager ? tabs : tabs}
      currentTabIndex={tabs[0].id as unknown as number}
      hideTabBar={isFullView}
      variant="scrollable"
      scrollButtons
    />
  );
};

export default ManagerGoalManagementView;
