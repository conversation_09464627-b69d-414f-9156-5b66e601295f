import React, { useEffect } from "react";
import { useAppDispatch } from "src/customHooks/useAppDispatch";
import { PeerNominationRequest } from "src/services/api_definitions/performanceManagement.service";
import { setFullviewMode } from "src/store/slices/app.slice";
import ReviewPeerNominations from "./ReviewPeerNominations";
import ViewPeerNominationRequests from "./ViewPeerNominationRequests";

export enum PeerNominationModes {
  VIEW_GOALS = "View Goals",
  ADD_PEER_NOMINATION = "Add Peer Nomination",
}

const PeerNominationRequests: React.FC = () => {
  const dispatch = useAppDispatch();
  const [selectedGoalDetails, setSelectedGoalDetails] = React.useState<PeerNominationRequest | null>(null);
  const [currentMode, setCurrentMode] = React.useState(PeerNominationModes.VIEW_GOALS);

  useEffect(() => {
    if (currentMode === PeerNominationModes.VIEW_GOALS) {
      dispatch(setFullviewMode(false));
    }
  }, [currentMode]);

  const renderViewsAccordingToModes = () => {
    switch (currentMode) {
      case PeerNominationModes.VIEW_GOALS:
        return (
          <ViewPeerNominationRequests setSelectedGoalDetails={setSelectedGoalDetails} setCurrentMode={setCurrentMode} />
        );
      case PeerNominationModes.ADD_PEER_NOMINATION:
        return (
          <ReviewPeerNominations
            selectedNomination={selectedGoalDetails as PeerNominationRequest}
            setCurrentMode={setCurrentMode}
          />
        );
    }
  };

  return <>{renderViewsAccordingToModes()}</>;
};

export default PeerNominationRequests;
