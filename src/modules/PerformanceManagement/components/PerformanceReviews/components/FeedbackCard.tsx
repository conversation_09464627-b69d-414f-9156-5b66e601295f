import { Percent } from "@mui/icons-material";
import {
  Alert,
  Box,
  Checkbox,
  Divider,
  FormControlLabel,
  Grid,
  IconButton,
  InputAdornment,
  Paper,
  Typography,
} from "@mui/material";
import { useQuery } from "@tanstack/react-query";
import { format } from "date-fns";
import React, { useMemo } from "react";
import { Option } from "src/app/global";
import CustomDateField from "src/modules/Common/FormInputs/CustomDateField";
import CustomSelect from "src/modules/Common/FormInputs/CustomSelect";
import CustomTextField from "src/modules/Common/FormInputs/CustomTextField";
import {
  Feedback,
  GoalObjectiveFeedbacks,
  RatingsConfiguration,
  ReviewerTypes,
} from "src/services/api_definitions/performanceManagement.service";
import masterdataService from "src/services/masterdata.service";
import performanceManagementService from "src/services/performanceManagement.service";
import { formatDateToDayMonthYear } from "src/utils/dateUtils";
import DetailListItem from "../../DetailListItem";
import { sortReviewersByPriority } from "./ReadonlyFeedbackForm";

interface FeedbackCardProps {
  feedback: GoalObjectiveFeedbacks;
  formDetails: any;
  setFormDetail: (key: string, value: string, index: number) => void;
  index: number;
  reviewerType: ReviewerTypes;
  overallRating?: number;
  status?: string;
}

const feedbackFormConfig: Partial<
  Record<
    ReviewerTypes,
    {
      formKeys: {
        [key: string]: string;
      };
      formTitles: {
        [key: string]: string;
      };
    }
  >
> = {
  self: {
    formKeys: {
      comments: "comments",
      ratingValue: "ratingValue",
      ratingDescription: "ratingDescription",
      overallComment: "overallComment",
    },
    formTitles: {
      comments: "Comments",
      ratingValue: "Rating Value",
      ratingDescription: "Rating Description",
      overallComment: "Overall Comment",
      overallRating: "",
      reviewTitle: "Self Review",
    },
  },
  manager: {
    formKeys: {
      comments: "managerComments",
      ratingValue: "managerRatingValue",
      ratingDescription: "managerRatingDescription",
      overallComment: "managerOverallComment",
    },
    formTitles: {
      comments: "Manager Comments",
      ratingValue: "Manager Rating Value",
      ratingDescription: "Manager Rating Description",
      overallComment: "Manager Overall Comment",
      overallRating: "Manager Overall Rating",
      reviewTitle: "Manager Review",
    },
  },
  peer: {
    formKeys: {
      comments: "peerComments",
      ratingValue: "peerRatingValue",
      ratingDescription: "peerRatingDescription",
      overallComment: "peerOverallComment",
      isAnonymous: "isAnonymous",
    },
    formTitles: {
      comments: "Peer Comments",
      ratingValue: "Peer Rating Value",
      ratingDescription: "Peer Rating Description",
      overallComment: "Peer Overall Comment",
      reviewTitle: "Peer Review",
      isAnonymous: "Submit my feedback without revealing my identity",
    },
  },
  hrbp: {
    formKeys: {
      comments: "hrbpComments",
      ratingValue: "hrbpRatingValue",
      ratingDescription: "hrbpRatingDescription",
      overallComment: "hrbpOverallComment",
    },
    formTitles: {
      comments: "HRBP Comments",
      ratingValue: "HRBP Rating Value",
      ratingDescription: "HRBP Rating Description",
      overallComment: "HRBP Overall Comment",
      reviewTitle: "HRBP Review",
    },
  },
  stakeholder: {
    formKeys: {
      comments: "stakeholderComments",
      ratingValue: "stakeholderRatingValue",
      ratingDescription: "stakeholderRatingDescription",
      overallComment: "stakeholderOverallComment",
    },
    formTitles: {
      comments: "Stakeholder Comments",
      ratingValue: "Stakeholder Rating Value",
      ratingDescription: "Stakeholder Rating Description",
      overallComment: "Stakeholder Overall Comment",
      reviewTitle: "Stakeholder Review",
    },
  },
};

const getFeedbackTitle = (feedback: Feedback, key: string) => {
  if (feedback.reviewer_name && feedback.reviewer_job_title) {
    return `${feedbackFormConfig[feedback?.reviewer_type]?.formTitles[key]} (${feedback.reviewer_name} : ${feedback.reviewer_job_title})`;
  }
  return feedbackFormConfig[feedback?.reviewer_type]?.formTitles[key];
};

const ReadonlyFeedbackItem: React.FC<{
  feedback: Feedback;
  reviewerType: ReviewerTypes;
}> = ({ feedback, reviewerType }) => {
  if (feedback?.reviewer_type === reviewerType) {
    return null;
  }
  if (feedback?.reviewer_type === "self") {
    return (
      <>
        <Grid item sm={12}>
          <Typography fontWeight={500} fontSize={18}>
            {getFeedbackTitle(feedback, "reviewTitle") || ""}
          </Typography>
        </Grid>
        <Grid item sm={8}>
          <DetailListItem title="Rating" value={feedback?.display_rating || ""} />
        </Grid>
        <Grid item sm={12}>
          <DetailListItem title="Comments" value={feedback?.comments || ""} />
        </Grid>
      </>
    );
  }
  return (
    <Paper elevation={3} sx={{ margin: 1, padding: 2, width: "100%" }}>
      <Grid container spacing={2}>
        <Grid item sm={12}>
          <Typography fontWeight={500} fontSize={18}>
            {getFeedbackTitle(feedback, "reviewTitle") || ""}
          </Typography>
        </Grid>
        <Grid item sm={8}>
          <DetailListItem title="Rating" value={feedback?.display_rating || ""} />
        </Grid>
        <Grid item sm={12}>
          <DetailListItem title="Comments" value={feedback?.comments || ""} />
        </Grid>
      </Grid>
    </Paper>
  );
};

const ReviewForms: React.FC<
  FeedbackCardProps & {
    ratings: RatingsConfiguration[];
    ratingsToDisplay: Option<keyof RatingsConfiguration, RatingsConfiguration>[];
  }
> = ({ formDetails, setFormDetail, index, reviewerType, ratingsToDisplay, ratings }) => {
  switch (reviewerType) {
    case "self":
      return (
        <>
          <Grid item xs={6}>
            <CustomSelect
              id="rating"
              name="rating"
              label="Rating"
              required
              sx={{ width: "100%" }}
              value={formDetails?.ratingValue || ""}
              options={ratingsToDisplay || []}
              size="small"
              onChange={(e: any) => {
                setFormDetail("ratingValue", e.target.value, index);
                setFormDetail(
                  "ratingDescription",
                  ratings?.find((rating) => rating.value === e.target.value)?.description || "",
                  index,
                );
              }}
            />
          </Grid>
          <Grid item xs={12}>
            <CustomTextField
              name="comment"
              title="Comment"
              required
              multiline
              rows={3}
              value={formDetails?.comment || ""}
              fullWidth
              onChange={(e) => setFormDetail("comment", e.target.value, index)}
            />
          </Grid>
        </>
      );
    case "manager":
      return (
        <>
          <Grid item xs={12}>
            <Box display="flex" flexDirection="column" gap={3}>
              <Divider />
              <Typography fontWeight={600} fontSize={16}>
                Manager Feedback
              </Typography>
            </Box>
          </Grid>
          <Grid item sm={3}>
            <CustomSelect
              id="managerRating"
              name="managerRating"
              required
              label="Manager Rating"
              sx={{ width: "100%" }}
              value={formDetails?.managerRatingValue || ""}
              options={ratingsToDisplay || []}
              size="small"
              onChange={(e: any) => {
                setFormDetail("managerRatingValue", e.target.value, index);
                setFormDetail(
                  "managerRatingDescription",
                  ratings?.find((rating) => rating.value === e.target.value)?.description || "",
                  index,
                );
              }}
            />
          </Grid>
          <Grid item sm={3}>
            <CustomTextField
              id="managerFinalWeightage"
              name="managerFinalWeightage"
              required
              type="number"
              inputProps={{
                min: 0,
                max: 100,
                step: 1,
              }}
              title="Final Weightage"
              sx={{ width: "100%" }}
              value={formDetails?.managerFinalWeightage || ""}
              size="small"
              InputProps={{
                endAdornment: (
                  <InputAdornment position="end">
                    <IconButton>
                      <Percent fontSize="small" />
                    </IconButton>
                  </InputAdornment>
                ),
              }}
              onChange={(e: any) => setFormDetail("managerFinalWeightage", e.target.value, index)}
            />
          </Grid>
          <Grid item xs={12}>
            <CustomTextField
              name="managerComment"
              title="Manager Comment"
              multiline
              required
              rows={3}
              value={formDetails?.managerComment || ""}
              fullWidth
              onChange={(e) => setFormDetail("managerComment", e.target.value, index)}
            />
          </Grid>
        </>
      );
    case "peer":
      return (
        <>
          <Grid item xs={12}>
            <Box display="flex" flexDirection="column" gap={3}>
              <Typography fontWeight={500} fontSize={16}>
                Peer Feedback
              </Typography>
            </Box>
          </Grid>
          <Grid item xs={6}>
            <CustomSelect
              id="peerRating"
              name="peerRating"
              label="Peer Rating"
              sx={{ width: "100%" }}
              value={formDetails?.peerRating || ""}
              options={ratingsToDisplay || []}
              size="small"
              onChange={(e: any) => {
                setFormDetail("peerRatingValue", e.target.value, index);
                setFormDetail(
                  "peerRating",
                  ratings?.find((rating) => rating.value === e.target.value)?.display_rating || "",
                  index,
                );
                setFormDetail(
                  "peerRatingDescription",
                  ratings?.find((rating) => rating.value === e.target.value)?.description || "",
                  index,
                );
              }}
            />
          </Grid>
          <Grid item xs={12}>
            <CustomTextField
              name="peerComment"
              title="Peer Comment"
              multiline
              rows={3}
              required={!!formDetails?.peerRating}
              value={formDetails?.peerComment || ""}
              fullWidth
              onChange={(e) => setFormDetail("peerComment", e.target.value, index)}
            />
          </Grid>
        </>
      );
    default:
      return null;
  }
};

/**
 * A functional component that renders a feedback card for performance management.
 * It handles different reviewer types and displays relevant information accordingly.
 *
 * @param {FeedbackCardProps} props - The properties for the FeedbackCard component.
 * @param {Feedback} props.feedback - The feedback object.
 * @param {object} props.formDetails - The form details object.
 * @param {function} props.setFormDetail - A function to update the form details.
 * @param {number} props.index - The index of the feedback card.
 * @param {string} props.reviewerType - The type of reviewer (e.g., self, manager, peer).
 * @param {any} props.overallRating - The overall rating.
 * @return {JSX.Element} The rendered feedback card.
 */

const FeedbackCard: React.FC<FeedbackCardProps> = ({
  feedback,
  formDetails,
  setFormDetail,
  index,
  reviewerType,
  overallRating,
}) => {
  const { data: ratings } = useQuery(["ratings"], async () => performanceManagementService.getRatingsConfiguration(), {
    refetchOnMount: false,
    refetchOnWindowFocus: false,
  });
  const { data: reviews } = useQuery(
    ["review-statuses"],
    async () => masterdataService.getACLs("GoalObjectiveStatus"),
    {
      refetchOnMount: false,
      refetchOnWindowFocus: false,
    },
  );

  const ratingsToDisplay = useMemo(() => {
    return ratings?.map((rating) => ({
      label: rating.display_rating,
      value: rating.value,
    }));
  }, [ratings]);

  // This is working till now because only completed goals have actual completion date, can i refactor it somehow?
  const getDisplayableGoalObjectives = () => {
    if (reviewerType === "self") {
      return (
        <>
          {formDetails.status === "Completed" && !formDetails.actualCompletionDate && (
            <Grid item sm={12}>
              <Alert severity="warning">Actual completion date is required when status is Completed</Alert>
            </Grid>
          )}
          {!formDetails.rating && !formDetails.comment && (
            <Grid item sm={12}>
              <Alert severity="warning">Both rating and comments are required</Alert>
            </Grid>
          )}
          <Grid item xs={4}>
            <CustomSelect
              id="status"
              name="status"
              label="Status"
              size="small"
              required
              value={formDetails.status}
              sx={{ width: "100%" }}
              options={
                reviews?.map((review) => ({
                  label: review,
                  value: review,
                })) || []
              }
              onChange={(e: any) => setFormDetail("status", e?.target?.value, index)}
            />
          </Grid>
          <Grid item xs={4}>
            <CustomDateField
              title="Planned Completion Date"
              name="plannedCompletionDate"
              value={feedback.goal_objective?.target_completion_date as unknown as Date}
              disabled
              required
              onChange={(value: Date | null) =>
                setFormDetail("plannedCompletionDate", format(value as Date, "yyyy-MM-dd"), index)
              }
              slotProps={{
                textField: {
                  size: "small",
                  fullWidth: true,
                  required: true,
                },
              }}
            />
          </Grid>
          <Grid item xs={4}>
            <CustomDateField
              title="Actual Completion Date"
              name="actualCompletionDate"
              value={formDetails.actualCompletionDate}
              onChange={(value: Date | null) =>
                setFormDetail("actualCompletionDate", format(value as Date, "yyyy-MM-dd"), index)
              }
              required={formDetails.status === "Completed"}
              disabled={formDetails.status !== "Completed"}
              slotProps={{
                textField: {
                  size: "small",
                  fullWidth: true,
                  required: true,
                },
              }}
            />
          </Grid>
        </>
      );
    }
    // should it be like this?

    return (
      <>
        <Grid item sm={4}>
          <DetailListItem title="Status" value={feedback.goal_objective?.status || ""} />
        </Grid>
        <Grid item sm={4}>
          <DetailListItem
            title="Planned Completion Date"
            value={formatDateToDayMonthYear(feedback?.goal_objective?.target_completion_date || null)}
          />
        </Grid>
        <Grid item sm={4}>
          <DetailListItem
            title="Actual Completion Date"
            value={formatDateToDayMonthYear(feedback?.goal_objective?.actual_completion_date || null)}
          />
        </Grid>
        <Grid item sm={4}>
          <DetailListItem
            title="Estimated Weightage"
            value={
              feedback.goal_objective?.estimated_weightage
                ? `${(feedback?.goal_objective?.estimated_weightage * 100).toFixed(0)}%`
                : "N/A"
            }
          />
        </Grid>
      </>
    );
  };

  const displayReadonlyFeedback = (feedback: Feedback) => {
    if (feedback?.reviewer_type === "self") {
      return (
        <Grid item sm={12}>
          <DetailListItem title={getFeedbackTitle(feedback, "overallComment") || ""} value={feedback.comments || ""} />
        </Grid>
      );
    }
    return (
      <Paper elevation={3} sx={{ margin: 1, padding: 2, width: "100%" }}>
        <Grid item sm={12}>
          <DetailListItem title={getFeedbackTitle(feedback, "overallComment") || ""} value={feedback.comments || ""} />
        </Grid>
      </Paper>
    );
  };

  const getDisplayableFeedbacks = (feedback: GoalObjectiveFeedbacks, reviewerType: ReviewerTypes) => {
    const filteredFeedbacks = feedback.feedbacks?.filter((feedback) => {
      if (reviewerType === feedback?.reviewer_type) {
        return false;
      }
      return true;
    });

    const sortedFeedbacks = sortReviewersByPriority(filteredFeedbacks)?.map((feedback: Feedback) =>
      displayReadonlyFeedback(feedback),
    );
    return sortedFeedbacks;
  };

  const renderViews = () => {
    if (!feedback?.goal_objective) {
      return (
        <>
          {getDisplayableFeedbacks(feedback, reviewerType)}
          {reviewerType === "manager" && (
            <Grid item sm={12}>
              <DetailListItem
                title={feedbackFormConfig[reviewerType]?.formTitles["overallRating"] || ""}
                value={(overallRating as unknown as string) || "Calculated when Ratings are given"}
              />
            </Grid>
          )}
          <Grid item sm={12}>
            <CustomTextField
              name={feedbackFormConfig[reviewerType]?.formKeys["overallComment"]}
              title={feedbackFormConfig[reviewerType]?.formTitles["overallComment"]}
              multiline
              rows={3}
              value={
                formDetails?.[
                  feedbackFormConfig[reviewerType]?.formKeys["overallComment"] as keyof typeof formDetails
                ] || ""
              }
              fullWidth
              onChange={(e) =>
                setFormDetail(
                  feedbackFormConfig[reviewerType]?.formKeys["overallComment"] as string,
                  e.target.value,
                  index,
                )
              }
              required
            />
          </Grid>
          {reviewerType === "peer" && (
            <Grid item sm={12}>
              <FormControlLabel
                label={feedbackFormConfig[reviewerType]?.formTitles["isAnonymous"]}
                control={
                  <Checkbox
                    name={feedbackFormConfig[reviewerType]?.formKeys["isAnonymous"]}
                    title={feedbackFormConfig[reviewerType]?.formTitles["isAnonymous"]}
                    checked={!!formDetails.isAnonymous}
                    onChange={(_e, checked) =>
                      setFormDetail(
                        feedbackFormConfig[reviewerType]?.formKeys["isAnonymous"] as string,
                        checked as unknown as string,
                        index,
                      )
                    }
                  />
                }
              />
            </Grid>
          )}
        </>
      );
    }

    return (
      <>
        {getDisplayableGoalObjectives()}
        <Grid item sm={12}>
          <DetailListItem title="Objective" value={feedback.goal_objective?.title || ""} />
        </Grid>
        <Grid item sm={12}>
          <DetailListItem title="Description" value={feedback.goal_objective?.description || ""} />
        </Grid>
        <Grid item sm={12}>
          <Divider />
        </Grid>
        {/* This means that the employee hasnt reviewed it yet */}
        {!(reviewerType === "self") &&
          sortReviewersByPriority(feedback?.feedbacks).map((feedback: Feedback) => (
            <ReadonlyFeedbackItem key={feedback.id} feedback={feedback} reviewerType={reviewerType} />
          ))}
        <ReviewForms
          feedback={feedback}
          formDetails={formDetails}
          index={index}
          ratings={ratings as RatingsConfiguration[]}
          ratingsToDisplay={ratingsToDisplay as any}
          reviewerType={reviewerType}
          setFormDetail={setFormDetail}
          overallRating={overallRating}
        />
      </>
    );
  };

  return (
    <Paper elevation={3}>
      <Grid container spacing={2} padding={3}>
        {renderViews()}
      </Grid>
    </Paper>
  );
};

export default FeedbackCard;
