import { Box } from "@mui/material";
import React from "react";
import DataTable from "src/modules/Common/Table/DataTable";
import { PeerNominationModes } from "./PeerNominations";
import { Header } from "./PerformanceReviewForm";
import PeerNominations from "./ViewPeerNominationGoals";

interface ReadOnlyPeerNominationsProps {
  selectedNomination: PeerNominations;
  setCurrentMode: (currentMode: PeerNominationModes) => void;
}

const ReadOnlyPeerNominations: React.FC<ReadOnlyPeerNominationsProps> = ({ selectedNomination, setCurrentMode }) => {
  return (
    <Box>
      <Header setCurrentMode={() => setCurrentMode(PeerNominationModes.VIEW_GOALS)} title="View Review Cycles" />
      <DataTable
        data={selectedNomination?.peer_nominations || []}
        columns={[
          {
            accessorKey: "nominee_email",
            header: "Nominee Email",
          },
          {
            accessorKey: "review_status",
            header: "Review Status",
          },
        ]}
      />
    </Box>
  );
};

export default ReadOnlyPeerNominations;
