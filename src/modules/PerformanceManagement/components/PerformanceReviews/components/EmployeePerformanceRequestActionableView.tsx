import { Box } from "@mui/material";
import { useMutation } from "@tanstack/react-query";
import React, { useMemo, useState } from "react";
import { useForm } from "src/customHooks/useForm";
import { PerformanceReviewRequest } from "src/services/api_definitions/performanceManagement.service";
import performanceManagementService from "src/services/performanceManagement.service";
import { EmployeeDetailHeader } from "../../EmployeeGoalRequestActionableView";
import PromotionRecommendation from "../PromotionRecommendation";
import FeedbackCard from "./FeedbackCard";
import FeedbackVerdicts from "./FeedbackVerdicts";
import { PerformanceRequestStates } from "./PerformanceRequests";
import { Footer, Header } from "./PerformanceReviewForm";

interface EmployeePerformanceReviewActionableViewProps {
  setCurrentMode: (state: PerformanceRequestStates) => void;
  selectedRequest: PerformanceReviewRequest;
}

export type PromotionFormDetail = {
  isUpForPromotion: boolean;
  justification: string;
  band: string;
  grade: string;
  level: string;
  jobTitle: string;
};

const EmployeePerformanceRequestActionableView: React.FC<EmployeePerformanceReviewActionableViewProps> = ({
  setCurrentMode,
  selectedRequest,
}) => {
  const [currentSelectedAction, setCurrentSelectedAction] = useState<any>({
    status: null,
    reason: null,
  });

  const defaultFormState = useMemo(() => {
    return selectedRequest?.review.goal_objective_feedbacks?.map((feedbackObjective) => {
      return {
        status: feedbackObjective.goal_objective?.status,
        actualCompletionDate: feedbackObjective.goal_objective?.actual_completion_date,
        managerRatingValue: feedbackObjective.feedbacks.find((eachFeedback) => eachFeedback.reviewer_type === "manager")
          ?.rating_value,
        managerRatingDescription: feedbackObjective.feedbacks.find(
          (eachFeedback) => eachFeedback.reviewer_type === "manager",
        )?.rating_description,
        managerComment: feedbackObjective.feedbacks.find((eachFeedback) => eachFeedback.reviewer_type === "manager")
          ?.comments,
        feedbackId: feedbackObjective.feedbacks.find((eachFeedback) => eachFeedback.reviewer_type === "manager")?.id,
        managerOverallComment:
          feedbackObjective.goal_objective === null
            ? feedbackObjective.feedbacks.find((eachFeedback) => eachFeedback.reviewer_type === "manager")?.comments
            : "",
        managerFinalWeightage: feedbackObjective?.goal_objective?.final_weightage
          ? feedbackObjective?.goal_objective?.final_weightage * 100
          : null,
      };
    });
  }, [selectedRequest.review]);

  const { setFormDetail, formDetails } = useForm({
    initialState: defaultFormState as any,
    isBulk: true,
    validations: {
      overallManagerFeedback: [],
      ratingValue: [],
      ratingDescription: [],
    },
  });

  const defaultPromotionFormState = useMemo(
    () => ({
      isUpForPromotion: !!selectedRequest?.review?.promotion,
      justification: selectedRequest?.review?.promotion?.justification || "",
      band: selectedRequest?.review?.promotion?.job_title?.band || "",
      level: selectedRequest?.review?.promotion?.job_title?.level || "",
      grade: selectedRequest?.review?.promotion?.job_title?.grade || "",
      jobTitle: selectedRequest?.review?.promotion?.job_title?.name || "",
    }),
    [selectedRequest?.review],
  );

  const {
    formDetails: promotionFormDetails,
    formErrors: promotionFormErrors,
    setFormDetail: setPromotionFormDetail,
  } = useForm<PromotionFormDetail>({
    initialState: defaultPromotionFormState as PromotionFormDetail,
    validations: {
      isUpForPromotion: [],
      justification: [],
      band: [],
      level: [],
      grade: [],
      jobTitle: [],
    },
  });

  const updatePerformanceReviewMutation = useMutation({
    mutationFn: async (payload: any) => {
      return performanceManagementService.updatePerformanceReviewsByReviewerType("manager", payload);
    },
    onSuccess: () => {
      setCurrentMode(PerformanceRequestStates.VIEW_REQUESTS);
    },
  });

  const sendBackReviewMutation = useMutation({
    mutationFn: async (payload: any) => {
      return performanceManagementService.sendBackPerformanceReview("manager", payload.review_id, payload.reason);
    },
    onSuccess: () => {
      setCurrentMode(PerformanceRequestStates.VIEW_REQUESTS);
    },
  });

  const createPromotionRequestBody = () => {
    if (!promotionFormDetails.isUpForPromotion) {
      return null;
    }
    return {
      justification: promotionFormDetails?.justification,
      job_title: {
        band: promotionFormDetails?.band || null,
        level: promotionFormDetails?.level || null,
        grade: promotionFormDetails?.grade || null,
        name: promotionFormDetails?.jobTitle || null,
        work_role: [promotionFormDetails?.band, promotionFormDetails?.level, promotionFormDetails?.grade]
          .filter(Boolean)
          .join(" : "),
      },
    };
  };

  const createRequestBody = (status: "Approved" | "Draft") => {
    return {
      review_id: selectedRequest?.review?.review_id,
      status: status,
      promotion: createPromotionRequestBody(),
      feedbacks: selectedRequest?.review?.goal_objective_feedbacks
        ?.map((eachFeedback, index) => {
          // handle overall goal request body
          if (eachFeedback.goal_objective === null) {
            return {
              id: formDetails[index].feedbackId,
              goal_objective: null,
              comments: formDetails[index].managerOverallComment,
            };
          }
          return {
            id: formDetails[index].feedbackId,
            goal_objective: {
              ...eachFeedback.goal_objective,
              final_weightage: Number(formDetails[index].managerFinalWeightage) / 100,
            },
            comments: formDetails[index].managerComment,
            rating_value: formDetails[index].managerRatingValue,
            rating_description: formDetails[index].managerRatingDescription,
          };
        })
        .filter((eachFeedback) => eachFeedback.comments),
    };
  };

  const isSubmitDisabled = useMemo(() => {
    if (!currentSelectedAction.status) {
      return true;
    }

    if (currentSelectedAction.status === "Send Back" && !currentSelectedAction.reason) {
      return true;
    }

    // escape hatch to not send back if feedback is not filled
    if (currentSelectedAction.status === "Send Back" && currentSelectedAction.reason) {
      return false;
    }

    if (
      !selectedRequest ||
      !selectedRequest?.review.goal_objective_feedbacks ||
      selectedRequest?.review?.goal_objective_feedbacks?.length === 0
    ) {
      return true;
    }
    return selectedRequest?.review?.goal_objective_feedbacks.some((eachFeedback, index) => {
      if (eachFeedback.goal_objective === null) {
        const isOverallFeedbackFilled: boolean = formDetails[index].managerOverallComment;
        return !isOverallFeedbackFilled;
      }

      const areOtherFormDetailsFilled: boolean =
        !!formDetails[index].managerRatingValue &&
        !!formDetails[index].managerRatingDescription &&
        !!formDetails[index].managerComment &&
        !!formDetails[index].managerFinalWeightage;

      return !areOtherFormDetailsFilled;
    });
  }, [formDetails, selectedRequest, currentSelectedAction]);

  const onSaveToDrafts = () => {
    updatePerformanceReviewMutation.mutate(createRequestBody("Draft"));
  };

  const onSubmit = () => {
    if (currentSelectedAction.status === "Send Back") {
      sendBackReviewMutation.mutate({
        review_id: selectedRequest?.review?.review_id,
        reason: currentSelectedAction.reason as unknown as string,
      });
      return;
    }
    updatePerformanceReviewMutation.mutate(createRequestBody("Approved"));
  };

  const getOverallRating = useMemo(() => {
    return formDetails
      .reduce((finalRating: number, currentFormDetail: any) => {
        if (currentFormDetail.managerRatingValue && currentFormDetail.managerFinalWeightage) {
          finalRating =
            finalRating +
            Number(currentFormDetail.managerRatingValue) *
              Number(Number(currentFormDetail.managerFinalWeightage) / 100);
        }
        return finalRating;
      }, 0)
      ?.toFixed(0);
  }, [formDetails]);

  const arePromotionDetailsValid = () => {
    if (!promotionFormDetails?.isUpForPromotion) {
      return true;
    }
    if (promotionFormDetails?.isUpForPromotion && !promotionFormDetails?.justification) {
      return false;
    }
    return true;
  };

  return (
    <Box display="flex" flexDirection="column" gap={2}>
      <Header
        setCurrentMode={() => setCurrentMode(PerformanceRequestStates.VIEW_REQUESTS)}
        title="View Review Requests"
      />
      <EmployeeDetailHeader
        title={selectedRequest.job_title}
        name={selectedRequest.employee_name}
        goal={selectedRequest.review.performance_review_cycle?.name || ""}
        appliedDate=""
      />
      <Box m={1} marginBottom={10} display="flex" flexDirection="column" gap={2}>
        {selectedRequest.review.goal_objective_feedbacks?.map((eachObjectiveFeedback, index) => (
          <FeedbackCard
            feedback={eachObjectiveFeedback}
            key={eachObjectiveFeedback?.goal_objective?.id || "overall"}
            reviewerType="manager"
            formDetails={formDetails[index]}
            index={index}
            setFormDetail={setFormDetail}
            overallRating={getOverallRating}
            status={selectedRequest?.review?.status}
          />
        ))}
        <PromotionRecommendation
          reviewerType="manager"
          selectedRequest={selectedRequest}
          promotion={{
            promotionFormDetails,
            promotionFormErrors,
            setPromotionFormDetail,
          }}
        />
        <FeedbackVerdicts
          status={selectedRequest?.review?.status}
          currentSelectedAction={currentSelectedAction}
          setCurrentSelectedAction={setCurrentSelectedAction}
        />
      </Box>
      <Footer
        isSubmitDisabled={isSubmitDisabled || !arePromotionDetailsValid()}
        onCancel={() => setCurrentMode(PerformanceRequestStates.VIEW_REQUESTS)}
        onSaveToDrafts={onSaveToDrafts}
        onSubmit={onSubmit}
      />
    </Box>
  );
};

export default EmployeePerformanceRequestActionableView;
