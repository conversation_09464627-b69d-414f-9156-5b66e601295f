import { Box } from "@mui/material";
import { useMutation } from "@tanstack/react-query";
import React, { useMemo } from "react";
import { useForm } from "src/customHooks/useForm";
import { PerformanceReviewRequest } from "src/services/api_definitions/performanceManagement.service";
import performanceManagementService from "src/services/performanceManagement.service";
import { EmployeeDetailHeader } from "../../EmployeeGoalRequestActionableView";
import FeedbackCard from "./FeedbackCard";
import { PerformanceRequestStates } from "./PerformanceRequests";
import { Footer, Header } from "./PerformanceReviewForm";

interface EmployeePerformanceReviewActionableViewProps {
  setCurrentMode: (state: PerformanceRequestStates) => void;
  selectedRequest: PerformanceReviewRequest;
}

const PeerPerformanceRequestActionableView: React.FC<EmployeePerformanceReviewActionableViewProps> = ({
  setCurrentMode,
  selectedRequest,
}) => {
  const defaultFormState = useMemo(() => {
    return selectedRequest?.review.goal_objective_feedbacks?.map((feedbackObjective) => {
      return {
        status: feedbackObjective.goal_objective?.status,
        actualCompletionDate: feedbackObjective.goal_objective?.actual_completion_date,
        peerRatingValue: feedbackObjective.feedbacks.find((eachFeedback) => eachFeedback.reviewer_type === "peer")
          ?.rating_value,
        peerRating: feedbackObjective.feedbacks.find((eachFeedback) => eachFeedback.reviewer_type === "peer")
          ?.display_rating,
        peerRatingDescription: feedbackObjective.feedbacks.find((eachFeedback) => eachFeedback.reviewer_type === "peer")
          ?.rating_description,
        peerComment:
          feedbackObjective?.goal_objective !== null
            ? feedbackObjective.feedbacks.find((eachFeedback) => eachFeedback.reviewer_type === "peer")?.comments
            : "",
        feedbackId: feedbackObjective.feedbacks.find((eachFeedback) => eachFeedback.reviewer_type === "peer")?.id,
        peerOverallComment:
          feedbackObjective.goal_objective === null
            ? feedbackObjective.feedbacks.find((eachFeedback) => eachFeedback.reviewer_type === "peer")?.comments
            : "",
        isAnonymous: selectedRequest?.review?.anonymous,
      };
    });
  }, [selectedRequest.review]);

  const { setFormDetail, formDetails, formErrors } = useForm({
    initialState: defaultFormState as any,
    isBulk: true,
    validations: {
      overallPeerFeedback: [],
      ratingValue: [],
      ratingDescription: [],
    },
  });

  const updatePerformanceReviewMutation = useMutation({
    mutationFn: async (payload: any) => {
      return performanceManagementService.updatePerformanceReviewsByReviewerType("peer", payload);
    },
    onSuccess: () => {
      setCurrentMode(PerformanceRequestStates.VIEW_REQUESTS);
    },
  });

  // TODO: The anonymous check need to be changed, cause its a hack!!.
  const createRequestBody = (status: "Submitted" | "Draft") => {
    return {
      review_id: selectedRequest?.review?.review_id,
      status: status,
      anonymous: formDetails?.some(
        (eachFormDetail: any, index: number) =>
          !!eachFormDetail.isAnonymous && !selectedRequest?.review?.goal_objective_feedbacks?.[index].goal_objective,
      ),
      feedbacks: selectedRequest?.review?.goal_objective_feedbacks
        ?.map((eachFeedback, index) => {
          // handle overall goal request body
          if (eachFeedback.goal_objective === null) {
            return {
              id: formDetails[index].feedbackId,
              goal_objective: null,
              comments: formDetails[index].peerOverallComment,
              anonymous: formDetails[index].isAnonymous,
            };
          }
          return {
            id: formDetails[index].feedbackId,
            goal_objective: {
              ...eachFeedback.goal_objective,
            },
            comments: formDetails[index].peerComment,
            anonymous: formDetails[index].isAnonymous,
            rating_value: formDetails[index].peerRatingValue,
            rating_description: formDetails[index].peerRatingDescription,
          };
        })
        .filter((eachFeedback) => eachFeedback.comments),
    };
  };

  const isSubmitDisabled = useMemo(() => {
    if (
      !selectedRequest ||
      !selectedRequest?.review.goal_objective_feedbacks ||
      selectedRequest?.review?.goal_objective_feedbacks?.length === 0
    ) {
      return true;
    }
    return selectedRequest.review.goal_objective_feedbacks.some((eachFeedback, index) => {
      if (eachFeedback.goal_objective === null) {
        return !formDetails[index].peerOverallComment;
      }

      const areSomeFormFieldsFilled = formDetails.every((detail: any) => {
        const newFormDetail = {
          peerComment: detail.peerComment,
          peerRatingValue: detail.peerRatingValue,
          peerRatingDescription: detail.peerRatingDescription,
        };
        const values = Object.values(newFormDetail);
        const allFilled = values.every((value) => value !== "" && value !== null && value !== undefined);
        const allEmpty = values.every((value) => value === "" || value === null || value === undefined);
        return allFilled || allEmpty;
      });
      return !areSomeFormFieldsFilled;
    });
  }, [formDetails, selectedRequest]);

  const onSaveToDrafts = () => {
    updatePerformanceReviewMutation.mutate(createRequestBody("Draft"));
  };

  const onSubmit = () => {
    updatePerformanceReviewMutation.mutate(createRequestBody("Submitted"));
  };

  return (
    <Box display="flex" flexDirection="column" gap={2}>
      <Header setCurrentMode={() => setCurrentMode(PerformanceRequestStates.VIEW_REQUESTS)} title="View Peer Review" />
      <EmployeeDetailHeader
        title={selectedRequest.job_title}
        name={selectedRequest.employee_name}
        goal={selectedRequest.review.performance_review_cycle?.name || ""}
        appliedDate=""
      />
      <Box m={1} marginBottom={10} display="flex" flexDirection="column" gap={2}>
        {selectedRequest.review.goal_objective_feedbacks?.map((eachObjectiveFeedback, index) => (
          <FeedbackCard
            feedback={eachObjectiveFeedback}
            key={eachObjectiveFeedback?.goal_objective?.id || "overall"}
            reviewerType="peer"
            formDetails={formDetails[index]}
            formErrors={formErrors[index]}
            index={index}
            status={selectedRequest?.review?.status}
            setFormDetail={setFormDetail}
          />
        ))}
      </Box>
      <Footer
        isSubmitDisabled={isSubmitDisabled}
        onCancel={() => setCurrentMode(PerformanceRequestStates.VIEW_REQUESTS)}
        onSaveToDrafts={onSaveToDrafts}
        onSubmit={onSubmit}
      />
    </Box>
  );
};

export default PeerPerformanceRequestActionableView;
