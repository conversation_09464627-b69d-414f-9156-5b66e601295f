import { Grid2, Paper, Typography } from "@mui/material";
import React from "react";
import { PerformanceReview } from "src/services/api_definitions/performanceManagement.service";
import DetailListItem from "../../DetailListItem";

type ReadOnlyPromotionRecommendationProps = {
  promotion: PerformanceReview["promotion"];
};

const typography = {
  band: "Band",
  level: "Level",
  grade: "Grade",
  name: "Job Title", // job title name
  justification: "Justification",
  status: "Status",
  comments: "HR Comments",
  effectiveDate: "Effective Date",
};

const ReadOnlyPromotionRecommendation = ({ promotion }: ReadOnlyPromotionRecommendationProps) => {
  if (!promotion) {
    return null;
  }
  const jobTitlesToDisplay = promotion?.job_title
    ? [
        {
          title: typography.band,
          value: promotion.job_title.band,
        },
        {
          title: typography.level,
          value: promotion.job_title.level,
        },
        {
          title: typography.grade,
          value: promotion.job_title.grade,
        },
        {
          title: typography.name,
          value: promotion.job_title.name,
        },
      ].filter((item) => item.value)
    : [];
  const columnDivider = 12 / jobTitlesToDisplay.length;
  return (
    <Grid2 spacing={2} container component={Paper} elevation={3} m={1} p={2}>
      <Grid2 size={12}>
        <Typography variant="overline" fontWeight={600} fontSize={16}>
          Promotion Recommendation
        </Typography>
      </Grid2>
      {jobTitlesToDisplay?.map((jobTitle, index) => (
        <Grid2 key={index} size={columnDivider}>
          <DetailListItem title={jobTitle.title} value={jobTitle.value} />
        </Grid2>
      ))}
      <Grid2 size={12}>
        <DetailListItem title={typography.justification} value={promotion?.justification} />
      </Grid2>
      {promotion?.status && (
        <Grid2 size={4}>
          <DetailListItem title={typography.status} value={promotion?.status} />
        </Grid2>
      )}
      {promotion?.effective_date && (
        <Grid2 size={4}>
          <DetailListItem title={typography.effectiveDate} value={promotion?.effective_date || "N/A"} />
        </Grid2>
      )}
      {promotion?.comments && (
        <Grid2 size={12}>
          <DetailListItem title={typography.comments} value={promotion?.comments || "N/A"} />
        </Grid2>
      )}
    </Grid2>
  );
};

export default ReadOnlyPromotionRecommendation;
