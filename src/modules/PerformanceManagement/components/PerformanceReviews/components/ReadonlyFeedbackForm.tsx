import { Box, Divider, Grid, Paper, Typography } from "@mui/material";
import React, { useMemo } from "react";
import {
  Feedback,
  GoalObjectiveFeedbacks,
  ReviewerTypes,
} from "src/services/api_definitions/performanceManagement.service";
import { formatDateToDayMonthYear } from "src/utils/dateUtils";
import { getStatusColors } from "src/utils/typographyUtils";
import DetailListItem from "../../DetailListItem";

interface ReadonlyFeedbackFromProps {
  goalObjectiveFeedback: GoalObjectiveFeedbacks;
  reviewerType: ReviewerTypes;
}

// TODO: This might be needed in different places also, if that requirement comes just make it generic & change the method name
export const sortReviewersByPriority = (reviewers: any[]): any => {
  if (!reviewers) {
    return [];
  }
  const priorityOrder = ["self", "peer", "manager", "hrbp", "stakeholder"];

  return reviewers.sort((a, b) => {
    return priorityOrder.indexOf(a.reviewer_type) - priorityOrder.indexOf(b.reviewer_type);
  });
};

const allowedReviewsForReviewerTypes: Partial<Record<ReviewerTypes, Set<ReviewerTypes>>> = {
  self: new Set(["self", "manager", "peer"]),
  manager: new Set(["self", "manager", "peer"]),
  peer: new Set(["self", "manager", "peer"]),
};

const feedbackConfig: Partial<
  Record<
    ReviewerTypes,
    {
      ratingTitle: string;
      commentTitle: string;
    }
  >
> = {
  self: {
    commentTitle: "Self Review",
    ratingTitle: "Self Rating",
  },
  manager: {
    commentTitle: "Manager Review",
    ratingTitle: "Manager Rating",
  },
  peer: {
    commentTitle: "Peer Review",
    ratingTitle: "Peer Rating",
  },
  hrbp: {
    commentTitle: "HRBP Review",
    ratingTitle: "HRBP Rating",
  },
  stakeholder: {
    commentTitle: "Stakeholder Review",
    ratingTitle: "Stakeholder Rating",
  },
};

type ReviewerFeedback = {
  reviewerTypeFrequency: number;
  reviewer_comments: {
    reviewer_name?: string;
    reviewer_job_title?: string;
    comment?: string;
  }[];
};

const getReviewerEmployeeDetailSuffix = (feedback: Feedback) => {
  if (feedback?.reviewer_name && feedback?.reviewer_job_title) {
    return `(${feedback?.reviewer_name} : ${feedback?.reviewer_job_title})`;
  }
  return "";
};

const getFeedbacksByReviewerType = (feedback: Feedback & ReviewerFeedback, reviewerType: ReviewerTypes) => {
  const allowedFeedbacks = allowedReviewsForReviewerTypes[reviewerType];

  if (allowedFeedbacks?.has(reviewerType)) {
    if (reviewerType === "peer" || reviewerType === "stakeholder" || reviewerType === "hrbp") {
      const overallRating = Number(feedback.rating_value) / feedback.reviewerTypeFrequency;
      return (
        <>
          <Grid key={feedback?.id} item sm={12}>
            <DetailListItem
              title={feedbackConfig[feedback?.reviewer_type]?.ratingTitle || ""}
              value={`${overallRating} (Average)`}
            />
          </Grid>
          <Grid key={feedback?.id} item sm={12} spacing={2}>
            {feedback?.reviewer_comments?.map((eachPeerComment) => (
              <Box key={eachPeerComment.reviewer_name} sx={{ margin: "16px 0px" }}>
                <DetailListItem
                  title={`${feedbackConfig[feedback?.reviewer_type]?.commentTitle} ${getReviewerEmployeeDetailSuffix(eachPeerComment as Feedback)}`}
                  value={eachPeerComment?.comment || ""}
                />
              </Box>
            ))}
          </Grid>
        </>
      );
    }
    return (
      <>
        <Grid key={feedback?.id} item sm={12}>
          <DetailListItem
            title={feedbackConfig[feedback?.reviewer_type]?.ratingTitle || ""}
            value={feedback?.display_rating || ""}
          />
        </Grid>
        <Grid key={feedback?.id} item sm={12}>
          <DetailListItem
            title={
              `
              ${feedbackConfig[feedback?.reviewer_type]?.commentTitle} ${getReviewerEmployeeDetailSuffix(feedback)}` ||
              ""
            }
            value={feedback.comments as unknown as string}
          />
        </Grid>
        {/* <Grid key={feedback?.id} sm={12} item>
          <Divider />
        </Grid> */}
      </>
    );
  }
  return null;
};

const ReadonlyFeedbackFrom: React.FC<ReadonlyFeedbackFromProps> = ({ goalObjectiveFeedback }) => {
  const enrichOverallFeedbacks = useMemo(() => {
    return goalObjectiveFeedback?.feedbacks?.reduce((accumFeedbacksMap, currFeedbacks) => {
      if (accumFeedbacksMap.has(currFeedbacks?.reviewer_type)) {
        const previousFeedback: Feedback & ReviewerFeedback = accumFeedbacksMap.get(currFeedbacks.reviewer_type);

        previousFeedback.reviewer_comments.push({
          reviewer_name: currFeedbacks?.reviewer_name,
          reviewer_job_title: currFeedbacks?.reviewer_job_title,
          comment: currFeedbacks?.comments,
        });

        accumFeedbacksMap.set(currFeedbacks.reviewer_type, {
          ...previousFeedback,
          rating_value: Number(previousFeedback?.rating_value || 0) + Number(currFeedbacks?.rating_value || 0),
          reviewerTypeFrequency: Number(previousFeedback?.reviewerTypeFrequency) + 1,
          reviewer_comments: previousFeedback?.reviewer_comments,
        });
        return accumFeedbacksMap;
      }
      accumFeedbacksMap.set(currFeedbacks.reviewer_type, {
        ...currFeedbacks,
        reviewerTypeFrequency: 1,
        reviewer_comments: [
          {
            reviewer_name: currFeedbacks?.reviewer_name,
            reviewer_job_title: currFeedbacks?.reviewer_job_title,
            comment: currFeedbacks?.comments,
          },
        ],
      });
      return accumFeedbacksMap;
    }, new Map());
  }, [goalObjectiveFeedback?.feedbacks]);

  const getOverallFeedback = (feedbackReviewerType: ReviewerTypes, feedback: Feedback & ReviewerFeedback) => {
    switch (feedbackReviewerType) {
      case "self":
        return (
          <Grid item sm={12}>
            <DetailListItem
              title={`Self Overall Comment ${getReviewerEmployeeDetailSuffix(feedback)}`}
              value={feedback?.comments || ""}
            />
          </Grid>
        );
      case "manager": {
        const overallRating = goalObjectiveFeedback?.feedbacks.reduce((a, b) => (a += b.rating_value || 0), 0);
        return (
          <>
            <Grid item sm={12}>
              <DetailListItem title="Overall Rating (Manager)" value={(overallRating as unknown as string) || ""} />
            </Grid>
            <Grid item sm={12}>
              <DetailListItem
                title={`Manager Overall Comment ${getReviewerEmployeeDetailSuffix(feedback)}`}
                value={feedback?.comments || ""}
              />
            </Grid>
          </>
        );
      }
      case "peer": {
        return (
          <Grid item sm={12}>
            {feedback?.reviewer_comments?.map((eachComment) => (
              <Box key={eachComment?.reviewer_name} sx={{ margin: "16px 0px" }}>
                <DetailListItem
                  key={eachComment?.reviewer_name}
                  title={`Peer Overall Comment ${getReviewerEmployeeDetailSuffix(eachComment as Feedback)}`}
                  value={eachComment?.comment || ""}
                />
              </Box>
            ))}
          </Grid>
        );
      }
      case "hrbp":
        return (
          <Grid item sm={12}>
            {feedback?.reviewer_comments?.map((eachComment) => (
              <Box key={eachComment?.reviewer_name} sx={{ margin: "16px 0px" }}>
                <DetailListItem
                  key={eachComment?.reviewer_name}
                  title={`HRBP Overall Comment ${getReviewerEmployeeDetailSuffix(eachComment as Feedback)}`}
                  value={eachComment?.comment || ""}
                />
              </Box>
            ))}
          </Grid>
        );
      default:
        return (
          <Grid item sm={12}>
            {feedback?.reviewer_comments?.map((eachComment) => (
              <Box key={eachComment?.reviewer_name} sx={{ margin: "16px 0px" }}>
                <DetailListItem
                  key={eachComment?.reviewer_name}
                  title={`Stakeholder Overall Comment ${getReviewerEmployeeDetailSuffix(eachComment as Feedback)}`}
                  value={eachComment?.comment || ""}
                />
              </Box>
            ))}
          </Grid>
        );
    }
  };

  if (!goalObjectiveFeedback.goal_objective) {
    return (
      <>
        <Box display="flex" flexDirection="column" gap={2} component={Paper} elevation={2} p={2} m={1}>
          <Typography variant="overline" fontWeight={600} fontSize={16}>
            Overall Feedbacks
          </Typography>
          {sortReviewersByPriority(Array.from(enrichOverallFeedbacks.values()))?.map((feedback, index) =>
            feedback?.reviewer_type === "self" ? (
              <Grid key={index} container spacing={2}>
                {getOverallFeedback(feedback?.reviewer_type, feedback)}
              </Grid>
            ) : (
              <Paper elevation={3} key={index} sx={{ padding: 2 }}>
                <Grid container spacing={2}>
                  {getOverallFeedback(feedback?.reviewer_type, feedback)}
                </Grid>
              </Paper>
            ),
          )}
        </Box>
      </>
    );
  }

  const renderFeedbacks = (feedback: any) => {
    if (feedback.reviewer_type === "self") {
      return getFeedbacksByReviewerType(feedback, feedback.reviewer_type);
    }
    return (
      <Grid item sm={12}>
        <Paper key={feedback?.id} elevation={3} sx={{ padding: 2, width: "100%" }}>
          <Grid container spacing={2}>
            {getFeedbacksByReviewerType(feedback, feedback.reviewer_type)}
          </Grid>
        </Paper>
      </Grid>
    );
  };

  return (
    <Box display="flex" flexDirection="column" gap={2} component={Paper} elevation={2} p={2} m={1}>
      <Grid container spacing={2}>
        <Grid item sm={4}>
          <DetailListItem
            title="Status"
            value={
              <Typography fontWeight={600} color={getStatusColors(goalObjectiveFeedback?.goal_objective?.status || "")}>
                {goalObjectiveFeedback?.goal_objective?.status || ""}
              </Typography>
            }
          />
        </Grid>
        <Grid item sm={4}>
          <DetailListItem
            title="Planned Completion Date"
            value={formatDateToDayMonthYear(goalObjectiveFeedback?.goal_objective?.target_completion_date) || "N/A"}
          />
        </Grid>
        <Grid item sm={4}>
          <DetailListItem
            title="Actual Completion Date"
            value={formatDateToDayMonthYear(goalObjectiveFeedback?.goal_objective?.actual_completion_date || null)}
          />
        </Grid>
        {goalObjectiveFeedback?.goal_objective?.estimated_weightage && (
          <Grid item sm={4}>
            <DetailListItem
              title="Estimated Weightage"
              value={`${goalObjectiveFeedback?.goal_objective?.estimated_weightage * 100}%` || ""}
            />
          </Grid>
        )}
        {goalObjectiveFeedback?.goal_objective?.final_weightage && (
          <Grid item sm={4}>
            <DetailListItem
              title="Final Weightage"
              value={`${goalObjectiveFeedback?.goal_objective?.final_weightage * 100}%` || ""}
            />
          </Grid>
        )}
        <Grid item sm={12}>
          <DetailListItem title="Objective" value={goalObjectiveFeedback?.goal_objective?.title || ""} />
        </Grid>
        <Grid item sm={12}>
          <DetailListItem title="Description" value={goalObjectiveFeedback?.goal_objective?.description || ""} />
        </Grid>
        <Grid item sm={12}>
          <Divider />
        </Grid>
        {sortReviewersByPriority(Array.from(enrichOverallFeedbacks.values())).map((feedback) =>
          renderFeedbacks(feedback),
        )}
      </Grid>
    </Box>
  );
};

export default ReadonlyFeedbackFrom;
