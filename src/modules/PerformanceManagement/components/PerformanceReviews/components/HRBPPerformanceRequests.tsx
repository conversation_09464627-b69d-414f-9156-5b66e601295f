import { Box } from "@mui/material";
import React, { useEffect } from "react";
import { useAppDispatch } from "src/customHooks/useAppDispatch";
import { PerformanceReviewRequest } from "src/services/api_definitions/performanceManagement.service";
import { setFullviewMode } from "src/store/slices/app.slice";
import EmployeePerformanceRequestView from "./EmployeePerformanceRequestView";
import HRBPPerformanceRequestActionableView from "./HRBPPerformanceRequestActionableView";
import { PerformanceRequestStates } from "./PerformanceRequests";
import ReadOnlyPerformanceReviewForm from "./ReadOnlyPerformanceReviewFormView";

const HRBPPerformanceRequests = () => {
  const dispatch = useAppDispatch();
  const [currentMode, setCurrentMode] = React.useState(PerformanceRequestStates.VIEW_REQUESTS);
  const [selectedRequest, setSelectedRequest] = React.useState<PerformanceReviewRequest | null>(null);

  useEffect(() => {
    if (
      currentMode === PerformanceRequestStates.TAKE_ACTION_ON_REQUESTS ||
      currentMode === PerformanceRequestStates.VIEW_ACTION_ON_REQUESTS
    ) {
      dispatch(setFullviewMode(true));
      return;
    }
    dispatch(setFullviewMode(false));
  }, [currentMode]);

  return (
    <Box>
      {currentMode === PerformanceRequestStates.VIEW_REQUESTS && (
        <EmployeePerformanceRequestView
          setCurrentMode={setCurrentMode}
          setSelectedRequest={setSelectedRequest}
          requester="hrbp"
        />
      )}
      {currentMode === PerformanceRequestStates.TAKE_ACTION_ON_REQUESTS && selectedRequest && (
        <HRBPPerformanceRequestActionableView selectedRequest={selectedRequest} setCurrentMode={setCurrentMode} />
      )}
      {currentMode === PerformanceRequestStates.VIEW_ACTION_ON_REQUESTS && selectedRequest && (
        <ReadOnlyPerformanceReviewForm
          selectedPerformanceReview={selectedRequest.review}
          setCurrentMode={setCurrentMode as any}
          reviewerType="hrbp"
          selectedRequest={selectedRequest}
        />
      )}
    </Box>
  );
};

export default HRBPPerformanceRequests;
