import { Box } from "@mui/material";
import React from "react";
import { PerformanceReviewRequest, ReviewerTypes } from "src/services/api_definitions/performanceManagement.service";
import { EmployeeDetailHeader } from "../../EmployeeGoalRequestActionableView";
import { PerformanceRequestStates } from "./PerformanceRequests";
import PerformanceReview, { PerformanceReviewFormStates } from "./PerformanceReview";
import { Header } from "./PerformanceReviewForm";
import ReadOnlyPromotionRecommendation from "./ReadOnlyPromotionRecommendation";
import ReadonlyFeedbackFrom from "./ReadonlyFeedbackForm";

interface ReadOnlyPerformanceReviewFormProps {
  setCurrentMode: (mode: PerformanceReviewFormStates | PerformanceRequestStates) => void;
  selectedPerformanceReview: PerformanceReview;
  reviewerType?: ReviewerTypes;
  selectedRequest?: PerformanceReviewRequest;
}

const ReadOnlyPerformanceReviewForm: React.FC<ReadOnlyPerformanceReviewFormProps> = ({
  selectedPerformanceReview,
  setCurrentMode,
  reviewerType = "self",
  selectedRequest,
}) => {
  return (
    <Box display="flex" flexDirection="column" gap={2}>
      <Header
        setCurrentMode={() => setCurrentMode(PerformanceReviewFormStates.VIEW_REVIEWS)}
        title="View Performance Review"
      />
      {selectedRequest && (
        <EmployeeDetailHeader
          goal={selectedRequest?.review.performance_review_cycle?.name || ""}
          name={selectedRequest?.employee_name || ""}
          title={selectedRequest?.job_title || ""}
          submittedAt={selectedRequest?.review?.submitted_at}
          appliedDate=""
        />
      )}
      {selectedPerformanceReview.goal_objective_feedbacks?.map((eachFeedback) => (
        <ReadonlyFeedbackFrom
          key={eachFeedback?.goal_objective?.id || "overall"}
          goalObjectiveFeedback={eachFeedback}
          reviewerType={reviewerType}
        />
      ))}
      <ReadOnlyPromotionRecommendation promotion={selectedPerformanceReview?.promotion} />
    </Box>
  );
};

export default ReadOnlyPerformanceReviewForm;
