import { ArrowBack } from "@mui/icons-material";
import { <PERSON><PERSON>, Box, Button, Divider, IconButton, Typography } from "@mui/material";
import { useMutation } from "@tanstack/react-query";
import React, { useMemo } from "react";
import { useForm } from "src/customHooks/useForm";
import { PerformanceReview } from "src/services/api_definitions/performanceManagement.service";
import performanceManagementService from "src/services/performanceManagement.service";
import validators from "src/utils/validators";
import FeedbackCard from "./FeedbackCard";
import { PerformanceReviewFormStates } from "./PerformanceReview";

interface PerformanceReviewFormProps {
  setCurrentMode: (mode: PerformanceReviewFormStates) => void;
  selectedPerformanceReview: PerformanceReview;
}

export const Header: React.FC<
  Omit<
    PerformanceReviewFormProps & {
      title?: string;
    },
    "selectedRequest" | "selectedPerformanceReview"
  >
> = ({ setCurrentMode, title = "Review Goals" }) => (
  <React.Fragment>
    <Box display="flex" gap={2} alignItems="center">
      <IconButton onClick={() => setCurrentMode(PerformanceReviewFormStates.VIEW_REVIEWS)}>
        <ArrowBack />
      </IconButton>
      <Typography>{title}</Typography>
    </Box>
    <Divider sx={{ margin: "10px 0px" }} />
  </React.Fragment>
);

export const Footer: React.FC<
  Omit<
    PerformanceReviewFormProps & {
      onSaveToDrafts: () => void;
      onSubmit: () => void;
      isSubmitDisabled: boolean;
      onCancel: () => void;
      hideDrafts?: boolean;
      title?: string;
    },
    "selectedPerformanceReview" | "setCurrentMode"
  >
> = ({ isSubmitDisabled, onSaveToDrafts, onSubmit, onCancel, hideDrafts = false, title = "Submit" }) => (
  <Box alignSelf="flex-end" position="fixed" bottom={20} gap={2} zIndex={10} bgcolor="white">
    <Box display="flex" alignItems="center" gap={2}>
      <Button variant="outlined" onClick={onCancel}>
        Cancel
      </Button>
      {!hideDrafts && (
        <Button variant="contained" onClick={onSaveToDrafts}>
          Save Draft
        </Button>
      )}
      <Button disabled={isSubmitDisabled} variant="contained" onClick={onSubmit}>
        {title}
      </Button>
    </Box>
  </Box>
);

const PerformanceReviewForm: React.FC<PerformanceReviewFormProps> = ({ selectedPerformanceReview, setCurrentMode }) => {
  const defaultFormState = useMemo(() => {
    return selectedPerformanceReview?.goal_objective_feedbacks?.map((feedbackObjective) => {
      return {
        status: feedbackObjective.goal_objective?.status,
        actualCompletionDate: feedbackObjective.goal_objective?.actual_completion_date,
        ratingValue: feedbackObjective.feedbacks.find((eachFeedback) => eachFeedback.reviewer_type === "self")
          ?.rating_value,
        ratingDescription: feedbackObjective.feedbacks.find((eachFeedback) => eachFeedback.reviewer_type === "self")
          ?.rating_description,
        comment: feedbackObjective.feedbacks.find((eachFeedback) => eachFeedback.reviewer_type === "self")?.comments,
        feedbackId: feedbackObjective.feedbacks.find((eachFeedback) => eachFeedback.reviewer_type === "self")?.id,
        overallComment:
          feedbackObjective.goal_objective === null
            ? feedbackObjective.feedbacks.find((eachFeedback) => eachFeedback.reviewer_type === "self")?.comments
            : "",
      };
    });
  }, [selectedPerformanceReview]);

  const { formDetails, setFormDetail } = useForm({
    initialState: defaultFormState as any,
    isBulk: true,
    validations: {
      status: [validators.validateInput],
      actualCompletionDate: [validators.validateInput],
      ratingValue: [validators.validateInput],
      comment: [validators.shouldNotContainSpecialCharacters, validators.validateInput],
    },
  });

  const updatePerformanceReviewMutation = useMutation({
    mutationFn: async (payload: any) => {
      return performanceManagementService.updatePerformanceReviewsByReviewerType("self", payload);
    },
    onSuccess: () => {
      setCurrentMode(PerformanceReviewFormStates.VIEW_REVIEWS);
    },
  });

  const createRequestBody = (status: "Submitted" | "Draft") => {
    return {
      review_id: selectedPerformanceReview.review_id,
      status: status,
      feedbacks: selectedPerformanceReview.goal_objective_feedbacks
        ?.map((eachFeedback, index) => {
          // handle overall goal request body
          if (eachFeedback.goal_objective === null) {
            return {
              id: formDetails[index].feedbackId,
              goal_objective: null,
              comments: formDetails[index].overallComment,
            };
          }
          return {
            id: formDetails[index].feedbackId,
            goal_objective: {
              ...eachFeedback.goal_objective,
              status: formDetails[index].status,
              actual_completion_date: formDetails[index].actualCompletionDate,
            },
            comments: formDetails[index].comment,
            rating_value: formDetails[index].ratingValue,
            rating_description: formDetails[index].ratingDescription,
          };
        })
        .filter((eachFeedback) => eachFeedback.comments),
    };
  };

  const isSubmitDisabled = useMemo(() => {
    if (
      !selectedPerformanceReview ||
      !selectedPerformanceReview.goal_objective_feedbacks ||
      selectedPerformanceReview?.goal_objective_feedbacks?.length === 0
    ) {
      return true;
    }
    return selectedPerformanceReview.goal_objective_feedbacks.some((eachFeedback, index) => {
      const isActualDateRequired: boolean = eachFeedback?.goal_objective?.status === "Completed";
      if (
        isActualDateRequired &&
        (formDetails[index].actualCompletionDate === null || formDetails[index].actualCompletionDate === undefined)
      ) {
        return true;
      }
      if (eachFeedback.goal_objective === null) {
        const isOverallFeedbackFilled: boolean = formDetails[index].overallComment;
        return !isOverallFeedbackFilled;
      }

      const areOtherFormDetailsFilled: boolean =
        !!formDetails[index].ratingValue &&
        !!formDetails[index].ratingDescription &&
        !!formDetails[index].comment &&
        !!formDetails[index].status;
      return !areOtherFormDetailsFilled;
    });
  }, [formDetails, selectedPerformanceReview]);

  const onSaveToDrafts = () => {
    updatePerformanceReviewMutation.mutate(createRequestBody("Draft"));
  };

  const onSubmit = () => {
    updatePerformanceReviewMutation.mutate(createRequestBody("Submitted"));
  };

  return (
    <Box display="flex" flexDirection="column" gap={2}>
      <Header setCurrentMode={setCurrentMode} title="Performance Review" />
      <Box marginBottom={10} display={"flex"} flexDirection="column" gap={2}>
        {isSubmitDisabled && (
          <Alert severity="error">Please fill all of the reviews along with overall comment before submitting</Alert>
        )}
        {isSubmitDisabled && (
          <Alert severity="warning">Overall review comment is mandatory for saving to drafts!</Alert>
        )}
        {selectedPerformanceReview?.goal_objective_feedbacks?.map((feedback, index) => (
          <FeedbackCard
            key={feedback.goal_objective?.id}
            feedback={feedback}
            formDetails={formDetails[index]}
            setFormDetail={setFormDetail}
            index={index}
            status={selectedPerformanceReview?.status}
            reviewerType="self"
          />
        ))}
      </Box>
      <Footer
        isSubmitDisabled={isSubmitDisabled}
        onCancel={() => setCurrentMode(PerformanceReviewFormStates.VIEW_REVIEWS)}
        onSaveToDrafts={onSaveToDrafts}
        onSubmit={onSubmit}
      />
    </Box>
  );
};

export default PerformanceReviewForm;
