import { Grid } from "@mui/material";
import { useQuery } from "@tanstack/react-query";
import React from "react";
import { useAppDispatch } from "src/customHooks/useAppDispatch";
import { Goal, PeerNominations } from "src/services/api_definitions/performanceManagement.service";
import performanceManagementService from "src/services/performanceManagement.service";
import { setFullviewMode } from "src/store/slices/app.slice";
import GoalCard from "../../GoalCards";
import { PeerNominationModes } from "./PeerNominations";

interface PeerNominationProps {
  setSelectedGoalDetails: (goal: PeerNominations) => void;
  setCurrentMode: (currentMode: PeerNominationModes) => void;
}

const PeerNominations: React.FC<PeerNominationProps> = ({ setCurrentMode, setSelectedGoalDetails }) => {
  const dispatch = useAppDispatch();

  const { data: peerNominations = [], isFetching } = useQuery({
    queryKey: ["peer-nominations"],
    queryFn: async () => {
      return performanceManagementService.getPeerNominations();
    },
    refetchOnMount: true,
    refetchOnWindowFocus: false,
  });

  const onAddPeerNominationClick = (goal: PeerNominations) => {
    setSelectedGoalDetails(goal);
    dispatch(setFullviewMode(true));
    setCurrentMode(
      goal?.status === "Approved"
        ? PeerNominationModes.READ_ONY_PEER_NOMINATIONS
        : PeerNominationModes.ADD_PEER_NOMINATION,
    );
  };

  return (
    <Grid container spacing={2} p={1}>
      {!isFetching &&
        peerNominations.map((peerNomination, index) => (
          <Grid item xs={12} sm={6} md={6} key={index}>
            <GoalCard
              quarter={peerNomination?.performance_review_cycle?.name || ""}
              isAddGoal={false}
              iconIndex={index % 4}
              onViewAddedGoalClick={() => onAddPeerNominationClick(peerNomination)}
              status={peerNomination.status}
              buttonTitle={peerNomination.status === "Not Started" ? "Create Goal" : "Update Goal"}
              start_date={peerNomination?.performance_review_cycle?.start_date}
              end_date={peerNomination?.performance_review_cycle?.end_date}
              dueDate={peerNomination?.performance_review_cycle?.performance_review_end_date}
            />
          </Grid>
        ))}
    </Grid>
  );
};

export default PeerNominations;
