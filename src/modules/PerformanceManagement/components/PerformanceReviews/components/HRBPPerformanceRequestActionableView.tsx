import { Box } from "@mui/material";
import { useMutation } from "@tanstack/react-query";
import { format } from "date-fns";
import React, { useMemo, useState } from "react";
import { useForm } from "src/customHooks/useForm";
import { PerformanceReviewRequest } from "src/services/api_definitions/performanceManagement.service";
import performanceManagementService from "src/services/performanceManagement.service";
import validators from "src/utils/validators";
import { EmployeeDetailHeader } from "../../EmployeeGoalRequestActionableView";
import PromotionRecommendation, { PromotionFormDetail } from "../PromotionRecommendation";
import FeedbackCard from "./FeedbackCard";
import FeedbackVerdicts from "./FeedbackVerdicts";
import { PerformanceRequestStates } from "./PerformanceRequests";
import { Footer, Header } from "./PerformanceReviewForm";

interface EmployeePerformanceReviewActionableViewProps {
  setCurrentMode: (state: PerformanceRequestStates) => void;
  selectedRequest: PerformanceReviewRequest;
}

const HRBPPerformanceRequestActionableView: React.FC<EmployeePerformanceReviewActionableViewProps> = ({
  setCurrentMode,
  selectedRequest,
}) => {
  const [currentSelectedAction, setCurrentSelectedAction] = useState<{
    status: string | null;
    reason: string | null;
  }>({
    status: null,
    reason: null,
  });
  const defaultFormState = useMemo(() => {
    return selectedRequest?.review.goal_objective_feedbacks?.map((feedbackObjective) => {
      return {
        status: feedbackObjective.goal_objective?.status,
        actualCompletionDate: feedbackObjective.goal_objective?.actual_completion_date,
        feedbackId: feedbackObjective.feedbacks.find((eachFeedback) => eachFeedback.reviewer_type === "hrbp")?.id,
        hrbpOverallComment:
          feedbackObjective.goal_objective === null
            ? feedbackObjective.feedbacks.find((eachFeedback) => eachFeedback.reviewer_type === "hrbp")?.comments
            : "",
      };
    });
  }, [selectedRequest.review]);

  const { setFormDetail, formDetails } = useForm({
    initialState: defaultFormState as any,
    isBulk: true,
    validations: {
      overallHrbpFeedback: [],
      ratingValue: [],
      ratingDescription: [],
    },
  });

  const defaultPromotionFormState = useMemo(
    () => ({
      status: selectedRequest?.review?.promotion?.status || "",
      reason: selectedRequest?.review?.promotion?.comments || null,
      effectiveDate: selectedRequest?.review?.promotion?.effective_date || "",
    }),
    [selectedRequest],
  );

  const {
    setFormDetail: setPromoFormDetail,
    formDetails: promoFormDetails,
    formErrors: promoFormErrors,
    areFormDetailsValid,
  } = useForm({
    initialState: defaultPromotionFormState,
    validations: {
      status: [validators.validateInput],
      reason: [validators.validateInput],
      effectiveDate: [validators.validateEffectiveDate],
    },
  });

  const typedPromoFormDetails = promoFormDetails as PromotionFormDetail;

  const updatePerformanceReviewMutation = useMutation({
    mutationFn: async (payload: any) => {
      return performanceManagementService.updatePerformanceReviewsByReviewerType("hrbp", payload);
    },
    onSuccess: () => {
      setCurrentMode(PerformanceRequestStates.VIEW_REQUESTS);
    },
  });

  const sendBackReviewMutation = useMutation({
    mutationFn: async (payload: any) => {
      return performanceManagementService.sendBackPerformanceReview("hrbp", payload.review_id, payload.reason);
    },
    onSuccess: () => {
      setCurrentMode(PerformanceRequestStates.VIEW_REQUESTS);
    },
  });

  const createRequestBody = (status: "Submitted" | "Draft") => {
    return {
      review_id: selectedRequest?.review?.review_id,
      status: status,
      promotion: selectedRequest?.review?.promotion
        ? {
            ...selectedRequest?.review?.promotion,
            status: typedPromoFormDetails.status || null,
            comments: typedPromoFormDetails.reason || null,
            effective_date: typedPromoFormDetails?.effectiveDate
              ? format(typedPromoFormDetails.effectiveDate, "yyyy-MM-dd")
              : null,
          }
        : null,
      feedbacks: selectedRequest?.review?.goal_objective_feedbacks
        ?.map((eachFeedback, index) => {
          // handle overall goal request body
          if (eachFeedback.goal_objective === null) {
            return {
              id: formDetails[index].feedbackId,
              goal_objective: null,
              comments: formDetails[index].hrbpOverallComment,
            };
          }
          return {
            id: formDetails[index].feedbackId,
            goal_objective: {
              ...eachFeedback.goal_objective,
            },
            comments: null,
          };
        })
        .filter((eachFeedback) => eachFeedback.comments),
    };
  };

  const isSubmitDisabled = useMemo(() => {
    if (
      !selectedRequest ||
      !selectedRequest?.review.goal_objective_feedbacks ||
      selectedRequest?.review?.goal_objective_feedbacks?.length === 0
    ) {
      return true;
    }

    if (currentSelectedAction.status === "Send Back" && !currentSelectedAction.reason) {
      return true;
    }

    // escape hatch to not send back if feedback is not filled
    if (currentSelectedAction.status === "Send Back" && currentSelectedAction.reason) {
      return false;
    }

    return selectedRequest.review.goal_objective_feedbacks.some((eachFeedback, index) => {
      if (eachFeedback.goal_objective === null) {
        return !formDetails[index].hrbpOverallComment;
      }
      return false;
    });
  }, [formDetails, selectedRequest, areFormDetailsValid, currentSelectedAction]);

  const onSaveToDrafts = () => {
    updatePerformanceReviewMutation.mutate(createRequestBody("Draft"));
  };

  const onSubmit = () => {
    if (currentSelectedAction.status === "Send Back") {
      sendBackReviewMutation.mutate({
        review_id: selectedRequest?.review?.review_id,
        reason: currentSelectedAction.reason as unknown as string,
      });
      return;
    }
    updatePerformanceReviewMutation.mutate(createRequestBody("Submitted"));
  };

  const arePromotionDetailsValid = useMemo(() => {
    if (!selectedRequest?.review?.promotion || currentSelectedAction?.status === "Send Back") {
      return true;
    }
    return areFormDetailsValid;
  }, [promoFormDetails, promoFormErrors, currentSelectedAction]);

  const isFinalVerdictValid = useMemo(() => {
    if (!currentSelectedAction?.status) {
      return false;
    }
    if (currentSelectedAction?.status === "Send Back" && !currentSelectedAction?.reason) {
      return false;
    }
    return true;
  }, [currentSelectedAction]);

  return (
    <Box display="flex" flexDirection="column" gap={2}>
      <Header setCurrentMode={() => setCurrentMode(PerformanceRequestStates.VIEW_REQUESTS)} title="View HRBP Review" />
      <EmployeeDetailHeader
        title={selectedRequest.job_title}
        name={selectedRequest.employee_name}
        goal={selectedRequest.review.performance_review_cycle?.name || ""}
        appliedDate=""
      />
      <Box m={1} marginBottom={10} display="flex" flexDirection="column" gap={2}>
        {selectedRequest.review.goal_objective_feedbacks?.map((eachObjectiveFeedback, index) => (
          <FeedbackCard
            feedback={eachObjectiveFeedback}
            key={eachObjectiveFeedback?.goal_objective?.id || "overall"}
            reviewerType="hrbp"
            formDetails={formDetails[index]}
            status={selectedRequest.review?.status}
            index={index}
            setFormDetail={setFormDetail}
          />
        ))}
        <PromotionRecommendation
          promotion={{
            promotionFormDetails: typedPromoFormDetails,
            promotionFormErrors: promoFormErrors as unknown as Record<"status" | "reason" | "effectiveDate", string>,
            setPromotionFormDetail: setPromoFormDetail,
          }}
          reviewerType="hrbp"
          selectedRequest={selectedRequest}
        />
        <FeedbackVerdicts
          currentSelectedAction={currentSelectedAction}
          setCurrentSelectedAction={setCurrentSelectedAction}
        />
      </Box>
      <Footer
        isSubmitDisabled={isSubmitDisabled || !arePromotionDetailsValid || !isFinalVerdictValid}
        onCancel={() => setCurrentMode(PerformanceRequestStates.VIEW_REQUESTS)}
        onSaveToDrafts={onSaveToDrafts}
        onSubmit={onSubmit}
      />
    </Box>
  );
};

export default HRBPPerformanceRequestActionableView;
