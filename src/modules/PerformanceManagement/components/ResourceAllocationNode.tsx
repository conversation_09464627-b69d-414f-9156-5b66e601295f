import { <PERSON><PERSON>, <PERSON>, Card, Chip, Divider, Toolt<PERSON>, Typography } from "@mui/material";
import { <PERSON><PERSON>, Position } from "@xyflow/react";
import React from "react";
import { ResourceAllocationNodeProps } from "../ResourceAllocationTypes";

const ResourceAllocationNode: React.FC<ResourceAllocationNodeProps> = ({ id, data }) => {
  const isObjective = id.startsWith("objective-");

  const renderAvatars = (objectives: any[]) => {
    return objectives.slice(0, 5).map((objective, index) => (
      <Tooltip
        key={index}
        title={`${objective.goal_objective_title} (${(objective.estimated_weightage * 100)?.toFixed(2)}%)`}
      >
        <Avatar
          src={objective.employee.display_pic}
          alt={objective.employee.display_name}
          sx={{
            width: 45,
            height: 45,
            border: `2px solid ${data.colorTheme.border}`,
          }}
        />
      </Tooltip>
    ));
  };

  if (isObjective) {
    return (
      <div style={{ position: "relative" }}>
        <Handle
          type="target"
          position={Position.Top}
          id="top"
          style={{ background: data.colorTheme.primary, width: 10, height: 10 }}
        />

        <Card
          sx={{
            width: 370,
            minHeight: 120,
            borderRadius: 8,
            boxShadow: 3,
            overflow: "visible",
            position: "relative",
            cursor: "pointer",
            bgcolor: data.colorTheme.bg,
            "&:hover": {
              boxShadow: 4,
              bgcolor: data.colorTheme.bgHover,
            },
          }}
        >
          <Box sx={{ p: 2 }}>
            <Box display="flex" alignItems="center" gap={2} mb={1}>
              <Box sx={{ flexGrow: 1, minWidth: 0 }}>
                <Typography
                  variant="subtitle1"
                  fontWeight="bold"
                  sx={{
                    overflow: "hidden",
                    display: "-webkit-box",
                    WebkitLineClamp: 2,
                    WebkitBoxOrient: "vertical",
                    lineHeight: "1.3em",
                    maxHeight: "2.6em",
                    marginBottom: "4px",
                  }}
                >
                  {data.goal_objective_title}
                </Typography>
                <Typography
                  variant="body2"
                  color="text.bold"
                  sx={{
                    overflow: "hidden",
                    textOverflow: "ellipsis",
                    whiteSpace: "nowrap",
                    display: "flex",
                    justifyContent: "space-between",
                    alignItems: "center",
                  }}
                >
                  <span>
                    Total Resources Assigned: {data.estimated_weightage} (
                    <span style={{ fontWeight: "bold" }}>
                      {((data.estimated_weightage / data?.totalWeightage) * 100).toFixed(2)}%)
                    </span>
                  </span>
                </Typography>
              </Box>
            </Box>

            {data.objectives && data.objectives.length > 0 && (
              <Box>
                <Divider sx={{ my: 1 }} />
                <Typography variant="caption" color="text.secondary" display="block" mb={1}>
                  Resources:
                </Typography>
                <Box
                  sx={{
                    display: "flex",
                    gap: 1,
                    alignItems: "center",
                    overflowX: "hidden",
                  }}
                >
                  {renderAvatars(data.objectives)}
                  {data.objectives.length > 5 && (
                    <Chip
                      label={`+${data.objectives.length - 5}`}
                      sx={{
                        bgcolor: data.colorTheme.bgHover,
                        "&:hover": {
                          bgcolor: data.colorTheme.bg,
                        },
                      }}
                    />
                  )}
                </Box>
              </Box>
            )}
          </Box>
        </Card>

        <Handle
          type="source"
          position={Position.Bottom}
          id="bottom"
          style={{
            opacity: 0,
            width: 10,
            height: 10,
            background: data.colorTheme.primary,
            border: `2px solid ${data.colorTheme.primary}`,
          }}
        />
      </div>
    );
  }

  // Resource Node
  return (
    <div style={{ position: "relative" }}>
      <Handle
        type="target"
        position={Position.Top}
        id="top"
        style={{ background: data.colorTheme.primary, width: 10, height: 10 }}
      />

      <Card
        sx={{
          width: 370,
          minHeight: 120,
          borderRadius: 2,
          boxShadow: 3,
          overflow: "visible",
          position: "relative",
          cursor: "default",
          "&:hover": {
            boxShadow: 4,
          },
        }}
      >
        <Box
          sx={{ height: 4, bgcolor: data.colorTheme.primary, borderTopLeftRadius: "8px", borderTopRightRadius: "8px" }}
        />

        <Box sx={{ p: 1 }}>
          <Box display="flex" alignItems="center" gap={2} mb={1}>
            <Avatar
              src={data.display_pic}
              sx={{
                width: 60,
                height: 60,
                border: "4px solid white",
                boxShadow: 1,
              }}
            />
            <Box sx={{ flexGrow: 1, minWidth: 0 }}>
              <Box sx={{ display: "flex", alignItems: "center", justifyContent: "space-between" }}>
                <Typography
                  variant="subtitle1"
                  fontWeight="bold"
                  sx={{
                    overflow: "hidden",
                    textOverflow: "ellipsis",
                    whiteSpace: "nowrap",
                  }}
                >
                  {data.display_name}
                </Typography>
                <Typography
                  variant="body2"
                  sx={{
                    backgroundColor: "#ecf9f2",
                    borderRadius: 1,
                    fontWeight: "bold",
                    padding: "2px 6px",
                    textAlign: "center",
                    minWidth: "fit-content",
                  }}
                >
                  {(data.estimated_weightage * 100)?.toFixed(0)}%
                </Typography>
              </Box>
              <Typography
                variant="body2"
                color="text.secondary"
                sx={{
                  overflow: "hidden",
                  textOverflow: "ellipsis",
                  whiteSpace: "nowrap",
                }}
              >
                {data.job_title}
              </Typography>
            </Box>
          </Box>

          {data.objectives && data.objectives.length > 0 && (
            <Box>
              <Divider sx={{ my: 1 }} />
              <Typography variant="caption" color="text.secondary" display="block" mb={1}>
                Resources:
              </Typography>
              <Box
                sx={{
                  display: "flex",
                  gap: 1,
                  alignItems: "center",
                  overflowX: "hidden",
                }}
              >
                {renderAvatars(data.objectives)}
                {data.objectives.length > 5 && (
                  <Chip
                    label={`+${data.objectives.length - 5}`}
                    sx={{
                      bgcolor: data.colorTheme.bgHover,
                      "&:hover": {
                        bgcolor: data.colorTheme.bg,
                      },
                    }}
                  />
                )}
              </Box>
            </Box>
          )}
        </Box>
      </Card>

      <Handle
        type="source"
        position={Position.Bottom}
        id="bottom"
        style={{
          opacity: 0,
          width: 10,
          height: 10,
          background: data.colorTheme.primary,
          border: `2px solid ${data.colorTheme.primary}`,
        }}
      />
    </div>
  );
};

export default ResourceAllocationNode;
