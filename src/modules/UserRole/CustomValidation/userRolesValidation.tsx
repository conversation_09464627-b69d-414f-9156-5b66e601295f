/* eslint-disable @typescript-eslint/no-explicit-any */
import { ValidationError } from "src/utils/errors";

export type ValidatorReturnType = Error | null;

class UserRoleValidation {
  shouldNotIncludeEmployee = (value: string, formDetails: Record<string, any>): ValidatorReturnType => {
    if (!value?.length) return new ValidationError("required", "Mandatory field");
    const employees = formDetails?.[0]?.addedEmployee;
    if (employees?.includes(value)) {
      return new ValidationError("required", "Employee already added.");
    }
    return null;
  };
}

export default new UserRoleValidation();
