import { useQuery } from "@tanstack/react-query";
import React, { useMemo, useState } from "react";
import { BaseObject } from "src/app/global";
import { getCurrentTenantId } from "src/utils/authUtils";

import languageConfig from "src/configs/language/en.lang";
import { useAppSelector } from "src/customHooks/useAppSelector";
import { useForm } from "src/customHooks/useForm";
import roleManagementService from "src/services/roleManagement.service";
import validators from "src/utils/validators";
import { CrudTable } from "../../Settings/components/Common/CrudTable";
import userRolesValidation from "../CustomValidation/userRolesValidation";

const { userRoleManagement: userRoleManagementLang } = languageConfig;

const getRequestPayload = (formDetail: BaseObject) => {
  return {
    email: formDetail.email,
    rolesArray: formDetail.rolesArray,
  };
};

export const UserRoleManagement = () => {
  const tenantId = getCurrentTenantId();
  const { selectedRole, selectedOrganisation } = useAppSelector((state) => state.userManagement);
  const { data: allRoles } = useQuery(
    ["all-allowed-roles", selectedRole],
    async () => roleManagementService.getAllAllowedRoles(),
    {
      enabled: !!tenantId && !!selectedRole && !!selectedOrganisation,
      retryOnMount: false,
      refetchOnWindowFocus: false,
    },
  );

  const {
    data: allUsers,
    isLoading: allUsersLoading,
    refetch: refetchAllUsers,
  } = useQuery(["all-user-roles", selectedRole], async () => roleManagementService.getAllUserDetails(), {
    enabled: !!tenantId && !!selectedRole && !!selectedOrganisation,
    retryOnMount: false,
    refetchOnWindowFocus: false,
    retry: false,
  });

  const roleOptions = allRoles?.map((role) => ({ value: role, label: role }));

  const deafaultResponse =
    allUsers?.map((userRole) => ({
      email: userRole.email,
      roles: userRole.roles.join(", "),
      rolesArray: userRole.roles,
    })) || [];

  const rowAdditionaInitialValues = {
    email: "",
    rolesArray: [],
    addedEmployee: allUsers?.map((user) => user.email),
  };
  const defaultFormState = deafaultResponse;

  const postFormSubmit = () => {
    refetchAllUsers();
  };

  const handleEditDetailsClick = async (formDetails: BaseObject) => {
    const parsedData = getRequestPayload(formDetails);
    const requestObject = { email: parsedData.email as string, roles: parsedData.rolesArray as string[] };
    await roleManagementService.updateUserRole(requestObject);
    postFormSubmit();
  };

  const handleAddDetailsClick = async (formDetails: BaseObject) => {
    const parsedData = getRequestPayload(formDetails);
    const requestObject = { email: parsedData.email as string, roles: parsedData.rolesArray as string[] };
    await roleManagementService.updateUserRole(requestObject);
    postFormSubmit();
  };

  const handleDeleteConfirmed = async (index: number) => {
    await roleManagementService.deleteUserRole(allUsers?.[index]?.email || "");
    postFormSubmit();
  };

  const formValidators = {
    email: [userRolesValidation.shouldNotIncludeEmployee],
    rolesArray: [validators.validateSelectDropdown],
  };

  const [selectedRow, setSelectedRow] = useState<number | null>(null);

  const selectedRowData = useMemo(
    () => (selectedRow !== null ? deafaultResponse?.[selectedRow] : rowAdditionaInitialValues),
    [selectedRow],
  );

  const useFormDetails = useForm<any>({
    initialState: selectedRowData,
    isBulk: false,
    validations: formValidators,
  });

  const inputElements = [
    {
      name: "email",
      label: userRoleManagementLang.userNameTitle,
      variant: "search",
      placeholder: "Search user",
      xs: 6,
    },
    {
      name: "rolesArray",
      label: userRoleManagementLang.roleTitle,
      variant: "multi-select",
      options: roleOptions,
      placeholder: "Select multiple roles",
      isEditable: true,
      xs: 6,
    },
  ];
  const editFormConfig = {
    nextButtonText: "Save",
    onNextClick: handleEditDetailsClick,
    formTitle: userRoleManagementLang.editUserRole,
  };

  const addFormConfig = {
    nextButtonText: "Create",
    onNextClick: handleAddDetailsClick,
    formTitle: userRoleManagementLang.addUserRole,
    addButtonText: userRoleManagementLang.addUserRole,
  };

  const deleteFormConfig = {
    nextButtonText: "Delete",
    onNextClick: handleDeleteConfirmed,
    formTitle: userRoleManagementLang.deleteUserRole,
    getQuestion: (rowIndex: number) =>
      `Are you sure you want to delete the roles for ${deafaultResponse[rowIndex]?.email}?`,
  };

  const formConfig = {
    editFormConfig,
    addFormConfig,
    deleteFormConfig,
    tableHeaderTitle: userRoleManagementLang.screenTitle,
  };
  const columns = [
    { accessorKey: "email", header: "User" },
    { accessorKey: "roles", header: "Roles" },
  ];

  const selectOptions = { rolesArray: roleOptions };
  const disabledInputFields = { email: true }; //TODO: remove this
  return (
    <CrudTable
      isLoading={allUsersLoading}
      formConfig={formConfig}
      selectOptions={selectOptions}
      defaultFormState={defaultFormState}
      inputElements={inputElements}
      disabledInputFields={disabledInputFields}
      rowAdditionaInitialValues={rowAdditionaInitialValues}
      captureFormChange={() => {}}
      columns={columns}
      useFormDetails={useFormDetails}
      setSelectedRow={setSelectedRow}
      selectedRow={selectedRow}
    />
  );
};
