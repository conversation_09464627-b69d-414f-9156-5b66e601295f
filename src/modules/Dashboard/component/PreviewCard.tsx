import { Card, Paper } from "@mui/material";
import React from "react";

interface PreviewCardProps {
  children: JSX.Element;
}

const PreviewCard: React.FC<PreviewCardProps> = ({ children }) => {
  return (
    <Card
      component={Paper}
      elevation={4}
      sx={{
        padding: "20px 15px",
        borderRadius: 4,
        height: 350,
      }}
    >
      {children}
    </Card>
  );
};

export default PreviewCard;
