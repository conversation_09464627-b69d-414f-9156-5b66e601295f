import { Avatar, Box, Typography } from "@mui/material";
// src/components/EventCard.js
import React from "react";

interface InfoCardProps {
  color: string;
  photo?: string;
  rightIcon: string;
  name: string;
  date: string;
  eventType: string;
}

const EventCard = ({ color, rightIcon, name, date, eventType, photo }: InfoCardProps) => {
  return (
    <Box
      sx={{
        display: "flex",
        alignItems: "center",
        justifyContent: "space-between",
        backgroundColor: color,
        borderRadius: "7px",
        padding: "10px 16px",
        height: "68px",
        boxSizing: "border-box",
        marginBottom: "16px",
      }}
    >
      <Avatar alt={name} src={photo} sx={{ width: 40, height: 40 }} />
      <Box sx={{ flex: "1 0 auto", mx: 2 }}>
        <Typography variant="subtitle1" fontSize={14}>
          {name}
        </Typography>
        <Typography variant="subtitle2" fontSize={12}>
          {eventType} | {date}
        </Typography>
      </Box>
      <Box component="img" src={rightIcon} alt="event icon" sx={{ width: 40, height: 40 }} />
    </Box>
  );
};

export default EventCard;
