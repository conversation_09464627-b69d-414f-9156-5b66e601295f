import { ListItem, ListItemText, Typography } from "@mui/material";
import React from "react";

const LineItemCard = ({
  heading,
  subHeading,
  rhsTitle,
}: { heading: Element | string; subHeading: Element | string; rhsTitle?: Element | string }) => {
  return (
    <ListItem divider secondaryAction={<Typography fontSize={14}>{rhsTitle as string}</Typography>}>
      <ListItemText
        primary={heading as string}
        secondary={subHeading as string}
        slotProps={{
          primary: {
            fontSize: 14,
          },
        }}
      />
    </ListItem>
  );
};

export default LineItemCard;
