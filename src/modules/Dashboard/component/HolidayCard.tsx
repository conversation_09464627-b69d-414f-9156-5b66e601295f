import { Box, Typography } from "@mui/material";
// src/components/EventCard.js
import React from "react";

interface InfoCardProps {
  name: string;
  date: string;
  dayAbbrevation: string;
  isMandatory: boolean;
}

const HolidayCard = ({ name, date, dayAbbrevation, isMandatory }: InfoCardProps) => {
  const backgroundColor = isMandatory ? "#B8EEF8" : "#EEE9E4";
  return (
    <Box
      sx={{
        display: "flex",
        alignItems: "flex-start",
        justifyContent: "space-between",
        backgroundColor: backgroundColor,
        borderRadius: "7px",
        padding: "10px 16px",
        // width: '320px',
        height: "68px",
        boxSizing: "border-box",
        marginBottom: "16px",
      }}
    >
      <Box>
        <Typography variant="subtitle1" fontSize={14}>
          {name}
        </Typography>
        <Typography variant="subtitle2" fontSize={12}>
          {isMandatory ? "Mandatory" : "Optional"}
        </Typography>
      </Box>

      <Box sx={{ textAlign: "end" }}>
        <Typography variant="subtitle1" fontSize={14}>
          {date}
        </Typography>
        <Typography variant="subtitle2" fontSize={12}>
          {dayAbbrevation}
        </Typography>
      </Box>
    </Box>
  );
};

export default HolidayCard;
