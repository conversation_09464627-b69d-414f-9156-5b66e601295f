import { Box, CircularProgress, Typography } from "@mui/material";
import { useQuery } from "@tanstack/react-query";
import React from "react";
import { useAppSelector } from "src/customHooks/useAppSelector";
import ScrollableBox from "src/modules/Common/Container/ScrollableBox";
import dashboardService from "src/services/dashboard.service";
import { getStatusColors } from "src/utils/typographyUtils";
import TeamStatusCard from "../TeamStatusCard";
import NoData from "./components/NoDataScreens/NoData";
import { HeaderContainer } from "./style";

const img = "https://lh3.googleusercontent.com/a/ACg8ocKPW4zTckmq1tnyoDvclfnNmWElfQb9JRWZRf4LTSTD_bOKSg=s96-c";

const getTeamStatusColor = (status: string) => {
  switch (status) {
    default:
      return "#F3F3F3";
  }
};

const TeamStatus = () => {
  const { userDetails } = useAppSelector((state) => state.userManagement);
  const {
    data: organisationEvents = [],
    isLoading,
    isFetching,
  } = useQuery({
    queryKey: ["get-team-status"],
    queryFn: async () => dashboardService.getTeamStatus(),
    retryOnMount: false,
    refetchOnWindowFocus: false,
    refetchIntervalInBackground: true,
    refetchInterval: 1000 * 60 * 5,
    enabled: userDetails?.organisations?.length > 0,
  });

  if (organisationEvents?.length === 0) {
    return <NoData title="No Team Status Found" />;
  }

  return (
    <Box display="flex" flexDirection="column">
      <HeaderContainer>
        <Typography fontSize={16} color="#000">
          Today’s Team Status
        </Typography>
      </HeaderContainer>
      <ScrollableBox maxHeight={260}>
        {isLoading || isFetching ? (
          <Box display="flex" flexDirection="column" justifyContent="center" alignItems="center" minHeight="100%">
            <CircularProgress />
          </Box>
        ) : (
          organisationEvents?.map((event) => {
            return (
              <Box sx={{ margin: "10px 0px" }} key={event.team_member.employee_code}>
                <TeamStatusCard
                  photo={event?.team_member?.display_pic || img}
                  color={getTeamStatusColor(event?.status)}
                  textColor={getStatusColors(event?.status)}
                  name={event.team_member.display_name}
                  designation={event.team_member.job_title}
                  status={event.status}
                />
              </Box>
            );
          })
        )}
      </ScrollableBox>
    </Box>
  );
};

export default TeamStatus;
