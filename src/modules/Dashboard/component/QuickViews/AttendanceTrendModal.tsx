import { Box, CircularProgress, MenuItem, Select, Typography } from "@mui/material";
import { Bar<PERSON><PERSON> } from "@mui/x-charts/BarChart";
import { useQuery } from "@tanstack/react-query";
import { format } from "date-fns";
import React from "react";
import { useMemo, useState } from "react";
import EffiMonthDatePicker from "src/modules/Common/FormInputs/EffiMonthDatePicker";
import { ActivityLogDetails } from "src/services/api_definitions/employeeAttendance.service";
import { Attendance } from "src/services/api_definitions/timesheets.service";
import employeeAttendanceService from "src/services/employeeAttendance.service";

interface TimeBreakdown {
  hours: number;
  minutes: number;
  seconds: number;
}

interface ChartData {
  date: string;
  home: TimeBreakdown;
  office: TimeBreakdown;
  onsite: TimeBreakdown;
}

const LOCATION_COLORS = {
  OFFICE: "#87bc45",
  HOME: "#63bff0",
  ONSITE: "#de6e56",
};

const convertDurationToTimeBreakdown = (duration: string): TimeBreakdown => {
  const [hours, minutes, seconds] = duration.split(":").map(Number);
  return { hours: hours || 0, minutes: minutes || 0, seconds: seconds || 0 };
};

const formatTimeBreakdown = (time: TimeBreakdown): string => {
  return `${String(time.hours).padStart(2, "0")}h ${String(time.minutes).padStart(2, "0")}m`;
};

export const processAttendanceData = (response?: ActivityLogDetails[]): ChartData[] => {
  if (!response || response?.length === 0) {
    return [];
  }
  const daysInMonth = Array.from({ length: 31 }, (_, i) => {
    const date = new Date(response[0]?.login_date || Date.now());
    date.setDate(i + 1);
    return date.toISOString().split("T")[0];
  });

  const attendanceMap = new Map<string, ChartData>();

  response.forEach((record) => {
    let home = { hours: 0, minutes: 0, seconds: 0 };
    let office = { hours: 0, minutes: 0, seconds: 0 };
    let onsite = { hours: 0, minutes: 0, seconds: 0 };

    if (record.status === "Absent" || record.status === "Week Off") {
      attendanceMap.set(record.login_date, {
        date: record.login_date,
        home,
        office,
        onsite,
      });
      return;
    }

    record?.details?.forEach((detail) => {
      const timeBreakdown = convertDurationToTimeBreakdown(detail.duration);

      if (detail.location === "Home") {
        home = addTimes(home, timeBreakdown);
      } else if (detail.location === "Office") {
        office = addTimes(office, timeBreakdown);
      } else if (detail.location === "On-Site") {
        onsite = addTimes(onsite, timeBreakdown);
      }
    });

    attendanceMap.set(record.login_date, {
      date: record.login_date,
      home,
      office,
      onsite,
    });
  });

  return daysInMonth
    .map(
      (date) =>
        attendanceMap.get(date) || {
          date,
          home: { hours: 0, minutes: 0, seconds: 0 },
          office: { hours: 0, minutes: 0, seconds: 0 },
          onsite: { hours: 0, minutes: 0, seconds: 0 },
        },
    )
    .sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());
};

const addTimes = (time1: TimeBreakdown, time2: TimeBreakdown): TimeBreakdown => {
  let totalSeconds =
    (time1.hours + time2.hours) * 3600 + (time1.minutes + time2.minutes) * 60 + (time1.seconds + time2.seconds);

  const hours = Math.floor(totalSeconds / 3600);
  totalSeconds %= 3600;
  const minutes = Math.floor(totalSeconds / 60);
  const seconds = totalSeconds % 60;

  return { hours, minutes, seconds };
};

type AttendanceLocationTypes = "home" | "office" | "onsite";

const calculateAverage = (
  data: ChartData[],
  type: AttendanceLocationTypes,
  attendanceData?: Attendance[],
): TimeBreakdown => {
  const nonWorkingStatuses = ["Week Off", "On Leave", "Holiday"];
  const totalWorkingDays = attendanceData?.filter((record) => !nonWorkingStatuses.includes(record.status)).length || 0;
  const totalTime = data.reduce(
    (acc, curr) => {
      return addTimes(acc, curr[type]);
    },
    { hours: 0, minutes: 0, seconds: 0 },
  );
  if (totalWorkingDays === 0) {
    return { hours: 0, minutes: 0, seconds: 0 };
  }
  const totalMinutes = (totalTime.hours * 60 + totalTime.minutes) / totalWorkingDays;
  const avgHours = Math.floor(totalMinutes / 60);
  const avgMinutes = Math.round(totalMinutes % 60);
  return {
    hours: avgHours,
    minutes: avgMinutes,
    seconds: 0,
  };
};

interface StatCardProps {
  bgcolor: string;
  title: string;
  value: number | string;
}

const StatCard = ({ bgcolor, title, value }: StatCardProps): JSX.Element => {
  return (
    <Box sx={{ display: "flex", alignItems: "center" }}>
      <Box sx={{ display: "flex", alignItems: "center", width: 90 }}>
        <Box sx={{ width: 16, height: 16, bgcolor, mr: 1 }} />
        <Typography sx={{ fontSize: "0.875rem" }}>{title}:</Typography>
      </Box>
      <Box sx={{ display: "flex", alignItems: "center" }}>
        <Typography sx={{ fontSize: "0.875rem", fontWeight: "bold" }}>{value}</Typography>
        <Typography sx={{ fontSize: "0.875rem", ml: 0.5 }}>per day (Avg.)</Typography>
      </Box>
    </Box>
  );
};

interface Props {
  attendanceDetails?: ActivityLogDetails[];
}

const DEFAULT_SELECTED_MONTH: string = format(new Date(), "MM-yyyy");

function AttendanceTrendModal({ attendanceDetails = [] }: Props) {
  const [selectedDate, setSelectedDate] = useState(DEFAULT_SELECTED_MONTH);

  const { data: employeeAttendanceData, isLoading } = useQuery(
    ["activity-logs", selectedDate],
    async () => employeeAttendanceService.getAttendanceActivityLog(selectedDate),
    {
      enabled: !!selectedDate && attendanceDetails?.length === 0,
      refetchOnWindowFocus: false,
    },
  );

  const { chartData, stats } = useMemo(() => {
    const resolvedAttendanceData = attendanceDetails?.length === 0 ? employeeAttendanceData : attendanceDetails;
    const processedChartData = processAttendanceData(resolvedAttendanceData);

    const calculatedAverages = {
      home: calculateAverage(processedChartData, "home", resolvedAttendanceData),
      office: calculateAverage(processedChartData, "office", resolvedAttendanceData),
      onsite: calculateAverage(processedChartData, "onsite", resolvedAttendanceData),
    };

    return {
      chartData: processedChartData,
      averages: calculatedAverages,
      stats: [
        { bgcolor: LOCATION_COLORS.ONSITE, title: "On-Site", value: formatTimeBreakdown(calculatedAverages.onsite) },
        { bgcolor: LOCATION_COLORS.OFFICE, title: "Office", value: formatTimeBreakdown(calculatedAverages.office) },
        { bgcolor: LOCATION_COLORS.HOME, title: "Home", value: formatTimeBreakdown(calculatedAverages.home) },
      ],
    };
  }, [attendanceDetails, employeeAttendanceData]);

  if (attendanceDetails?.length === 0 && isLoading) {
    return (
      <Box
        sx={{
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
          height: "500px",
        }}
      >
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box sx={{ width: "100%", height: "500px", position: "relative" }}>
      <Box
        sx={{
          position: "absolute",
          right: 80,
          top: 20,
          zIndex: 1,
          display: "flex",
          gap: 3,
          alignItems: "center",
        }}
      >
        {chartData && chartData.length > 0 && (
          <Box
            sx={{
              display: "flex",
              flexDirection: "column",
              paddingBottom: 2,
              backgroundColor: "#f4ffee",
              p: 1.5,
              borderRadius: 1,
            }}
          >
            {stats.map((stat, index) => (
              <StatCard key={index} bgcolor={stat.bgcolor} title={stat.title} value={stat.value} />
            ))}
          </Box>
        )}

        <EffiMonthDatePicker
          sx={{
            visibility: attendanceDetails?.length !== 0 ? "hidden" : "visible",
          }}
          value={selectedDate}
          onChange={(date) => setSelectedDate(date as string)}
        />
      </Box>

      {!chartData || chartData.length === 0 ? (
        <Box
          sx={{
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            height: "calc(100% - 60px)",
            color: "text.secondary",
            fontSize: "1rem",
          }}
        >
          {/* No attendance statistics available for {MONTHS[selectedMonth]} {selectedYear}. */}
          No attendance trends available for selected month
        </Box>
      ) : (
        <BarChart
          series={[
            {
              data: chartData.map((item) => item.office.hours + item.office.minutes / 60),
              label: "Office:",
              stack: "total",
              color: LOCATION_COLORS.OFFICE,
              valueFormatter: (value) => (value ? `${Math.floor(value)}h ${Math.round((value % 1) * 60)}m` : "0h 0m"),
            },
            {
              data: chartData.map((item) => item.home.hours + item.home.minutes / 60),
              label: "Home:",
              stack: "total",
              color: LOCATION_COLORS.HOME,
              valueFormatter: (value) => (value ? `${Math.floor(value)}h ${Math.round((value % 1) * 60)}m` : "0h 0m"),
            },
            {
              data: chartData.map((item) => item.onsite.hours + item.onsite.minutes / 60),
              label: "On-Site:",
              stack: "total",
              color: LOCATION_COLORS.ONSITE,
              valueFormatter: (value) => (value ? `${Math.floor(value)}h ${Math.round((value % 1) * 60)}m` : "0h 0m"),
            },
          ]}
          xAxis={[
            {
              data: chartData.map((item) => new Date(item.date).getDate()),
              scaleType: "band",
              label: "Day of Month",
              labelStyle: {
                fontSize: 14,
                transform: "translateY(36px)",
              },
              tickLabelStyle: {
                angle: 45,
                textAnchor: "start",
                fontSize: 12,
              },
              valueFormatter: (value) => {
                const date = new Date(chartData[value - 1]?.date || "");
                return format(date, "do MMM");
              },
            },
          ]}
          yAxis={[
            {
              label: "Hours Worked",
              tickMinStep: 1,
              min: 0,
            },
          ]}
          height={450}
          margin={{
            left: 50,
            right: 30,
            top: 120,
            bottom: 120,
          }}
          slotProps={{
            legend: {
              hidden: true,
            },
          }}
        />
      )}
    </Box>
  );
}

export default AttendanceTrendModal;
