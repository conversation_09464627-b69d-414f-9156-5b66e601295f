import { Box, CircularProgress, Typography } from "@mui/material";
import { useQuery } from "@tanstack/react-query";
import { format, parse } from "date-fns";
import React, { useState } from "react";
import { useAppSelector } from "src/customHooks/useAppSelector";
import ScrollableBox from "src/modules/Common/Container/ScrollableBox";
import Swiper from "src/modules/Common/Swiper/Swiper";
import dashboardService from "src/services/dashboard.service";
import masterdataService from "src/services/masterdata.service";
import { formatDateToDayMonth } from "src/utils/dateUtils";
import HolidayCard from "../HolidayCard";
import NoData from "./components/NoDataScreens/NoData";
import { HeaderContainer } from "./style";

const Holidays = () => {
  const { userDetails } = useAppSelector((state) => state.userManagement);
  const [selectedOption, setSelectedOption] = useState<string | null>(null);
  const { data: calendarYears } = useQuery(
    ["get-calendar-years"],
    async () => {
      const resp = await masterdataService.getACLs("CalendarYear");
      const currentSelectedOption = resp?.find((year) => year === format(new Date(), "yyyy")) as string;
      if (currentSelectedOption) {
        setSelectedOption(currentSelectedOption);
      }
      return resp;
    },
    {
      refetchOnWindowFocus: false,
    },
  );

  const {
    data: organisationHolidays = [],
    isLoading,
    isFetching,
  } = useQuery(
    ["get-org-holidays", selectedOption],
    async () => {
      const resp = await dashboardService.getHolidays(selectedOption as string);
      return resp
        ?.filter((holiday) => holiday.date >= format(new Date(), "yyyy-MM-dd"))
        .sort(
          (a, b) =>
            parse(a?.date, "yyyy-MM-dd", new Date()).getTime() - parse(b?.date, "yyyy-MM-dd", new Date()).getTime(),
        )
        .map((holiday) => ({
          ...holiday,
          formattedDate: formatDateToDayMonth(holiday.date),
          dayAbbreviation: new Intl.DateTimeFormat("en-US", { weekday: "long" }).format(
            parse(holiday.date, "yyyy-MM-dd", new Date()),
          ),
        }));
    },
    {
      enabled: !!selectedOption && userDetails?.organisations?.length > 0,
      refetchOnWindowFocus: false,
    },
  );

  return (
    <Box display="flex" flexDirection="column">
      <HeaderContainer>
        <Typography fontSize={16} color="#000">
          Holidays
        </Typography>
        {selectedOption && calendarYears && calendarYears?.length > 0 && (
          <Swiper
            currentValue={selectedOption}
            setCurrentValue={setSelectedOption}
            options={calendarYears as string[]}
          />
        )}
      </HeaderContainer>
      <ScrollableBox maxHeight={260} marginTop={2}>
        {isLoading || isFetching ? (
          <Box display="flex" flexDirection="column" justifyContent="center" alignItems="center" minHeight="100%">
            <CircularProgress />
          </Box>
        ) : (
          <>
            {organisationHolidays?.length === 0 && <NoData title={`No holidays found for ${selectedOption}`} />}
            {organisationHolidays?.length > 0 &&
              organisationHolidays?.map((holiday) => (
                <Box sx={{ margin: "10px 0px" }} key={holiday.name + holiday.type}>
                  <HolidayCard
                    name={holiday.name}
                    date={holiday.formattedDate}
                    dayAbbrevation={holiday.dayAbbreviation}
                    isMandatory={holiday.is_mandatory}
                  />
                </Box>
              ))}
          </>
        )}
      </ScrollableBox>
    </Box>
  );
};

export default Holidays;
