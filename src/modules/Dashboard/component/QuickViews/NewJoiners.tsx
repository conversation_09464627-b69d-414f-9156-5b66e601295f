import { Box, CircularProgress, <PERSON>, Tooltip, Typography } from "@mui/material";
import { useQuery } from "@tanstack/react-query";
import React, { useMemo } from "react";
import { useNavigate } from "react-router-dom";
import ScrollableBox from "src/modules/Common/Container/ScrollableBox";
import LineItemCard from "src/modules/Dashboard/component/LineItemCard";
import { PATH_CONFIG } from "src/modules/Routing/config";
import dashboardService from "src/services/dashboard.service";
import NoData from "./components/NoDataScreens/NoData";
import { HeaderContainer } from "./style";

const typography = {
  noDataTitle: "No new joiners for today",
  titleText: "Today's New Joiners",
  buttonText: "View All",
  tooltipText: "Click here to email",
};

const NewJoiners = () => {
  const navigate = useNavigate();
  const { data = [], isLoading } = useQuery({
    queryKey: ["get-dashboard-new-joiners"],
    queryFn: async () => dashboardService.getNewJoiners(),
    refetchOnWindowFocus: false,
  });

  const onViewAllClick = () => {
    navigate(PATH_CONFIG.NEWJOINEES.path);
  };

  const renderChildren = useMemo(() => {
    if (isLoading) {
      return <CircularProgress />;
    }

    if (data?.length === 0) {
      return <NoData title={typography.noDataTitle} />;
    }
    return (
      <React.Fragment>
        {data?.map(({ first_name, last_name, personal_email }) => (
          <LineItemCard
            heading={`${first_name} ${last_name}`}
            subHeading={
              (
                <Tooltip title={`${typography.tooltipText} ${personal_email}`}>
                  <Link color="primary" href={`mailto:${personal_email}`}>
                    {personal_email}
                  </Link>
                </Tooltip>
              ) as unknown as Element
            }
            key={`${first_name} ${last_name}`}
          />
        ))}
      </React.Fragment>
    );
  }, [data, isLoading]);

  return (
    <Box display="flex" flexDirection="column" gap={1}>
      <HeaderContainer>
        <Typography>{typography.titleText}</Typography>
        <Link sx={{ cursor: "pointer", textDecoration: "none" }} onClick={onViewAllClick}>
          <Typography fontSize={14} color="#1F7ABF">
            {typography.buttonText}
          </Typography>
        </Link>
      </HeaderContainer>
      <ScrollableBox maxHeight={240}>{renderChildren}</ScrollableBox>
    </Box>
  );
};

export default NewJoiners;
