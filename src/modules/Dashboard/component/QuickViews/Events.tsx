import { Box, CircularProgress, Typography } from "@mui/material";
import { useQuery } from "@tanstack/react-query";
import BoxIcon from "assets/box.png";
import HatIcon from "assets/hat.png";
import { format } from "date-fns";
import React from "react";
import { useAppSelector } from "src/customHooks/useAppSelector";
import ScrollableBox from "src/modules/Common/Container/ScrollableBox";
import dashboardService from "src/services/dashboard.service";
import { formatDateToDayMonth } from "src/utils/dateUtils";
import EventCard from "../EventCard";
import NoData from "./components/NoDataScreens/NoData";
import { HeaderContainer } from "./style";

const getEventColor = (eventType: string) => {
  switch (eventType) {
    case "Birthday":
      return "#FFE2EB";
    case "Anniversary":
      return "#DAD4FF";
    default:
      return "#FFE2EB";
  }
};

const Events = () => {
  const { userDetails } = useAppSelector((state) => state.userManagement);
  const {
    data: organisationEvents = [],
    isLoading,
    isFetching,
  } = useQuery(
    ["get-org-events"],
    async () => {
      const resp = await dashboardService.getEvents();
      return resp;
    },
    {
      refetchOnWindowFocus: false,
      refetchOnMount: false,
      enabled: userDetails?.organisations?.length > 0,
    },
  );

  if (organisationEvents?.length === 0) {
    return (
      <Box display="flex" flexDirection="column">
        <HeaderContainer>
          <Typography fontSize={16} color="#000">
            Celebration
          </Typography>
        </HeaderContainer>
        <NoData title="No events to show" />
      </Box>
    );
  }

  return (
    <Box display="flex" flexDirection="column">
      <HeaderContainer>
        <Typography fontSize={16} color="#000">
          Celebrations
        </Typography>
      </HeaderContainer>
      <ScrollableBox maxHeight={260} marginTop={2}>
        {isLoading || isFetching ? (
          <Box display="flex" flexDirection="column" justifyContent="center" alignItems="center" minHeight="100%">
            <CircularProgress />
          </Box>
        ) : (
          organisationEvents?.map((event) => (
            <Box sx={{ margin: "10px 0px" }} key={event.title + event.event_type}>
              <EventCard
                photo={event?.display_pic}
                rightIcon={(event?.event_type === "Birthday" ? HatIcon : BoxIcon) as string}
                color={getEventColor(event.event_type)}
                name={event.title}
                date={formatDateToDayMonth(event.event_date)}
                eventType={event.event_type}
              />
            </Box>
          ))
        )}
      </ScrollableBox>
    </Box>
  );
};

export default Events;
