import { Box, Tooltip } from "@mui/material";
import LinearProgress from "@mui/material/LinearProgress";
import React, { useEffect, useState } from "react";

interface Props {
  derivedSeconds: number | null; // Seconds passed from parent, can be null
}

const MinuteLinearProgressBar: React.FC<Props> = ({ derivedSeconds }) => {
  const [progress, setProgress] = useState(0);

  useEffect(() => {
    if (derivedSeconds === null || derivedSeconds < 0 || derivedSeconds > 60) {
      // Guard: If no valid seconds passed, reset progress
      setProgress(0);
      return;
    }

    // Calculate the progress based on derivedSeconds
    const calculatedProgress = (derivedSeconds / 60) * 100;
    setProgress(calculatedProgress);
  }, [derivedSeconds]);

  return (
    <Box width="100%">
      <Tooltip title={`${derivedSeconds} sec(s)`}>
        <LinearProgress
          sx={{ borderRadius: 4 }}
          variant="determinate"
          value={progress} // Linear progress based on derivedSeconds
        />
      </Tooltip>
    </Box>
  );
};

export default MinuteLinearProgressBar;
