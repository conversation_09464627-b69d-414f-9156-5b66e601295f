import styled from "@emotion/styled";
import { ScheduleOutlined, UpdateOutlined } from "@mui/icons-material";
import { Box, Typography } from "@mui/material";
import React, { useCallback, useEffect, useState } from "react";
import { DashboardAttendanceDetails } from "src/services/api_definitions/dashboard.service";
import { calculateNightShiftProgress } from "src/utils/dateUtils";

const TimerText = styled(Typography)(() => ({
  fontFamily: "monospace",
  width: "80px",
  textAlign: "right",
  fontWeight: 600,
  fontSize: 16,
}));

const AttendanceTimeDelta: React.FC<{
  attendanceDetail?: DashboardAttendanceDetails | null;
  hasUserCheckedOut: boolean;
  deltaSincePreviousCheckin: string | null;
}> = ({ attendanceDetail, hasUserCheckedOut }) => {
  const [delta, setDelta] = useState<string | null>(null);

  const updateDelta = useCallback(() => {
    const res = calculateNightShiftProgress({
      totalDuration: attendanceDetail?.duration as string,
      shiftStartTime: attendanceDetail?.work_start_time as string,
      shiftEndTime: attendanceDetail?.work_end_time as string,
      lastCheckinTimestamp: attendanceDetail?.last_check_in_time as string,
    });
    setDelta(res.formattedTime);
  }, [attendanceDetail]);

  useEffect(() => {
    if (!attendanceDetail?.check_in_time) {
      return () => null;
    }

    updateDelta();
    const interval = setInterval(updateDelta, 1000);
    return () => clearInterval(interval);
  }, [attendanceDetail]);

  // if both checkin time & checkout time is present then just show the time diff
  if (hasUserCheckedOut) {
    return (
      <Box display="flex" flexDirection="row" gap={1}>
        {hasUserCheckedOut ? <ScheduleOutlined /> : <UpdateOutlined />}
        <TimerText> {`${attendanceDetail?.duration || "--:--"}`}</TimerText>
      </Box>
    );
  }

  return (
    <Box display="flex" flexDirection="row" gap={1}>
      {hasUserCheckedOut ? <ScheduleOutlined /> : <UpdateOutlined />}
      <TimerText>{!delta ? "--:--" : `${delta}`}</TimerText>
    </Box>
  );
};

export default AttendanceTimeDelta;
