import { LinearProgress, Typography } from "@mui/material";
import { differenceInMinutes, parse } from "date-fns";
import React, { useState, useEffect } from "react";
import { DashboardAttendanceDetails } from "src/services/api_definitions/dashboard.service";
import { calculateNightShiftProgress } from "src/utils/dateUtils";

const calculateWorkProgressPercentage = (
  checkinTime: string,
  checkoutTime: string,
  workStartTime: string,
  workEndTime: string,
) => {
  const today = new Date().toISOString().split("T")[0]; // Get today's date as YYYY-MM-DD
  const checkin = parse(`${today} ${checkinTime}`, "yyyy-MM-dd HH:mm", new Date());
  const checkout = parse(`${today} ${checkoutTime}`, "yyyy-MM-dd HH:mm", new Date());
  const standardStart = parse(`${today} ${workStartTime}`, "yyyy-MM-dd HH:mm", new Date());
  const standardEnd = parse(`${today} ${workEndTime}`, "yyyy-MM-dd HH:mm", new Date());

  const actualWorkMinutes = differenceInMinutes(checkout, checkin);
  const minWorkMinutes = differenceInMinutes(standardEnd, standardStart);

  const progressPercentage = Math.min((actualWorkMinutes / minWorkMinutes) * 100, 100);

  return progressPercentage;
};

const AttendanceProgress: React.FC<{
  attendanceDetail?: DashboardAttendanceDetails | null;
  hasUserCheckedOut: boolean;
  deltaSincePreviousCheckin: string | null;
}> = ({ attendanceDetail, hasUserCheckedOut }) => {
  const [progress, setProgress] = useState(0);

  useEffect(() => {
    const interval = setInterval(() => {
      if (!attendanceDetail?.check_in_time) {
        return null;
      }
      const { progress = 0 } = calculateNightShiftProgress({
        totalDuration: attendanceDetail?.duration as string,
        shiftStartTime: attendanceDetail?.work_start_time as string,
        shiftEndTime: attendanceDetail?.work_end_time as string,
        lastCheckinTimestamp: attendanceDetail?.last_check_in_time as string,
      });
      setProgress(progress);
    }, 1000);
    return () => clearInterval(interval);
  }, [attendanceDetail, hasUserCheckedOut]);

  if (hasUserCheckedOut && attendanceDetail?.check_in_time && attendanceDetail?.check_out_time) {
    return (
      <Typography fontSize={16} fontWeight={600}>
        <LinearProgress
          color="secondary"
          variant="determinate"
          sx={{ borderRadius: 4 }}
          value={calculateWorkProgressPercentage(
            attendanceDetail?.check_in_time,
            attendanceDetail?.check_out_time,
            attendanceDetail?.work_start_time as string,
            attendanceDetail?.work_end_time as string,
          )}
        />
      </Typography>
    );
  }

  return <LinearProgress variant="determinate" sx={{ borderRadius: 4 }} value={(progress as number) || 0} />;
};

export default AttendanceProgress;
