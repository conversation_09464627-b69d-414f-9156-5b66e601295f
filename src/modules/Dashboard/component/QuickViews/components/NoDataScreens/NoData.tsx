import { Box, Typography } from "@mui/material";
import React from "react";

interface NoDataProps {
  title: string;
}

const NoData: React.FC<NoDataProps> = ({ title }) => {
  return (
    <Box display="flex" flexDirection="column" justifyContent="center" alignItems="center" height={300}>
      <Typography fontSize={16} color="#667085" textAlign="center">
        {title}
      </Typography>
    </Box>
  );
};

export default NoData;
