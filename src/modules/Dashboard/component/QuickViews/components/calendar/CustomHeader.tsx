import ChevronLeft from "@mui/icons-material/ChevronLeft";
import ChevronRight from "@mui/icons-material/ChevronRight";
import IconButton from "@mui/material/IconButton";
import Stack from "@mui/material/Stack";
import Typography from "@mui/material/Typography";
import { styled } from "@mui/material/styles";
import { PickersCalendarHeaderProps } from "@mui/x-date-pickers/PickersCalendarHeader";
import { add, format } from "date-fns";
import * as React from "react";

const CustomCalendarHeaderRoot = styled("div")({
  display: "flex",
  justifyContent: "space-between",
  padding: "8px 16px",
  alignItems: "center",
});

function CustomCalendarHeader(props: PickersCalendarHeaderProps<Date>) {
  const { currentMonth, onMonthChange } = props;

  const selectNextMonth = () => {
    const newDate = add(currentMonth, { months: 1 });
    onMonthChange(newDate, "left");
  };
  const selectPreviousMonth = () => {
    const newDate = add(currentMonth, { months: -1 });
    onMonthChange(newDate, "right");
  };

  return (
    <CustomCalendarHeaderRoot>
      <Stack spacing={1} direction="row">
        <IconButton onClick={selectPreviousMonth} title="Previous month">
          <ChevronLeft />
        </IconButton>
      </Stack>
      <Typography variant="body2">{format(currentMonth, "MMMM yyyy")}</Typography>
      <Stack spacing={1} direction="row">
        <IconButton onClick={selectNextMonth} title="Next month">
          <ChevronRight />
        </IconButton>
      </Stack>
    </CustomCalendarHeaderRoot>
  );
}

export default CustomCalendarHeader;
