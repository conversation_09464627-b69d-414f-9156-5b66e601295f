import { ATTENDACE_TYPES } from "src/modules/Calendar/constants";

export const DAY_BG_COLOR_STYLES = {
  [ATTENDACE_TYPES.PRESENT]: {
    backgroundColor: "#C6FFC2",
    color: "#000000",
    fontSize: "14px",
  },
  [ATTENDACE_TYPES.ABSENT]: {
    backgroundColor: "#FFC2C2",
    color: "#000000",
    fontSize: "14px",
  },
  [ATTENDACE_TYPES.REGULARISED]: {
    backgroundColor: "#C6FFC2",
    color: "#000000",
    fontSize: "14px",
  },
  [ATTENDACE_TYPES.REGULARISATION_PENDING]: {
    backgroundColor: "#FFF1DF",
    color: "#000000",
    fontSize: "14px",
  },
  [ATTENDACE_TYPES.REGULARISATION_REQUESTED]: {
    backgroundColor: "#FFF1DF",
    color: "#000000",
    fontSize: "14px",
  },
  [ATTENDACE_TYPES.HALF_DAY]: {
    backgroundColor: "#FFF1DF",
    color: "#000000",
    fontSize: "14px",
  },
  [ATTENDACE_TYPES.LOP_FULL]: {
    backgroundColor: "#FFC2C2",
    color: "#000000",
    fontSize: "14px",
  },
  [ATTENDACE_TYPES.LOP_HALF]: {
    backgroundColor: "#FFC2C2",
    color: "#000000",
    fontSize: "14px",
  },
  [ATTENDACE_TYPES.ON_LEAVE]: {
    backgroundColor: "#C6FFC2",
    color: "#000000",
    fontSize: "14px",
  },
  [ATTENDACE_TYPES.HOLIDAY]: {
    backgroundColor: "#B8EEF8",
    color: "#000000",
    fontSize: "14px",
  },
  [ATTENDACE_TYPES.ON_LEAVE_WITHOUT_PAY]: {
    backgroundColor: "#FFC2C2",
    color: "#000000",
    fontSize: "14px",
  },
  [ATTENDACE_TYPES.WEEK_OFF]: {
    backgroundColor: "#EDEDED",
    color: "#000000",
    fontSize: "14px",
  },
};
