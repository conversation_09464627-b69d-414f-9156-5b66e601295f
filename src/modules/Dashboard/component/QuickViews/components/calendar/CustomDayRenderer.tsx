import { Tooltip } from "@mui/material";
import { PickersDay, PickersDayProps } from "@mui/x-date-pickers/PickersDay";
import React, { memo, useMemo } from "react";
import { EVENT_TYPES } from "src/modules/Calendar/constants";
import { CalendarEventProps } from "src/services/api_definitions/calendar";
import { DAY_BG_COLOR_STYLES } from "./constants";

interface FilterEventTypes {
  type: string;
  start: Date;
}

interface CustomPickersDayProps extends PickersDayProps<Date> {
  events?: CalendarEventProps[];
}

const CustomDayRenderer = memo((props: CustomPickersDayProps) => {
  const { day, outsideCurrentMonth, events, disabled, ...other } = props;

  const currentEvent = useMemo(
    () =>
      events?.find(
        ({ type, start }: FilterEventTypes) =>
          [EVENT_TYPES.Attendance, EVENT_TYPES.Holiday].includes(type) && day.toDateString() === start.toDateString(),
      ),
    [events],
  );

  const getStyles = () => {
    if (currentEvent?.type === "Attendance") {
      return DAY_BG_COLOR_STYLES[currentEvent?.title || ""];
    }

    if (currentEvent?.type === "Holiday") {
      return DAY_BG_COLOR_STYLES["Holiday"];
    }
  };

  return (
    <Tooltip title={(!disabled && currentEvent?.title) || ""} placement="top" key={currentEvent?.title}>
      <PickersDay
        day={day}
        sx={getStyles()}
        {...other}
        showDaysOutsideCurrentMonth={false}
        outsideCurrentMonth={outsideCurrentMonth}
      />
    </Tooltip>
  );
});

export default CustomDayRenderer;
