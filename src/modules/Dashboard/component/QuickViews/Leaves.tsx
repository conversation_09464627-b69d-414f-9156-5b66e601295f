import { Box, CircularProgress, Divider, Link, Paper, Tab, Tabs, Typography } from "@mui/material";
import { useQueries } from "@tanstack/react-query";
import React, { useMemo, useState } from "react";
import { useNavigate } from "react-router-dom";
import { useAppSelector } from "src/customHooks/useAppSelector";
import ScrollableBox from "src/modules/Common/Container/ScrollableBox";
import { PATH_CONFIG } from "src/modules/Routing/config";
import leavesService from "src/services/leaves.service";
import { formatDateToDayMonth } from "src/utils/dateUtils";
import { getStatusColors } from "src/utils/typographyUtils";
import { HeaderContainer } from "./style";

const leaveTabs = [
  {
    id: 0,
    title: "Requests",
  },
];

const managerTabs = [
  ...leaveTabs,
  {
    id: 1,
    title: "Approvals",
    needsManagerAccess: true,
  },
];

const AttendanceCard = ({ heading, subHeading, status }: { heading: string; subHeading: string; status: string }) => {
  return (
    <React.Fragment>
      <Box display="flex" flexDirection="row" justifyContent="space-between" alignItems="center" padding={2}>
        <Box display="flex" flexDirection="column">
          <Typography color="#000" fontSize={14}>
            {heading}
          </Typography>
          <Typography color="#66705" fontSize={12}>
            {subHeading}
          </Typography>
        </Box>
        <Typography fontSize={14} color={getStatusColors(status)}>
          {status}
        </Typography>
      </Box>
      <Divider />
    </React.Fragment>
  );
};

const Leaves = () => {
  const navigate = useNavigate();
  const {
    userDetails: { is_manager = false, organisations },
  } = useAppSelector((state) => state.userManagement);
  const tabsToShow = useMemo(() => (is_manager ? managerTabs : leaveTabs), [is_manager]);
  const [tabId, setTabId] = useState(tabsToShow[0].id);

  const [leaves, requests] = useQueries({
    queries: [
      {
        queryKey: ["get-leaves"],
        queryFn: async () => leavesService.getLeaveRequests(),
        refetchOnWindowFocus: false,
        enabled: tabId === 0 && organisations?.length > 0,
      },
      {
        queryKey: ["get-requests"],
        queryFn: async () => leavesService.getLeaveRequestToApprove(),
        refetchOnWindowFocus: false,
        enabled: tabId === 1,
      },
    ],
  });
  const data = useMemo(() => (tabId === 0 ? leaves?.data : requests?.data), [requests?.isFetching, leaves?.isFetching]);
  const isDataLoading = tabId === 0 ? leaves?.isFetching : requests?.isFetching;
  const handleTabChange = (_event: React.SyntheticEvent, index: number) => {
    setTabId(index);
  };

  const onViewAllClick = () => {
    navigate(PATH_CONFIG.LEAVES.path);
  };

  return (
    <Box display="flex" flexDirection="column" gap={1}>
      <HeaderContainer>
        <Typography>Leaves</Typography>
        <Link sx={{ cursor: "pointer", textDecoration: "none" }} onClick={onViewAllClick}>
          <Typography fontSize={14} color="#1F7ABF">
            View All
          </Typography>
        </Link>
      </HeaderContainer>
      <Tabs variant="fullWidth" value={tabId} onChange={handleTabChange} component={Paper} elevation={0}>
        {tabsToShow.map((tab) => (
          <Tab sx={{ textTransform: "none" }} tabIndex={tab.id} key={tab.id} label={tab.title} />
        ))}
      </Tabs>
      <ScrollableBox maxHeight={240}>
        {isDataLoading && (
          <Box height="100%" display="flex" justifyContent="center" alignItems="center">
            <CircularProgress />
          </Box>
        )}
        {!isDataLoading &&
          data?.map((request) => {
            const dateRange = `${formatDateToDayMonth(request.startDate)} - ${formatDateToDayMonth(request.endDate)}`;
            return (
              <Paper key={request.requestId} elevation={0}>
                {tabId === 0 ? (
                  <AttendanceCard heading={request.requestType || ""} subHeading={dateRange} status={request.status} />
                ) : (
                  <AttendanceCard
                    heading={request?.raisedBy?.displayName || ""}
                    subHeading={dateRange}
                    status={request.status}
                  />
                )}
              </Paper>
            );
          })}
      </ScrollableBox>
    </Box>
  );
};

export default Leaves;
