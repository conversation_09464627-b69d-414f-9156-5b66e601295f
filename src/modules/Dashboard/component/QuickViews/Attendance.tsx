import { BarChartRounded, Bedtime, WbSunny } from "@mui/icons-material";
import {
  <PERSON><PERSON>,
  Box,
  Button,
  DialogActions,
  FormControl,
  FormControlLabel,
  FormLabel,
  IconButton,
  Radio,
  RadioGroup,
  Tooltip,
  Typography,
  styled,
} from "@mui/material";
import { useMutation, useQuery } from "@tanstack/react-query";
import React, { useEffect, useMemo, useState } from "react";
import { useAppSelector } from "src/customHooks/useAppSelector";
import { useMasterData } from "src/customHooks/useMasterData";
import Modal from "src/modules/Common/Modal/Modal";
import dashboardService from "src/services/dashboard.service";
import {
  convertTimeToAMPM,
  getIntlTimeToSpecifiedFormat,
  getMinutesDifference,
  getTodaysDate,
} from "src/utils/dateUtils";
import AttendanceTrendModal from "./AttendanceTrendModal";
import AttendanceProgress from "./components/Attendance/AttendanceProgress";
import AttendanceTimeDelta from "./components/Attendance/AttendanceTimeDelta";

const WorkTimingsTypography = styled(Typography)(() => ({
  fontSize: 12,
  color: "#667085",
}));

const AttendanceQuickView = () => {
  const { selectedOrganisationDetails, userDetails } = useAppSelector((state) => state.userManagement);
  const [isWorkLocationModalOpen, setIsWorkLocationModalOpen] = useState(false);
  const { data: attendanceLocations } = useMasterData<string>("AttendanceLocation");

  const [value, setValue] = React.useState<string | null>("Office");
  const [isModalOpen, setIsModalOpen] = useState(false);

  const isGeofencingEnabled = useMemo(
    () => selectedOrganisationDetails?.addresses?.find((eachAddress) => !!eachAddress?.primary)?.geofence_enabled,
    [selectedOrganisationDetails],
  );
  const attendanceOptions = useMemo(
    () => attendanceLocations?.filter((eachLocation) => (isGeofencingEnabled ? eachLocation !== "Office" : true)),
    [attendanceLocations],
  );

  useEffect(() => {
    if (isGeofencingEnabled) {
      setValue(attendanceOptions?.[0] || "Office");
      return;
    }
  }, [isGeofencingEnabled, attendanceOptions]);

  const { data: attendanceDetail, refetch } = useQuery(
    ["get-attendance-detail"],
    async () => {
      return dashboardService.getAttendanceDetails();
    },
    {
      refetchOnWindowFocus: false,
      enabled: userDetails?.organisations?.length > 0,
    },
  );

  const isCheckoutDisabled = useMemo(
    () => isGeofencingEnabled && attendanceDetail?.location === "Office" && !attendanceDetail?.check_out_time,
    [attendanceDetail, isGeofencingEnabled],
  );

  const { checkoutTime, checkinTime } = useMemo(() => {
    if (attendanceDetail?.details?.length === 0) {
      return {
        checkoutTime: null,
        checkinTime: null,
      };
    }
    const startSlot = attendanceDetail?.details[0];
    const endSlot = attendanceDetail?.details[attendanceDetail?.details?.length - 1];
    return {
      checkoutTime: getIntlTimeToSpecifiedFormat(endSlot?.check_out_time as unknown as string, "p").formattedDate,
      checkinTime: getIntlTimeToSpecifiedFormat(startSlot?.check_in_time as unknown as string, "p").formattedDate,
    };
  }, [attendanceDetail]);

  const checkInMutation = useMutation({
    mutationKey: ["check-in"],
    mutationFn: async (location: string) => dashboardService.checkIn(location),
    onSuccess: () => {
      refetch();
      setIsWorkLocationModalOpen(false);
    },
  });

  const checkOutMutation = useMutation({
    mutationKey: ["check-out"],
    mutationFn: async () => dashboardService.checkOut(),
    onSuccess: () => {
      refetch();
    },
  });

  const onCheckIn = () => {
    setIsWorkLocationModalOpen(true);
  };

  const onCheckOut = () => {
    checkOutMutation.mutate();
  };

  const enableCheckOut = useMemo(() => {
    return attendanceDetail?.details?.some((attendance) => attendance?.check_in_time && !attendance?.check_out_time);
  }, [attendanceDetail]);

  const onSave = () => {
    if (value) {
      checkInMutation.mutate(value);
    }
  };

  const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setValue((event.target as HTMLInputElement).value);
  };

  // Todo: This is getting used in different locations, so make it generic later
  const isNightShift =
    getMinutesDifference(attendanceDetail?.work_start_time as string, attendanceDetail?.work_end_time as string) > 0;

  const getEndAdornmentForInputs = () => {
    if (!attendanceDetail?.work_start_time || !attendanceDetail?.work_end_time) {
      return null;
    }

    if (isNightShift) {
      return (
        <Tooltip title="Night Shift">
          <Bedtime color="secondary" />
        </Tooltip>
      );
    }
    return (
      <Tooltip title="Day Shift">
        <WbSunny color="warning" />
      </Tooltip>
    );
  };

  return (
    <Box width="100%" flexDirection="column" alignItems="stretch" height="100%" position="relative">
      <Box display="flex" height="50%" alignSelf="flex-start" width="100%">
        <Box display="flex" justifyContent="space-between" width="100%">
          <Box display="flex" flexDirection="column" alignItems="flex-start">
            <Box display="flex" justifyContent="flex-start" alignItems="center">
              <Typography fontSize={16} fontWeight={500}>
                Today
              </Typography>
              <Tooltip title="View Attendance Trends">
                <IconButton size="small" color="primary" onClick={() => setIsModalOpen(true)}>
                  <BarChartRounded fontSize="small" />
                </IconButton>
              </Tooltip>
            </Box>
            <Typography fontSize={12}>{getTodaysDate("EEE, d MMM yyyy")}</Typography>
          </Box>
          <Box>
            <AttendanceTimeDelta
              deltaSincePreviousCheckin={attendanceDetail?.duration as string}
              hasUserCheckedOut={!enableCheckOut}
              attendanceDetail={attendanceDetail}
            />
          </Box>
        </Box>
      </Box>
      <Box
        position="absolute"
        sx={{ width: "100%", top: "20%" }}
        visibility={isCheckoutDisabled ? "visible" : "hidden"}
      >
        <Alert variant="outlined" severity="info" aria-label="Alerted" sx={{ padding: "2px" }}>
          Please check out using effiHR mobile app, as your last check-in location was at the office.
        </Alert>
      </Box>
      <Box flexGrow={1} alignSelf="flex-end">
        <Box display="flex" alignItems="center" justifyContent="space-between">
          <Box display="flex" flexDirection="column">
            <WorkTimingsTypography>Work Start Time</WorkTimingsTypography>
            <WorkTimingsTypography textAlign="left">
              {convertTimeToAMPM(attendanceDetail?.work_start_time as string)}
            </WorkTimingsTypography>
          </Box>
          <Box>{getEndAdornmentForInputs()}</Box>
          <Box display="flex" flexDirection="column">
            <WorkTimingsTypography>Work End Time</WorkTimingsTypography>
            <WorkTimingsTypography textAlign="right">
              <Box gap={0.5} display={isNightShift ? "flex" : "block"} justifySelf="flex-end">
                {convertTimeToAMPM(attendanceDetail?.work_end_time as string)}
                {isNightShift && (
                  <Typography fontSize={12} fontWeight="bold" color="error">
                    (+1 day)
                  </Typography>
                )}
              </Box>
            </WorkTimingsTypography>
          </Box>
        </Box>
        <Box margin="20px 0px 10px 0px">
          <AttendanceProgress
            deltaSincePreviousCheckin={attendanceDetail?.duration as string}
            hasUserCheckedOut={!enableCheckOut}
            attendanceDetail={attendanceDetail}
          />
          <Box display="flex" alignItems="center" justifyContent="space-between" margin="10px 0px">
            <WorkTimingsTypography fontSize={10}>{checkinTime}</WorkTimingsTypography>
            <WorkTimingsTypography fontSize={10}>{attendanceDetail?.location}</WorkTimingsTypography>
            <WorkTimingsTypography fontSize={10}>{checkoutTime}</WorkTimingsTypography>
          </Box>
        </Box>
      </Box>
      <Box position="absolute" sx={{ width: "100%", bottom: 0 }}>
        <Button
          fullWidth
          variant="contained"
          disabled={isCheckoutDisabled}
          onClick={() => (enableCheckOut ? onCheckOut() : onCheckIn())}
          sx={{
            backgroundColor: enableCheckOut ? "#FEE6E7" : "primary.main",
            color: enableCheckOut ? "#f34a4d" : "#FFFFFF",
            "&:hover": {
              backgroundColor: enableCheckOut ? "#F8C0BC" : "primary.dark",
            },
          }}
        >
          {enableCheckOut ? "Check Out" : "Check In"}
        </Button>
      </Box>
      {isWorkLocationModalOpen && (
        <Modal
          isOpen={isWorkLocationModalOpen}
          onClose={() => setIsWorkLocationModalOpen(false)}
          title="Select Work Location"
          subtitle="Please select the location from where you are checking in"
          showBackButton
          actions={
            <DialogActions>
              <Button variant="contained" onClick={onSave}>
                Submit
              </Button>
            </DialogActions>
          }
        >
          <FormControl sx={{ width: "100%", display: "flex", flexDirection: "column", gap: 1 }}>
            <FormLabel id="demo-controlled-radio-buttons-group">Location</FormLabel>
            <RadioGroup
              aria-labelledby="demo-controlled-radio-buttons-group"
              name="controlled-radio-buttons-group"
              value={value}
              onChange={handleChange}
              defaultValue="Office"
              sx={{ display: "flex", flexDirection: "column", gap: 1 }}
            >
              {attendanceOptions?.map((attendanceLocation) => (
                <FormControlLabel
                  key={attendanceLocation}
                  value={attendanceLocation}
                  control={<Radio />}
                  label={attendanceLocation}
                  labelPlacement="start"
                  sx={{
                    justifyContent: "space-between",
                    margin: 0,
                    border: "1px solid #D0D5DD",
                    borderRadius: 4,
                    padding: 1,
                  }}
                />
              ))}
            </RadioGroup>
          </FormControl>
        </Modal>
      )}
      {isModalOpen && (
        <Modal
          isOpen={isModalOpen}
          onClose={() => setIsModalOpen(false)}
          title="Attendance Statistics"
          showBackButton
          modalWidth="90%"
        >
          <Box sx={{ width: "100%", overflowX: "hidden" }}>
            <AttendanceTrendModal />
          </Box>
        </Modal>
      )}
    </Box>
  );
};

export default AttendanceQuickView;
