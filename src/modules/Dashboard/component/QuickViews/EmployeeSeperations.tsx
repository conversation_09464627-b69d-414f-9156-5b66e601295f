import { Box, CircularProgress, Divider, Link, Paper, Tooltip, Typography } from "@mui/material";
import { useQuery } from "@tanstack/react-query";
import React, { useMemo } from "react";
import { useNavigate } from "react-router-dom";
import ScrollableBox from "src/modules/Common/Container/ScrollableBox";
import LineItemCard from "src/modules/Dashboard/component/LineItemCard";
import { PATH_CONFIG } from "src/modules/Routing/config";
import dashboardService from "src/services/dashboard.service";
import NoData from "./components/NoDataScreens/NoData";
import { HeaderContainer } from "./style";

const typography = {
  noDataText: "No employee separations for today",
  titleText: "Today's Employee Separations",
  buttonText: "View All",
  tooltipText: "Click here to email",
};

const EmployeeSeperations = () => {
  const navigate = useNavigate();
  const { data = [], isLoading } = useQuery({
    queryKey: ["get-dashboard-seperations"],
    queryFn: async () => dashboardService.getEmployeeSeperations(),
    refetchOnWindowFocus: false,
  });

  const renderChildren = useMemo(() => {
    if (isLoading) {
      return <CircularProgress />;
    }

    if (data?.length === 0) {
      return <NoData title={typography.noDataText} />;
    }
    return (
      <React.Fragment>
        {data?.map(({ email, employee_code, name }) => (
          <LineItemCard
            heading={name}
            subHeading={
              (
                <Tooltip title={`${typography.tooltipText} ${email}`}>
                  <Link color="primary" href={`mailto:${email}`}>
                    {email}
                  </Link>
                </Tooltip>
              ) as unknown as Element
            }
            rhsTitle={employee_code}
            key={name}
          />
        ))}
      </React.Fragment>
    );
  }, [data, isLoading]);

  const onViewAllClick = () => {
    navigate(PATH_CONFIG.EMPLOYEE_SEPERATIONS.path);
  };

  return (
    <Box display="flex" flexDirection="column" gap={1}>
      <HeaderContainer>
        <Typography>{typography.titleText}</Typography>
        <Link sx={{ cursor: "pointer", textDecoration: "none" }} onClick={onViewAllClick}>
          <Typography fontSize={14} color="#1F7ABF">
            {typography.buttonText}
          </Typography>
        </Link>
      </HeaderContainer>
      <ScrollableBox maxHeight={240}>{renderChildren}</ScrollableBox>
    </Box>
  );
};

export default EmployeeSeperations;
