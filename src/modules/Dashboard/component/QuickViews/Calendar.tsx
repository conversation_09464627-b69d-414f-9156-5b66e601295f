import { Box, Typography } from "@mui/material";
import { DateCalendar } from "@mui/x-date-pickers/DateCalendar";
import { DayCalendarSkeleton } from "@mui/x-date-pickers/DayCalendarSkeleton";
import { useQuery } from "@tanstack/react-query";
import React, { useCallback, useMemo, useState } from "react";
import languageConfig from "src/configs/language/en.lang";
import { useAppSelector } from "src/customHooks/useAppSelector";
import { createCalendarPayload } from "src/modules/Calendar/Calendar";
import { DAYS_NAMES_LIST } from "src/modules/Calendar/constants";
import calendarServiceAPI from "src/services/calendar.service";
import CustomDayRenderer from "./components/calendar/CustomDayRenderer";
import CustomCalendarHeader from "./components/calendar/CustomHeader";
import { HeaderContainer } from "./style";

const Calendar = () => {
  const { userDetails } = useAppSelector((state) => state.userManagement);
  const { calendar: calendarLangConfig } = languageConfig;
  const [calendarPayload, setCalendarPayload] = useState(createCalendarPayload());
  const [selectedDate, setSelectedDate] = useState(new Date());
  const { data: calendarDetails, isLoading } = useQuery({
    queryKey: ["my-calendar", calendarPayload],
    queryFn: async () => {
      const response = await calendarServiceAPI.getCalendarEvents(calendarPayload);
      return response;
    },
    refetchOnMount: false,
    refetchOnWindowFocus: false,
    enabled: userDetails?.organisations?.length > 0,
  });

  const calendarEvents = useMemo(() => calendarDetails?.events, [calendarDetails?.events]);

  const onChangeHandler = (date: Date) => {
    setCalendarPayload(createCalendarPayload(date));
  };

  const handleTodayClick = () => {
    setSelectedDate(new Date());
  };

  return (
    <Box display="flex" flexDirection="column" width="100%">
      <HeaderContainer>
        <Typography fontSize={16} color="#000">
          {calendarLangConfig.calendarText}
        </Typography>

        <Typography
          sx={{ height: "20px", color: "#00776f", cursor: "pointer", fontWeight: "600" }}
          onClick={handleTodayClick}
        >
          {calendarLangConfig.today}
        </Typography>
      </HeaderContainer>
      <Box
        sx={{
          "& .MuiDateCalendar-root": {
            // Targets the main calendar container - this is the outermost wrapper of the entire calendar
            width: "100%", // Makes the calendar take full width of its container
            maxWidth: "400px", // But doesn't let it grow bigger than 400px
            height: "auto", // Lets the height adjust automatically based on content
          },
          "& .MuiDayCalendar-monthContainer": {
            // Targets the container that holds all the days of the current month
            margin: "0 8px", // Adds 8px spacing on left and right of the month grid
          },
          "& .MuiDayCalendar-weekContainer": {
            // Targets each row of days (each week) in the calendar
            margin: "0", // Removes any spacing around each week row
            justifyContent: "space-between", // Spreads out the days evenly across the width
          },
          "& .MuiPickersDay-root": {
            // Targets each individual day cell in the calendar
            margin: "1px", // Tiny gap between each day
            padding: "0", // No internal spacing in day cells
            height: "32px", // Each day cell is 32px tall
            width: "32px", // Each day cell is 32px wide
          },
          "& .MuiDayCalendar-header": {
            // Targets the row containing weekday labels (S,M,T,W,T,F,S)
            justifyContent: "space-between", // Spreads out the weekday labels (S,M,T,W,T,F,S)
            paddingLeft: "8px", // Space on the left of weekday labels
            paddingRight: "8px", // Space on the right of weekday labels
          },
          "& .MuiTypography-root": {
            // Targets all text elements in the calendar (days, month names, year)
            width: "auto", // Text containers can adjust their width as needed
            height: "32px", // All text containers are 32px tall
            display: "flex", // Uses flexible box layout
            alignItems: "center", // Centers content vertically
            justifyContent: "center", // Centers content horizontally
          },
          "& .MuiPickersCalendarHeader-label .MuiTypography-root": {
            // Specifically targets the month and year text at the top (e.g., "February 2025")
            width: "auto", // Makes the month/year text container flexible in width
          },
          "& .MuiDayCalendar-header .MuiTypography-root": {
            // Specifically targets the weekday label text (S,M,T,W,T,F,S)
            width: "32px", // Keeps weekday labels (S,M,T,W,T,F,S) exactly 32px wide
            fontWeight: "bold", // Makes weekday labels bold
          },
        }}
      >
        <DateCalendar
          disableHighlightToday={true}
          showDaysOutsideCurrentMonth={true}
          onMonthChange={onChangeHandler}
          loading={isLoading}
          value={selectedDate}
          onChange={(newValue) => setSelectedDate(newValue)}
          renderLoading={() => <DayCalendarSkeleton />}
          slots={{
            day: CustomDayRenderer,
            calendarHeader: CustomCalendarHeader,
          }}
          readOnly={true}
          slotProps={{
            day: {
              events: calendarEvents,
            } as any,
          }}
        />
      </Box>
    </Box>
  );
};

export default Calendar;
