import { Avatar, Box, Paper, Typography } from "@mui/material";
import React from "react";

interface TeamStatusCardProps {
  color: string;
  photo?: string;
  name: string;
  designation: string;
  status: string;
  textColor: string;
}

const TeamStatusCard = ({ color, textColor, name, designation, status, photo }: TeamStatusCardProps) => {
  return (
    <Paper
      elevation={0}
      sx={{
        display: "flex",
        backgroundColor: color,
        borderRadius: 2,
        padding: { xs: 2, sm: 2 },
        marginBottom: 2,
        width: "100%",
        flexDirection: { xs: "column", sm: "row" },
        alignItems: "center",
        gap: 2,
      }}
    >
      <Avatar
        alt={name}
        src={photo}
        sx={{
          width: { xs: 32, sm: 40 },
          height: { xs: 32, sm: 40 },
          flexShrink: 0,
        }}
      />

      <Box>
        <Typography
          variant="subtitle1"
          fontSize={{ xs: 12, sm: 14 }}
          sx={{
            display: "inline-block",
            maxWidth: "100%",
            lineHeight: 1.2,
          }}
        >
          {name}
          <Typography
            component="span"
            fontSize={{ xs: 10, sm: 12 }}
            sx={{
              color: "text.primary",
              // fontSize: '12px',
              lineHeight: 0.8,
              ml: 0.5,
            }}
          >
            ({designation})
          </Typography>
        </Typography>

        <Typography variant="subtitle2" fontSize={{ xs: 10, sm: 12 }} color={textColor}>
          {status}
        </Typography>
      </Box>
    </Paper>
  );
};

export default TeamStatusCard;
