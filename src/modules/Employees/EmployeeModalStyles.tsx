import { Box, Divider, Typography } from "@mui/material";
import React from "react";
import { effiHRLogo as CompanyLogo, effiHRTextLogo as CompanyLogoText } from "src/assets/icons.svg";

export const ModalControllerStyles = {
  root: {
    ".MuiPaper-root": {
      width: "auto !important",
    },
    ".MuiDialogContent-root": {
      display: "flex",
      padding: "30px 15px",
      maxHeight: "90vh",
      width: "auto",
    },
  },
  paper: {
    display: "block",
    bgcolor: "background.paper",
    borderRadius: "20px",
    boxShadow: 24,
    p: 4,
  },
};

export const containerStyle = {
  display: "flex",
  flexDirection: "column",
  alignItems: "center",
  gap: 4,
  height: "100%",
};

const logoContainerStyle = {
  display: "flex",
  position: "relative",
  alignItems: "center",
  gap: 1,
  justifyContent: "space-between",
  width: "100%",
  minWidth: "800px",
};

const dividerStyle = {
  width: "100%",
  backgroundColor: "#007F6F",
  opacity: 0.7,
};

const headingStyle = {
  fontSize: "24px",
  fontWeight: 600,
  lineHeight: 1.4,
  letterSpacing: "normal",
  textAlign: "center",
  color: "#000000",
};

type ModalHeaderProps = {
  title: string;
};

export const ModalHeader = ({ title }: ModalHeaderProps) => {
  return (
    <>
      <Box sx={logoContainerStyle}>
        <Box display="flex" gap={1} alignItems="center" justifyContent="flex-start" position="absolute" left={0}>
          <CompanyLogo width={50} height={50} />
          <CompanyLogoText width={70} height={70} />
        </Box>
        <Box sx={{ width: "100%" }}>
          <Typography sx={headingStyle}>{title}</Typography>
        </Box>
      </Box>
      <Divider sx={dividerStyle} />
    </>
  );
};
