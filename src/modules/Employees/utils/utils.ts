import { useQuery } from "@tanstack/react-query";
import { Grade, HierarchyResponse, Level } from "src/services/api_definitions/workRoleHierarchy.service";
import fileuploaderService from "src/services/fileuploader.service";
import masterdataService from "src/services/masterdata.service";
import { INPUT_FIELDS as employementDetailsForm } from "../config/EmploymentDetails";
import { WorkRoleHierarchyList } from "../types/employeeTypes";

export const getEnumValues = (enumKey: string) => {
  const { data, isLoading } = useQuery([`get-${enumKey}-details`], async () => masterdataService.getACLs(enumKey), {
    retryOnMount: false,
    refetchOnWindowFocus: false,
  });
  return { data, isLoading };
};

export const convertListToOptions = (list?: string[]) => {
  return (
    list?.map((element: string) => ({
      value: element,
      label: element,
    })) || []
  );
};

export const getBandGradeLevel = (response: HierarchyResponse[]): WorkRoleHierarchyList => {
  const structure = {
    isBand: Boolean(response[0]?.band),
    isBandGrade: Boolean(response[0]?.band?.grades),
    isBandLevel: Boolean(response[0]?.band?.levels),
    isBandLevelGrade: Boolean(response[0]?.band?.levels?.[0]?.grades),
    isLevel: Boolean(response[0]?.level),
    isLevelGrade: Boolean(response[0]?.level?.grades),
    isGrade: Boolean(response[0]?.grade),
  };

  return {
    ...structure,
    bandOptions: structure.isBand ? response.map((element: HierarchyResponse) => ({ name: element.band.name })) : null,
    levelOptions: structure.isLevel
      ? response.map((element: HierarchyResponse) => ({ name: element.level?.name || "", bandName: "" }))
      : structure.isBandLevel
        ? response
            .map((element: HierarchyResponse) =>
              element.band.levels.map((level: Level) => ({
                name: level.name,
                bandName: element.band.name,
              })),
            )
            .flat()
        : null,
    gradeOptions: structure.isGrade
      ? response.map((element: HierarchyResponse) => ({ name: element.grade?.name || "", levelName: "", bandName: "" }))
      : structure.isLevelGrade
        ? response
            .map(
              (element: HierarchyResponse) =>
                element.level?.grades.map((grade: Grade) => ({
                  name: grade.name,
                  levelName: element.level?.name || "",
                  bandName: "",
                })) || [],
            )
            .flat()
        : structure.isBandLevelGrade
          ? response
              .map((element: HierarchyResponse) =>
                element.band.levels
                  .map((level: Level) =>
                    level.grades.map((grade: Grade) => ({
                      name: grade.name,
                      levelName: level.name,
                      bandName: element.band.name,
                    })),
                  )
                  .flat(),
              )
              .flat()
          : null,
  };
};

type WorkRoleConfig = {
  isBand: boolean;
  isBandGrade: boolean;
  isBandLevel: boolean;
  isBandLevelGrade: boolean;
  isLevel: boolean;
  isLevelGrade: boolean;
  isGrade: boolean;
};

const getBandOptions = (
  hierarchy: HierarchyResponse[],
  { isBand, isBandGrade, isBandLevel, isBandLevelGrade }: WorkRoleConfig,
) => {
  return isBand || isBandLevel || isBandGrade || isBandLevelGrade
    ? hierarchy.map((element: HierarchyResponse) => ({ name: element.band.name }))
    : null;
};

const getLevelOptions = (
  hierarchies: HierarchyResponse[],
  { isLevel, isLevelGrade, isBandLevel, isBandLevelGrade }: WorkRoleConfig,
) => {
  const result: any[] = [];
  if (isLevel || isBandLevel || isLevelGrade || isBandLevelGrade) {
    hierarchies?.forEach((eachHierarchy) => {
      if (isBandLevel) {
        eachHierarchy?.band?.levels?.forEach((level) => {
          result.push({
            name: level.name,
            bandName: eachHierarchy?.band?.name,
          });
        });
      }

      if (isLevel) {
        result.push({
          name: eachHierarchy?.level?.name,
        });
      }
    });
  }
  return result;
};

const getGradeOptions = (hierarchies: HierarchyResponse[], config: WorkRoleConfig) => {
  const result: any[] = [];
  if (config.isGrade || config.isLevelGrade || config.isBandLevelGrade) {
    hierarchies?.forEach((eachHierarchy) => {
      if (config.isBandLevelGrade) {
        eachHierarchy?.band?.levels?.forEach((level) => {
          level?.grades?.forEach((grade) => {
            result.push({
              name: grade?.name,
              levelName: level?.name,
              bandName: eachHierarchy?.band?.name,
            });
          });
        });
        return result;
      }
      if (config.isGrade) {
        result.push({
          name: eachHierarchy?.grade?.name,
        });
      }
    });
  }
  return result;
};

// TODO: use this for employee stepper when this is stabilised and tested
export const getBandGradeLevelV2 = (response: HierarchyResponse[]): WorkRoleHierarchyList => {
  const structure = {
    isBand: Boolean(response[0]?.band) && Boolean(!response[0]?.band?.levels) && Boolean(!response[0]?.band?.grades),
    isBandGrade: Boolean(response[0]?.band?.grades),
    isBandLevel: Boolean(response[0]?.band?.levels) && Boolean(!response[0]?.band?.levels?.[0]?.grades),
    isBandLevelGrade: Boolean(response[0]?.band?.levels?.[0]?.grades),
    isLevel: Boolean(response[0]?.level) && Boolean(!response[0]?.level?.grades),
    isLevelGrade: Boolean(response[0]?.level?.grades),
    isGrade: Boolean(response[0]?.grade),
  };

  return {
    ...structure,
    bandOptions: getBandOptions(response, structure),
    levelOptions: getLevelOptions(response, structure),
    gradeOptions: getGradeOptions(response, structure),
  };
};

// Todo - refactor and move this logic from here to actual services

export const uploadEmployeeDocument = async (
  acceptedFiles: File[],
  documentType: string,
  employeeDocumentUpload?: boolean,
) => {
  const formData = new FormData();
  formData.append("file", acceptedFiles[0]);
  formData.append("key", documentType);
  const email = localStorage.getItem(employementDetailsForm.OFFICIAL_EMAIL);
  const url = email && !employeeDocumentUpload ? `/document/upload?email=${email}` : "/document/upload";
  const result = await fileuploaderService.uploadFile(url, formData);
  if (result.message) {
    const newFileName = (result.message as string).split("/").pop();
    const document = {
      name: newFileName || "",
      s3_link: result.message,
      document_type: documentType,
    };
    return document;
  }
  return null;
};

export const uploadProfilePic = async (acceptedFiles: File[], documentType: string) => {
  const formData = new FormData();
  formData.append("file", acceptedFiles[0]);
  formData.append("key", documentType);
  const url = "/document/upload/display_pic";
  const result = await fileuploaderService.uploadFile(url, formData);
  if (result.message && result.type === "success") {
    const newFileName = (result.message as string).split("/").pop();
    const document = {
      name: newFileName || "",
      s3_link: result.message,
      document_type: documentType,
    };
    return document;
  }
  return null;
};
