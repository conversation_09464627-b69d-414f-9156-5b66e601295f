import { useMutation } from "@tanstack/react-query";
import React from "react";
import { BaseObject } from "src/app/global";
import employeesService from "src/services/employees.service";

import EmployeeStepper, { CandidateGlobalFormType, stepsKey } from "./EmployeeStepper";
import { transformUpdateCandidateDetails } from "./utils/candidateTransformer";

type Props = {
  setFinishForm: (value: boolean) => void;
};

const CreateEmployee = ({ setFinishForm }: Props) => {
  const mutation = useMutation({
    mutationKey: ["create-new-employee"],
    mutationFn: async (employeeData: BaseObject) => employeesService.createEmployees(employeeData),
    onSuccess: (response) => {
      if (response) setFinishForm(true);
    },
  });

  const handleFinish = (finalForm: CandidateGlobalFormType) => {
    mutation.mutate(transformUpdateCandidateDetails(finalForm, true));
  };

  const steps: string[] = [
    stepsKey.personalInformation,
    stepsKey.employementDetails,
    stepsKey.employeeCompensation,
    stepsKey.employmentHistory,
    stepsKey.bankDetails,
    stepsKey.familyDetails,
    stepsKey.emergencyDetails,
    stepsKey.educationDetails,
    stepsKey.documentUpload,
  ];

  const stepsProps = {
    [stepsKey.employmentHistory]: {
      isSkip: true,
    },
    [stepsKey.familyDetails]: {
      isSkip: true,
    },
  };

  return <EmployeeStepper steps={steps} onSubmit={handleFinish} stepsProps={stepsProps} />;
};

export default CreateEmployee;
