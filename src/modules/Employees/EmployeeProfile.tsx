import { Box } from "@mui/material";
import { useMutation, useQueries } from "@tanstack/react-query";
import React, { useMemo } from "react";

import { BaseObject } from "src/app/global";
import Modal from "src/modules/Common/Modal/Modal";
import { ModalControllerStyles, containerStyle } from "src/modules/Employees/EmployeeModalStyles";
import { TransformedEmployee } from "src/services/api_definitions/employees";
import employeesService from "src/services/employees.service";

import EmployeeStepper, { CandidateGlobalFormType, stepsKey } from "./EmployeeStepper";
import { INPUT_FIELDS as educationDetailsInputField } from "./config/EducationDetails";
import { INPUT_FIELDS as employmentDetailsInputField } from "./config/EmploymentDetails";
import { INPUT_FIELDS as personalInformationInputField } from "./config/PersonalInformation";
import {
  CandidateDetails,
  transformCandidateDetails,
  transformUpdateCandidateDetails,
} from "./utils/candidateTransformer";

type CreateEmployeeModalProps = {
  isOpen: boolean;
  onClose: (refetchList?: boolean) => void;
  employee?: TransformedEmployee | null;
  isViewOnlyMode?: boolean;
};

const disabledInputFields = {
  [stepsKey.personalInformation]: [
    personalInformationInputField.FIRST_NAME,
    personalInformationInputField.LAST_NAME,
    personalInformationInputField.DATE_OF_BIRTH,
    personalInformationInputField.PERSONAL_EMAIL,
    personalInformationInputField.MOBILE_NO,
    personalInformationInputField.PAN,
    personalInformationInputField.AADHAAR,
    personalInformationInputField.NATIONALITY,
    personalInformationInputField.BLOOD_GROUP,
    personalInformationInputField.MARITAL_STATUS,
    personalInformationInputField.GENDER,
    personalInformationInputField.HOUSE,
    personalInformationInputField.STREET,
    personalInformationInputField.CITY,
    personalInformationInputField.STATE,
    personalInformationInputField.COUNTRY,
    personalInformationInputField.ZIPCODE,
    personalInformationInputField.PERMANENT_HOUSE,
    personalInformationInputField.PERMANENT_STREET,
    personalInformationInputField.PERMANENT_CITY,
    personalInformationInputField.PERMANENT_STATE,
    personalInformationInputField.PERMANENT_COUNTRY,
    personalInformationInputField.PERMANENT_ZIPCODE,
  ],
  [stepsKey.employementDetails]: [
    employmentDetailsInputField.OFFICIAL_EMAIL,
    employmentDetailsInputField.DATE_OF_JOINING,
  ],
  [stepsKey.educationDetails]: [
    educationDetailsInputField.INSTITUTE,
    educationDetailsInputField.UNIVERSITY,
    educationDetailsInputField.DEGREE,
    educationDetailsInputField.DEGREE_TYPE,
    educationDetailsInputField.START_YEAR,
    educationDetailsInputField.END_YEAR,
    educationDetailsInputField.DOCUMENT,
  ],
};

const EmployeeProfile: React.FC<CreateEmployeeModalProps> = ({ isOpen, onClose, employee, isViewOnlyMode }) => {
  const result = useQueries({
    queries: [
      {
        queryKey: ["employee-profile-view", employee?.employee_code],
        queryFn: async (): Promise<CandidateDetails | null> =>
          employeesService.getEmployeeData(employee?.employee_code as string),
        retryOnMount: false,
        refetchOnWindowFocus: false,
        enabled: employee?.employee_code !== undefined,
      },
    ],
  });

  const mutation = useMutation({
    mutationKey: ["update-employee-profile"],
    mutationFn: async (employeeData: BaseObject) => employeesService.updateEmployeeData(employeeData),
    onSuccess: (response) => {
      if (response) onClose(true);
    },
  });

  const [{ data: employeeData, isLoading: isEmployeeDataLoading }] = result;

  const steps: string[] = !isViewOnlyMode
    ? [stepsKey.personalInformation, stepsKey.employementDetails, stepsKey.employeeCompensation, stepsKey.bankDetails]
    : [
        stepsKey.personalInformation,
        stepsKey.employementDetails,
        stepsKey.employmentHistory,
        stepsKey.employeeCompensation,
        stepsKey.bankDetails,
        stepsKey.familyDetails,
        stepsKey.emergencyDetails,
        stepsKey.educationDetails,
        stepsKey.documentUpload,
      ];

  const initialData = useMemo(
    () => (!isEmployeeDataLoading && employeeData ? transformCandidateDetails(employeeData, true) : {}),
    [employeeData, isEmployeeDataLoading, isViewOnlyMode],
  );

  if (!employee || isEmployeeDataLoading || !employeeData) return null;

  const stepsProps = {
    [stepsKey.personalInformation]: {
      disabledInputFields: disabledInputFields[stepsKey.personalInformation],
    },
    [stepsKey.employementDetails]: {
      disabledInputFields: disabledInputFields[stepsKey.employementDetails],
    },
    [stepsKey.employmentHistory]: {
      isViewOnlyMode: true,
    },
    [stepsKey.familyDetails]: {
      isViewOnlyMode: true,
    },
    [stepsKey.emergencyDetails]: {
      isViewOnlyMode: true,
    },
    [stepsKey.educationDetails]: {
      isViewOnlyMode: true,
      // disableDelete: initialData[stepsKey.educationDetails].map(() => true),
    },
    [stepsKey.documentUpload]: {
      isViewOnlyMode: true,
    },
  };

  const onSubmit = (finalForm: CandidateGlobalFormType) => {
    if (isViewOnlyMode) {
      onClose();
      return;
    }
    mutation.mutate(transformUpdateCandidateDetails(finalForm, true));
  };

  return (
    <Modal
      title={`${employee.first_name} ${employee.last_name} Profile`}
      isOpen={isOpen}
      onClose={onClose}
      showBackButton
      maxWidth="unset"
      sx={ModalControllerStyles.root}
      PaperProps={{ style: ModalControllerStyles.paper }}
      isLoading={mutation.isLoading}
    >
      <Box sx={containerStyle}>
        <EmployeeStepper
          isViewOnlyMode={isViewOnlyMode}
          initialForm={initialData}
          steps={steps}
          onSubmit={onSubmit}
          stepsProps={stepsProps}
          isLoading={mutation.isLoading}
        />
      </Box>
    </Modal>
  );
};

export default EmployeeProfile;
