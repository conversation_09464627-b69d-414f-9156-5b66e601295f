import { Add, Close } from "@mui/icons-material";
import { Box, Button, IconButton } from "@mui/material";
import { useQuery } from "@tanstack/react-query";
import { format, startOfMonth } from "date-fns";
import React, { useImperativeHandle, useMemo } from "react";
import { useAppSelector } from "src/customHooks/useAppSelector";
import ContentHeader from "src/modules/Common/ContentHeader/ContentHeader";
import { useAppForm } from "src/modules/Common/Form/effiFormContext";
import NoData from "src/modules/Dashboard/component/QuickViews/components/NoDataScreens/NoData";
import { PayrollTemplateV2 } from "src/services/api_definitions/payroll.service";
import payrollService from "src/services/payroll.service";
import { z } from "zod";
import { INPUT_FIELDS } from "../config/EmploymentDetails";
import { FormDataType, StepperComponentProps } from "../types/FormDataTypes";
import CompensationItem from "./CompensationItem";
import FixedTemplateFormComponents from "./FixedTemplateFormComponents";

export const FormulaSchema = z.object({
  value: z.union([z.number(), z.string()]),
  code: z.union([z.null(), z.string()]),
  display_name: z.union([z.null(), z.string()]),
  calculation_type: z.string(),
});
export type Formula = z.infer<typeof FormulaSchema>;

export const CompensationComponentSchema = z.object({
  id: z.string(),
  assigned: z.boolean(),
  name: z.string().optional(),
  code: z.string(),
  currency: z.string(),
  formula: FormulaSchema,
  mandatory: z.boolean(),
  taxable: z.boolean(),
  system_defined: z.boolean(),
  pro_rated: z.boolean(),
  pay_type: z.string(),
  calculation_type: z.string(),
  component_type: z.string(),
  sort_order: z.null(),
  include_in_ctc: z.boolean().optional(),
  active: z.boolean(),
  employee_types: z.array(z.string()).optional(),
});
export type CompensationComponent = z.infer<typeof CompensationComponentSchema>;

export const ComponentSchema = z.object({
  compensation_component: CompensationComponentSchema,
  amount: z.number(),
});
export type Component = z.infer<typeof ComponentSchema>;

export const TEmployeePayrollSchema = z.object({
  effective_from: z.string(),
  effective_to: z.null(),
  ctc: z
    .number({
      message: "",
    })
    .optional(),
  gross: z
    .number({
      message: "",
    })
    .optional(),
  components: z.array(ComponentSchema),
  name: z.string().optional(),
});

export type TEmployeePayroll = z.infer<typeof TEmployeePayrollSchema>;

type Props = StepperComponentProps & {
  formData?: {
    current_compensation: TEmployeePayroll;
    next_compensation: TEmployeePayroll;
  };
  disbaleFields?: string[];
  onFormComplete: (form: FormDataType[], isFormSubmit?: boolean, isSaveDraft?: boolean) => void;
  disabledInputFields?: string[][];
  disableDelete?: boolean[];
  initialFormState?: {
    current_compensation: TEmployeePayroll;
    next_compensation: TEmployeePayroll;
  };
  globalFormData: any;
};

enum NextCompensationViewModes {
  CREATE = "CREATE",
}

enum NextCompensationModes {
  READ_ONLY = "READ_ONLY",
  EDITABLE = "EDITABLE",
}

const Compensation: React.FC<Props> = ({
  formActionButton,
  onFormComplete,
  formData,
  isViewOnlyMode,
  setDisableNext,
  initialFormState,
  globalFormData,
}) => {
  const { selectedOrganisationDetails } = useAppSelector((state) => state.userManagement);
  const [preview, setPreview] = React.useState<any>({
    current_compensation: null,
    next_compensation: null,
  });
  const currentCompensationExists = !!formData?.current_compensation || !!initialFormState?.current_compensation;

  // Create conditional validation based on compensation_basis
  const compensationValidators = useMemo(() => {
    const isCtcBased = selectedOrganisationDetails?.compensation_basis === "ctc";

    const baseCompensationObject = {
      name: z.string().nullish(),
      effective_from: z.string({ message: "" }),
      components: z.array(
        z.object({
          compensation_component: z.object({
            id: z.string(),
            name: z.string(),
            formula: z.object({
              value: z.number().or(z.string()),
            }),
          }),
          amount: z.number().optional(),
        }),
      ),
    };

    // Add conditional CTC or gross validation
    const compensationObjectWithAmount = isCtcBased
      ? {
          ...baseCompensationObject,
          ctc: z
            .number({
              message: "",
            })
            .min(1, {
              message: "",
            })
            .gt(0, {
              message: "",
            }),
        }
      : {
          ...baseCompensationObject,
          gross: z
            .number({
              message: "",
            })
            .min(1, {
              message: "",
            })
            .gt(0, {
              message: "",
            }),
        };

    return z.object({
      next_compensation: z.object(compensationObjectWithAmount).nullish(),
      current_compensation: z.object(compensationObjectWithAmount).optional().nullable(),
    });
  }, [selectedOrganisationDetails?.compensation_basis, currentCompensationExists]);

  const isNextComp = !!formData?.next_compensation || !!initialFormState?.next_compensation;
  const [nextCompensationViewModes, setNextCompensationViewModes] = React.useState<NextCompensationViewModes | null>(
    isNextComp ? NextCompensationViewModes.CREATE : null,
  );

  // Track whether next_compensation is in read-only mode (when pre-populated) or editable mode
  const [nextCompensationMode, setNextCompensationMode] = React.useState<NextCompensationModes>(
    !!formData?.next_compensation || !!initialFormState?.next_compensation
      ? NextCompensationModes.READ_ONLY
      : NextCompensationModes.EDITABLE,
  );

  const { data: templates } = useQuery({
    queryKey: ["get-all-employee-templates"],
    queryFn: async () =>
      await payrollService.getTemplateDetails({
        business_unit: globalFormData?.employementDetails?.[0]?.[INPUT_FIELDS.BUSINESS],
        department: globalFormData?.employementDetails?.[0]?.[INPUT_FIELDS.DEPARTMENT],
        job_title: globalFormData?.employementDetails?.[0]?.[INPUT_FIELDS.JOB_TITLE],
        employee_type: globalFormData?.employementDetails?.[0]?.employee_type,
        country: globalFormData?.employementDetails?.[0]?.["location.country"],
        work_role: globalFormData?.employementDetails?.[0]?.work_role_name,
      }),
  });

  const defaultFormState = useMemo(() => {
    return {
      current_compensation: formData?.current_compensation
        ? formData?.current_compensation
        : initialFormState?.current_compensation,
      next_compensation: formData?.next_compensation
        ? formData?.next_compensation
        : initialFormState?.next_compensation,
    };
  }, [formData]);

  const form = useAppForm({
    defaultValues: defaultFormState,
    // TODO: Re-enable validation after fixing type issues
    validators: {
      onChange: compensationValidators,
    },
  });

  const getRequestStruct = () => {
    const isCtcBased = selectedOrganisationDetails?.compensation_basis === "ctc";

    if (
      !initialFormState?.current_compensation ||
      (initialFormState?.current_compensation && !nextCompensationViewModes)
    ) {
      const components =
        form.getFieldValue("current_compensation.components") || formData?.current_compensation?.components;
      const currentCompensationEffectiveFrom =
        form.getFieldValue("current_compensation.effective_from") || formData?.current_compensation?.effective_from;
      const currentCompensationData: any = {
        ...initialFormState?.current_compensation,
        name: form.getFieldValue("current_compensation.name") || formData?.current_compensation?.name || null,
        effective_date: currentCompensationEffectiveFrom,
        effective_from: currentCompensationEffectiveFrom,
        components: components?.map((component) => ({
          ...component,
          include_in_ctc: component?.compensation_component?.include_in_ctc,
          amount:
            preview?.["current_compensation"]?.[component?.compensation_component?.name || ""] ||
            component?.amount ||
            0,
          formula_used: component?.compensation_component?.formula,
        })),
      };

      // Conditionally add CTC or gross based on compensation_basis
      if (isCtcBased) {
        currentCompensationData.ctc =
          form.getFieldValue("current_compensation.ctc") || formData?.current_compensation?.ctc;
      } else {
        currentCompensationData.gross =
          form.getFieldValue("current_compensation.gross") || formData?.current_compensation?.gross;
      }

      return {
        current_compensation: currentCompensationData,
        next_compensation: null,
      };
    }

    const components =
      form.getFieldValue("current_compensation.components") || formData?.current_compensation?.components;
    const nextComponents =
      form.getFieldValue("next_compensation.components") || formData?.next_compensation?.components;

    const currentCompensationEffectiveFrom =
      form.getFieldValue("current_compensation.effective_from") || formData?.current_compensation?.effective_from;

    const currentCompensationData: any = {
      ...initialFormState?.current_compensation,
      name: form.getFieldValue("current_compensation.name") || formData?.current_compensation?.name || null,
      effective_date: currentCompensationEffectiveFrom,
      effective_from: currentCompensationEffectiveFrom,
      components: components?.map((component) => ({
        ...component,
        include_in_ctc: component?.compensation_component?.include_in_ctc,
        amount:
          preview?.["current_compensation"]?.[component?.compensation_component?.name || ""] || component?.amount || 0,
        formula_used: component?.compensation_component?.formula,
      })),
    };
    const nextCompensationEffectiveFrom =
      form.getFieldValue("next_compensation.effective_from") || formData?.next_compensation?.effective_from;
    //We are only taking month and year from the effective_from date, so sending 1st day of the month to the backend
    const nextCompensationEffectiveDate = format(startOfMonth(nextCompensationEffectiveFrom as string), "yyyy-MM-dd");

    const nextCompensationData: any = {
      ...initialFormState?.next_compensation,
      name: form.getFieldValue("next_compensation.name") || formData?.next_compensation?.name || null,
      effective_date: nextCompensationEffectiveDate,
      effective_from: nextCompensationEffectiveDate,
      components: nextComponents?.map((component) => ({
        ...component,
        include_in_ctc: component?.compensation_component?.include_in_ctc,
        amount:
          preview?.["next_compensation"]?.[component?.compensation_component?.name || ""] || component?.amount || 0,
        formula_used: component?.compensation_component?.formula,
      })),
    };

    // Conditionally add CTC or gross based on compensation_basis
    if (isCtcBased) {
      currentCompensationData.ctc =
        form.getFieldValue("current_compensation.ctc") || formData?.current_compensation?.ctc;
      nextCompensationData.ctc = form.getFieldValue("next_compensation.ctc") || formData?.next_compensation?.ctc;
      nextCompensationData.amount =
        form.getFieldValue("next_compensation.ctc") || formData?.next_compensation?.ctc || null;
    } else {
      currentCompensationData.gross =
        form.getFieldValue("current_compensation.gross") || formData?.current_compensation?.gross;
      nextCompensationData.gross = form.getFieldValue("next_compensation.gross") || formData?.next_compensation?.gross;
      nextCompensationData.amount =
        form.getFieldValue("next_compensation.gross") || formData?.next_compensation?.gross || null;
    }

    return {
      current_compensation: currentCompensationData,
      next_compensation: nextCompensationData,
    };
  };

  useImperativeHandle(formActionButton, () => ({
    next: (isFormSubmit, isSaveDraft) => {
      onFormComplete(getRequestStruct() as unknown as FormDataType[], isFormSubmit, isSaveDraft);
    },
  }));

  const handleRemoveNextCompensation = () => {
    form.setFieldValue("next_compensation", undefined);
    form.resetField("next_compensation");
    form.setErrorMap({
      onChange: {
        fields: {
          current_compensation: {
            name: null,
            effective_from: null,
            components: null,
            ctc: null,
            gross: null,
          },
          next_compensation: {
            name: null,
            effective_from: null,
            components: null,
            ctc: null,
            gross: null,
          },
        },
      },
    });
    setNextCompensationMode(NextCompensationModes.EDITABLE);
    setNextCompensationViewModes(null);
  };

  if (!initialFormState?.current_compensation && !initialFormState?.next_compensation && isViewOnlyMode) {
    return <NoData title="No compensation details found" />;
  }

  return (
    <Box display="flex" flexDirection="column" gap={2} sx={{ margin: "8px 0px" }}>
      <Box display="flex" flexDirection="column" gap={2}>
        <ContentHeader title="Current Compensation Details" />
        {!initialFormState?.current_compensation && (
          <FixedTemplateFormComponents form={form} isCurrentCompensation templates={templates as PayrollTemplateV2[]} />
        )}
        <CompensationItem
          form={form}
          isCurrentCompensation
          templates={templates as PayrollTemplateV2[]}
          isReadOnly={isViewOnlyMode || !!initialFormState?.current_compensation}
          preview={preview["current_compensation"]}
          setPreview={setPreview}
        />
      </Box>
      {(!isViewOnlyMode || !!formData?.next_compensation) && (
        <Box visibility={nextCompensationViewModes !== NextCompensationViewModes.CREATE ? "visible" : "hidden"}>
          <Button
            variant="outlined"
            onClick={() => {
              setNextCompensationViewModes(NextCompensationViewModes.CREATE);
              setNextCompensationMode(NextCompensationModes.EDITABLE);
            }}
            endIcon={<Add />}
          >
            Next Compensation
          </Button>
        </Box>
      )}
      {nextCompensationViewModes === NextCompensationViewModes.CREATE && (
        <Box display="flex" flexDirection="column" gap={2}>
          <ContentHeader
            title="Next Compensation Details"
            actions={
              isViewOnlyMode ? null : (
                <IconButton onClick={handleRemoveNextCompensation}>
                  <Close />
                </IconButton>
              )
            }
          />
          {(nextCompensationMode === NextCompensationModes.EDITABLE || !form.getFieldValue("next_compensation")) && (
            <FixedTemplateFormComponents
              form={form}
              isCurrentCompensation={false}
              templates={templates as PayrollTemplateV2[]}
              isReadOnlyMode={nextCompensationMode === NextCompensationModes.READ_ONLY}
            />
          )}
          {!isViewOnlyMode &&
            nextCompensationMode === NextCompensationModes.READ_ONLY &&
            form.getFieldValue("next_compensation") && (
              <Box display="flex" flexDirection="column" gap={1}>
                {/* Show effective date field as editable in read-only mode */}
                <form.AppField name="next_compensation.effective_from">
                  {(field: any) => (
                    <field.EffiDate
                      label="Effective Date"
                      minDate={
                        form.getFieldValue("current_compensation.effective_from")
                          ? new Date(form.getFieldValue("current_compensation.effective_from") as string)
                          : undefined
                      }
                      views={["year", "month"]}
                    />
                  )}
                </form.AppField>
              </Box>
            )}
          <CompensationItem
            isReadOnly={
              isViewOnlyMode ||
              (nextCompensationMode === NextCompensationModes.READ_ONLY && !!form.getFieldValue("next_compensation"))
            }
            form={form}
            isCurrentCompensation={false}
            templates={templates as PayrollTemplateV2[]}
            preview={preview["next_compensation"]}
            setPreview={setPreview}
          />
        </Box>
      )}
      <form.Subscribe selector={(state) => [state.values, state.errors]}>
        {([formValues, errors]) => {
          const isDisabled =
            errors?.length > 0 || (!currentCompensationExists ? !formValues?.current_compensation?.name : false);
          const isNextCompensationAggregatePresent =
            !!formValues?.next_compensation?.ctc || !!formValues?.next_compensation?.gross;
          const isNextCompDisabled =
            nextCompensationViewModes === NextCompensationViewModes.CREATE
              ? !formValues?.next_compensation?.effective_from || !isNextCompensationAggregatePresent
              : false;
          setDisableNext?.(isDisabled || isNextCompDisabled);
          return null;
        }}
      </form.Subscribe>
    </Box>
  );
};

export default Compensation;
