import { FileDownload } from "@mui/icons-material";
import { Box, Button, DialogActions, Grid, SelectChangeEvent } from "@mui/material";
import { useMutation, useQueries } from "@tanstack/react-query";
import React, { useCallback, useMemo } from "react";
import { useForm } from "src/customHooks/useForm";
import ButtonWithLoading from "src/modules/Common/ButtonWithLoading/ButtonWithLoading";
import CustomSelect from "src/modules/Common/FormInputs/CustomSelect";
import Modal from "src/modules/Common/Modal/Modal";
import businessunitsService from "src/services/businessunits.service";
import departmentService from "src/services/department.service";
import employeesService from "src/services/employees.service";
import masterdataService from "src/services/masterdata.service";
import payrollService from "src/services/payroll.service";

type ExportEmployeesModalProps = {
  open: boolean;
  onClose: () => void;
  isPayrollView?: boolean;
};

const defaultFormState = {
  employment_status: "",
  employee_type: "",
  business_unit: "",
  department: "",
  job_title: "",
};

export const ExportEmployeesModal: React.FC<ExportEmployeesModalProps> = ({ onClose, open, isPayrollView }) => {
  const [employmentStatus, employeeTypes, businessUnits, departments, jobTitles] = useQueries({
    queries: [
      {
        queryKey: ["employment-status"],
        queryFn: async () => masterdataService.getACLs("EmploymentStatus"),
        refetchOnMount: true,
        refetchOnWindowFocus: false,
      },
      {
        queryKey: ["employee-type"],
        queryFn: async () => masterdataService.getACLs("EmployeeType"),
        refetchOnMount: true,
        refetchOnWindowFocus: false,
      },
      {
        queryKey: ["biz-unit"],
        queryFn: async () => businessunitsService.getBusinessUnitDetails(),
        refetchOnMount: true,
        refetchOnWindowFocus: false,
      },
      {
        queryKey: ["biz-department"],
        queryFn: async () => departmentService.getAllDepartments(),
        refetchOnMount: true,
        refetchOnWindowFocus: false,
      },
      {
        queryKey: ["biz-job-title"],
        queryFn: async () => departmentService.getAllJobTitles(),
        refetchOnMount: true,
        refetchOnWindowFocus: false,
      },
    ],
  });

  const { formDetails, handleSelectChange, setFormDetail } = useForm({
    initialState: defaultFormState,
    validations: {
      business_unit: [],
      department: [],
      employee_type: [],
      employment_status: [],
      job_title: [],
    },
    isBulk: false,
  });

  const typedFormDetails = formDetails as typeof defaultFormState;

  const exportEmployeesMutation = useMutation({
    mutationKey: ["export-employees"],
    mutationFn: async () =>
      isPayrollView
        ? payrollService.exportPayroll(typedFormDetails)
        : employeesService.exportEmployees(typedFormDetails),
    onSuccess: () => {
      onClose();
    },
  });

  const onChange = (ev: SelectChangeEvent<string | number | boolean>, key: keyof typeof defaultFormState) => {
    if (key === "business_unit") {
      setFormDetail("department", "");
      setFormDetail("job_title", "");
    }

    if (key === "department") {
      setFormDetail("job_title", "");
    }

    setFormDetail(key, ev.target.value);
  };

  const businessUnitDepartments = useMemo(() => {
    return departments?.data?.filter((data) => data.business_unit === typedFormDetails?.business_unit) || [];
  }, [typedFormDetails?.business_unit]);

  const jobTitleOptions = useMemo(() => {
    const filteredJobTitles =
      jobTitles?.data
        ?.filter(
          (data) =>
            data.business_unit === typedFormDetails?.business_unit && data.department === typedFormDetails?.department,
        )
        .map((eachJobTitle) => eachJobTitle.name) || [];
    return [...new Set(filteredJobTitles)];
  }, [typedFormDetails?.business_unit, typedFormDetails?.department]);

  const onExport = useCallback(() => {
    exportEmployeesMutation.mutate();
  }, []);

  return (
    <Modal
      title="Choose filters to export"
      subtitle="Select filters to export employees or leave it blank to export all employees"
      isOpen={open}
      onClose={onClose}
      showBackButton
      actions={
        <DialogActions>
          <Box
            sx={{
              display: "flex",
              gap: 2,
              alignItems: "center",
            }}
          >
            <Button onClick={onClose} variant="outlined">
              Cancel
            </Button>
            <ButtonWithLoading
              onClick={onExport}
              variant="contained"
              endIcon={<FileDownload />}
              isLoading={exportEmployeesMutation.isLoading}
            >
              Export
            </ButtonWithLoading>
          </Box>
        </DialogActions>
      }
    >
      <Grid container spacing={3} zeroMinWidth>
        <Grid item sm={6}>
          <CustomSelect
            label="Employment Status"
            id="employment_status"
            name="employment_status"
            size="small"
            fullWidth
            options={
              employmentStatus?.data?.map((data) => ({
                label: data,
                value: data,
              })) || []
            }
            onChange={(ev) => handleSelectChange(ev as any, "employment_status")}
            value={typedFormDetails?.employment_status}
          />
        </Grid>
        <Grid item sm={6}>
          <CustomSelect
            label="Employee Types"
            id="employee_type"
            name="employee_type"
            size="small"
            fullWidth
            options={
              employeeTypes?.data?.map((data) => ({
                label: data,
                value: data,
              })) || []
            }
            onChange={(ev) => handleSelectChange(ev as any, "employee_type")}
            value={typedFormDetails?.employee_type}
          />
        </Grid>
        <Grid item sm={6}>
          <CustomSelect
            label="Business Unit"
            id="business_unit"
            size="small"
            name="business_unit"
            fullWidth
            options={
              businessUnits?.data?.map((data) => ({
                label: data.name,
                value: data.name,
              })) || []
            }
            onChange={(ev) => onChange(ev, "business_unit")}
            value={typedFormDetails?.business_unit}
          />
        </Grid>
        <Grid item sm={6}>
          <CustomSelect
            label="Department"
            id="department"
            name="department"
            size="small"
            fullWidth
            disabled={businessUnitDepartments?.length === 0}
            options={
              businessUnitDepartments?.map((data) => ({
                label: data.name,
                value: data.name,
              })) || []
            }
            onChange={(ev) => onChange(ev, "department")}
            value={typedFormDetails?.department}
          />
        </Grid>
        <Grid item sm={6}>
          <CustomSelect
            label="Job titles"
            id="job_title"
            size="small"
            name="job_title"
            fullWidth
            disabled={jobTitleOptions?.length === 0}
            options={
              jobTitleOptions?.map((data) => ({
                label: data,
                value: data,
              })) || []
            }
            onChange={(ev) => onChange(ev, "job_title")}
            value={typedFormDetails?.job_title}
          />
        </Grid>
      </Grid>
    </Modal>
  );
};

export default ExportEmployeesModal;
