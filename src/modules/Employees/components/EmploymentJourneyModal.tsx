import { Container } from "@mui/material";
import React from "react";
import Modal from "src/modules/Common/Modal/Modal";
import EmployeeJourney from "src/modules/Profile/components/EmployeeJourney";
import { TransformedEmployee } from "src/services/api_definitions/employees";

interface EmploymentJourneyModalProps {
  isOpen: boolean;
  onClose: (value: boolean) => void;
  employee: TransformedEmployee;
}

const EmploymentJourneyModal: React.FC<EmploymentJourneyModalProps> = ({ isOpen, onClose, employee }) => {
  return (
    <Modal
      title={`${employee.employee_name}'s Journey`}
      showBackButton
      isOpen={isOpen}
      onClose={() => onClose(false)}
      maxWidth="unset"
    >
      <Container maxWidth="md">
        <EmployeeJourney isAdminFlow employeeCode={employee.employee_code} />
      </Container>
    </Modal>
  );
};

export default EmploymentJourneyModal;
