import { Box, Checkbox, Grid, debounce } from "@mui/material";
import { useMutation } from "@tanstack/react-query";
import React, { useCallback, useState } from "react";
import locationService, { ZIPCODE_APIResponse } from "src/services/location.service";

import ContentHeader from "src/modules/Common/ContentHeader/ContentHeader";
import { INPUT_FIELDS, addressForm, permanentAdressForm } from "../config/PersonalInformation";
import { CommonForm } from "./CommonForm";

type AddressFormUnitProps = {
  onChange: (name: string, value: unknown) => void;
  formErrors: Record<string, string>;
  formDetails: Record<string, unknown>;
  isViewOnlyMode?: boolean;
  disabledInputFields?: Record<string, boolean>;
};

const addressDisabledInputFields = {
  [INPUT_FIELDS.STATE]: true,
  [INPUT_FIELDS.COUNTRY]: true,
};

const permanentAddressDisabledInputFields = {
  [INPUT_FIELDS.PERMANENT_STATE]: true,
  [INPUT_FIELDS.PERMANENT_COUNTRY]: true,
};

const AddressFormUnit = ({
  onChange,
  formErrors,
  formDetails,
  isViewOnlyMode,
  disabledInputFields,
}: AddressFormUnitProps) => {
  const [sameAsCurrentAddress, setSameAsCurrentAddress] = useState(false);

  const getDetailsfromZipcode = useMutation({
    mutationKey: ["get-address-info"],
    mutationFn: (zipcode: string): Promise<ZIPCODE_APIResponse | null> =>
      locationService.getAddressDetailsFromZipcode(zipcode),
  });

  const setAddressFields = (data: ZIPCODE_APIResponse | null) => {
    const { city = "", state = "", country = "" } = data || {};
    onChange(INPUT_FIELDS.CITY, city);
    onChange(INPUT_FIELDS.STATE, state);
    onChange(INPUT_FIELDS.COUNTRY, country);
  };

  const setPermanentAddressFields = (data?: ZIPCODE_APIResponse | null) => {
    const { city = "", state = "", country = "" } = data || {};
    onChange(INPUT_FIELDS.PERMANENT_CITY, city);
    onChange(INPUT_FIELDS.PERMANENT_STATE, state);
    onChange(INPUT_FIELDS.PERMANENT_COUNTRY, country);
  };

  const debouncedZipcodeChange = debounce((value: string) => {
    getDetailsfromZipcode
      .mutateAsync(value)
      .then((response) => setAddressFields(response))
      .catch(() => setAddressFields(null));
  }, 200);

  const debouncedPermanentZipcodeChange = debounce((value: string) => {
    getDetailsfromZipcode
      .mutateAsync(value)
      .then((response) => setPermanentAddressFields(response))
      .catch(() => setPermanentAddressFields(null));
  }, 200);

  const fieldChangeCallbacks = {
    [INPUT_FIELDS.ZIPCODE]: (value: string) => {
      if (value.length === 6) debouncedZipcodeChange(value);
      else setAddressFields(null);
    },
    [INPUT_FIELDS.PERMANENT_ZIPCODE]: (value: string) => {
      if (value.length === 6) debouncedPermanentZipcodeChange(value);
      else setPermanentAddressFields(null);
    },
  };

  const onFieldChange = (name: string, value: unknown) => {
    onChange(name, value);
    if (fieldChangeCallbacks[name]) {
      fieldChangeCallbacks[name](value as string);
    }
  };

  const onCurrentAddressChange = (name: string, value: unknown) => {
    setSameAsCurrentAddress(false);
    onFieldChange(name, value);
  };

  const copyPermanentAddressFromCurrentAdress = useCallback(
    (checked: boolean) => {
      if (isViewOnlyMode) return;
      setSameAsCurrentAddress(checked);
      if (checked) {
        permanentAdressForm.forEach((element, index) => {
          onChange(element.name, formDetails[addressForm[index].name]);
        });
      }
    },
    [formDetails, addressForm, permanentAdressForm],
  );

  const addressDisabledInputFieldsObject = {
    ...addressDisabledInputFields,
    ...disabledInputFields,
  };
  const permanentAddressDisabledInputFieldsObject = {
    ...permanentAddressDisabledInputFields,
    ...disabledInputFields,
  };
  const isAllFieldDisabled = permanentAdressForm.every(({ name }) => permanentAddressDisabledInputFieldsObject[name]);

  return (
    <Box sx={{ display: "flex", flexDirection: "column", gap: 2 }}>
      <Box sx={{ fontSize: 16 }}>Current Address</Box>
      <CommonForm
        onChange={onCurrentAddressChange}
        inputElements={addressForm}
        isViewOnlyMode={isViewOnlyMode}
        formErrors={formErrors as Record<string, string>}
        formValues={formDetails as Record<string, unknown>}
        disabledInputFields={addressDisabledInputFieldsObject}
      />
      <Grid item xs={12} sx={{ width: "100%" }}>
        <ContentHeader
          subtitle={
            <Box sx={{ gap: 8, display: "flex", flexDirection: "row", alignItems: "center" }}>
              <Box sx={{ fontSize: 16 }}>Permanent Address</Box>
              <Box sx={{ display: "flex", alignItems: "center" }}>
                <Checkbox
                  disabled={isViewOnlyMode || isAllFieldDisabled}
                  color="success"
                  checked={sameAsCurrentAddress}
                  onChange={(event) => copyPermanentAddressFromCurrentAdress(event.target.checked)}
                />
                Same as current address
              </Box>
            </Box>
          }
        />
      </Grid>
      <CommonForm
        onChange={onFieldChange}
        isViewOnlyMode={isViewOnlyMode}
        inputElements={permanentAdressForm}
        formErrors={formErrors as Record<string, string>}
        formValues={formDetails as Record<string, unknown>}
        disabledInputFields={permanentAddressDisabledInputFieldsObject}
      />
    </Box>
  );
};

export default AddressFormUnit;
