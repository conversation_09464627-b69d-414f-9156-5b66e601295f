import { Add } from "@mui/icons-material";
import { Button } from "@mui/material";
import React, { useEffect, useImperativeHandle, useMemo } from "react";
import { useForm } from "src/customHooks/useForm";
import {
  FamilyDetailsformValidators,
  FamilyDetailsInitialValues,
  FamilyDetailsNewRowValues,
  form,
  INPUT_FIELDS,
} from "../config/FamilyDetails";
import LoadingScreen from "../LoadingScreen";
import { FormDataType, StepperComponentProps } from "../types/FormDataTypes";
import { convertListToOptions, getEnumValues } from "../utils/utils";
import { CommonFormWithAddMore } from "./CommonForm";

type Props = StepperComponentProps & {
  formData?: FormDataType[];
  onFormComplete: (form: FormDataType[], isFormSubmit?: boolean, isSaveDraft?: boolean) => void;
};

const isArrayEmpty = (arr: FormDataType[] = []) => {
  if (arr.length === 0) {
    return true;
  }
  if (arr?.length === 1) {
    return arr[0]?.["dependents.first_name"] === "" && arr[0]?.["dependents.last_name"] === "";
  }
  return false;
};

const addMotherAndFather = (initialState: FormDataType[] = []) => {
  const motherDetails =
    initialState.find((item) => item["dependents.relation"] === "Mother") || FamilyDetailsInitialValues[1];
  const fatherDetails =
    initialState.find((item) => item["dependents.relation"] === "Father") || FamilyDetailsInitialValues[0];
  const filteredInitialState = initialState.filter(
    (item) => item["dependents.relation"] !== "Mother" && item["dependents.relation"] !== "Father",
  );
  const newState = [motherDetails, fatherDetails, ...filteredInitialState];
  return newState;
};
const FamilyDetails = ({ formData, onFormComplete, setDisableNext, formActionButton, isViewOnlyMode }: Props) => {
  const initialState = useMemo(() => (isArrayEmpty(formData) ? [] : addMotherAndFather(formData)), [formData]);

  const { formDetails, formErrors, setFormDetail, addNewFormDetailRow, deleteFormDetails, areFormDetailsValid } =
    useForm({
      isBulk: true,
      initialState: initialState || [],
      validations: FamilyDetailsformValidators,
    });

  useEffect(() => {
    setDisableNext?.(!areFormDetailsValid);
  }, [areFormDetailsValid]);

  useImperativeHandle(formActionButton, () => ({
    next: (isFormSubmit, isSaveDraft) => {
      onFormComplete(formDetails as FormDataType[], isFormSubmit, isSaveDraft);
    },
  }));

  const { data: relationList = [], isLoading: relationLoading } = getEnumValues("Relationship");

  const onChange = (index: number, fieldName: string, value: unknown) => {
    setFormDetail(fieldName, value, index);
  };

  if (relationLoading) return <LoadingScreen />;

  const selectOptions = {
    [INPUT_FIELDS.RELATION]: convertListToOptions(relationList)?.filter(
      (item) => item.value !== "Father" && item.value !== "Mother",
    ),
  };

  const handleAddInitialForm = () => {
    addNewFormDetailRow(FamilyDetailsNewRowValues);
  };

  const shouldShowAddButton = formDetails?.length === 0 && !isViewOnlyMode;

  return (
    <>
      {shouldShowAddButton && (
        <Button
          variant="text"
          sx={{
            margin: "8px 0",
            padding: "12px 24px",
            fontWeight: 500,
            fontSize: "16px",
            textTransform: "none",
            fontFamily: "Poppins",
          }}
          onClick={handleAddInitialForm}
          startIcon={<Add fontSize="small" />}
        >
          Add Family Details
        </Button>
      )}
      <CommonFormWithAddMore
        onChange={onChange}
        inputElements={form}
        selectOptions={selectOptions}
        isViewOnlyMode={isViewOnlyMode}
        formErrors={formErrors as Record<string, string>[]}
        formValues={formDetails as Record<string, unknown>[]}
        onAddMoreClick={() => addNewFormDetailRow(FamilyDetailsNewRowValues)}
        onDeleteClick={deleteFormDetails}
        isOptional
        disabledInputFields={[
          {
            [INPUT_FIELDS.RELATION]: true,
          },
          {
            [INPUT_FIELDS.RELATION]: true,
          },
        ]}
        disableDelete={[true, true]}
      />
    </>
  );
};

export default FamilyDetails;
