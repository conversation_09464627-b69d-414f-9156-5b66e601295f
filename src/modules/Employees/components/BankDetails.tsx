import { useMutation } from "@tanstack/react-query";
import React, { useEffect, useImperativeHandle } from "react";

import { useForm } from "src/customHooks/useForm";
import bankService, { IFSC_APIResponse } from "src/services/bank.service";

import { debounce } from "@mui/material";
import { BankDetailsInitialValues, BankDetailsformValidators, INPUT_FIELDS, form } from "../config/BankDetails";
import { FormDataType, StepperComponentProps } from "../types/FormDataTypes";
import { uploadEmployeeDocument } from "../utils/utils";
import { CommonForm } from "./CommonForm";

type Props = StepperComponentProps & {
  formData?: FormDataType[];
  disbaleFields?: string[];
  onFormComplete: (form: FormDataType, isFormSubmit?: boolean, isSaveDraft?: boolean) => void;
};

const disabledInputFields = {
  [INPUT_FIELDS.BANK_NAME]: true,
  [INPUT_FIELDS.BRANCH]: true,
  [INPUT_FIELDS.ADDRESS]: true,
};

const BankDetails = ({ formData = [], onFormComplete, formActionButton, setDisableNext, isViewOnlyMode }: Props) => {
  const { formDetails, formErrors, setFormDetail, areFormDetailsValid } = useForm({
    initialState: formData[0] || BankDetailsInitialValues[0],
    validations: BankDetailsformValidators,
  });

  useEffect(() => {
    setDisableNext?.(!areFormDetailsValid);
  }, [areFormDetailsValid]);

  useImperativeHandle(formActionButton, () => ({
    next: (isFormSubmit, isSaveDraft) => {
      onFormComplete(formDetails as FormDataType, isFormSubmit, isSaveDraft);
    },
  }));

  const getDetailsfromIFSC = useMutation({
    mutationKey: ["get-bank-details"],
    mutationFn: (IFSCCode: string) => bankService.getBankDetails(IFSCCode),
  });

  const setBankDetails = (data?: IFSC_APIResponse | null) => {
    const { bank = "", branch = "", address = "" } = data || {};
    setFormDetail(INPUT_FIELDS.BANK_NAME, bank);
    setFormDetail(INPUT_FIELDS.BRANCH, branch);
    setFormDetail(INPUT_FIELDS.ADDRESS, address);
  };

  const debouncedIFSCChange = debounce(
    (value: string) =>
      getDetailsfromIFSC
        .mutateAsync(value)
        .then((response) => setBankDetails(response))
        .catch(() => setBankDetails(null)),
    200,
  );

  const fieldChangeCallbacks = {
    [INPUT_FIELDS.IFSC]: (value: string) => {
      if (value.length === 11) {
        debouncedIFSCChange(value);
      }
    },
  };

  useEffect(() => {
    const ifsc = formData?.[0]?.[INPUT_FIELDS.IFSC] as string;
    if (ifsc) {
      fieldChangeCallbacks[INPUT_FIELDS.IFSC](ifsc as string);
    }
  }, [formData]);

  const onFileChange = async (acceptedFiles: File[], fieldName: string) => {
    const type = "Canceled Cheque";
    const document = await uploadEmployeeDocument(acceptedFiles, type);
    if (document) {
      setFormDetail(fieldName, document);
    }
  };

  const onChange = (fieldName: string, value: unknown) => {
    switch (fieldName) {
      case INPUT_FIELDS.BANK_DOCUMENT: {
        if (value) onFileChange(value as File[], fieldName);
        else setFormDetail(fieldName, null);
        break;
      }
      default: {
        setFormDetail(fieldName, value);
        if (fieldChangeCallbacks[fieldName]) {
          fieldChangeCallbacks[fieldName](value as string);
        }
      }
    }
  };

  return (
    <CommonForm
      onChange={onChange}
      inputElements={form}
      isViewOnlyMode={isViewOnlyMode}
      formErrors={formErrors as Record<string, string>}
      formValues={formDetails as Record<string, unknown>}
      disabledInputFields={disabledInputFields}
    />
  );
};

export default BankDetails;
