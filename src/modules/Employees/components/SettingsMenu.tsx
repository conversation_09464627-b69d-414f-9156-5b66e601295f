import { Collapse, Menu, MenuItem } from "@mui/material";
import React from "react";
import { Actions } from "src/app/constants";
import { TransformedEmployee } from "src/services/api_definitions/employees";
import { SettingsMenuStyles } from "./styles/styles.module";
interface SettingsMenuProps {
  anchorEl: null | HTMLElement;
  handleClose: () => void;
  onOptionClick?: (option: string) => void;
  employee?: TransformedEmployee | null;
}

const selectableActions = (options: string[]) => {
  return options?.map((option) => ({ value: option, label: Actions[option as keyof typeof Actions] }));
};

const SettingsMenu: React.FC<SettingsMenuProps> = ({ anchorEl, handleClose, onOptionClick, employee }) => {
  return (
    <Menu
      elevation={2}
      id="long-menu"
      MenuListProps={{
        "aria-labelledby": "long-button",
        ...SettingsMenuStyles,
      }}
      anchorEl={anchorEl}
      open={Boolean(anchorEl)}
      onClose={handleClose}
      anchorOrigin={{
        vertical: "bottom",
        horizontal: "right",
      }}
      transformOrigin={{
        vertical: "top",
        horizontal: "right",
      }}
    >
      <Collapse in={Boolean(anchorEl)} unmountOnExit>
        {selectableActions(employee?.actionMenu as any as string[])?.map((option) => (
          <MenuItem
            key={option.value}
            onClick={() => {
              onOptionClick?.(option.label);
              handleClose();
            }}
            sx={SettingsMenuStyles.menuitem}
          >
            {option.label}
          </MenuItem>
        ))}
      </Collapse>
    </Menu>
  );
};

export default SettingsMenu;
