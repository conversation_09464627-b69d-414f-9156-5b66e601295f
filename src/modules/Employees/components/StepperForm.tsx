import { ArrowBackIos } from "@mui/icons-material";
import { Box, Button, styled } from "@mui/material";
import Step from "@mui/material/Step";
import StepConnector, { stepConnectorClasses } from "@mui/material/StepConnector";
import StepLabel from "@mui/material/StepLabel";
import Stepper from "@mui/material/Stepper";
import React, { useRef, useState } from "react";
import ButtonWithLoading from "src/modules/Common/ButtonWithLoading/ButtonWithLoading";
import { CustomInputElement, StepperComponentProps } from "../types/FormDataTypes";

const containerStyles = {
  display: "flex",
  flexDirection: "column",
  gap: 4,
  width: "100%",
  height: "100%",
  overflow: "hidden",
  alignItems: "center",
};

export const backButtonStyle = {
  fontFamily: "Poppins",
  fontSize: "16px",
  fontWeight: 500,
  textTransform: "none",
  margin: 0,
  width: "172px",
  height: "48px",
  flexGrow: 0,
  display: "flex",
  flexDirection: "row",
  justifyContent: "center",
  alignItems: "center",
  padding: "0 20px",
  borderRadius: "50px",
  backgroundColor: "#eff4f8",
  border: "#eff4f8",
  marginRight: "auto",
};

const skipButtonStyle = {
  ...backButtonStyle,
  marginRight: 0,
};

export const nextButtonStyle = {
  fontFamily: "Poppins",
  fontSize: "16px",
  fontWeight: 500,
  textTransform: "none",
  margin: 0,
  width: "172px",
  height: "48px",
  flexGrow: 0,
  display: "flex",
  flexDirection: "row",
  justifyContent: "center",
  alignItems: "center",
  gap: "8px",
  padding: "0 20px",
  borderRadius: "50px",
  backgroundColor: "#007f6f",
  boxShadow: "none",
};

export const activeIconStyles = {
  backgroundColor: "white",
  borderRadius: "50%",
  display: "flex",
  justifyContent: "center",
  alignItems: "center",
  border: "#57ba57 3px solid",
  width: 55,
  height: 55,
};

export const disableIconStyles = {
  backgroundColor: "#d2d2d2",
  width: 55,
  height: 55,
  borderRadius: "50%",
  display: "flex",
  justifyContent: "center",
  alignItems: "center",
};

export const completedIconStyle = {
  ...activeIconStyles,
  backgroundColor: "#57ba57",
};

export const CustomConnector = styled(StepConnector)(() => ({
  [`&.${stepConnectorClasses.alternativeLabel}`]: {
    top: 26,
    left: "calc(-50% + 27.5px)",
    right: "calc(50% + 27.5px)",
  },
  [`&.${stepConnectorClasses.active}`]: {
    [`& .${stepConnectorClasses.line}`]: {
      borderColor: "#57ba57",
    },
  },
  [`&.${stepConnectorClasses.completed}`]: {
    [`& .${stepConnectorClasses.line}`]: {
      borderColor: "#57ba57",
    },
  },
  [`& .${stepConnectorClasses.line}`]: {
    borderColor: "#eaeaf0",
    borderTopWidth: 3,
    borderRadius: 1,
  },
}));

export type Steps = {
  key: string;
  heading: string;
  component: (props: StepperComponentProps) => React.ReactNode;
  icon: (props: { fill: string; stroke: string }) => React.JSX.Element;
  isSkip?: boolean;
  isViewOnlyMode?: boolean;
}[];

type Props = {
  steps: Steps;
  formCompleteCTAText?: string;
  isViewOnlyMode?: boolean;
  isSaveDraft?: boolean;
  isLoading?: boolean;
};

const StepperForm = ({ steps, formCompleteCTAText = "Submit", isViewOnlyMode, isSaveDraft, isLoading }: Props) => {
  const [activeStep, setActiveStep] = useState(0);
  const [disableNext, setDisableNext] = useState(false);
  const formActionButton = useRef<CustomInputElement>(null);

  const handleNext = () => {
    formActionButton.current?.next?.();
    setActiveStep((prevActiveStep) => (prevActiveStep >= steps.length - 1 ? steps.length - 1 : prevActiveStep + 1));
  };

  const handleBack = () => {
    formActionButton.current?.next?.();
    setActiveStep((prevActiveStep) => (prevActiveStep <= 0 ? 0 : prevActiveStep - 1));
  };

  const onFormFinish = () => {
    formActionButton.current?.next?.(true);
  };

  const handleSaveDraft = () => {
    formActionButton.current?.next?.(false, true);
  };

  return (
    <Box sx={containerStyles}>
      {steps.length > 1 && (
        <Box sx={{ display: "flex", width: "100%", justifyContent: "center" }}>
          <Stepper activeStep={activeStep} alternativeLabel connector={<CustomConnector />}>
            {steps.map(({ heading, icon: Icon }, index) => {
              return (
                <Step key={heading} sx={{ padding: "0" }}>
                  <StepLabel
                    sx={{ width: "130px", padding: "0 2px" }}
                    icon={
                      <Box
                        sx={
                          index < activeStep
                            ? completedIconStyle
                            : index === activeStep
                              ? activeIconStyles
                              : disableIconStyles
                        }
                      >
                        <Icon
                          fill={index === activeStep ? "#57ba57" : "white"}
                          stroke={index === activeStep ? "#57ba57" : "white"}
                        />
                      </Box>
                    }
                  >
                    {heading}
                  </StepLabel>
                </Step>
              );
            })}
          </Stepper>
        </Box>
      )}
      <Box sx={{ width: "100%", overflowY: "auto", flex: 1, padding: "0 20px" }}>
        {steps[activeStep].component({
          formActionButton,
          setDisableNext,
          isViewOnlyMode: steps[activeStep].isViewOnlyMode || isViewOnlyMode,
        })}
      </Box>
      <Box sx={{ display: "flex", justifyContent: "flex-end", gap: 2, width: "100%", padding: "0 20px" }}>
        {activeStep > 0 && (
          <Button
            startIcon={<ArrowBackIos />}
            variant="text"
            sx={backButtonStyle}
            onClick={handleBack}
            disabled={isLoading}
          >
            Back
          </Button>
        )}
        {steps[activeStep].isSkip && (
          <Button variant="text" sx={skipButtonStyle} onClick={handleNext}>
            Skip
          </Button>
        )}
        {isSaveDraft && !isViewOnlyMode && !steps[activeStep].isViewOnlyMode && (
          <Button variant="contained" sx={nextButtonStyle} onClick={handleSaveDraft} disabled={disableNext}>
            Save Draft
          </Button>
        )}
        {activeStep < steps.length - 1 && (
          <Button
            variant="contained"
            disabled={!isViewOnlyMode && !steps[activeStep].isViewOnlyMode && disableNext}
            sx={nextButtonStyle}
            onClick={handleNext}
          >
            Next
          </Button>
        )}
        {!isViewOnlyMode && activeStep === steps.length - 1 && (
          <ButtonWithLoading
            isLoading={isLoading}
            variant="contained"
            disabled={disableNext}
            sx={nextButtonStyle}
            onClick={onFormFinish}
          >
            {formCompleteCTAText}
          </ButtonWithLoading>
        )}
      </Box>
    </Box>
  );
};

export default StepperForm;
