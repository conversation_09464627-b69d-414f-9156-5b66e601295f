import { Box, Button, DialogActions } from "@mui/material";
import { useMutation } from "@tanstack/react-query";
import { addDays, format } from "date-fns";
import React, { useCallback, useMemo } from "react";
import { useForm } from "src/customHooks/useForm";
import CustomDateField from "src/modules/Common/FormInputs/CustomDateField";
import Modal from "src/modules/Common/Modal/Modal";
import { TransformedEmployee } from "src/services/api_definitions/employees";
import employeesService from "src/services/employees.service";
import validators from "src/utils/validators";

interface Props {
  employee: TransformedEmployee;
  isOpen: boolean;
  onClose: (refetch: boolean) => void;
}
const ChangeDateOfConfirmation: React.FC<Props> = ({ employee, isOpen, onClose }) => {
  const defaultFormState = useMemo(() => {
    return {
      dateOfConfirmation: employee?.date_of_confirmation || "",
    };
  }, [employee]);

  const { formDetails, formErrors, setFormDetail, areFormDetailsValid } = useForm({
    initialState: defaultFormState,
    isBulk: false,
    validations: {
      dateOfConfirmation: [validators.validateInput],
    },
  });

  const typedFormDetails = formDetails as typeof defaultFormState;
  const typedFormErrors = formErrors as Record<keyof typeof defaultFormState, string>;

  const updateDateOfConfirmationMutation = useMutation({
    mutationKey: ["update-date-of-confirmation"],
    mutationFn: async () =>
      employeesService.updateDateOfConfirmation(employee.employee_code, typedFormDetails.dateOfConfirmation),
    onSuccess: () => {
      onClose(true);
    },
  });

  const onSubmit = useCallback(() => {
    updateDateOfConfirmationMutation.mutate();
  }, []);

  const onChange = (value: Date | null) => {
    setFormDetail("dateOfConfirmation", format(value as Date, "yyyy-MM-dd"));
  };

  return (
    <Modal
      title="Change Date Of Confirmation"
      showBackButton
      showDivider
      isOpen={isOpen}
      onClose={() => onClose(false)}
      actions={
        <DialogActions>
          <Box display="flex" gap={2} padding={2} alignItems="center">
            <Button variant="outlined" onClick={() => onClose(false)}>
              Cancel
            </Button>
            <Button
              variant="contained"
              disabled={!areFormDetailsValid || typedFormDetails?.dateOfConfirmation === employee?.date_of_confirmation}
              onClick={onSubmit}
            >
              Submit
            </Button>
          </Box>
        </DialogActions>
      }
    >
      <CustomDateField
        title="Date Of Confirmation"
        name="dateOfConfirmation"
        onChange={onChange}
        value={typedFormDetails.dateOfConfirmation as unknown as Date}
        disablePast
        minDate={addDays(new Date(), 1)}
        slotProps={{
          textField: {
            id: "dateOfConfirmation",
            error: !!typedFormErrors.dateOfConfirmation,
            helperText: !!typedFormErrors.dateOfConfirmation && typedFormErrors.dateOfConfirmation,
            fullWidth: true,
          },
          actionBar: {
            actions: ["today"],
          },
        }}
      />
    </Modal>
  );
};

export default ChangeDateOfConfirmation;
