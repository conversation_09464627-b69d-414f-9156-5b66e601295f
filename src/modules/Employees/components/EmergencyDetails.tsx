import React, { useEffect, useImperativeHandle } from "react";

import { useForm } from "src/customHooks/useForm";
import LoadingScreen from "../LoadingScreen";
import {
  EmergencyDetailsInitialValues,
  EmergencyDetailsformValidators,
  INPUT_FIELDS,
  form,
} from "../config/EmergencyDetails";
import { FormDataType, StepperComponentProps } from "../types/FormDataTypes";
import { convertListToOptions, getEnumValues } from "../utils/utils";
import { CommonFormWithAddMore } from "./CommonForm";

const primaryRelationOptions = [
  {
    value: "Yes",
    label: "Yes",
  },
  {
    value: "No",
    label: "No",
  },
];

type Props = StepperComponentProps & {
  formData?: FormDataType[];
  onFormComplete: (form: FormDataType[], isFormSubmit?: boolean, isSaveDraft?: boolean) => void;
};

const EmergencyDetails = ({ formData, formActionButton, onFormComplete, setDisableNext, isViewOnlyMode }: Props) => {
  const { formDetails, formErrors, setFormDetail, addNewFormDetailRow, deleteFormDetails, areFormDetailsValid } =
    useForm({
      isBulk: true,
      initialState: formData || EmergencyDetailsInitialValues,
      validations: EmergencyDetailsformValidators,
    });
  const { data: relationList, isLoading: relationLoading } = getEnumValues("Relationship");

  useEffect(() => {
    const isAnyPrimary = (formDetails as FormDataType[]).some(
      (formDetail: FormDataType) => formDetail[INPUT_FIELDS.PRIMARY] === "Yes",
    );
    setDisableNext?.(!areFormDetailsValid || !isAnyPrimary);
  }, [formDetails, areFormDetailsValid]);

  useImperativeHandle(formActionButton, () => ({
    next: (isFormSubmit, isSaveDraft) => {
      onFormComplete(formDetails as FormDataType[], isFormSubmit, isSaveDraft);
    },
  }));

  const onChange = (index: number, fieldName: string, value: unknown) => {
    setFormDetail(fieldName, value, index);
  };

  if (relationLoading) return <LoadingScreen />;

  const selectOptions = {
    [INPUT_FIELDS.RELATION]: convertListToOptions(relationList),
    [INPUT_FIELDS.PRIMARY]: primaryRelationOptions,
  };

  return (
    <CommonFormWithAddMore
      onChange={onChange}
      inputElements={form}
      selectOptions={selectOptions}
      isViewOnlyMode={isViewOnlyMode}
      formErrors={formErrors as Record<string, string>[]}
      formValues={formDetails as Record<string, unknown>[]}
      onAddMoreClick={() => addNewFormDetailRow(EmergencyDetailsInitialValues)}
      onDeleteClick={deleteFormDetails}
    />
  );
};

export default EmergencyDetails;
