import { Add, DeleteOutlined } from "@mui/icons-material";
import { AttachFileOutlined, FileDownloadOutlined } from "@mui/icons-material";
import {
  Box,
  Button,
  Checkbox,
  Divider,
  Grid,
  InputLabel,
  ListItemText,
  MenuItem,
  SelectChangeEvent,
  Switch,
} from "@mui/material";
import { DateView } from "@mui/x-date-pickers/models/views.d";
import { format, parse } from "date-fns";
import React, { useEffect, useImperativeHandle } from "react";

import { useForm } from "src/customHooks/useForm";
import DateRangePicker from "src/modules/Common/DateRangePicker/DateRangePicker";
import FileDropzone from "src/modules/Common/FileDropzone/FileDropzone";
import { CustomDropzoneContainer } from "src/modules/Common/FileDropzone/style";
import { CustomAutocomplete } from "src/modules/Common/FormInputs/CustomAutoComplete";
import CustomDateField from "src/modules/Common/FormInputs/CustomDateField";
import { CustomInputLabel } from "src/modules/Common/FormInputs/CustomInputLabel";
import CustomMultiSelect from "src/modules/Common/FormInputs/CustomMultiSelect";
import CustomSearchField from "src/modules/Common/FormInputs/CustomSearchField";
import CustomSelect from "src/modules/Common/FormInputs/CustomSelect";
import CustomTextField from "src/modules/Common/FormInputs/CustomTextField";
import PhoneNumberField from "src/modules/Common/PhoneInput/PhoneNumberField";
import Span from "src/modules/Common/Span/Span";
import fileuploaderService from "src/services/fileuploader.service";
import { ValidatorReturnType } from "src/utils/validators";
import { FormDataType, FormInputOption, FormInputType, PhoneNumberType } from "../types/FormDataTypes";

const formatDateTime = (dateStructure: DateView[] | undefined) => {
  if (!dateStructure) return undefined;
  dateStructure.sort((a, b) => ("" + b).localeCompare(a));
  return dateStructure
    .map((ele) => {
      if (ele === "year") return "yyyy";
      else if (ele === "month") return "MM";
      else if (ele === "day") return "dd";
    })
    .join("-");
};

type GidStyles = {
  rowSpacing?: number;
  columnSpacing?: number;
  gridSize?: number;
};

const inputLabelStyle = {
  fontSize: "14px",
  marginBottom: "8px",
};

interface CommonFormProps {
  inputElements: FormInputType[];
  onChange: (name: string, value: unknown) => void;
  selectOptions?: { [key: string]: FormInputOption[] | string[] };
  formValues: Record<string, unknown>;
  formErrors: Record<string, string>;
  disabledInputFields?: Record<string, boolean>;
  isViewOnlyMode?: boolean;
  gridStyles?: GidStyles;
  readOnlyFields?: Record<string, boolean>;
  customDayContent?: (date: Date) => React.ReactNode;
}

type CommonFormWithAddMoreProps = {
  onAddMoreClick: () => void;
  inputElements: FormInputType[];
  onDeleteClick: (index: number) => void;
  formValues: { [key: string]: unknown }[];
  formErrors?: { [key: string]: string }[];
  selectOptions?: { [key: string]: FormInputOption[] | string[] };
  onChange: (index: number, name: string, value: unknown) => void;
  disabledInputFields?: Record<string, boolean>[];
  disableDelete?: boolean[];
  isViewOnlyMode?: boolean;
  gridStyles?: GidStyles;
  isOptional?: boolean;
};

export const addButtonStyle = {
  margin: 0,
  fontWeight: 500,
  fontSize: "16px",
  marginTop: "8px",
  textTransform: "none",
  fontFamily: "Poppins",
};

const FileDownloader = ({
  file,
  isViewOnlyMode,
  onDelete,
  height = 200,
  disabled = false,
}: {
  file: {
    name: string;
    s3_link: string;
  };
  isViewOnlyMode: boolean;
  onDelete: () => void;
  height?: string | number;
  disabled?: boolean;
}) => {
  if (!file) return null;

  const downloadFile = async () => {
    if (!file.s3_link) return;
    await fileuploaderService.downloadDocumentS3(file.s3_link as string);
  };

  return (
    <Box sx={{ mt: 2, position: "relative" }}>
      {!isViewOnlyMode && !disabled && (
        <DeleteOutlined
          onClick={onDelete}
          sx={{
            flex: 1,
            margin: "4px",
            fill: "#667085",
            cursor: "pointer",
            alignSelf: "flex-end",
            position: "absolute",
            right: 0,
          }}
        />
      )}
      <CustomDropzoneContainer
        sx={{
          height,
          width: "100%",
          borderRadius: "10px",
          border: "1px dashed #007F6F",
          backgroundColor: "#A1CBC7",
          flexDirection: "column",
          cursor: "pointer",
        }}
        onClick={downloadFile}
      >
        <FileDownloadOutlined sx={{ width: 50, height: 50 }} />
        <Box display="flex" alignItems="center">
          <AttachFileOutlined sx={{ width: 16, height: 16 }} />
          &nbsp;
          <Span sx={{ fontSize: "13px" }}>{file.name}</Span>
        </Box>
      </CustomDropzoneContainer>
    </Box>
  );
};

export const CommonForm = ({
  inputElements,
  onChange,
  selectOptions = {},
  formValues,
  formErrors = {},
  disabledInputFields = {},
  isViewOnlyMode = false,
  readOnlyFields = {},
  gridStyles: { rowSpacing = 2, columnSpacing = 4, gridSize = 4 } = {},
  customDayContent,
}: CommonFormProps) => {
  const onFormChange = (name: string, value: unknown) => {
    if (!isViewOnlyMode) onChange(name, value);
  };
  const onMultiSelectInputChange = (event: SelectChangeEvent) => {
    const {
      target: { value, name },
    } = event;
    // On autofill we get a stringified value.
    const valueArray = typeof value === "string" ? value.split(",") : value;
    onChange(name, valueArray);
  };
  if (isViewOnlyMode) {
    const isAllEmpty = Object.values(formValues).every((value) => value === "" || value == null);
    if (isAllEmpty)
      return (
        <Box sx={{ display: "flex", flexDirection: "column", width: "100%" }}>
          <Span sx={{ width: "100%", textAlign: "center" }}> No Data available</Span>
        </Box>
      );
  }
  return (
    <Grid container rowSpacing={rowSpacing} columnSpacing={columnSpacing}>
      {inputElements.map((inputElement: FormInputType) => {
        if (inputElement.isHidden) {
          return null;
        }
        switch (inputElement.variant) {
          case "select":
            return (
              <Grid item xs={inputElement?.xs || gridSize} key={inputElement.name}>
                <CustomSelect
                  size="small"
                  sx={{ width: "100%" }}
                  id={inputElement.name}
                  name={inputElement.name}
                  label={inputElement.label}
                  required={inputElement.isRequired}
                  options={(selectOptions[inputElement.name] as FormInputOption[]) || []}
                  value={(formValues[inputElement.name] as string) || ""}
                  disabled={isViewOnlyMode || disabledInputFields[inputElement.name]}
                  onChange={(event) => onFormChange(inputElement.name, event.target.value)}
                  readOnly={readOnlyFields[inputElement.name]}
                />
              </Grid>
            );
          case "multi-select":
            return (
              <Grid item xs={inputElement?.xs || gridSize} key={inputElement.name}>
                <CustomMultiSelect
                  size="small"
                  sx={{ width: "100%" }}
                  id={inputElement.name}
                  name={inputElement.name}
                  label={inputElement.label}
                  required={inputElement.isRequired}
                  options={(selectOptions[inputElement.name] as { value: string; label: string }[]) || []}
                  value={(formValues[inputElement.name] as string[]) || []}
                  readOnly={isViewOnlyMode || disabledInputFields[inputElement.name]}
                  placeholder={inputElement?.placeholder}
                  onChange={(ev: SelectChangeEvent<string>) => {
                    onMultiSelectInputChange(ev);
                  }}
                  multiple
                  displayEmpty
                >
                  {(selectOptions[inputElement.name] as FormInputOption[]).map((option: FormInputOption) => (
                    <MenuItem key={option.value as string} value={option.value as string}>
                      <Checkbox
                        checked={((formValues[inputElement.name] as string[]) || []).includes(option.value as string)}
                      ></Checkbox>
                      <ListItemText primary={option.label} />
                    </MenuItem>
                  ))}
                </CustomMultiSelect>
              </Grid>
            );
          case "date":
            return (
              <Grid item xs={inputElement?.xs || gridSize} key={inputElement.name}>
                <CustomDateField
                  name={inputElement.name}
                  title={inputElement.label}
                  slotProps={{
                    textField: {
                      variant: "outlined",
                      color: "primary",
                      fullWidth: true,
                      size: "small",
                      InputProps: {
                        style: {
                          width: "100%",
                        },
                      },
                      InputLabelProps: {
                        style: {
                          fontSize: "18px",
                          color: "transparent",
                        },
                      },
                    },
                  }}
                  required={inputElement.isRequired}
                  value={
                    formValues[inputElement.name]
                      ? parse(
                          formValues[inputElement.name] as string,
                          formatDateTime(inputElement?.views) || "yyyy-MM-dd",
                          new Date(),
                        )
                      : null
                  }
                  disabled={isViewOnlyMode || disabledInputFields[inputElement.name]}
                  views={inputElement.views || ["year", "month", "day"]}
                  minDate={formValues[inputElement.minDate || ""] as Date}
                  maxDate={formValues[inputElement.maxDate || ""] as Date}
                  onChange={(date) => {
                    const isInvalidDate = date && date.toString() === "Invalid Date";
                    const formatedDate =
                      isInvalidDate || !date ? null : format(date, formatDateTime(inputElement?.views) || "yyyy-MM-dd");
                    onFormChange(inputElement.name, formatedDate);
                  }}
                />
              </Grid>
            );
          case "date-range":
            return (
              <Grid item xs={inputElement?.xs || gridSize} key={inputElement.name} sx={{ width: "100%" }}>
                <DateRangePicker
                  required={inputElement.isRequired}
                  value={formValues[inputElement.name] as any}
                  onChange={(value: unknown) => onFormChange(inputElement.name, value)}
                  error={!!formErrors[inputElement.name]}
                  helperText={formErrors[inputElement.name] ? formErrors[inputElement.name] : ""}
                  title={inputElement.label}
                  maxDate={(selectOptions[inputElement.name] as any)?.maxDate}
                  minDate={(selectOptions[inputElement.name] as any)?.minDate}
                  disabledDates={(selectOptions[inputElement.name] as any)?.disabledDates}
                  dayContentRenderer={customDayContent}
                  onRangeFocusChange={(selectOptions[inputElement.name] as any)?.onFocusChange}
                  dragSelectionEnabled={(selectOptions[inputElement.name] as any)?.dragSelectionEnabled}
                />
              </Grid>
            );
          case "autocomplete":
            return (
              <Grid item xs={inputElement?.xs || gridSize} key={inputElement.name} sx={{ width: "100%" }}>
                <CustomAutocomplete
                  size="small"
                  width={"100%"}
                  id={inputElement.name}
                  title={inputElement.label}
                  required={inputElement.isRequired}
                  error={!!formErrors[inputElement.name]}
                  helperText={formErrors[inputElement.name] || ""}
                  disabled={isViewOnlyMode || disabledInputFields[inputElement.name]}
                  options={selectOptions[inputElement.name] as string[]}
                  searchInputValue={formValues[inputElement.name] as string}
                  placeholder={inputElement.placeholder || `Search ${inputElement.label}`}
                  setSearchInputValue={(searchResult: string) => onFormChange(inputElement.name, searchResult)}
                />
              </Grid>
            );
          case "search":
            return (
              <Grid item xs={inputElement?.xs || gridSize} key={inputElement.name} sx={{ width: "100%" }}>
                <CustomSearchField
                  width="100%"
                  size="small"
                  getData="email"
                  id={inputElement.name}
                  title={inputElement.label}
                  required={inputElement.isRequired}
                  error={!!formErrors[inputElement.name]}
                  helperText={formErrors[inputElement.name] || ""}
                  disabled={isViewOnlyMode || disabledInputFields[inputElement.name]}
                  searchInputValue={formValues[inputElement.name] as string}
                  placeholder={inputElement.placeholder || `Search ${inputElement.label}`}
                  setSearchInputValue={(searchResult: string) => onFormChange(inputElement.name, searchResult)}
                />
              </Grid>
            );
          case "file":
            return isViewOnlyMode || (formValues[inputElement.name] as any) ? (
              (formValues[inputElement.name] as any) ? (
                <Grid item xs={inputElement?.xs || gridSize} key={inputElement.name} sx={{ width: "100%" }}>
                  <CustomInputLabel title={inputElement.label} required={inputElement.isRequired} />
                  <FileDownloader
                    height={inputElement?.height}
                    file={formValues[inputElement.name] as any}
                    isViewOnlyMode={isViewOnlyMode}
                    onDelete={() => {
                      return onFormChange(inputElement.name, null);
                    }}
                    disabled={disabledInputFields[inputElement.name]}
                  />
                </Grid>
              ) : null
            ) : (
              <Grid item xs={inputElement?.xs || gridSize} key={inputElement.name} sx={{ width: "100%" }}>
                <InputLabel sx={inputLabelStyle} id={`label-${inputElement.label}`}>
                  {inputElement.label}
                  {inputElement.isRequired ? <span style={{ color: "red" }}> *</span> : ""}
                </InputLabel>
                <Box sx={{ mt: 2 }}>
                  <FileDropzone
                    height={inputElement?.height}
                    acceptFileTypes={inputElement.acceptFileTypes || (["*"] as any)}
                    onFileDrop={(files: File[]) => onFormChange(inputElement.name, files)}
                    disabled={disabledInputFields[inputElement.name]}
                    variant={disabledInputFields[inputElement.name] ? "disabled" : "default"}
                  />
                </Box>
              </Grid>
            );
          case "text":
          case "number":
          case "email":
          case "password":
          case "textarea":
            return (
              <Grid item xs={inputElement?.xs || gridSize} key={inputElement.name} sx={{ width: "100%" }}>
                <CustomTextField
                  type={inputElement?.variant || "text"}
                  placeholder={`Enter ${inputElement.label}`}
                  {...(inputElement?.props || {})}
                  size="small"
                  id={inputElement.name}
                  rows={inputElement?.rows}
                  title={inputElement.label}
                  required={inputElement.isRequired}
                  value={formValues[inputElement.name]}
                  multiline={Boolean(inputElement?.rows)}
                  error={!!formErrors[inputElement.name]}
                  sx={{ width: inputElement?.width || "100%" }}
                  helperText={formErrors[inputElement.name] || ""}
                  disabled={isViewOnlyMode || disabledInputFields[inputElement.name]}
                  onChange={async (ev: React.ChangeEvent<HTMLInputElement>) =>
                    onFormChange(inputElement.name, ev.target.value)
                  }
                  inputProps={{ readOnly: readOnlyFields[inputElement.name] }}
                />
              </Grid>
            );
          case "phone":
            return (
              <Grid item xs={inputElement?.xs || gridSize} key={inputElement.name} sx={{ width: "100%" }}>
                <PhoneNumberField
                  title={inputElement.label}
                  required={inputElement.isRequired}
                  placeholder={`Enter ${inputElement.label}`}
                  value={(formValues[inputElement.name] as PhoneNumberType) || {}}
                  error={!!formErrors[inputElement.name]}
                  helperText={formErrors[inputElement.name]}
                  onChange={(value) => onFormChange(inputElement.name, value)}
                  disabled={isViewOnlyMode || disabledInputFields[inputElement.name]}
                />
              </Grid>
            );
          case "checkbox":
            return (
              <Grid item xs={inputElement?.xs || gridSize} key={inputElement.name} sx={{ width: "100%" }}>
                <InputLabel sx={inputLabelStyle} htmlFor={inputElement.name}>
                  {inputElement?.label}
                </InputLabel>
                <Switch
                  checked={(formValues[inputElement.name] as boolean) || false}
                  id={inputElement.name}
                  name={inputElement.name}
                  value={(formValues[inputElement.name] as boolean) || false}
                  onChange={(ev: any) => onFormChange(inputElement.name, ev.target.checked)}
                  disabled={isViewOnlyMode || inputElement?.props?.disabled || false}
                />
              </Grid>
            );
          case "switch":
            return (
              <Grid item xs={inputElement?.xs || gridSize} key={inputElement.name} sx={{ width: "100%" }}>
                <Box display="flex" alignItems="center" height="42px" sx={inputElement?.props?.sx}>
                  <InputLabel sx={{ fontSize: "14px" }} htmlFor={inputElement.name}>
                    {inputElement?.label}
                  </InputLabel>
                  <Switch
                    checked={(formValues[inputElement.name] as boolean) || false}
                    id={inputElement.name}
                    name={inputElement.name}
                    value={(formValues[inputElement.name] as boolean) || false}
                    onChange={(ev: any) => onFormChange(inputElement.name, ev.target.checked)}
                    disabled={isViewOnlyMode || inputElement?.props?.disabled || false}
                    // sx={{ marginLeft: "18px" }}
                  />
                </Box>
              </Grid>
            );
          default:
            return <Grid item xs={gridSize} />;
        }
      })}
    </Grid>
  );
};

export const CommonFormWithAddMore = ({
  onChange,
  formValues,
  inputElements,
  selectOptions = {},
  formErrors = [],
  onDeleteClick,
  onAddMoreClick,
  disabledInputFields = [],
  disableDelete = [],
  gridStyles,
  isViewOnlyMode = false,
  isOptional = false,
}: CommonFormWithAddMoreProps) => {
  return (
    <Box sx={{ display: "flex", flexDirection: "column", width: "100%" }}>
      {formValues.map((formValue, index) => (
        <Grid item key={`${index + 1}`} sx={{ width: "100%" }}>
          <CommonForm
            key={index}
            formValues={formValue}
            inputElements={Array.isArray(inputElements?.[index]) ? inputElements?.[index] : inputElements}
            selectOptions={selectOptions}
            formErrors={formErrors[index]}
            disabledInputFields={disabledInputFields[index]}
            onChange={(name, value) => onChange(index, name, value)}
            gridStyles={gridStyles}
            isViewOnlyMode={isViewOnlyMode}
          />
          {(formValues.length > 1 || (isOptional && formValues.length > 0)) && (
            <Grid item xs={12}>
              <Box
                sx={{
                  display: "flex",
                  justifyContent: "flex-end",
                  flexDirection: "column",
                  marginTop: isViewOnlyMode || disableDelete[index] ? "20px" : 0,
                }}
              >
                {!isViewOnlyMode && !disableDelete[index] && (
                  <DeleteOutlined
                    onClick={() => onDeleteClick(index)}
                    sx={{
                      flex: 1,
                      margin: "10px",
                      fill: "#667085",
                      cursor: "pointer",
                      alignSelf: "flex-end",
                    }}
                  />
                )}
                {index < formValues.length - 1 && <Divider sx={{ width: "100%", margin: "8px 0 16px 0" }} />}
              </Box>
            </Grid>
          )}
        </Grid>
      ))}
      {formValues.length === 0 && isViewOnlyMode && (
        <Span sx={{ width: "100%", textAlign: "center" }}> No Data available</Span>
      )}
      {!isViewOnlyMode && (isOptional ? formValues.length > 0 : true) && (
        <Grid item>
          <Button variant="text" sx={addButtonStyle} onClick={onAddMoreClick} startIcon={<Add fontSize="small" />}>
            Add more
          </Button>
        </Grid>
      )}
    </Box>
  );
};

export type ActionComponentProps = {
  areFormDetailsValid: boolean;
  formDetails?: FormDataType | FormDataType[];
  onSubmit: () => void;
};

export interface CustomInputElement {
  submit?: () => void;
}

type CommonFormWithStateProps = {
  withAddMore?: boolean;
  initialState: FormDataType | FormDataType[];
  inputElements: FormInputType[];
  selectOptions?: { [key: string]: FormInputOption[] | string[] };
  disabledInputFields?: Record<string, boolean> | Record<string, boolean>[];
  validators: { [x: string]: ((value: string | number) => ValidatorReturnType)[] };
  ActionComponent?: ({ areFormDetailsValid, formDetails, onSubmit }: ActionComponentProps) => JSX.Element;
  onFormSubmit?: (form: FormDataType | FormDataType[]) => void;
  onChange?: (
    name: string,
    value: unknown,
    setFormDetail: (name: string, value: unknown) => void,
    formDetails: FormDataType,
  ) => void;

  isViewOnlyMode?: boolean;
  gridStyles?: GidStyles;
  formActionButton?: React.RefObject<CustomInputElement>;
  setSubmitDisabled?: (value: boolean) => void;
};

export const CommonFormWithState = ({
  inputElements,
  selectOptions = {},
  disabledInputFields = {},
  initialState,
  validators,
  withAddMore,
  onFormSubmit,

  onChange, // onChange will be called when form value changes, and it has a setFormDetail method to update value of any field

  ActionComponent, // use Action Component to render button, onSubmit & areFormDetailsValid will be passed to ActionComponent
  formActionButton, // or use formActionButton to submit the form, submit method will be available in formActionButton
  setSubmitDisabled, // setSubmitDisabled to disable the submit button

  isViewOnlyMode = false,
  gridStyles, // Grid styles for the form
}: CommonFormWithStateProps) => {
  const { formDetails, formErrors, setFormDetail, areFormDetailsValid, addNewFormDetailRow, deleteFormDetails } =
    useForm({
      initialState,
      validations: validators,
      isBulk: withAddMore,
    });

  // use ActionComponent or formActionButton to submit the form
  useImperativeHandle(formActionButton, () => ({
    submit: () => {
      onFormSubmit?.(formDetails);
    },
  }));

  useEffect(() => {
    if (setSubmitDisabled) setSubmitDisabled(!areFormDetailsValid);
  }, [areFormDetailsValid]);

  const onFormChange = (name: string, value: unknown, index?: number) => {
    setFormDetail(name, value, index);
    onChange?.(
      name,
      value,
      (fileldName, value) => setFormDetail(fileldName, value, index),
      withAddMore && index != null ? (formDetails as FormDataType[])[index] : (formDetails as FormDataType),
    );
  };

  return (
    <>
      {withAddMore ? (
        <CommonFormWithAddMore
          inputElements={inputElements}
          selectOptions={selectOptions}
          onDeleteClick={deleteFormDetails}
          onAddMoreClick={() => addNewFormDetailRow(initialState)}
          formValues={formDetails as FormDataType[]}
          formErrors={formErrors as Record<string, string>[]}
          onChange={(index, name, value) => onFormChange(name, value, index)}
          disabledInputFields={disabledInputFields as Record<string, boolean>[]}
          gridStyles={gridStyles}
          isViewOnlyMode={isViewOnlyMode}
        />
      ) : (
        <CommonForm
          inputElements={inputElements}
          onChange={onFormChange}
          selectOptions={selectOptions}
          formValues={formDetails as FormDataType}
          formErrors={formErrors as Record<string, string>}
          disabledInputFields={disabledInputFields as Record<string, boolean>}
          gridStyles={gridStyles}
          isViewOnlyMode={isViewOnlyMode}
        />
      )}
      {ActionComponent && (
        <ActionComponent
          areFormDetailsValid={areFormDetailsValid}
          formDetails={formDetails}
          onSubmit={() => onFormSubmit?.(formDetails)}
        />
      )}
    </>
  );
};
