import { Box, Typography, styled } from "@mui/material";
import StepConnector, { stepConnectorClasses } from "@mui/material/StepConnector";

export const CustomConnector = styled(StepConnector)(() => ({
  [`&.${stepConnectorClasses.alternativeLabel}`]: {
    top: 20,
    left: "calc(-50% + 22.5px)",
    right: "calc(50% + 22.5px)",
  },
  [`&.${stepConnectorClasses.active}`]: {
    [`& .${stepConnectorClasses.line}`]: {
      borderColor: "#57ba57",
    },
  },
  [`&.${stepConnectorClasses.completed}`]: {
    [`& .${stepConnectorClasses.line}`]: {
      borderColor: "#57ba57",
    },
  },
  [`& .${stepConnectorClasses.line}`]: {
    borderColor: "#eaeaf0",
    borderTopWidth: 3,
    borderRadius: 1,
  },
}));

export const commonIconStyles = {
  height: 45,
  width: 45,
};

export const activeIconStyles = {
  backgroundColor: "white",
  borderRadius: "50%",
  display: "flex",
  justifyContent: "center",
  alignItems: "center",
  border: "#57ba57 3px solid",
  width: 45,
  height: 45,
};

export const disableIconStyles = {
  backgroundColor: "#d2d2d2",
  width: 45,
  height: 45,
  borderRadius: "50%",
  display: "flex",
  justifyContent: "center",
  alignItems: "center",
};

export const disableIconTextStyle = {
  color: "white",
};

export const headingStyle = {
  fontFamily: "Poppins",
  fontSize: "16px",
  fontWeight: 500,
};

export const addButtonStyle = {
  fontFamily: "Poppins",
  fontSize: "16px",
  fontWeight: 500,
  textTransform: "none",
  margin: 0,
  marginTop: "15px",
};

export const backButtonStyle = {
  fontFamily: "Poppins",
  fontSize: "16px",
  fontWeight: 500,
  textTransform: "none",
  margin: 0,
  width: "172px",
  height: "48px",
  flexGrow: 0,
  display: "flex",
  flexDirection: "row",
  justifyContent: "center",
  alignItems: "center",
  padding: "0 20px",
  borderRadius: "50px",
  backgroundColor: "#eff4f8",
  border: "#eff4f8",
};

export const nextButtonStyle = {
  fontFamily: "Poppins",
  fontSize: "16px",
  fontWeight: 500,
  textTransform: "none",
  margin: 0,
  width: "172px",
  height: "48px",
  flexGrow: 0,
  display: "flex",
  flexDirection: "row",
  justifyContent: "center",
  alignItems: "center",
  gap: "8px",
  padding: "0 20px",
  borderRadius: "50px",
  backgroundColor: "#007f6f",
  marginLeft: "auto",
};

export const CustomTypography = styled(Typography)(() => ({
  color: "#667085",
  fontSize: 14,
  fontFamily: "Poppins",
  fontWeight: "400",
  wordWrap: "break-word",
}));

export const CustomFileDownloadContainer = styled(Box)(() => ({
  background: "#E6F2F1",
  display: "flex",
  alignItems: "center",
  justifyContent: "space-between",
  borderRadius: "15px",
}));

export const CustomSampleIconContainer = styled(Box)(() => ({
  width: "52px",
  height: "52px",
  borderRadius: "50%",
  background: "white",
  display: "flex",
  justifyContent: "center",
  alignItems: "center",
  "&:hover": {
    opacity: 0.5,
    cursor: "pointer",
  },
}));
