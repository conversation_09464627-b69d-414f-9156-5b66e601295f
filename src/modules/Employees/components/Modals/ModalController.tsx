import React from "react";
import CreateEmployeeModal from "../../CreateEmployeeModal";
import { ExportEmployeesModal } from "../ExportEmployeesModal";
import { ImportModal } from "./index";

type RenderModalProps = {
  open: boolean;
  modalId: string | null;
  handleClose: () => void;
  isPayrollView?: boolean;
};

const RenderModal: React.FC<RenderModalProps> = ({ open, modalId, handleClose, isPayrollView }) => {
  switch (modalId) {
    case "import":
      return <ImportModal open={open} onClose={handleClose} isPayrollView={!!isPayrollView} />;
    case "add":
      return <CreateEmployeeModal open={open} onClose={handleClose} />;
    case "export":
      return <ExportEmployeesModal open={open} onClose={handleClose} isPayrollView={!!isPayrollView} />;
    default:
      return null;
  }
};

interface ModalControllerProps extends RenderModalProps {}

const ModalController: React.FC<ModalControllerProps> = ({ open, modalId, handleClose, isPayrollView }) => (
  <RenderModal open={open} modalId={modalId} handleClose={handleClose} isPayrollView={isPayrollView} />
);

export default ModalController;
