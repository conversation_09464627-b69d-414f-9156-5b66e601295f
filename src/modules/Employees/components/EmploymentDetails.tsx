import { useQueries } from "@tanstack/react-query";
import React, { useEffect, useImperativeHandle, useMemo } from "react";

import { useAppSelector } from "src/customHooks/useAppSelector";
import { useForm } from "src/customHooks/useForm";
import { WorkRoleDetails } from "src/services/api_definitions/tenants";
import { HierarchyResponse } from "src/services/api_definitions/workRoleHierarchy.service";
import businessunitsService from "src/services/businessunits.service";
import departmentService from "src/services/department.service";
import tenantsService from "src/services/tenants.service";
import { getCurrentTenantId } from "src/utils/authUtils";
import {
  dependentFields,
  EmploymentDetailsformValidators,
  EmploymentDetailsInitialValues,
  form,
  INPUT_FIELDS,
} from "../config/EmploymentDetails";
import LoadingScreen from "../LoadingScreen";
import { BandLevelGrade, BusinessDepartmentList, JobTitleList, WorkRoleHierarchyList } from "../types/employeeTypes";
import { FormDataType, FormDataTypeString, StepperComponentProps } from "../types/FormDataTypes";
import { convertListToOptions, getBandGradeLevel, getEnumValues } from "../utils/utils";
import { CommonForm } from "./CommonForm";

const findHierarchyStructure = (data: WorkRoleDetails[]) => ({
  isBandExist: Boolean(data[0].band),
  isGradeExist: Boolean(data[0].grade),
  isLevelExist: Boolean(data[0].level),
});

const getEmployeementDetailsformValidators = (isBandLevelGrade: BandLevelGrade | null) => {
  const formValidators = { ...EmploymentDetailsformValidators };
  if (!isBandLevelGrade) return formValidators;
  if (!isBandLevelGrade.isBandExist) delete formValidators[INPUT_FIELDS.BAND];
  if (!isBandLevelGrade.isLevelExist) delete formValidators[INPUT_FIELDS.LEVEL];
  if (!isBandLevelGrade.isGradeExist) delete formValidators[INPUT_FIELDS.GRADE];
  return formValidators;
};

const queries = [
  {
    queryKey: ["get-business-unit-details"],
    queryFn: async (): Promise<BusinessDepartmentList | null> => await businessunitsService.getBusinessUnitDetails(),
    retryOnMount: false,
    refetchOnWindowFocus: false,
  },
  {
    queryKey: ["get-band-level-grade-info"],
    queryFn: async (): Promise<BandLevelGrade> =>
      findHierarchyStructure(await tenantsService.getWorkRoleDetails(getCurrentTenantId())),
    retryOnMount: false,
    refetchOnWindowFocus: false,
  },
  {
    queryKey: ["get-work-role-hierarchy"],
    queryFn: async () => {
      const response: HierarchyResponse[] | null = await departmentService.getWorkRoleHierarchy(getCurrentTenantId());
      if (!response) {
        return {};
      }
      const { bandOptions, levelOptions, gradeOptions } = getBandGradeLevel(response);
      return { bandOptions, levelOptions, gradeOptions };
    },
    retryOnMount: false,
    refetchOnWindowFocus: false,
  },
];

type Props = StepperComponentProps & {
  formData?: FormDataType[];
  onFormComplete: (form: FormDataType, isFormSubmit?: boolean, isSaveDraft?: boolean) => void;
  disabledInputFields?: string[];
};

const EmploymentDetails = ({
  formData = [],
  onFormComplete,
  formActionButton,
  setDisableNext,
  isViewOnlyMode,
  disabledInputFields = [],
}: Props) => {
  const fetchWorkRoleHierarchy = useQueries({ queries });
  const [
    { data: businessUnitsList, isLoading: businessUnitsLoading },
    { data: isBandLevelGrade, isLoading: isBandLevelGradeLoading },
    { data: workRoleHierarchyList, isLoading: workRoleHierarchyLoading },
  ] = fetchWorkRoleHierarchy as unknown as [
    { data: BusinessDepartmentList; isLoading: boolean },
    { data: BandLevelGrade; isLoading: boolean },
    { data: WorkRoleHierarchyList; isLoading: boolean },
  ];

  const { selectedOrganisationDetails: organisation } = useAppSelector((state) => state.userManagement);

  const validators = useMemo(
    () => (isBandLevelGradeLoading ? {} : getEmployeementDetailsformValidators(isBandLevelGrade)),
    [isBandLevelGrade, isBandLevelGradeLoading],
  );
  const { formDetails, formErrors, setFormDetail, areFormDetailsValid } = useForm<FormDataType>({
    initialState: formData[0] || EmploymentDetailsInitialValues[0],
    validations: validators,
  });

  useEffect(() => {
    setDisableNext?.(!areFormDetailsValid);
  }, [areFormDetailsValid]);

  useImperativeHandle(formActionButton, () => ({
    next: (isFormSubmit, isSaveDraft) => {
      onFormComplete(formDetails as FormDataType, isFormSubmit, isSaveDraft);
    },
  }));

  const formValues = formDetails as FormDataTypeString;
  const businessUnit = formValues[INPUT_FIELDS.BUSINESS];
  const departmentUnit = formValues[INPUT_FIELDS.DEPARTMENT];
  const band = formValues[INPUT_FIELDS.BAND];
  const level = formValues[INPUT_FIELDS.LEVEL];
  const grade = formValues[INPUT_FIELDS.GRADE];

  const fetchData = useQueries({
    queries: [
      {
        queryKey: ["get-department-details", businessUnit],
        queryFn: async (): Promise<BusinessDepartmentList | null> =>
          await departmentService.getDepartmentDetails(businessUnit),
        enabled: !!businessUnit,
      },
      {
        queryKey: ["get-job-title-details", businessUnit, departmentUnit],
        queryFn: async (): Promise<JobTitleList | null> =>
          await departmentService.getJobTitleDetails(businessUnit, departmentUnit),
        enabled: !!businessUnit && !!departmentUnit,
      },
    ],
  });

  const [{ data: departmentList, isLoading: departmentLoading }, { data: jobTitleList, isLoading: jobTitleLoading }] =
    fetchData as unknown as [
      { data: BusinessDepartmentList; isLoading: boolean },
      { data: JobTitleList | null; isLoading: boolean },
    ];
  const { data: employeeTypeList, isLoading: employeeTypeLoading } = getEnumValues("EmployeeType");

  const resetAfterFields = (fieldName: string) => {
    const index = dependentFields.indexOf(fieldName);
    if (index === -1) return;
    for (let i = index + 1; i < dependentFields.length; i++) {
      setFormDetail(dependentFields[i], "");
    }
  };

  const onChange = (fieldName: string, value: unknown) => {
    resetAfterFields(fieldName);
    if (fieldName === INPUT_FIELDS.OFFICE_ADDRESS) {
      setFormDetail(
        "location.country",
        organisation?.addresses?.find((address) => address?.display_address === value)?.country,
      );
    }
    setFormDetail(fieldName, value);
    if (fieldName === INPUT_FIELDS.OFFICIAL_EMAIL) {
      localStorage.setItem(INPUT_FIELDS.OFFICIAL_EMAIL, value as string);
    }
  };

  const filteredJobTitle = useMemo(() => {
    if (jobTitleLoading || !jobTitleList) return [];
    const { isBandExist, isLevelExist, isGradeExist } = isBandLevelGrade || {};
    return jobTitleList
      .filter((job) => {
        if (isBandExist && job.band !== band) return false;
        if (isLevelExist && job.level !== level) return false;
        if (isGradeExist && job.grade !== grade) return false;
        return true;
      })
      .map((job) => job.name);
  }, [jobTitleLoading, jobTitleList, isBandLevelGrade, band, level, grade]);

  const filteredForm = useMemo(() => {
    const { isBandExist, isGradeExist, isLevelExist } = isBandLevelGrade ?? {};
    const fieldVisibility = {
      [INPUT_FIELDS.BAND]: isBandExist,
      [INPUT_FIELDS.BAND_PLACEHOLDER]: !isBandExist,
      [INPUT_FIELDS.LEVEL]: isLevelExist,
      [INPUT_FIELDS.LEVEL_PLACEHOLDER]: !isLevelExist,
      [INPUT_FIELDS.GRADE]: isGradeExist,
      [INPUT_FIELDS.GRADE_PLACEHOLDER]: !isGradeExist,
    };
    return form.filter((element) => fieldVisibility[element.name] ?? true);
  }, [form, isBandLevelGrade]);

  useEffect(() => {
    const jobTitleDisplayName = jobTitleList?.find(
      (eachJobTitle) => eachJobTitle.name === formValues[INPUT_FIELDS?.JOB_TITLE],
    );
    setFormDetail(
      "location.country",
      organisation?.addresses?.find((address) => address?.display_address === formValues?.[INPUT_FIELDS.OFFICE_ADDRESS])
        ?.country,
    );
    setFormDetail("work_role_name", jobTitleDisplayName?.work_role_name);
    setFormDetail(INPUT_FIELDS.JOB_TITLE_FORMATTED_NAME, jobTitleDisplayName?.formatted_name);
  }, [formValues[INPUT_FIELDS.JOB_TITLE], jobTitleList]);

  if (businessUnitsLoading || employeeTypeLoading || isBandLevelGradeLoading || workRoleHierarchyLoading) {
    return <LoadingScreen />;
  }

  const { isBandExist, isLevelExist, isGradeExist } = isBandLevelGrade || {};
  const { bandOptions, levelOptions, gradeOptions } = workRoleHierarchyList || {};

  const filteredLevelOptions = levelOptions
    ?.filter((element) => {
      return !isBandExist || element.bandName === band;
    })
    .map((element) => element.name);

  const filteredGradeOptions = gradeOptions
    ?.filter((element) => {
      return (!isBandExist || element.bandName === band) && (!isLevelExist || element.levelName === level);
    })
    .map((element) => element.name);

  const selectOptions = {
    [INPUT_FIELDS.BUSINESS]: convertListToOptions(businessUnitsList?.map((element) => element.name)),
    [INPUT_FIELDS.DEPARTMENT]: !departmentLoading
      ? convertListToOptions(departmentList?.map((element) => element.name))
      : [],
    [INPUT_FIELDS.EMPLOYEE_TYPE]: convertListToOptions(employeeTypeList as string[]),
    [INPUT_FIELDS.BAND]: convertListToOptions(bandOptions?.map((element) => element.name)),
    [INPUT_FIELDS.LEVEL]: convertListToOptions(filteredLevelOptions),
    [INPUT_FIELDS.GRADE]: convertListToOptions(filteredGradeOptions),
    [INPUT_FIELDS.JOB_TITLE]: convertListToOptions(filteredJobTitle),
    [INPUT_FIELDS.OFFICE_ADDRESS]: convertListToOptions(
      organisation?.addresses.map((address) => address.display_address),
    ),
  };

  const disabledInputFieldsObject = disabledInputFields?.reduce(
    (acc, field) => {
      return { ...acc, [field]: true };
    },
    {} as Record<string, boolean>,
  );

  const disabledInputFieldsAll = {
    ...disabledInputFieldsObject,
    [INPUT_FIELDS.DEPARTMENT]: !businessUnit,
    [INPUT_FIELDS.BAND]: !departmentUnit,
    [INPUT_FIELDS.LEVEL]: isBandExist ? !band : !departmentUnit,
    [INPUT_FIELDS.GRADE]: isLevelExist ? !level : isBandExist ? !band : !departmentUnit,
    [INPUT_FIELDS.JOB_TITLE]: isGradeExist ? !grade : isLevelExist ? !level : isBandExist ? !band : !departmentUnit,
  };

  return (
    <CommonForm
      onChange={onChange}
      inputElements={filteredForm}
      selectOptions={selectOptions}
      isViewOnlyMode={isViewOnlyMode}
      formErrors={formErrors as Record<string, string>}
      formValues={formDetails as Record<string, unknown>}
      disabledInputFields={disabledInputFieldsAll}
    />
  );
};

export default EmploymentDetails;
