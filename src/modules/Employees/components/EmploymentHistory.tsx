import { Add } from "@mui/icons-material";
import { Button } from "@mui/material";
import React, { useEffect, useImperativeHandle, useMemo } from "react";
import { useForm } from "src/customHooks/useForm";
import {
  EmploymentHistoryInitialValues,
  EmploymentHistoryformValidators,
  INPUT_FIELDS,
  getFormElements,
} from "../config/EmploymentHistory";
import { FormDataType, StepperComponentProps } from "../types/FormDataTypes";
import { uploadEmployeeDocument } from "../utils/utils";
import { CommonFormWithAddMore } from "./CommonForm";

type Props = StepperComponentProps & {
  formData?: FormDataType[];
  onFormComplete: (form: FormDataType[], isFormSubmit?: boolean, isSaveDraft?: boolean) => void;
};

const isArrayEmpty = (arr: FormDataType[] = []) => {
  if (arr.length === 0) {
    return true;
  }
  if (arr?.length === 1) {
    return Object.values(arr[0])?.every((value) => !value);
  }
  return false;
};

const EmploymentHistory = ({ formData, onFormComplete, formActionButton, setDisableNext, isViewOnlyMode }: Props) => {
  const initialState = useMemo(() => (isArrayEmpty(formData) ? [] : formData), [formData]);

  const { formDetails, formErrors, setFormDetail, addNewFormDetailRow, deleteFormDetails, areFormDetailsValid } =
    useForm({
      isBulk: true,
      initialState: initialState || [],
      validations: EmploymentHistoryformValidators,
    });

  useEffect(() => {
    const isFromDateLessThanToDate = (formDetails as FormDataType[])?.every((formDetail) => {
      const fromDate = formDetail[INPUT_FIELDS.FROM_DATE] as string;
      const toDate = formDetail[INPUT_FIELDS.TO_DATE] as string;
      const experienceLetter = formDetail[INPUT_FIELDS.EXPERIENCE_LETTER] as string;
      const isCurrentEmployee = formDetail[INPUT_FIELDS.IS_CURRENT_EMPLOYER] === true;
      return (fromDate <= toDate && experienceLetter) || isCurrentEmployee;
    });
    setDisableNext?.(!areFormDetailsValid || !isFromDateLessThanToDate);
  }, [formDetails, areFormDetailsValid]);

  useImperativeHandle(formActionButton, () => ({
    next: (isFormSubmit, isSaveDraft) => {
      onFormComplete(formDetails as FormDataType[], isFormSubmit, isSaveDraft);
    },
  }));

  const onFileChange = async (acceptedFiles: File[], index: number, fieldName: string) => {
    const type = fieldName === INPUT_FIELDS.OFFER_LETTER ? "Offer Letter" : "Experience Letter";
    const document = await uploadEmployeeDocument(acceptedFiles, type);
    if (document) {
      setFormDetail(fieldName, document, index);
    }
  };

  const onChange = (index: number, fieldName: string, value: unknown) => {
    setFormDetail(fieldName, value, index);
    switch (fieldName) {
      case INPUT_FIELDS.FROM_DATE: {
        setFormDetail(INPUT_FIELDS.TO_DATE_MIN_DATE, new Date(value as string), index);
        break;
      }
      case INPUT_FIELDS.TO_DATE: {
        if (value) setFormDetail(INPUT_FIELDS.FROM_DATE_MAX_DATE, new Date(value as string), index);
        break;
      }
      case INPUT_FIELDS.OFFER_LETTER: {
        if (value) onFileChange(value as File[], index, fieldName);
        break;
      }
      case INPUT_FIELDS.EXPERIENCE_LETTER: {
        if (value) onFileChange(value as File[], index, fieldName);
        break;
      }
      case INPUT_FIELDS.IS_CURRENT_EMPLOYER: {
        if (value) setFormDetail(INPUT_FIELDS.TO_DATE, null, index);
        break;
      }
      default:
        break;
    }
  };

  const disabledInputFields = (formDetails as FormDataType[])?.map((formDetail) => {
    const fromDate = formDetail[INPUT_FIELDS.FROM_DATE];
    const isCurrentEmployee = formDetail[INPUT_FIELDS.IS_CURRENT_EMPLOYER];
    return {
      [INPUT_FIELDS.TO_DATE]: !fromDate || isCurrentEmployee,
    };
  });

  const handleDeleteForm = (index: number) => {
    deleteFormDetails(index);
  };

  const handleAddInitialForm = () => {
    addNewFormDetailRow(EmploymentHistoryInitialValues);
  };

  const shouldShowAddButton = formDetails?.length === 0 && !isViewOnlyMode;
  const isCurrentEmployee =
    (formDetails as [])?.filter((formDetail) => formDetail[INPUT_FIELDS.IS_CURRENT_EMPLOYER])?.length > 0;
  const formUpdated = (formDetails as [])?.map((formDetail) => {
    const isCurrentToggleSelected = formDetail[INPUT_FIELDS.IS_CURRENT_EMPLOYER] === true;
    return getFormElements(isCurrentEmployee && !isCurrentToggleSelected, isCurrentToggleSelected);
  });

  return (
    <>
      {shouldShowAddButton && (
        <Button
          variant="text"
          sx={{
            margin: "8px 0",
            padding: "12px 24px",
            fontWeight: 500,
            fontSize: "16px",
            textTransform: "none",
            fontFamily: "Poppins",
          }}
          onClick={handleAddInitialForm}
          startIcon={<Add fontSize="small" />}
        >
          Add Employment Details
        </Button>
      )}
      <CommonFormWithAddMore
        onChange={onChange}
        inputElements={formUpdated as []}
        isViewOnlyMode={isViewOnlyMode}
        formErrors={formErrors as Record<string, string>[]}
        formValues={formDetails as Record<string, unknown>[]}
        onDeleteClick={handleDeleteForm}
        onAddMoreClick={handleAddInitialForm}
        disabledInputFields={disabledInputFields as Record<string, boolean>[]}
        isOptional={true}
      />
    </>
  );
};

export default EmploymentHistory;
