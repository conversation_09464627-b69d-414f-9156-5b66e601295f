import React from "react";
import Modal from "src/modules/Common/Modal/Modal";
import EmployeeCompensations from "src/modules/Profile/components/EmployeeCompensation/EmployeeCompensations";

const EmployeeCompensationHistory = ({ isOpen, onClose, employee }: any) => {
  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title="Compensation History"
      showBackButton
      showDivider
      fullWidth
      maxWidth="unset"
    >
      <EmployeeCompensations
        residentCountry={employee?.office_address?.country || "India"}
        compensations={employee?.compensations}
        key={employee?.employee_code}
      />
    </Modal>
  );
};

export default EmployeeCompensationHistory;
