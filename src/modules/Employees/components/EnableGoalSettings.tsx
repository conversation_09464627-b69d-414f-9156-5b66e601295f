import { useMutation } from "@tanstack/react-query";
import React from "react";
import ConfirmationModal from "src/modules/Common/Modal/ConfirmationModal";
import { TransformedEmployee } from "src/services/api_definitions/employees";
import performanceManagementService from "src/services/performanceManagement.service";

interface EnableGoalSettingsProps {
  isOpen: boolean;
  onClose: () => void;
  employee: TransformedEmployee;
}

const EnableGoalSettings: React.FC<EnableGoalSettingsProps> = ({ isOpen, employee, onClose }) => {
  const enableGoalSettingsMutation = useMutation({
    mutationKey: ["enable-goal-settings", employee.employee_code],
    mutationFn: async () => {
      return performanceManagementService.enableGoalSettingsForEmployee(employee.employee_code);
    },
    onSuccess: () => {
      onClose();
    },
  });

  const onSubmit = () => {
    enableGoalSettingsMutation.mutate();
  };

  return (
    <ConfirmationModal
      isOpen={isOpen}
      onCancel={onClose}
      onSubmit={onSubmit}
      title="Enable goal setting for the current performance cycle?"
    />
  );
};
export default EnableGoalSettings;
