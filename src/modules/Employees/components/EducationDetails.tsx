import { useQueries } from "@tanstack/react-query";
import React, { useEffect, useImperativeHandle } from "react";

import { useForm } from "src/customHooks/useForm";
import educationService, {
  GetAllInstitutesResponse,
  GetAvailableDegreesResponse,
} from "src/services/education.service";

import LoadingScreen from "../LoadingScreen";
import {
  EducationDetailsInitialValues,
  EducationDetailsformValidators,
  INPUT_FIELDS,
  form,
} from "../config/EducationDetails";
import { FormDataType, StepperComponentProps } from "../types/FormDataTypes";
import { convertListToOptions, getEnumValues, uploadEmployeeDocument } from "../utils/utils";
import { CommonFormWithAddMore } from "./CommonForm";

type Props = StepperComponentProps & {
  formData?: FormDataType[];
  disbaleFields?: string[];
  onFormComplete: (form: FormDataType[], isFormSubmit?: boolean, isSaveDraft?: boolean) => void;
  disabledInputFields?: string[][];
  disableDelete?: boolean[];
};

const queries = [
  {
    queryKey: ["get-all-institutes-details"],
    queryFn: async (): Promise<GetAllInstitutesResponse | null> => educationService.getAllInstitutes(),
    retryOnMount: false,
    refetchOnWindowFocus: false,
  },
  {
    queryKey: ["get-all-degree-details"],
    queryFn: async (): Promise<GetAvailableDegreesResponse | null> => educationService.getAvailableDegrees(),
    retryOnMount: false,
    refetchOnWindowFocus: false,
  },
];

const EducationDetails = ({
  formData,
  onFormComplete,
  formActionButton,
  setDisableNext,
  isViewOnlyMode,
  disabledInputFields,
  disableDelete,
}: Props) => {
  const { formDetails, formErrors, setFormDetail, addNewFormDetailRow, deleteFormDetails, areFormDetailsValid } =
    useForm({
      isBulk: true,
      initialState: formData || EducationDetailsInitialValues,
      validations: EducationDetailsformValidators,
    });

  useEffect(() => {
    const isStartYearLessThanEndYear = (formDetails as FormDataType[])?.every((formDetail) => {
      const startYear = formDetail[INPUT_FIELDS.START_YEAR] as string;
      const endYear = formDetail[INPUT_FIELDS.END_YEAR] as string;
      return startYear <= endYear;
    });
    setDisableNext?.(!areFormDetailsValid || !isStartYearLessThanEndYear);
  }, [formDetails, areFormDetailsValid]);

  useImperativeHandle(formActionButton, () => ({
    next: (isFormSubmit, isSaveDraft) => {
      onFormComplete(formDetails as FormDataType[], isFormSubmit, isSaveDraft);
    },
  }));

  const fetchData = useQueries({ queries: queries });
  const [
    { data: instituteDetailsList, isLoading: instituteDetailsLoading },
    { data: degreeDetailsList, isLoading: degreeDetailsLoading },
  ] = fetchData;
  const { data: degreeTypeDetailsList, isLoading: degreeTypeDetailsLoading } = getEnumValues("DegreeType");

  const onFileChange = async (acceptedFiles: File[], index: number) => {
    const document = await uploadEmployeeDocument(acceptedFiles, "Educational");
    if (document) {
      setFormDetail(INPUT_FIELDS.DOCUMENT, document, index);
    }
  };

  const onChange = (index: number, fieldName: string, value: unknown) => {
    setFormDetail(fieldName, value, index);
    switch (fieldName) {
      case INPUT_FIELDS.START_YEAR: {
        setFormDetail(INPUT_FIELDS.END_YEAR_MIN_DATE, value, index);
        break;
      }
      case INPUT_FIELDS.END_YEAR: {
        setFormDetail(INPUT_FIELDS.START_YEAR_MAX_DATE, value, index);
        break;
      }
      case INPUT_FIELDS.DOCUMENT: {
        if (value) onFileChange(value as File[], index);
        break;
      }
      default:
        break;
    }
  };

  if (instituteDetailsLoading || degreeDetailsLoading || degreeTypeDetailsLoading) return <LoadingScreen />;

  const selectOptions = {
    [INPUT_FIELDS.INSTITUTE]: instituteDetailsList || [],
    [INPUT_FIELDS.DEGREE]: degreeDetailsList || [],
    [INPUT_FIELDS.DEGREE_TYPE]: convertListToOptions(degreeTypeDetailsList),
  };

  const disabledInputFieldsObjectList = disabledInputFields?.map((disabledInputField) => {
    return disabledInputField.reduce(
      (acc, field) => {
        return { ...acc, [field]: true };
      },
      {} as Record<string, boolean>,
    );
  });

  const disabledInputFieldsObjectListAll = (formDetails as FormDataType[]).map((formDetail, index) => ({
    ...disabledInputFieldsObjectList?.[index],
    [INPUT_FIELDS.DOCUMENT]:
      !formDetail[INPUT_FIELDS.DEGREE] || !!disabledInputFieldsObjectList?.[index]?.[INPUT_FIELDS.DOCUMENT],
  }));

  return (
    <CommonFormWithAddMore
      onChange={onChange}
      inputElements={form}
      selectOptions={selectOptions}
      isViewOnlyMode={isViewOnlyMode}
      formErrors={formErrors as Record<string, string>[]}
      formValues={formDetails as Record<string, unknown>[]}
      onAddMoreClick={() => addNewFormDetailRow(EducationDetailsInitialValues)}
      onDeleteClick={deleteFormDetails}
      disabledInputFields={disabledInputFieldsObjectListAll}
      disableDelete={disableDelete}
    />
  );
};

export default EducationDetails;
