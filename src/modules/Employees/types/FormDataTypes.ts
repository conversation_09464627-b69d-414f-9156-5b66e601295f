import { TextFieldProps } from "@mui/material";
import { SelectInputProps } from "@mui/material/Select/SelectInput";
import { DateView } from "@mui/x-date-pickers/models/views.d";
import React from "react";

type DocumentType = {
  name: string;
  s3_url: string;
  document_type: string;
};

export type FormDataType = Record<string, unknown>;
export type FormDataTypeString = Record<string, string>;
export type FormDataTypeDocument = Record<string, DocumentType>;

export type FormInputOption = {
  value: string | boolean | number;
  label: string;
};

export interface FormInputType {
  name: string;
  label: string;
  sublabel?: string;
  variant:
    | "text"
    | "search"
    | "select"
    | "section"
    | "autocomplete"
    | "file"
    | "date"
    | "phone"
    | "multi-select"
    | "date-range"
    | "checkbox"
    | "switch"
    | "textarea"
    | "number"
    | "password"
    | "email"
    | "radio"
    | "custom"
    | null;
  placeholder?: string;
  isRequired: boolean;
  isHidden?: boolean;
  views?: DateView[];
  rows?: number;
  xs?: number;
  height?: number | string;
  width?: number | string;
  maxDate?: string;
  minDate?: string;
  acceptFileTypes?: Record<string, string[]>;
  documentType?: string;
  props?: TextFieldProps | SelectInputProps;
}

export interface PhoneNumberType {
  countryCode: string;
  number: string;
}

export interface CustomInputElement {
  next?: (isFormSubmit?: boolean, isSaveDraft?: boolean) => void;
  submit?: () => void;
}

export type StepperComponentProps = {
  formActionButton?: React.RefObject<CustomInputElement>;
  setDisableNext?: (value: boolean) => void;
  isViewOnlyMode?: boolean;
};
