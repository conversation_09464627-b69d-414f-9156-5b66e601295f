export type BandLevelGrade = {
  isBandExist: boolean;
  isGradeExist: boolean;
  isLevelExist: boolean;
};

export type JobTitleList = {
  name: string;
  band: string;
  level: string;
  grade: string;
  formatted_name: string;
}[];

export type WorkRoleHierarchyList = {
  isBand: boolean;
  isBandGrade: boolean;
  isBandLevel: boolean;
  isBandLevelGrade: boolean;
  isLevel: boolean;
  isLevelGrade: boolean;
  isGrade: boolean;
  bandOptions: { name: string }[] | null;
  levelOptions: { name: string; bandName: string }[] | null;
  gradeOptions: { name: string; levelName: string; bandName: string }[] | null;
};

export type BusinessDepartmentList = {
  cost_center: any;
  description: any;
  name: string;
}[];
