import { Box } from "@mui/material";
import React, { useEffect, useState } from "react";
import {
  BankDetailsIcon,
  DocumentsIcon,
  EducationDetailsIcon,
  EmergencyDetailsIcon,
  EmploymentDetailsIcon,
  EmploymentHistoryIcon,
  FamilyDetailsIcon,
  PersonalInformationIcon,
} from "src/assets/icons.svg";

import Compensation from "./Compensation/Compensation";
import {
  BankDetails,
  EducationDetails,
  EmergencyDetails,
  EmploymentDetails,
  EmploymentHistory,
  FamilyDetails,
  PersonalInformation,
} from "./components";
import DocumentForm from "./components/DocumentUpload";
import StepperForm, { Steps } from "./components/StepperForm";
import {
  BankDetailsInitialValues,
  EducationDetailsInitialValues,
  EmergencyDetailsInitialValues,
  EmploymentDetailsInitialValues,
  EmploymentHistoryInitialValues,
  FamilyDetailsInitialValues,
  PersonalInformationInitialValues,
} from "./config";
import { FormDataType } from "./types/FormDataTypes";

const containerStyle = {
  display: "flex",
  flexDirection: "column",
  alignItems: "center",
  width: "1200px",
  height: "calc(90vh - 161px)",
  padding: "0",
};

export const stepsKey = {
  personalInformation: "personalInformation",
  employementDetails: "employementDetails",
  employmentHistory: "employmentHistory",
  bankDetails: "bankDetails",
  familyDetails: "familyDetails",
  emergencyDetails: "emergencyDetails",
  educationDetails: "educationDetails",
  documentUpload: "documentUpload",
  employeeCompensation: "employeeCompensation",
};

export type CandidateGlobalFormType = {
  [key in string]: FormDataType[];
};

const globalForm = {
  [stepsKey.personalInformation]: PersonalInformationInitialValues,
  [stepsKey.employementDetails]: EmploymentDetailsInitialValues,
  [stepsKey.employeeCompensation]: EmploymentDetailsInitialValues,
  [stepsKey.employmentHistory]: EmploymentHistoryInitialValues,
  [stepsKey.bankDetails]: BankDetailsInitialValues,
  [stepsKey.familyDetails]: FamilyDetailsInitialValues,
  [stepsKey.emergencyDetails]: EmergencyDetailsInitialValues,
  [stepsKey.educationDetails]: EducationDetailsInitialValues,
};

export type StepsProps = {
  [key: string]: {
    disabledInputFields?: string[] | string[][];
    disableDelete?: boolean[]; // for add more forms
    isSkip?: boolean;
    isViewOnlyMode?: boolean;
  };
};

type Props = {
  initialForm?: CandidateGlobalFormType;
  steps: string[];
  onSubmit: (finalForm: CandidateGlobalFormType, isSaveDraft?: boolean) => void;
  stepsProps?: StepsProps;
  isViewOnlyMode?: boolean;
  isSaveDraft?: boolean;
  isLoading?: boolean;
};

const EmployeeStepper = ({
  onSubmit,
  initialForm,
  steps,
  stepsProps = {},
  isViewOnlyMode,
  isSaveDraft,
  isLoading,
}: Props) => {
  const [globalFormData, setGlobalFormData] = useState<CandidateGlobalFormType>(initialForm || globalForm);

  useEffect(() => {
    if (initialForm) {
      setGlobalFormData(initialForm);
    }
  }, [initialForm]);

  const lastStepKey = steps[steps.length - 1];
  const onFormComplete = (stepsKey: string, form: FormDataType[], isFormSubmit = false, isSaveDraft = false) => {
    setGlobalFormData((prev) => ({
      ...prev,
      [stepsKey]: form,
    }));
    if ((isFormSubmit && stepsKey === lastStepKey) || isSaveDraft) {
      const finalForm = {
        ...globalFormData,
        [stepsKey]: form,
      };
      onSubmit(finalForm, isSaveDraft);
    }
  };

  const allSteps: Steps = [
    {
      key: stepsKey.personalInformation,
      heading: "Personal Information",
      icon: PersonalInformationIcon,
      isSkip: stepsProps[stepsKey.personalInformation]?.isSkip,
      isViewOnlyMode: stepsProps[stepsKey.personalInformation]?.isViewOnlyMode,
      component: (props) => (
        <PersonalInformation
          formData={globalFormData[stepsKey.personalInformation]}
          onFormComplete={(form, isFormSubmit, isSaveDraft) =>
            onFormComplete(stepsKey.personalInformation, [form], isFormSubmit, isSaveDraft)
          }
          disabledInputFields={stepsProps[stepsKey.personalInformation]?.disabledInputFields as string[]}
          {...props}
        />
      ),
    },
    {
      key: stepsKey.employementDetails,
      heading: "Employment Details",
      icon: EmploymentDetailsIcon,
      isSkip: stepsProps[stepsKey.employementDetails]?.isSkip,
      isViewOnlyMode: stepsProps[stepsKey.employementDetails]?.isViewOnlyMode,
      component: (props) => (
        <EmploymentDetails
          formData={globalFormData[stepsKey.employementDetails]}
          onFormComplete={(form, isFormSubmit, isSaveDraft) =>
            onFormComplete(stepsKey.employementDetails, [form], isFormSubmit, isSaveDraft)
          }
          disabledInputFields={stepsProps[stepsKey.employementDetails]?.disabledInputFields as string[]}
          {...props}
        />
      ),
    },
    {
      key: stepsKey.employeeCompensation,
      heading: "Employee Compensation",
      icon: EmploymentDetailsIcon,
      isSkip: stepsProps[stepsKey.employeeCompensation]?.isSkip,
      isViewOnlyMode: stepsProps[stepsKey.employeeCompensation]?.isViewOnlyMode,
      component: (props) => (
        <Compensation
          formData={globalFormData[stepsKey.employeeCompensation] as any}
          onFormComplete={(form, isFormSubmit, isSaveDraft) =>
            onFormComplete(stepsKey.employeeCompensation, form, isFormSubmit, isSaveDraft)
          }
          globalFormData={globalFormData}
          initialFormState={initialForm?.[stepsKey.employeeCompensation] || ({} as any)}
          disabledInputFields={stepsProps[stepsKey.employeeCompensation]?.disabledInputFields as string[] as any}
          {...props}
        />
      ),
    },
    {
      key: stepsKey.employmentHistory,
      heading: "Employment History",
      icon: EmploymentHistoryIcon,
      // isSkip: stepsProps[stepsKey.employmentHistory]?.isSkip,
      isViewOnlyMode: stepsProps[stepsKey.employmentHistory]?.isViewOnlyMode,
      component: (props) => (
        <EmploymentHistory
          formData={globalFormData[stepsKey.employmentHistory]}
          onFormComplete={(form, isFormSubmit, isSaveDraft) =>
            onFormComplete(stepsKey.employmentHistory, form, isFormSubmit, isSaveDraft)
          }
          {...props}
        />
      ),
    },
    {
      key: stepsKey.bankDetails,
      heading: "Bank Details",
      icon: BankDetailsIcon,
      isSkip: stepsProps[stepsKey.bankDetails]?.isSkip,
      isViewOnlyMode: stepsProps[stepsKey.bankDetails]?.isViewOnlyMode,
      component: (props) => (
        <BankDetails
          formData={globalFormData[stepsKey.bankDetails]}
          onFormComplete={(form, isFormSubmit, isSaveDraft) =>
            onFormComplete(stepsKey.bankDetails, [form], isFormSubmit, isSaveDraft)
          }
          {...props}
        />
      ),
    },
    {
      key: stepsKey.familyDetails,
      heading: "Family Details",
      icon: FamilyDetailsIcon,
      // isSkip: stepsProps[stepsKey.familyDetails]?.isSkip,
      isViewOnlyMode: stepsProps[stepsKey.familyDetails]?.isViewOnlyMode,
      component: (props) => (
        <FamilyDetails
          formData={globalFormData[stepsKey.familyDetails]}
          onFormComplete={(form, isFormSubmit, isSaveDraft) =>
            onFormComplete(stepsKey.familyDetails, form, isFormSubmit, isSaveDraft)
          }
          {...props}
        />
      ),
    },
    {
      key: stepsKey.emergencyDetails,
      heading: "Emergency Details",
      icon: EmergencyDetailsIcon,
      isSkip: stepsProps[stepsKey.emergencyDetails]?.isSkip,
      isViewOnlyMode: stepsProps[stepsKey.emergencyDetails]?.isViewOnlyMode,
      component: (props) => (
        <EmergencyDetails
          formData={globalFormData[stepsKey.emergencyDetails]}
          onFormComplete={(form, isFormSubmit, isSaveDraft) =>
            onFormComplete(stepsKey.emergencyDetails, form, isFormSubmit, isSaveDraft)
          }
          {...props}
        />
      ),
    },
    {
      key: stepsKey.educationDetails,
      heading: "Education Details",
      icon: EducationDetailsIcon,
      isSkip: stepsProps[stepsKey.educationDetails]?.isSkip,
      isViewOnlyMode: stepsProps[stepsKey.educationDetails]?.isViewOnlyMode,
      component: (props) => (
        <EducationDetails
          formData={globalFormData[stepsKey.educationDetails]}
          onFormComplete={(form, isFormSubmit, isSaveDraft) =>
            onFormComplete(stepsKey.educationDetails, form, isFormSubmit, isSaveDraft)
          }
          disabledInputFields={stepsProps[stepsKey.educationDetails]?.disabledInputFields as string[][]}
          disableDelete={stepsProps[stepsKey.educationDetails]?.disableDelete}
          {...props}
        />
      ),
    },
    {
      key: stepsKey.documentUpload,
      heading: "Document Upload",
      icon: DocumentsIcon,
      isSkip: stepsProps[stepsKey.documentUpload]?.isSkip,
      isViewOnlyMode: stepsProps[stepsKey.documentUpload]?.isViewOnlyMode,
      component: (props) => (
        <DocumentForm
          formData={globalFormData[stepsKey.documentUpload]}
          onFormComplete={(form, isFormSubmit, isSaveDraft) =>
            onFormComplete(stepsKey.documentUpload, [form], isFormSubmit, isSaveDraft)
          }
          disabledInputFields={stepsProps[stepsKey.documentUpload]?.disabledInputFields as string[]}
          {...props}
        />
      ),
    },
  ];
  const filteredSteps = allSteps.filter((step) => steps.includes(step.key));
  return (
    <Box sx={containerStyle}>
      <StepperForm
        isLoading={isLoading}
        steps={filteredSteps}
        isViewOnlyMode={isViewOnlyMode}
        isSaveDraft={isSaveDraft}
      />
    </Box>
  );
};

export default EmployeeStepper;
