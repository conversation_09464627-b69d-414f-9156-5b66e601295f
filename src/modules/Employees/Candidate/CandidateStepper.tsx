import { useMutation, useQueries } from "@tanstack/react-query";
import React, { useCallback, useMemo, useRef } from "react";
import { BaseObject } from "src/app/global";
import { useAppSelector } from "src/customHooks/useAppSelector";
import employeesService from "src/services/employees.service";

import EmployeeStepper, { CandidateGlobalFormType, stepsKey } from "../EmployeeStepper";
import LoadingScreen from "../LoadingScreen";
import { INPUT_FIELDS as personalInformationInputFields } from "../config/PersonalInformation";
import {
  CandidateDetails,
  transformCandidateDetails,
  transformUpdateCandidateDetails,
} from "../utils/candidateTransformer";

type Props = {
  setFinishForm: (value: boolean) => void;
};

const stepsProps = {
  [stepsKey.personalInformation]: {
    disabledInputFields: [
      personalInformationInputFields.FIRST_NAME,
      personalInformationInputFields.LAST_NAME,
      personalInformationInputFields.PERSONAL_EMAIL,
    ],
  },
  [stepsKey.employmentHistory]: {
    isSkip: true,
  },
};

const steps: string[] = [
  stepsKey.personalInformation,
  stepsKey.employmentHistory,
  stepsKey.bankDetails,
  stepsKey.familyDetails,
  stepsKey.emergencyDetails,
  stepsKey.educationDetails,
  stepsKey.documentUpload,
];

const CandidateStepper = ({ setFinishForm }: Props) => {
  const { userDetails } = useAppSelector((state) => state.userManagement);
  const result = useQueries({
    queries: [
      {
        queryKey: ["get-candidate-details"],
        queryFn: async (): Promise<CandidateDetails | null> => employeesService.getCandidateDetails(),
        retryOnMount: false,
        refetchOnWindowFocus: false,
        enabled: userDetails?.organisations?.length > 0,
      },
    ],
  });
  const [{ isLoading: isCandidateDetailsLoading, data: candidateDetails, refetch: refetchCandidateDetails }] = result;

  const mutation = useMutation({
    mutationKey: ["update-candidate-details"],
    mutationFn: async (candidateDetails: BaseObject) => employeesService.updateCandidateDetails(candidateDetails),
    onSuccess: (response) => {
      if (response) {
        setFinishForm(true);
      }
    },
  });

  const saveDraftMutation = useMutation({
    mutationKey: ["save-draft-candidate-details"],
    mutationFn: async (candidateDetails: BaseObject) => employeesService.draftCandidateDetails(candidateDetails),
    onSuccess: (response) => {
      if (response) {
        refetchCandidateDetails();
      }
    },
  });

  const handleFinish = useCallback(
    (finalForm: CandidateGlobalFormType, isSaveDraft?: boolean) => {
      if (isSaveDraft) {
        saveDraftMutation.mutate(transformUpdateCandidateDetails(finalForm));
      } else {
        mutation.mutate(transformUpdateCandidateDetails(finalForm));
      }
    },
    [mutation, saveDraftMutation],
  );

  const initialForm = useMemo(() => transformCandidateDetails(candidateDetails), [candidateDetails]);

  if (isCandidateDetailsLoading || !candidateDetails) {
    return <LoadingScreen />;
  }

  return (
    <EmployeeStepper
      steps={steps}
      onSubmit={handleFinish}
      initialForm={initialForm}
      stepsProps={stepsProps}
      isSaveDraft
    />
  );
};

export default CandidateStepper;
