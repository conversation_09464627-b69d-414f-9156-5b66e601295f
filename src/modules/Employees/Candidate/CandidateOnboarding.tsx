import { Box } from "@mui/material";
import React from "react";
import Modal from "src/modules/Common/Modal/Modal";

import { ModalControllerStyles, ModalHeader, containerStyle } from "../EmployeeModalStyles";
import CandidateStepper from "./CandidateStepper";
import FormCompleted from "./FormCompleted";
import { WelcomeScreen } from "./WelcomeScreem";

const CandidateOnboarding = () => {
  const [finishForm, setFinishForm] = React.useState(false);
  const [showWelcomeScreen, setShowWelcomeScreen] = React.useState(true);
  return (
    <Modal
      isOpen
      title={null}
      maxWidth="unset"
      onClose={() => {}}
      sx={ModalControllerStyles.root}
      PaperProps={{ style: ModalControllerStyles.paper }}
    >
      <Box sx={containerStyle}>
        {!finishForm && <ModalHeader title="New Joinee Onboarding Form" />}
        {showWelcomeScreen ? (
          <WelcomeScreen onContinue={() => setShowWelcomeScreen(false)} />
        ) : finishForm ? (
          <FormCompleted
            tittle="Submission complete"
            subtitle="Thanks for taking time to fill out this form."
            handleClose={() => {
              window.location.href = "https://effihr.com";
            }}
          />
        ) : (
          <CandidateStepper setFinishForm={setFinishForm} />
        )}
      </Box>
    </Modal>
  );
};

export default CandidateOnboarding;
