import { Box, Button, Typography } from "@mui/material";
import React from "react";
import { WelcomeIcon } from "src/assets/icons.svg";

const containerStyle = {
  display: "flex",
  flexDirection: "column",
  alignItems: "center",
  rowGap: "40px",
  width: "800px",
};

const subHeadingStyle = {
  margin: 0,
  width: 632,
  lineHeight: 1.4,
  fontWeight: 500,
  fontSize: "16px",
  color: "#767272",
  textAlign: "center",
  fontFamily: "Poppins",
  letterSpacing: "normal",
};

const continueButtonStyle = {
  display: "flex",
  flexDirection: "row",
  justifyContent: "center",
  alignItems: "center",
  gap: "8px",
  width: 456,
  height: 48,
  padding: "0 20px",
  borderRadius: "50px",
  backgroundColor: "#007f6f",
  border: "solid 1.5px #007f6f",
};

export const WelcomeScreen = ({ onContinue }: { onContinue: () => void }) => {
  return (
    <Box sx={containerStyle}>
      <Typography sx={subHeadingStyle} align="center">
        &quot;Welcome to effiHR! Let&apos;s begin by establishing the configuration of information and relationships
        between our entities to ensure smooth management.&quot;
      </Typography>
      <WelcomeIcon alt="Welcome" height={235} />
      <Button sx={continueButtonStyle} onClick={onContinue} variant="contained" color="primary">
        Continue
      </Button>
    </Box>
  );
};
