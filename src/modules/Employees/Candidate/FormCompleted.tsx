import { Box, Button, Typography } from "@mui/material";
import React from "react";
import { SuccessIcon } from "src/assets/icons.svg";
import { default as languageConfig } from "src/configs/language/en.lang";
import { FormFilledStyles } from "../components/styles/styles.module";

interface ModalProps {
  handleClose: () => void;
  tittle?: string;
  subtitle?: string;
}

const titleStyle = {
  ...FormFilledStyles.caption,
  margin: 0,
  color: "#000000",
  fontWeight: 700,
};

const FormFilled: React.FC<ModalProps> = ({ handleClose, tittle, subtitle }) => (
  <Box sx={{ width: 536, padding: "0 25px" }}>
    <Typography variant="body1" sx={titleStyle}>
      {tittle}
    </Typography>
    {subtitle && (
      <Typography variant="body1" sx={FormFilledStyles.caption}>
        {subtitle}
      </Typography>
    )}
    <Box sx={FormFilledStyles.svgContainer.root}>
      <Box sx={FormFilledStyles.svgContainer.body}>
        <SuccessIcon />
      </Box>
    </Box>
    <Button variant="contained" onClick={handleClose} sx={FormFilledStyles.button}>
      {languageConfig.employees.modals.addedEmployee.button.confirm}
    </Button>
  </Box>
);

export default FormFilled;
