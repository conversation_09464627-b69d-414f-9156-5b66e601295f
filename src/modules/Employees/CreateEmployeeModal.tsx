import { Box } from "@mui/material";
import React from "react";
import languageConfig from "src/configs/language/en.lang";
import Modal from "src/modules/Common/Modal/Modal";
import { ModalControllerStyles, containerStyle } from "src/modules/Employees/EmployeeModalStyles";

import FormCompleted from "./Candidate/FormCompleted";
import CreateEmployee from "./CreateEmployee";

type CreateEmployeeModalProps = {
  open: boolean;
  onClose: () => void;
};

const CreateEmployeeModal: React.FC<CreateEmployeeModalProps> = ({ open, onClose }) => {
  const [finishForm, setFinishForm] = React.useState(false);
  return (
    <Modal
      title={languageConfig.employees.modals.addEmployee.title}
      isOpen={open}
      onClose={onClose}
      showBackButton
      maxWidth="unset"
      sx={ModalControllerStyles.root}
      PaperProps={{ style: ModalControllerStyles.paper }}
    >
      <Box sx={containerStyle}>
        {finishForm ? (
          <FormCompleted handleClose={onClose} tittle={languageConfig.employees.modals.addedEmployee.subtitle} />
        ) : (
          <CreateEmployee setFinishForm={setFinishForm} />
        )}
      </Box>
    </Modal>
  );
};

export default CreateEmployeeModal;
