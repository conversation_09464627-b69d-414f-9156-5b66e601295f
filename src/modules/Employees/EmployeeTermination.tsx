import { Box, Button } from "@mui/material";
import List from "@mui/material/List";
import ListItem from "@mui/material/ListItem";
import { useMutation } from "@tanstack/react-query";
import React from "react";

import { BaseObject } from "src/app/global";
import Modal from "src/modules/Common/Modal/Modal";
import Span from "src/modules/Common/Span/Span";
import { TransformedEmployee } from "src/services/api_definitions/employees";
import employeesService from "src/services/employees.service";
import validators from "src/utils/validators";

import { ModalControllerStyles, containerStyle } from "./EmployeeModalStyles";
import { ActionComponentProps, CommonFormWithState } from "./components/CommonForm";
import { FormDataType, FormInputType } from "./types/FormDataTypes";

const dangerButtonStyle = {
  background: "#FF4D4D",
  color: "#fff",
  "&:hover": {
    background: "#FF4D4D",
  },
};

const cancelButtonStyle = {
  background: "#FFFFFF",
  color: "#007F6F",
  border: "1px solid #DDE1E4",
  "&:hover": {
    background: "#FFFFFF",
  },
};

const guideLines = [
  "Terminated employees are no longer employed at Circle Enterprises, for either voluntary or involuntary reasons.",
  'Terminated employees will only show in the employee directory under the "Terminated" filter.Terminated employees will be stripped of their login credentials and will no longer be able to access information',
  "Upon termination, employees will be removed from all time off policies. Any time off hours accrued will be reset to zero and any pending requests will be removed.",
  "Terminated employees will be removed from their assigned payperiod polic. Any hours or time off for the current pay period will remain in the current period.",
];

const INPUT_FIELDS = {
  LAST_DAY_OF_WORK: "last_day_of_work",
  PRIMARY_REASON: "primary_reason",
  COMMENT: "comment",
  LAST_DAY_MIN_DATE: "last_day_of_work_min_date",
};

const employeeTerminationFormInitialState: FormDataType = {
  [INPUT_FIELDS.LAST_DAY_OF_WORK]: "",
  [INPUT_FIELDS.PRIMARY_REASON]: "",
  [INPUT_FIELDS.COMMENT]: "",
  [INPUT_FIELDS.LAST_DAY_MIN_DATE]: new Date(),
};

const employeeTerminationFormValidators = {
  [INPUT_FIELDS.LAST_DAY_OF_WORK]: [validators.validateInput],
  [INPUT_FIELDS.PRIMARY_REASON]: [validators.validateInput],
  [INPUT_FIELDS.COMMENT]: [validators.validateInput],
};

const employeeTerminationForm: FormInputType[] = [
  {
    name: INPUT_FIELDS.LAST_DAY_OF_WORK,
    label: "Last day of work",
    variant: "date",
    isRequired: true,
    xs: 6,
    minDate: INPUT_FIELDS.LAST_DAY_MIN_DATE,
  },
  {
    name: INPUT_FIELDS.PRIMARY_REASON,
    label: "Primary reason",
    variant: "text",
    isRequired: true,
    xs: 6,
  },
  {
    name: INPUT_FIELDS.COMMENT,
    label: "Additional comments regarding the termination of employment",
    variant: "text",
    isRequired: true,
    xs: 12,
    rows: 3,
  },
];

type TerminationGuidlinesProps = {
  onCancel: () => void;
  onContinue: () => void;
};

type EmployeeTerminationFormProps = {
  onClose: () => void;
  onSubmit: (form: FormDataType) => void;
};

type ActionComponentOtherProps = {
  onCancel?: () => void;
};

type EmployeeTerminationProps = {
  isOpen: boolean;
  onClose: (isTerminated?: boolean) => void;
  employee: TransformedEmployee;
};

const TerminationGuidlines = ({ onCancel, onContinue }: TerminationGuidlinesProps) => {
  return (
    <Box sx={{ display: "flex", flexDirection: "column", alignItems: "center" }}>
      <Span sx={{ fontSize: "14px" }}>
        Employees who are not expected to return should be marked as terminated. The following apply when an employee is
        terminated.
      </Span>
      <List sx={{ listStyleType: "disc", paddingLeft: "24px" }}>
        {guideLines.map((item, index) => (
          <ListItem sx={{ display: "list-item", fontSize: "14px" }} key={index}>
            {item}
          </ListItem>
        ))}
      </List>
      <Box width="100%" sx={{ display: "flex", justifyContent: "flex-end", gap: 2, marginTop: "16px" }}>
        <Button variant="outlined" sx={cancelButtonStyle} onClick={onCancel}>
          Cancel
        </Button>
        <Button variant="contained" sx={dangerButtonStyle} onClick={onContinue}>
          Continue with termination
        </Button>
      </Box>
    </Box>
  );
};

const ActionComponent = ({
  areFormDetailsValid,
  onSubmit,
  onCancel,
}: ActionComponentProps & ActionComponentOtherProps) => {
  return (
    <Box sx={{ display: "flex", justifyContent: "flex-end", gap: 2, width: "100%", marginTop: "32px" }}>
      <Button variant="outlined" onClick={onCancel} sx={cancelButtonStyle}>
        Cancel
      </Button>
      <Button variant="contained" sx={dangerButtonStyle} disabled={!areFormDetailsValid} onClick={onSubmit}>
        Yes, terminate this employee
      </Button>
    </Box>
  );
};

const terminateEmployeePayload = (data: FormDataType, employeeCode: string) => {
  return {
    employee_code: employeeCode,
    date_of_separation: data[INPUT_FIELDS.LAST_DAY_OF_WORK],
    separation_reason: data[INPUT_FIELDS.PRIMARY_REASON],
    comment: data[INPUT_FIELDS.COMMENT],
  };
};

const EmployeeTerminationForm = ({ onClose, onSubmit }: EmployeeTerminationFormProps) => {
  return (
    <Box>
      <CommonFormWithState
        inputElements={employeeTerminationForm}
        onFormSubmit={(form) => onSubmit(form as FormDataType)}
        initialState={employeeTerminationFormInitialState}
        validators={employeeTerminationFormValidators}
        ActionComponent={(props) => <ActionComponent onCancel={onClose} {...props} />}
        gridStyles={{ rowSpacing: 4 }}
      />
    </Box>
  );
};

const EmployeeTermination = ({ isOpen, onClose, employee }: EmployeeTerminationProps) => {
  const [showGuidlines, setShowGuidlines] = React.useState(true);
  const mutation = useMutation({
    mutationKey: ["employee-termination"],
    mutationFn: async (employeeData: BaseObject) => employeesService.terminateEmployee(employeeData),
    onSuccess: (response) => {
      if (response) onClose(true);
    },
  });

  const terminateEmployee = (form: FormDataType) => {
    mutation.mutate(terminateEmployeePayload(form, employee.employee_code));
  };

  return (
    <Modal
      isOpen={isOpen}
      showBackButton
      title={
        showGuidlines
          ? "Employee termination"
          : `Are you sure you wish to terminate ${employee.first_name} ${employee.last_name}`
      }
      subtitle="Update your business persona"
      maxWidth="unset"
      onClose={onClose}
      sx={ModalControllerStyles.root}
      PaperProps={{ style: ModalControllerStyles.paper }}
    >
      <Box sx={containerStyle} width="780px" padding="0 16px" marginTop="-10px">
        {showGuidlines ? (
          <TerminationGuidlines onCancel={() => onClose()} onContinue={() => setShowGuidlines(false)} />
        ) : (
          <EmployeeTerminationForm onClose={onClose} onSubmit={terminateEmployee} />
        )}
      </Box>
    </Modal>
  );
};

export default EmployeeTermination;
