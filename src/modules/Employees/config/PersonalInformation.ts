import validators from "src/utils/validators";
import { FormDataType, FormInputType, PhoneNumberType } from "../types/FormDataTypes";

export const INPUT_FIELDS = {
  FIRST_NAME: "first_name",
  LAST_NAME: "last_name",
  DATE_OF_BIRTH: "date_of_birth",
  PERSONAL_EMAIL: "personal_email",
  MOBILE_NO: "phone",
  UAN: "uan",
  PAN: "pan",
  PASSPORT: "passport",
  AADHAAR: "aadhaar",
  NATIONALITY: "nationality",
  BLOOD_GROUP: "blood_group",
  MARITAL_STATUS: "marital_status",
  GENDER: "gender",
  HOUSE: "current_address.address_line1",
  STREET: "current_address.address_line2",
  CITY: "current_address.city",
  STATE: "current_address.state",
  COUNTRY: "current_address.country",
  ZIPCODE: "current_address.zip_code",
  PERMANENT_HOUSE: "permanent_address.address_line1",
  PERMANENT_STREET: "permanent_address.address_line2",
  PERMANENT_CITY: "permanent_address.city",
  PERMANENT_STATE: "permanent_address.state",
  PERMANENT_COUNTRY: "permanent_address.country",
  PERMANENT_ZIPCODE: "permanent_address.zip_code",
};

export const PersonalInformationInitialValues: FormDataType[] = [
  {
    [INPUT_FIELDS.FIRST_NAME]: "",
    [INPUT_FIELDS.LAST_NAME]: "",
    [INPUT_FIELDS.DATE_OF_BIRTH]: "",
    [INPUT_FIELDS.PERSONAL_EMAIL]: "",
    [INPUT_FIELDS.MOBILE_NO]: { number: "", countryCode: "" } as PhoneNumberType,
    [INPUT_FIELDS.UAN]: "",
    [INPUT_FIELDS.PAN]: "",
    [INPUT_FIELDS.PASSPORT]: "",
    [INPUT_FIELDS.AADHAAR]: "",
    [INPUT_FIELDS.NATIONALITY]: "",
    [INPUT_FIELDS.BLOOD_GROUP]: "",
    [INPUT_FIELDS.MARITAL_STATUS]: "",
    [INPUT_FIELDS.GENDER]: "",
    [INPUT_FIELDS.HOUSE]: "",
    [INPUT_FIELDS.STREET]: "",
    [INPUT_FIELDS.CITY]: "",
    [INPUT_FIELDS.STATE]: "",
    [INPUT_FIELDS.COUNTRY]: "",
    [INPUT_FIELDS.ZIPCODE]: "",
    [INPUT_FIELDS.PERMANENT_HOUSE]: "",
    [INPUT_FIELDS.PERMANENT_STREET]: "",
    [INPUT_FIELDS.PERMANENT_CITY]: "",
    [INPUT_FIELDS.PERMANENT_STATE]: "",
    [INPUT_FIELDS.PERMANENT_COUNTRY]: "",
    [INPUT_FIELDS.PERMANENT_ZIPCODE]: "",
  },
];

export const PersonalInformationFormValidators = {
  [INPUT_FIELDS.FIRST_NAME]: [validators.validateInput, validators.validateName],
  [INPUT_FIELDS.LAST_NAME]: [validators.validateInput, validators.validateName],
  [INPUT_FIELDS.DATE_OF_BIRTH]: [validators.validateInput],
  [INPUT_FIELDS.PERSONAL_EMAIL]: [validators.validateInput, validators.validateEmail],
  [INPUT_FIELDS.MOBILE_NO]: [validators.validateInput, validators.validatePhone],
  [INPUT_FIELDS.UAN]: [],
  [INPUT_FIELDS.PAN]: [validators.validateInput, validators.validatePAN],
  [INPUT_FIELDS.PASSPORT]: [],
  [INPUT_FIELDS.AADHAAR]: [validators.validateInput, validators.validateAadhar],
  [INPUT_FIELDS.NATIONALITY]: [validators.validateInput],
  [INPUT_FIELDS.BLOOD_GROUP]: [validators.validateInput],
  [INPUT_FIELDS.MARITAL_STATUS]: [validators.validateInput],
  [INPUT_FIELDS.GENDER]: [validators.validateInput],
  [INPUT_FIELDS.HOUSE]: [validators.validateInput],
  [INPUT_FIELDS.STREET]: [],
  [INPUT_FIELDS.CITY]: [validators.validateInput],
  [INPUT_FIELDS.STATE]: [validators.validateInput],
  [INPUT_FIELDS.COUNTRY]: [validators.validateInput],
  [INPUT_FIELDS.ZIPCODE]: [validators.validateInput, validators.validateZipCode],
  [INPUT_FIELDS.PERMANENT_HOUSE]: [],
  [INPUT_FIELDS.PERMANENT_STREET]: [],
  [INPUT_FIELDS.PERMANENT_CITY]: [],
  [INPUT_FIELDS.PERMANENT_STATE]: [],
  [INPUT_FIELDS.PERMANENT_COUNTRY]: [],
  [INPUT_FIELDS.PERMANENT_ZIPCODE]: [],
};

export const form: FormInputType[] = [
  {
    name: INPUT_FIELDS.FIRST_NAME,
    label: "First Name",
    variant: "text",
    placeholder: "First Name",
    isRequired: true,
  },
  {
    name: INPUT_FIELDS.LAST_NAME,
    label: "Last Name",
    variant: "text",
    placeholder: "Last Name",
    isRequired: true,
  },
  {
    name: INPUT_FIELDS.DATE_OF_BIRTH,
    label: "Date of Birth",
    variant: "date",
    maxDate: "",
    placeholder: "XX-XX-20XX",
    isRequired: true,
  },
  {
    name: INPUT_FIELDS.PERSONAL_EMAIL,
    label: "Personal Email",
    variant: "text",
    placeholder: "<EMAIL>",
    isRequired: true,
  },
  {
    name: INPUT_FIELDS.MOBILE_NO,
    label: "Mobile",
    variant: "phone",
    placeholder: "e.g. 879959XXXX",
    isRequired: true,
  },
  {
    name: INPUT_FIELDS.UAN,
    label: "UAN",
    variant: "text",
    placeholder: "Enter your UAN Number",
    isRequired: false,
  },
  {
    name: INPUT_FIELDS.PAN,
    label: "PAN",
    variant: "text",
    placeholder: "Enter your PAN Number",
    isRequired: true,
  },
  {
    name: INPUT_FIELDS.PASSPORT,
    label: "Passport",
    variant: "text",
    placeholder: "Enter your passport Id",
    isRequired: false,
  },
  {
    name: INPUT_FIELDS.AADHAAR,
    label: "Aadhaar",
    variant: "text",
    placeholder: "Enter your Aadhaar Number",
    isRequired: true,
  },
  {
    name: INPUT_FIELDS.NATIONALITY,
    label: "Nationality",
    variant: "select",
    isRequired: true,
  },
  {
    name: INPUT_FIELDS.BLOOD_GROUP,
    label: "Blood Group",
    variant: "select",
    isRequired: true,
  },
  {
    name: INPUT_FIELDS.MARITAL_STATUS,
    label: "Marital Status",
    variant: "select",
    isRequired: true,
  },
  {
    name: INPUT_FIELDS.GENDER,
    label: "Gender",
    variant: "select",
    isRequired: true,
  },
];

export const addressForm: FormInputType[] = [
  {
    name: INPUT_FIELDS.HOUSE,
    label: "Address Line 1",
    variant: "text",
    placeholder: "House",
    isRequired: true,
  },
  {
    name: INPUT_FIELDS.STREET,
    label: "Address Line 2",
    variant: "text",
    placeholder: "Street",
    isRequired: false,
  },
  {
    name: INPUT_FIELDS.ZIPCODE,
    label: "Zip code",
    variant: "text",
    placeholder: "Zip code",
    isRequired: true,
  },
  {
    name: INPUT_FIELDS.CITY,
    label: "City",
    variant: "text",
    placeholder: "City",
    isRequired: true,
  },
  {
    name: INPUT_FIELDS.STATE,
    label: "State",
    variant: "text",
    placeholder: "State",
    isRequired: true,
  },
  {
    name: INPUT_FIELDS.COUNTRY,
    label: "Country",
    variant: "text",
    placeholder: "Country",
    isRequired: true,
  },
];

export const permanentAdressForm: FormInputType[] = [
  {
    name: INPUT_FIELDS.PERMANENT_HOUSE,
    label: "Address Line 1",
    variant: "text",
    placeholder: "House",
    isRequired: false,
  },
  {
    name: INPUT_FIELDS.PERMANENT_STREET,
    label: "Address Line 2",
    variant: "text",
    placeholder: "Street",
    isRequired: false,
  },
  {
    name: INPUT_FIELDS.PERMANENT_ZIPCODE,
    label: "Zip code",
    variant: "text",
    placeholder: "Zip code",
    isRequired: false,
  },
  {
    name: INPUT_FIELDS.PERMANENT_CITY,
    label: "City",
    variant: "text",
    placeholder: "City",
    isRequired: false,
  },
  {
    name: INPUT_FIELDS.PERMANENT_STATE,
    label: "State",
    variant: "text",
    placeholder: "State",
    isRequired: false,
  },
  {
    name: INPUT_FIELDS.PERMANENT_COUNTRY,
    label: "Country",
    variant: "text",
    placeholder: "Country",
    isRequired: false,
  },
];
