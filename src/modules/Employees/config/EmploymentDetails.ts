import validators from "src/utils/validators";
import { FormInputType } from "../types/FormDataTypes";

export const INPUT_FIELDS = {
  EMPLOYEE_TYPE: "employee_type",
  DATE_OF_JOINING: "date_of_joining",
  OFFICIAL_EMAIL: "email",
  MANAGER: "manager.email",
  OFFICE_ADDRESS: "location",
  OFFICE_ADDRESS_LINE_1: "location.address_line_1",
  OFFICE_ADDRESS_LINE_2: "location.address_line_2",
  OFFICE_CITY: "location.city",
  OFFICE_STATE: "location.state",
  OFFICE_COUNTRY: "location.country",
  OFFICE_ZIP: "location.zip",
  HRBP: "hrbp",
  DEPARTMENT: "job_title.department.name",
  BUSINESS: "job_title.department.business_unit.name",
  JOB_TITLE: "job_title.name",
  JOB_TITLE_FORMATTED_NAME: "job_title.formatted_name",
  BAND: "job_title.work_role.band.name",
  GRADE: "job_title.work_role.grade.name",
  LEVEL: "job_title.work_role.level.name",

  //empty placeholder
  BAND_PLACEHOLDER: "band_placeholder",
  LEVEL_PLACEHOLDER: "level_placeholder",
  GRADE_PLACEHOLDER: "grade_placeholder",
};

export const EmploymentDetailsInitialValues = [
  {
    [INPUT_FIELDS.EMPLOYEE_TYPE]: "",
    [INPUT_FIELDS.DATE_OF_JOINING]: "",
    [INPUT_FIELDS.OFFICIAL_EMAIL]: "",
    [INPUT_FIELDS.MANAGER]: "",
    [INPUT_FIELDS.OFFICE_ADDRESS]: "",
    [INPUT_FIELDS.HRBP]: "",
    [INPUT_FIELDS.DEPARTMENT]: "",
    [INPUT_FIELDS.BUSINESS]: "",
    [INPUT_FIELDS.JOB_TITLE]: "",
    [INPUT_FIELDS.BAND]: "",
    [INPUT_FIELDS.GRADE]: "",
    [INPUT_FIELDS.LEVEL]: "",
  },
];

export const EmploymentDetailsformValidators = {
  [INPUT_FIELDS.EMPLOYEE_TYPE]: [validators.validateInput],
  [INPUT_FIELDS.DATE_OF_JOINING]: [validators.validateInput],
  [INPUT_FIELDS.OFFICIAL_EMAIL]: [validators.validateInput, validators.validateEmail],
  [INPUT_FIELDS.MANAGER]: [validators.validateInput, validators.validateEmail],
  [INPUT_FIELDS.OFFICE_ADDRESS]: [validators.validateInput],
  [INPUT_FIELDS.HRBP]: [validators.validateInput, validators.validateEmail],
  [INPUT_FIELDS.DEPARTMENT]: [validators.validateInput],
  [INPUT_FIELDS.BUSINESS]: [validators.validateInput],
  [INPUT_FIELDS.JOB_TITLE]: [validators.validateInput],
  [INPUT_FIELDS.BAND]: [validators.validateInput],
  [INPUT_FIELDS.GRADE]: [validators.validateInput],
  [INPUT_FIELDS.LEVEL]: [validators.validateInput],
};

export const getEmptyOption = (fieldName: string) => {
  switch (fieldName) {
    case INPUT_FIELDS.BUSINESS:
      return { value: "", label: "Select Business Unit" };
    case INPUT_FIELDS.DEPARTMENT:
      return { value: "", label: "Select Department" };
    case INPUT_FIELDS.BAND:
      return { value: "", label: "Select Band" };
    case INPUT_FIELDS.LEVEL:
      return { value: "", label: "Select Level" };
    case INPUT_FIELDS.GRADE:
      return { value: "", label: "Select Grade" };
    case INPUT_FIELDS.JOB_TITLE:
      return { value: "", label: "Select Job Title" };
    case INPUT_FIELDS.EMPLOYEE_TYPE:
      return { value: "", label: "Select Employment Type" };
    default:
      return { value: "", label: "Select" };
  }
};

export const dependentFields = [
  INPUT_FIELDS.BUSINESS,
  INPUT_FIELDS.DEPARTMENT,
  INPUT_FIELDS.BAND,
  INPUT_FIELDS.LEVEL,
  INPUT_FIELDS.GRADE,
  INPUT_FIELDS.JOB_TITLE,
];

export const form: FormInputType[] = [
  {
    name: INPUT_FIELDS.BUSINESS,
    label: "Business Unit",
    variant: "select",
    isRequired: true,
  },
  {
    name: INPUT_FIELDS.DEPARTMENT,
    label: "Department",
    variant: "select",
    isRequired: true,
  },
  {
    name: INPUT_FIELDS.BAND,
    label: "Band",
    variant: "select",
    placeholder: "Select Band",
    isRequired: true,
  },

  {
    name: INPUT_FIELDS.LEVEL,
    label: "Level",
    variant: "select",
    isRequired: true,
  },
  {
    name: INPUT_FIELDS.GRADE,
    label: "Grade",
    variant: "select",
    isRequired: true,
  },
  {
    name: INPUT_FIELDS.JOB_TITLE,
    label: "Job Title",
    variant: "select",
    isRequired: true,
  },
  {
    name: INPUT_FIELDS.BAND_PLACEHOLDER,
    label: "",
    variant: null,
    isRequired: false,
  },
  {
    name: INPUT_FIELDS.LEVEL_PLACEHOLDER,
    label: "",
    variant: null,
    isRequired: false,
  },
  {
    name: INPUT_FIELDS.GRADE_PLACEHOLDER,
    label: "",
    variant: null,
    isRequired: false,
  },
  {
    name: INPUT_FIELDS.DATE_OF_JOINING,
    label: "Date of Joining",
    variant: "date",
    placeholder: "XX-XX-20XX",
    isRequired: true,
  },
  {
    name: INPUT_FIELDS.EMPLOYEE_TYPE,
    label: "Employment Type",
    variant: "select",
    isRequired: true,
  },
  {
    name: INPUT_FIELDS.OFFICIAL_EMAIL,
    label: "Official Email",
    variant: "text",
    placeholder: "<EMAIL>",
    isRequired: true,
  },
  {
    name: INPUT_FIELDS.MANAGER,
    label: "Manager",
    variant: "search",
    placeholder: "Search Manager",
    isRequired: true,
  },
  {
    name: INPUT_FIELDS.HRBP,
    label: "HRBP",
    variant: "search",
    placeholder: "Search HRBP",
    isRequired: true,
  },
  {
    name: INPUT_FIELDS.OFFICE_ADDRESS,
    label: "Office address",
    variant: "select",
    placeholder: "Enter your Job Location",
    isRequired: true,
    xs: 8,
  },
];

export const EmploymentDetailsformStateTransformer = (
  formState: typeof EmploymentDetailsInitialValues,
  band_level_grade: null | { isBandExist: boolean; isLevelExist: boolean; isGradeExist: boolean },
) => {
  if (!band_level_grade) return formState;
  if (!band_level_grade.isBandExist) delete formState[0][INPUT_FIELDS.BAND];
  if (!band_level_grade.isLevelExist) delete formState[0][INPUT_FIELDS.LEVEL];
  if (!band_level_grade.isGradeExist) delete formState[0][INPUT_FIELDS.GRADE];
  return formState;
};

export const EmploymentDetailsformValidatorTransformer = (
  formValidator: typeof EmploymentDetailsformValidators,
  band_level_grade: null | { isBandExist: boolean; isLevelExist: boolean; isGradeExist: boolean },
) => {
  if (!band_level_grade) return formValidator;
  if (!band_level_grade.isBandExist) delete formValidator[INPUT_FIELDS.BAND];
  if (!band_level_grade.isLevelExist) delete formValidator[INPUT_FIELDS.LEVEL];
  if (!band_level_grade.isGradeExist) delete formValidator[INPUT_FIELDS.GRADE];
  return formValidator;
};
