import validators from "src/utils/validators";
import { FormInputType } from "../types/FormDataTypes";

export const INPUT_FIELDS = {
  INSTITUTE: "education_details.institute",
  UNIVERSITY: "education_details.university",
  DEGREE: "education_details.degree",
  DEGREE_TYPE: "education_details.degree_type",
  START_YEAR: "education_details.start_year",
  END_YEAR: "education_details.end_year",
  DOCUMENT: "education_details.employee_document",
  START_YEAR_MAX_DATE: "start_year_max_date",
  END_YEAR_MIN_DATE: "end_year_min_date",
};

export const EducationDetailsInitialValues = [
  {
    [INPUT_FIELDS.INSTITUTE]: "",
    [INPUT_FIELDS.UNIVERSITY]: "",
    [INPUT_FIELDS.DEGREE]: "",
    [INPUT_FIELDS.START_YEAR]: "",
    [INPUT_FIELDS.END_YEAR]: "",
    [INPUT_FIELDS.DEGREE_TYPE]: "",
    [INPUT_FIELDS.DOCUMENT]: "",
  },
];

export const EducationDetailsformValidators = {
  [INPUT_FIELDS.INSTITUTE]: [validators.validateInput],
  [INPUT_FIELDS.UNIVERSITY]: [validators.validateInput],
  [INPUT_FIELDS.DEGREE]: [validators.validateInput],
  [INPUT_FIELDS.START_YEAR]: [validators.validateInput],
  [INPUT_FIELDS.END_YEAR]: [validators.validateInput],
  [INPUT_FIELDS.DEGREE_TYPE]: [validators.validateInput],
  [INPUT_FIELDS.DOCUMENT]: [validators.validateInput],
};

export const form: FormInputType[] = [
  {
    name: INPUT_FIELDS.INSTITUTE,
    label: "Institute",
    variant: "text",
    placeholder: "Enter Institution Name",
    isRequired: true,
  },
  {
    name: INPUT_FIELDS.UNIVERSITY,
    label: "University",
    variant: "text",
    placeholder: "Enter University Name",
    isRequired: true,
  },
  {
    name: INPUT_FIELDS.DEGREE,
    label: "Degree",
    variant: "autocomplete",
    placeholder: "Search Degree",
    isRequired: true,
  },
  {
    name: INPUT_FIELDS.DEGREE_TYPE,
    label: "Degree Type",
    variant: "select",
    isRequired: true,
  },
  {
    name: INPUT_FIELDS.START_YEAR,
    label: "Start Year",
    variant: "date",
    views: ["year"],
    isRequired: true,
    maxDate: INPUT_FIELDS.START_YEAR_MAX_DATE,
  },
  {
    name: INPUT_FIELDS.END_YEAR,
    label: "End Year",
    variant: "date",
    views: ["year"],
    isRequired: true,
    minDate: INPUT_FIELDS.END_YEAR_MIN_DATE,
  },
  {
    name: INPUT_FIELDS.DOCUMENT,
    label: "Proof of Education",
    sublabel: "Upload your education document",
    variant: "file",
    height: 150,
    xs: 12,
    isRequired: true,
    acceptFileTypes: {
      "application/zip": [".zip"],
      "application/pdf": [".pdf"],
      "image/jpeg": [".jpg", ".jpeg"],
      "image/png": [".png"],
      "application/msword": [".doc"],
      "application/vnd.openxmlformats-officedocument.wordprocessingml.document": [".docx"],
    },
  },
];
