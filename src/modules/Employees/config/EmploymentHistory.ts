import validators from "src/utils/validators";
import { FormDataType } from "../types/FormDataTypes";

export const INPUT_FIELDS = {
  COMPANY: "work_experience.company",
  DESIGNATION: "work_experience.designation",
  FROM_DATE: "work_experience.from_date",
  TO_DATE: "work_experience.to_date",
  FROM_DATE_MAX_DATE: "from_date_max_date",
  TO_DATE_MIN_DATE: "to_date_min_date",
  TO_DATE_MAX_DATE: "to_date_max_date",
  OFFER_LETTER: "work_experience.offer_letter",
  OFFER_LETTER_PLACEHOLDER: "offer_letter_placeholder",
  EXPERIENCE_LETTER: "work_experience.experience_letter",
  EXPERIENCE_LETTER_PLACEHOLDER: "experience_letter_placeholder",
  IS_CURRENT_EMPLOYER: "is_current_employer",
};

export const EmploymentHistoryInitialValues: FormDataType[] = [
  {
    [INPUT_FIELDS.COMPANY]: "",
    [INPUT_FIELDS.DESIGNATION]: "",
    [INPUT_FIELDS.FROM_DATE]: "",
    [INPUT_FIELDS.TO_DATE]: "",
    [INPUT_FIELDS.OFFER_LETTER]: null,
    [INPUT_FIELDS.EXPERIENCE_LETTER]: null,
    [INPUT_FIELDS.IS_CURRENT_EMPLOYER]: false,
    [INPUT_FIELDS.FROM_DATE_MAX_DATE]: new Date(),
    [INPUT_FIELDS.TO_DATE_MAX_DATE]: new Date(),
  },
];

export const EmploymentHistoryformValidators = {
  [INPUT_FIELDS.COMPANY]: [validators.validateInput],
  [INPUT_FIELDS.DESIGNATION]: [validators.validateInput],
  [INPUT_FIELDS.FROM_DATE]: [validators.validateInput],
  // [INPUT_FIELDS.TO_DATE]: [validators.validateInput],
  [INPUT_FIELDS.OFFER_LETTER]: [validators.validateInput],
};

export const getFormElements = (disableCurrentEmployee: boolean, isCurrentEmployee: boolean) => {
  return [
    {
      name: INPUT_FIELDS.COMPANY,
      label: "Company Name",
      variant: "text",
      placeholder: "Enter company name",
      isRequired: true,
    },
    {
      name: INPUT_FIELDS.DESIGNATION,
      label: "Designation",
      variant: "text",
      placeholder: "Enter designation",
      isRequired: true,
    },
    {
      name: INPUT_FIELDS.FROM_DATE,
      label: "From Date",
      variant: "date",
      isRequired: true,
      maxDate: INPUT_FIELDS.FROM_DATE_MAX_DATE,
    },
    {
      name: INPUT_FIELDS.TO_DATE,
      label: "To Date",
      variant: "date",
      isRequired: !isCurrentEmployee,
      minDate: INPUT_FIELDS.TO_DATE_MIN_DATE,
      maxDate: INPUT_FIELDS.TO_DATE_MAX_DATE,
    },
    {
      name: INPUT_FIELDS.IS_CURRENT_EMPLOYER,
      label: "Current Employer",
      variant: "switch",
      isRequired: false,
      props: {
        disabled: disableCurrentEmployee,
        sx: {
          marginTop: "26px",
        },
      },
    },
    {
      name: INPUT_FIELDS.OFFER_LETTER,
      label: "Offer Letter",
      sublabel: "Upload your employment document",
      variant: "file",
      height: 150,
      xs: 6,
      isRequired: true,
      acceptFileTypes: {
        "application/zip": [".zip"],
        "application/pdf": [".pdf"],
        "image/jpeg": [".jpg", ".jpeg"],
        "image/png": [".png"],
        "application/msword": [".doc"],
        "application/vnd.openxmlformats-officedocument.wordprocessingml.document": [".docx"],
      },
    },
    {
      name: INPUT_FIELDS.EXPERIENCE_LETTER,
      label: "Experience Letter",
      sublabel: "Upload your experience document",
      variant: "file",
      height: 150,
      xs: 6,
      isRequired: !isCurrentEmployee,
      acceptFileTypes: {
        "application/zip": [".zip"],
        "application/pdf": [".pdf"],
        "image/jpeg": [".jpg", ".jpeg"],
        "image/png": [".png"],
        "application/msword": [".doc"],
        "application/vnd.openxmlformats-officedocument.wordprocessingml.document": [".docx"],
      },
    },
  ];
};
