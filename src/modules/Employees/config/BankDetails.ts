import validators from "src/utils/validators";
import { FormInputType } from "../types/FormDataTypes";

export const INPUT_FIELDS = {
  ACCOUNT_NUMBER: "bank_account.account_number",
  IFSC: "bank_account.ifsc",
  BANK_NAME: "bank_account.bank_name",
  BR<PERSON>CH: "bank_account.bank_branch",
  ADDRESS: "bank_account.address",
  ACCOUNT_HOLDER_NAME: "bank_account.account_holder_name",
  BANK_DOCUMENT: "bank_account.bank_document",
};

export const BankDetailsInitialValues = [
  {
    [INPUT_FIELDS.ACCOUNT_NUMBER]: "",
    [INPUT_FIELDS.IFSC]: "",
    [INPUT_FIELDS.BANK_NAME]: "",
    [INPUT_FIELDS.BRANCH]: "",
    [INPUT_FIELDS.ADDRESS]: "",
    [INPUT_FIELDS.ACCOUNT_HOLDER_NAME]: "",
    [INPUT_FIELDS.BANK_DOCUMENT]: "",
  },
];

export const BankDetailsformValidators = {
  [INPUT_FIELDS.ACCOUNT_NUMBER]: [validators.validateInput, validators.shouldBeNumeric],
  [INPUT_FIELDS.IFSC]: [validators.validateInput, validators.validateIFSC],
  [INPUT_FIELDS.BANK_NAME]: [validators.validateInput],
  [INPUT_FIELDS.BRANCH]: [validators.validateInput],
  [INPUT_FIELDS.ADDRESS]: [],
  [INPUT_FIELDS.ACCOUNT_HOLDER_NAME]: [validators.validateInput],
  [INPUT_FIELDS.BANK_DOCUMENT]: null,
};

export const form: FormInputType[] = [
  {
    name: INPUT_FIELDS.ACCOUNT_HOLDER_NAME,
    label: "Account Holder Name",
    variant: "text",
    placeholder: "Enter name as per bank account",
    isRequired: true,
  },
  {
    name: INPUT_FIELDS.ACCOUNT_NUMBER,
    label: "Account Number",
    variant: "text",
    placeholder: "Enter your account number",
    isRequired: true,
  },
  {
    name: INPUT_FIELDS.IFSC,
    label: "IFSC Code",
    variant: "text",
    placeholder: "Enter Bank IFSC Code",
    isRequired: true,
  },
  {
    name: INPUT_FIELDS.BANK_NAME,
    label: "Bank",
    variant: "text",
    placeholder: "Enter your Bank Name",
    isRequired: true,
  },
  {
    name: INPUT_FIELDS.BRANCH,
    label: "Branch",
    variant: "text",
    placeholder: "Enter your Bank Branch",
    isRequired: true,
  },
  {
    name: INPUT_FIELDS.ADDRESS,
    label: "Bank Address",
    variant: "text",
    placeholder: "Enter Bank address",
    rows: 4,
    xs: 4,
    isRequired: false,
  },
  {
    name: INPUT_FIELDS.BANK_DOCUMENT,
    label: "Canceled Cheque",
    sublabel: "Upload your canceled cheque",
    variant: "file",
    height: 150,
    xs: 12,
    isRequired: false,
    acceptFileTypes: {
      "application/pdf": [".pdf"],
      "image/jpeg": [".jpg", ".jpeg"],
      "image/png": [".png"],
    },
  },
];
