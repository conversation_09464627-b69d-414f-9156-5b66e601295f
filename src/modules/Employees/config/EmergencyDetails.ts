import validators from "src/utils/validators";
import { FormInputType, PhoneNumberType } from "../types/FormDataTypes";

export const INPUT_FIELDS = {
  NAME: "emergency_contacts.name",
  PHONE: "emergency_contacts.phone",
  PRIMARY: "emergency_contacts.primary",
  RELATION: "emergency_contacts.relation",
};

export const EmergencyDetailsInitialValues = [
  {
    [INPUT_FIELDS.NAME]: "",
    [INPUT_FIELDS.PHONE]: { number: "", countryCode: "" } as PhoneNumberType,
    [INPUT_FIELDS.PRIMARY]: "",
    [INPUT_FIELDS.RELATION]: "",
  },
];

export const EmergencyDetailsformValidators = {
  [INPUT_FIELDS.NAME]: [validators.validateInput, validators.validateName],
  [INPUT_FIELDS.PHONE]: [validators.validateInput, validators.validatePhone],
  [INPUT_FIELDS.PRIMARY]: [validators.validateInput],
  [INPUT_FIELDS.RELATION]: [validators.validateInput],
};

export const form: FormInputType[] = [
  {
    name: INPUT_FIELDS.NAME,
    label: "Name",
    variant: "text",
    placeholder: "Enter your name",
    isRequired: true,
  },
  {
    name: INPUT_FIELDS.PHONE,
    label: "Phone Number",
    variant: "phone",
    placeholder: "e.g. 8799999999",
    isRequired: true,
  },
  {
    name: INPUT_FIELDS.PRIMARY,
    label: "Primary",
    variant: "select",
    isRequired: true,
  },
  {
    name: INPUT_FIELDS.RELATION,
    label: "Relation",
    variant: "select",
    isRequired: true,
  },
];
