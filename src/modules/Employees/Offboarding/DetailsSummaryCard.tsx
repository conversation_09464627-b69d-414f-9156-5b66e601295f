import { Edit } from "@mui/icons-material";
import { Box, Grid2, IconButton, Tooltip, Typography } from "@mui/material";
import React from "react";

type DetailsSummaryCardProps = {
  data: {
    label: string;
    value?: string;
    xs?: number;
  }[];
  title?: string;
  variant?: string;
  onEditClick?: () => void;
  showEditButton?: boolean;
};

const containerStyles = {
  width: "100%",
  background: "rgba(249, 249, 249, 1)",
  borderRadius: "10px",
  padding: "20px",
};

const DetailsSummaryCard = ({ title, data, onEditClick, showEditButton }: DetailsSummaryCardProps) => {
  return (
    <Box mt="16px">
      <Box display="flex" justifyContent="space-between" alignItems="center">
        {title && (
          <Typography variant="body1" fontWeight="400" color="text.primary" marginBottom="16px">
            {title}
          </Typography>
        )}
        {showEditButton && (
          <Box display="flex" justifyContent="flex-end" mb={1}>
            <Tooltip title="Edit">
              <IconButton onClick={onEditClick}>
                <Edit />
              </IconButton>
            </Tooltip>
          </Box>
        )}
      </Box>
      <Box sx={containerStyles}>
        <Grid2 container rowSpacing={2}>
          {data.map(({ label, value, xs }) => (
            <Grid2 size={xs || 3} key={label + value}>
              <Typography variant="body2" fontWeight="400" color="#667085">
                {label}
              </Typography>
              <Typography variant="body2" fontWeight="400" color="#000000" align="justify">
                {value}
              </Typography>
            </Grid2>
          ))}
        </Grid2>
      </Box>
    </Box>
  );
};

export default DetailsSummaryCard;
