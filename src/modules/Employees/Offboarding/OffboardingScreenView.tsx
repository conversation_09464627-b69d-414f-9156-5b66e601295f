import { Container } from "@mui/material";
import { useQueries, useQuery } from "@tanstack/react-query";
import React from "react";

import Tabs, { TabType } from "src/modules/Common/CustomTabs/CustomTabs";
import LoadingScreen from "src/modules/Common/LoadingScreen/LoadingScreen";
import EmployeeOffboardingDetails from "src/modules/Employees/Offboarding/EmployeeOffboardingDetails";
import EmployeeOffboardingForm from "src/modules/Employees/Offboarding/EmployeeOffboardingForm";
import { EmployeeOffboardingTableResposnse } from "src/services/api_definitions/employees";
import employeesService from "src/services/employees.service";
import EmployeeOffboardingApprovalDetails from "./EmployeeOffboardingApprovalDetails";
import EmployeeOffboardingTable from "./EmployeeOffboardingTable";
import OffboardingSummary from "./OffboardingSummary";

const OffboardingSummaryOverview = () => {
  const result = useQueries({
    queries: [
      {
        queryKey: ["offboarding-summary-overview"],
        queryFn: async () => employeesService.getOffboardingSummary(),
        retryOnMount: false,
        refetchOnWindowFocus: false,
      },
    ],
  });
  const [{ data: offboardingSummary, isLoading }] = result;
  if (!isLoading && offboardingSummary) {
    return <OffboardingSummary offboardingSummary={offboardingSummary} />;
  }
  return <LoadingScreen />;
};

const EmployeeOffboardingView = () => {
  const {
    data: employeeOffboardingDetails,
    isFetched,
    refetch,
  } = useQuery(["get-user-offboarding-details"], {
    queryFn: async () => employeesService.getEmployeeOffboardingDetails(),
    retryOnMount: false,
    refetchOnWindowFocus: false,
  });

  if (!isFetched || !employeeOffboardingDetails) {
    return <LoadingScreen />;
  }

  if (!employeeOffboardingDetails.resignation_date) {
    return (
      <EmployeeOffboardingForm onClose={() => refetch()} employeeOffboardingDetails={employeeOffboardingDetails} />
    );
  }

  return (
    <Container disableGutters maxWidth={false}>
      <EmployeeOffboardingDetails employeeOffboardingDetails={employeeOffboardingDetails} refetch={refetch} />
    </Container>
  );
};

const ManagerOffboardingView = () => {
  const [currentTab, setCurrentTab] = React.useState<number>(0);
  const [activeEmployee, setActiveEmployee] = React.useState<EmployeeOffboardingTableResposnse | null>(null);
  const {
    data: result,
    refetch,
    isLoading,
  } = useQuery({
    queryKey: ["get-all-employee-offboarding-details"],
    queryFn: async () => employeesService.getAllEmployeeOffboardingDetails(false),
    retryOnMount: false,
    refetchOnWindowFocus: false,
    enabled: currentTab === 1,
    onSuccess: (data) => {
      if (activeEmployee?.resignation_date) {
        const refetchedEmployee = data?.find(
          (employee) => employee.resignation_date === activeEmployee.resignation_date,
        );
        setActiveEmployee(refetchedEmployee ?? null);
      }
    },
  });

  const tabs: TabType[] = [
    {
      id: "separation",
      component: <EmployeeOffboardingView />,
      label: "Self",
    },
    {
      id: "exit-request",
      component: (
        <EmployeeOffboardingTable
          onViewClick={(employee) => setActiveEmployee(employee)}
          employeeOffboardingDetails={result ?? []}
          loading={isLoading}
        />
      ),
      label: "Exit Requests",
    },
  ];
  if (activeEmployee) {
    return (
      <EmployeeOffboardingApprovalDetails
        isManagerView
        employee={activeEmployee}
        onClose={() => setActiveEmployee(null)}
        refetchAllEmployeeDetails={refetch}
      />
    );
  }
  return (
    <Tabs
      containerStyle={{ height: "100%" }}
      componentContainerStyle={{ margin: "20px 0 0" }}
      tabs={tabs}
      currentTabIndex={currentTab}
      handleTabChange={setCurrentTab}
    />
  );
};

const HRAdminOffboardingView = () => {
  const [currentTab, setCurrentTab] = React.useState<number>(0);
  const [activeEmployee, setActiveEmployee] = React.useState<EmployeeOffboardingTableResposnse | null>(null);
  const {
    data: result,
    refetch,
    isLoading,
  } = useQuery({
    queryKey: ["get-all-employee-offboarding-details"],
    queryFn: async () => employeesService.getAllEmployeeOffboardingDetails(true),
    retryOnMount: false,
    refetchOnWindowFocus: false,
    refetchOnMount: true,
    enabled: currentTab === 1,
    onSuccess: (data) => {
      if (activeEmployee?.resignation_date) {
        const refetchedEmployee = data?.find(
          (employee) => employee.resignation_date === activeEmployee.resignation_date,
        );
        setActiveEmployee(refetchedEmployee ?? null);
      }
    },
  });

  const tabs: TabType[] = [
    {
      id: "overview",
      component: <OffboardingSummaryOverview />,
      label: "Overview",
    },
    {
      id: "exit-request",
      component: (
        <EmployeeOffboardingTable
          onViewClick={(employee) => setActiveEmployee(employee)}
          employeeOffboardingDetails={result ?? []}
          loading={isLoading}
        />
      ),
      label: "Exit Requests",
    },
  ];
  if (activeEmployee) {
    return (
      <EmployeeOffboardingApprovalDetails
        isHRAdminView
        employee={activeEmployee ?? null}
        onClose={() => setActiveEmployee(null)}
        refetchAllEmployeeDetails={refetch}
      />
    );
  }
  return (
    <Tabs
      containerStyle={{ height: "100%" }}
      componentContainerStyle={{ margin: "20px 0 0" }}
      tabs={tabs}
      currentTabIndex={currentTab}
      handleTabChange={setCurrentTab}
    />
  );
};

export { ManagerOffboardingView, EmployeeOffboardingView, HRAdminOffboardingView };
