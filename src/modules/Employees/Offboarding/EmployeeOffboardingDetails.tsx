import { But<PERSON>, Grid2 } from "@mui/material";
import { useMutation } from "@tanstack/react-query";
import { format } from "date-fns/format";
import React from "react";
import ContentHeader from "src/modules/Common/ContentHeader/ContentHeader";
import { EmployeeOffboardingDetailsResposnse } from "src/services/api_definitions/employees";
import employeesService from "src/services/employees.service";
import { DD_MM_YYYY, getIntlTimeToSpecifiedFormat } from "src/utils/dateUtils";
import { RescindConfirmationModal } from "./ConfirmationModal";
import DetailsSummaryCard from "./DetailsSummaryCard";
import EmployeeOffboardingStatus from "./EmployeeOffboardingStatus";

type Props = {
  employeeOffboardingDetails: EmployeeOffboardingDetailsResposnse;
  refetch: () => void;
};

const rescindButtonStyle = {
  marginBottom: "10px",
};

export const getOffboardingData = (employeeOffboardingDetails: EmployeeOffboardingDetailsResposnse) => {
  const managerResponse = employeeOffboardingDetails.request_status.find(
    (status) => status.approver_role === "Manager",
  );
  const hrResponse = employeeOffboardingDetails.request_status.find((status) => status.approver_role === "HRBP");

  const employeeOffboardingDetailsData = [
    {
      label: "Name",
      value: employeeOffboardingDetails.name,
    },
    {
      label: "Employee ID",
      value: employeeOffboardingDetails.employee_code,
    },
    {
      label: "Department",
      value: employeeOffboardingDetails.department,
    },
    {
      label: "Designation",
      value: employeeOffboardingDetails.job_title,
    },
  ].filter((detail) => !!detail.value);

  const employeeRequestDetails = [
    {
      label: "Resignation Date",
      value: format(employeeOffboardingDetails.resignation_date, "dd MMM yyyy"),
    },
    {
      label: "Last Working Date",
      value: format(employeeOffboardingDetails.last_working_date, "dd MMM yyyy"),
    },
    {
      label: "Desired Last Working Date",
      value: format(employeeOffboardingDetails.desired_last_working_date, "dd MMM yyyy"),
    },
    {
      label: "Separation Reason",
      value: employeeOffboardingDetails.separation_reason,
    },
    {
      label: "Description",
      value: employeeOffboardingDetails.description,
      xs: 16,
    },
  ].filter((detail) => !!detail.value);

  let managerOffboardingDetails = [
    {
      label: "Approved Last Working Date",
      value: managerResponse?.approved_last_working_date
        ? format(managerResponse?.approved_last_working_date, "dd MMM yyyy")
        : "",
    },
    {
      label: "Waive Off Notice Period",
      value:
        managerResponse?.notice_period_waived_off != null
          ? managerResponse?.notice_period_waived_off === true
            ? "Yes"
            : "No"
          : "",
    },
    {
      label: "Comments",
      value: managerResponse?.comments,
      xs: 16,
    },
  ].filter((detail) => !!detail.value);

  let hrOffboardingDetails = [
    {
      label: "Approved Last Working Date",
      value: hrResponse?.approved_last_working_date
        ? format(hrResponse?.approved_last_working_date, "dd MMM yyyy")
        : "",
    },
    {
      label: "Waive Off Notice Period",
      value:
        hrResponse?.notice_period_waived_off != null
          ? hrResponse?.notice_period_waived_off === true
            ? "Yes"
            : "No"
          : "",
    },
    {
      label: "Comments",
      value: hrResponse?.comments,
      xs: 12,
    },
  ].filter((detail) => !!detail.value);

  if (
    managerResponse?.status === "Denied" ||
    managerResponse?.status === "Rescinded" ||
    managerResponse?.status === "No Action Required"
  ) {
    managerOffboardingDetails = managerOffboardingDetails.filter(
      (detail) => detail.label === "Comments" || detail.label === "Approver Name",
    );
  }
  if (
    hrResponse?.status === "Denied" ||
    hrResponse?.status === "Rescinded" ||
    hrResponse?.status === "No Action Required"
  ) {
    hrOffboardingDetails = hrOffboardingDetails.filter(
      (detail) => detail.label === "Comments" || detail.label === "Approver Name",
    );
  }

  return {
    employeeOffboardingDetailsData,
    employeeRequestDetails,
    managerOffboardingDetails,
    hrOffboardingDetails,
  };
};

const EmployeeOffboardingDetails = ({ employeeOffboardingDetails, refetch }: Props) => {
  const [showConfirmationModal, setShowConfirmationModal] = React.useState(false);

  const mutation = useMutation({
    mutationKey: ["offboarding-rescind"],
    mutationFn: async () => employeesService.rescindEmployeeOffboarding(),
    onSuccess: (response) => {
      if (response) {
        refetch();
        setShowConfirmationModal(false);
      }
    },
  });

  const managerResponse = employeeOffboardingDetails.request_status.find(
    (status) => status.approver_role === "Manager",
  );
  const hrResponse = employeeOffboardingDetails.request_status.find((status) => status.approver_role === "HRBP");

  const { managerOffboardingDetails, hrOffboardingDetails, employeeRequestDetails } =
    getOffboardingData(employeeOffboardingDetails);

  const hasHRBPApproved = hrResponse?.status === "Accepted";

  const offboardingStatus = [
    {
      title: "Employee Request",
      status: "Resigned",
      date: format(employeeOffboardingDetails?.resignation_date, DD_MM_YYYY),
    },
    {
      title: "Manager Response",
      status: managerResponse?.status || "",
      date: managerResponse?.actioned_at
        ? getIntlTimeToSpecifiedFormat(managerResponse?.actioned_at, DD_MM_YYYY).formattedDate
        : null,
    },
    {
      title: "HR Response",
      status: hrResponse?.status || "",
      date: hrResponse?.actioned_at
        ? getIntlTimeToSpecifiedFormat(hrResponse?.actioned_at, DD_MM_YYYY).formattedDate
        : null,
    },
  ];

  const actionButton = !hasHRBPApproved && (
    <Button sx={rescindButtonStyle} variant="contained" color="error" onClick={() => setShowConfirmationModal(true)}>
      Rescind
    </Button>
  );

  return (
    <Grid2 container columnSpacing={2}>
      <Grid2 size={12} mb={1}>
        <ContentHeader actions={actionButton} />
      </Grid2>
      <Grid2 size={12}>
        <EmployeeOffboardingStatus steps={offboardingStatus} title="" />
        <DetailsSummaryCard title="Employee Request" data={employeeRequestDetails} variant="v2" />
        {managerResponse?.actioned_at && managerOffboardingDetails.length > 0 && (
          <DetailsSummaryCard title="Manager Response" data={managerOffboardingDetails} variant="v2" />
        )}
        {hrResponse?.actioned_at && hrOffboardingDetails.length > 0 && (
          <DetailsSummaryCard title="HR Response" data={hrOffboardingDetails} variant="v2" />
        )}
        <RescindConfirmationModal
          showConfirmationModal={showConfirmationModal}
          setShowConfirmationModal={setShowConfirmationModal}
          onSubmit={() => mutation.mutate()}
        />
      </Grid2>
    </Grid2>
  );
};

export default EmployeeOffboardingDetails;
