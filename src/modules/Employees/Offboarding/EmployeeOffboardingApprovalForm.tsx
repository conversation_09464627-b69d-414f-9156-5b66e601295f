import { <PERSON>, Button, Paper, Typography } from "@mui/material";
import { useStore } from "@tanstack/react-form";
import { useMutation } from "@tanstack/react-query";
import React, { useMemo } from "react";
import { BaseObject } from "src/app/global";
import EffiDynamicForm, { InputFormStruct } from "src/modules/Common/Form/components/EffiDynamicForm";
import { useAppForm } from "src/modules/Common/Form/effiFormContext";
import Modal from "src/modules/Common/Modal/Modal";
import Span from "src/modules/Common/Span/Span";
import employeesService from "src/services/employees.service";
import { z } from "zod";
import { FormDataType } from "../types/FormDataTypes";
import { convertListToOptions } from "../utils/utils";

export const containerStyle = {
  display: "flex",
  flexDirection: "column",
  // alignItems: "center",
  gap: 4,
  borderRadius: "10px",
  border: "1px solid #EDEDED",
  padding: "20px",
};

export const cancelButtonStyle = {
  backgroundColor: "#EFF4F8",
  color: "#007F6F",
  height: "40px",
};

export const applyButtonStyle = {
  height: "40px",
};

const INPUT_FIELDS = {
  STATUS: "status",
  WAVE_OFF_NOTICE_PERIOD: "notice_period_waived_off",
  APPROVED_LAST_WORKING_DATE: "approved_last_working_date",
  APPROVED_LAST_WORKING_DATE_MIN_DATE: "approved_last_working_date_min_date",
  COMMENTS: "comments",
};

const statusInputField: InputFormStruct = {
  fieldProps: {
    name: INPUT_FIELDS.STATUS,
  },
  formProps: {
    label: "Status",
    type: "select",
    required: true,
    options: convertListToOptions(["Accept", "Deny"]),
    // disabled: isFirstTime,
  },
  containerProps: {
    size: 3,
  },
};

const employeeApprovalForm: (isFirstTime: boolean) => InputFormStruct[] = (isFirstTime: boolean) => {
  const form = [
    {
      fieldProps: {
        name: INPUT_FIELDS.APPROVED_LAST_WORKING_DATE,
      },
      formProps: {
        label: "Approved Last Working Date",
        type: "date",
        required: true,
        minDate: new Date(),
      },
      containerProps: {
        size: 3,
      },
    },
    {
      fieldProps: {
        name: INPUT_FIELDS.WAVE_OFF_NOTICE_PERIOD,
      },
      formProps: {
        label: "Waive Off Notice Period",
        type: "select",
        required: true,
        // vertical: true,
        options: convertListToOptions(["Yes", "No"]),
        placeholder: "Select Option",
      },
      containerProps: {
        size: 3,
      },
    },
    {
      fieldProps: {
        name: INPUT_FIELDS.COMMENTS,
      },
      formProps: {
        label: "Comments",
        type: "text",
        required: true,
        multiline: true,
        rows: 3,
      },
      containerProps: {
        size: 12,
      },
    },
  ];
  if (!isFirstTime) {
    return [statusInputField, ...form];
  }
  return form;
};

const employeeRejectionForm: (isFirstTime: boolean) => InputFormStruct[] = (isFirstTime: boolean) => {
  const form = [
    {
      fieldProps: {
        name: INPUT_FIELDS.COMMENTS,
      },
      formProps: {
        label: "Comments",
        type: "text",
        required: true,
        multiline: true,
        rows: 3,
      },
      containerProps: {
        size: 12,
      },
    },
  ];
  if (!isFirstTime) {
    return [statusInputField, ...form];
  }
  return form;
};

type ActionComponentProps = {
  areFormDetailsValid: boolean;
  formDetails?: FormDataType | FormDataType[];
  onSubmit: () => void;
  showCancelButton?: boolean;
  onCancelEdit?: () => void;
};

const ActionComponent = ({
  areFormDetailsValid,
  onSubmit,
  formDetails,
  onCancelEdit,
  showCancelButton,
}: ActionComponentProps) => {
  const [showConfirmationModal, setShowConfirmationModal] = React.useState(false);

  const handleSubmit = () => {
    onSubmit();
    setShowConfirmationModal(false);
  };

  const status = (formDetails as FormDataType)[INPUT_FIELDS.STATUS] as string;
  return (
    <>
      <Box
        sx={{
          display: "flex",
          justifyContent: "flex-end",
          gap: 2,
          width: "100%",
          marginTop: "16px",
        }}
      >
        {showCancelButton && (
          <Button variant="text" sx={cancelButtonStyle} onClick={onCancelEdit}>
            Cancel
          </Button>
        )}
        <Button
          variant="contained"
          sx={applyButtonStyle}
          disabled={!areFormDetailsValid}
          onClick={() => setShowConfirmationModal(true)}
        >
          Submit
        </Button>
      </Box>
      <Modal isOpen={showConfirmationModal} onClose={() => setShowConfirmationModal(false)} maxWidth="630px">
        <Box
          sx={{
            display: "flex",
            flexDirection: "column",
            gap: 1,
            padding: "8px",
          }}
        >
          <Span sx={{ fontSize: "20px" }}>
            {showCancelButton ? "Update Resignation Approval" : `${status} Resignation request`}{" "}
          </Span>
          <Span sx={{ fontSize: "14px" }}>
            {showCancelButton
              ? "Are you sure you want to update the approved resignation request?"
              : `Are you sure you want to ${status.toLowerCase()} resignation request?`}
          </Span>
          <Box display="flex" justifyContent="flex-end" sx={{ gap: 2, marginTop: "36px" }}>
            <Button variant="text" sx={cancelButtonStyle} onClick={() => setShowConfirmationModal(false)}>
              Cancel
            </Button>
            <Button variant="contained" sx={applyButtonStyle} onClick={handleSubmit}>
              {showCancelButton ? "Update" : status}
            </Button>
          </Box>
        </Box>
      </Modal>
    </>
  );
};

const employeeOffboardingApprovalPayload = (
  employee_code: string,
  data: FormDataType,
  isHRAdminView: boolean = false,
) => {
  return {
    employee_code,
    status: data[INPUT_FIELDS.STATUS] === "Accept" ? "Accepted" : "Denied",
    notice_period_waived_off: data[INPUT_FIELDS.WAVE_OFF_NOTICE_PERIOD] === "Yes",
    approved_last_working_date: data[INPUT_FIELDS.APPROVED_LAST_WORKING_DATE],
    comments: data[INPUT_FIELDS.COMMENTS],
    approver_role: isHRAdminView ? "HRBP" : "Manager",
  };
};

type EmployeeOffboardingApprovalFormProps = {
  title?: string;
  onClose: (isTerminated?: boolean) => void;
  employee_code: string;
  isHRAdminView?: boolean;
  onCancelEdit?: () => void;
  formData?: FormDataType;
  showCancelButton?: boolean;
};

const acceptFormValidators = z.object({
  [INPUT_FIELDS.WAVE_OFF_NOTICE_PERIOD]: z.string().min(1, "Waive off notice period is required"),
  [INPUT_FIELDS.APPROVED_LAST_WORKING_DATE]: z.string().min(1, "Approved last working date is required"),
  [INPUT_FIELDS.COMMENTS]: z.string().min(1, "Comments are required"),
});
const rejectFormValidators = z.object({
  [INPUT_FIELDS.COMMENTS]: z.string().min(1, "Comments are required"),
});

const formValidators = z
  .object({
    [INPUT_FIELDS.STATUS]: z.string().min(1),
    [INPUT_FIELDS.COMMENTS]: z.string().min(1, "Comments are required"),
    [INPUT_FIELDS.APPROVED_LAST_WORKING_DATE]: z.string().optional(),
    [INPUT_FIELDS.COMMENTS]: z.string().optional(),
    [INPUT_FIELDS.WAVE_OFF_NOTICE_PERIOD]: z.string().optional(),
  })
  .refine((data) => {
    if (data[INPUT_FIELDS.STATUS] === "Accept") {
      return acceptFormValidators.safeParse(data).success;
    }
    return rejectFormValidators.safeParse(data).success;
  });

const EmployeeOffboardingApprovalForm = ({
  employee_code,
  onClose,
  title,
  isHRAdminView = false,
  formData,
  onCancelEdit,
  showCancelButton = false,
}: EmployeeOffboardingApprovalFormProps) => {
  const mutation = useMutation({
    mutationKey: ["offboarding-approval"],
    mutationFn: async (employeeData: BaseObject) => employeesService.approveEmployeeOffboarding(employeeData),
    onSuccess: (response) => {
      if (response) {
        onClose();
        onCancelEdit?.();
      }
    },
  });

  const editEmployeeOffboardingMutation = useMutation({
    mutationKey: ["offboarding-edit"],
    mutationFn: async (employeeData: BaseObject) => employeesService.editEmployeeOffboarding(employeeData),
    onSuccess: (response) => {
      if (response) {
        onClose(true);
        // onCancelEdit?.();
      }
    },
  });

  const isEditFlow = !!formData?.actioned_at;
  const employeeOffboardingApprovalFormInitialState = useMemo(() => {
    return {
      [INPUT_FIELDS.STATUS]: (formData?.[INPUT_FIELDS.STATUS] as string)?.replace("ed", "") || "",
      [INPUT_FIELDS.WAVE_OFF_NOTICE_PERIOD]: formData?.actioned_at
        ? formData?.[INPUT_FIELDS.WAVE_OFF_NOTICE_PERIOD] === true
          ? "Yes"
          : "No"
        : "",
      [INPUT_FIELDS.APPROVED_LAST_WORKING_DATE]: formData?.[INPUT_FIELDS.APPROVED_LAST_WORKING_DATE] || "",
      [INPUT_FIELDS.APPROVED_LAST_WORKING_DATE_MIN_DATE]: new Date(),
      [INPUT_FIELDS.COMMENTS]: formData?.[INPUT_FIELDS.COMMENTS] || "",
    };
  }, [formData]);

  const form = useAppForm({
    defaultValues: employeeOffboardingApprovalFormInitialState,
    validators: {
      onChange: formValidators as any,
      // onSubmit: employeeOffboardingApprovalFormValidators,
    },
    onSubmit: (form) => {
      if (isEditFlow) {
        editEmployeeOffboarding(form?.value as FormDataType);
      } else {
        approveEmployeeOffboarding(form?.value as FormDataType);
      }
    },
  });

  const { status } = useStore(form.store, (state: any) => state.values);

  const approveEmployeeOffboarding = (form: FormDataType) => {
    mutation.mutate(employeeOffboardingApprovalPayload(employee_code, form, isHRAdminView));
  };

  const editEmployeeOffboarding = (form: FormDataType) => {
    editEmployeeOffboardingMutation.mutate(employeeOffboardingApprovalPayload(employee_code, form, isHRAdminView));
  };

  return (
    <Box>
      {title && (
        <Typography variant="body1" fontWeight="400" color="text.primary" marginBottom="16px">
          {title}
        </Typography>
      )}
      <Box component={Paper} elevation={2} p={2} borderRadius={2}>
        {status !== "Accept" && status !== "Deny" && <EffiDynamicForm form={form} inputFields={[statusInputField]} />}
        {status === "Accept" && (
          <EffiDynamicForm form={form} inputFields={employeeApprovalForm(!!formData?.actioned_at)} />
        )}
        {status === "Deny" && (
          <EffiDynamicForm form={form} inputFields={employeeRejectionForm(!!formData?.actioned_at)} />
        )}
        <form.Subscribe
          selector={(state) => [
            state.canSubmit,
            state.isSubmitting,
            state.isPristine,
            state.values,
            state.isDefaultValue,
          ]}
        >
          {([canSubmit, isSubmitting, isPristine, values, isDefaultValue]) => {
            return (
              <ActionComponent
                areFormDetailsValid={
                  !(!canSubmit || (isSubmitting as boolean) || (isPristine as boolean) || (isDefaultValue as boolean))
                }
                onSubmit={() => form.handleSubmit()}
                formDetails={values as FormDataType}
                onCancelEdit={onCancelEdit}
                showCancelButton={showCancelButton}
              />
            );
          }}
        </form.Subscribe>
      </Box>
    </Box>
  );
};

export default EmployeeOffboardingApprovalForm;
