import CircleIcon from "@mui/icons-material/Circle";
import { Box, styled, Typography } from "@mui/material";
import Step from "@mui/material/Step";
import StepConnector, { stepConnectorClasses } from "@mui/material/StepConnector";
import StepLabel from "@mui/material/StepLabel";
import Stepper from "@mui/material/Stepper";
import React from "react";
import { getStatusColors } from "src/utils/typographyUtils";

const containerStyles = {
  display: "flex",
  width: "100%",
  background: "rgba(249, 249, 249, 1)",
  borderRadius: "10px",
  padding: "20px",
  paddingBottom: "0px",
};

const activeIconStyles = {
  backgroundColor: "rgba(87, 186, 87, 0.7)",
  borderRadius: "50%",
  display: "flex",
  justifyContent: "center",
  alignItems: "center",
  width: 18,
  height: 18,
};

const disableIconStyles = {
  width: 18,
  height: 18,
  borderRadius: "50%",
  display: "flex",
  justifyContent: "center",
  alignItems: "center",
};

const labelStyle = {
  color: "#42526B",
  fontSize: "14px",
};

const valueStyle = {
  color: "#000000",
  marginLeft: "4px",
  fontSize: "14px",
  fontWeight: "500",
};

export const CustomConnector = styled(StepConnector)(() => ({
  [`&.${stepConnectorClasses.alternativeLabel}`]: {
    top: 7.5,
    left: "calc(-50% + 7.5px)",
    right: "calc(50% + 7.5px)",
  },
  [`&.${stepConnectorClasses.active}`]: {
    [`& .${stepConnectorClasses.line}`]: {
      borderColor: "#57ba57",
    },
  },
  [`&.${stepConnectorClasses.completed}`]: {
    [`& .${stepConnectorClasses.line}`]: {
      borderColor: "#57ba57",
    },
  },
  [`& .${stepConnectorClasses.line}`]: {
    borderColor: "#eaeaf0",
    borderTopWidth: 2,
    borderRadius: 1,
  },
}));

export type Steps = {
  title: string;
  status: string;
  date: string | null;
}[];

type Props = {
  steps: Steps;
  title?: string;
};

const getStatusStyle = (status: string) => {
  switch (status) {
    case "Accepted":
    case "Resigned":
      return activeIconStyles;
    case "Rescinded":
    case "Denied":
      return {
        ...activeIconStyles,
        backgroundColor: "rgba(255, 76, 76, 0.7)",
      };
    case "Pending":
      return {
        ...activeIconStyles,
        backgroundColor: "rgba(255, 165, 0, 0.7)",
      };
    default:
      return activeIconStyles;
  }
};

const getStatusTextStyle = (status: string) => {
  switch (status) {
    case "Accepted":
    case "Resigned":
      return {
        ...valueStyle,
        color: "#007F6F",
      };
    case "Rescinded":
    case "Denied":
      return {
        ...valueStyle,
        color: "#FF4C4C",
      };
    case "Pending":
      return {
        ...valueStyle,
        color: "#FFA500",
      };
    default:
      return valueStyle;
  }
};

const stepLabelStyle = {
  "& .MuiStepLabel-labelContainer": {
    marginTop: "6px !important",
  },
  "& .MuiStepLabel-label": {
    marginTop: "6px !important",
  },
  width: "28vw",
  padding: "0 2px",
};

const EmployeeOffboardingStatus = ({ steps, title }: Props) => {
  const pendingStatusIndex = steps.findIndex(
    ({ status }) => status === "Pending" || status === "Rescinded" || status === "Denied",
  );
  const activeStep = pendingStatusIndex === -1 ? steps.length - 1 : pendingStatusIndex;
  return (
    <Box>
      <Box display="flex" justifyContent="space-between" alignItems="center">
        {title && (
          <Typography variant="body1" fontWeight="500" color="text.primary" marginBottom="6px">
            {title}
          </Typography>
        )}
      </Box>
      <Box sx={containerStyles}>
        <Box sx={{ display: "flex", width: "100%", justifyContent: "center" }}>
          <Stepper activeStep={activeStep} alternativeLabel connector={<CustomConnector />}>
            {steps.map(({ title, status, date }, index) => {
              return (
                <Step key={title} sx={{ padding: "0" }}>
                  <StepLabel
                    sx={stepLabelStyle}
                    icon={
                      <Box sx={index <= activeStep ? getStatusStyle(status) : disableIconStyles}>
                        <CircleIcon
                          sx={{
                            width: index <= activeStep ? 8 : 18,
                            height: index <= activeStep ? 8 : 18,
                            color: index <= activeStep ? getStatusColors(status) : "#D2D2D2",
                          }}
                        />
                      </Box>
                    }
                  >
                    <Box
                      sx={{
                        marginLeft: "10px",
                        // borderBottom: index < steps.length - 1 ? "1px solid rgba(230, 230, 230, 1)" : "none",
                        paddingBottom: "20px",
                      }}
                    >
                      <Typography variant="body1" fontWeight="400" color="#000000" marginBottom="4px">
                        {title}
                      </Typography>
                      <Box display="flex" marginTop="6px" justifyContent="center">
                        <Typography sx={labelStyle}>Action:</Typography>
                        <Typography sx={getStatusTextStyle(status)}>{status}</Typography>
                      </Box>
                      {date && (
                        <Box display="flex" marginTop="6px" justifyContent="center">
                          <Typography sx={labelStyle}>Date:</Typography>
                          <Typography sx={valueStyle}>{date}</Typography>
                        </Box>
                      )}
                    </Box>
                  </StepLabel>
                </Step>
              );
            })}
          </Stepper>
        </Box>
      </Box>
    </Box>
  );
};

export default EmployeeOffboardingStatus;
