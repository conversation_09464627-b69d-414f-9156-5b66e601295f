import { VisibilityOutlined } from "@mui/icons-material";
import { IconButton, Typography } from "@mui/material";
import { format } from "date-fns";
import { MRT_ColumnDef } from "material-react-table";
import React from "react";

import { EmployeeCellInfo } from "src/modules/Common/EmployeeViews/EmployeeCellInfo";
import LoadingScreen from "src/modules/Common/LoadingScreen/LoadingScreen";
import DataTable from "src/modules/Common/Table/DataTable";
import { EmployeeOffboardingTableResposnse } from "src/services/api_definitions/employees";
import { formatDateToDayMonthYear } from "src/utils/dateUtils";
import { getStatusColors } from "src/utils/typographyUtils";

const columns: MRT_ColumnDef<EmployeeOffboardingTableResposnse>[] = [
  {
    accessorKey: "name",
    header: "Employee",
    Cell: ({ row }) => (
      <EmployeeCellInfo
        name={row?.original?.name}
        jobTitle={row?.original?.job_title}
        displayPic={row?.original?.display_pic}
      />
    ),
    size: 220,
  },
  {
    accessorKey: "department",
    header: "Department",
    size: 180,
  },
  {
    accessorKey: "resignation_date",
    header: "Resignation Date",
    size: 180,
    Cell: ({ row }) => formatDateToDayMonthYear(row?.original?.resignation_date),
  },
  {
    accessorKey: "notice_period",
    header: "Notice Period",
    size: 180,
  },
  {
    accessorKey: "status",
    header: "Current Stage",
    Cell: ({ row }) => <Typography color={getStatusColors(row?.original?.status)}>{row?.original?.status}</Typography>,
    size: 180,
  },
  {
    accessorKey: "action",
    header: "Action",
  },
];

const EmployeeOffboardingTable = ({
  onViewClick,
  employeeOffboardingDetails,
  loading,
}: {
  onViewClick: (request: EmployeeOffboardingTableResposnse) => void;
  employeeOffboardingDetails: EmployeeOffboardingTableResposnse[];
  loading: boolean;
}) => {
  const tableData = React.useMemo(() => {
    if (!employeeOffboardingDetails) return [];
    return employeeOffboardingDetails.map((request) => ({
      ...request,
      resignation_date: format(request.resignation_date, "yyyy-MM-dd"),
      action: (
        <IconButton onClick={() => onViewClick(request)}>
          <VisibilityOutlined />
        </IconButton>
      ),
    }));
  }, [employeeOffboardingDetails]);

  if (!loading && employeeOffboardingDetails) {
    return <DataTable columns={columns} data={tableData} />;
  }

  return <LoadingScreen />;
};

export default EmployeeOffboardingTable;
