import { Box, Tooltip } from "@mui/material";
import React from "react";

import Span from "src/modules/Common/Span/Span";
import { OffboardingSummary } from "src/services/api_definitions/employees";

type Props = {
  offboardingSummary: OffboardingSummary;
};

const colors = ["#CEF2E9", "#D7EDFF", "#DAD4FF", "#FFE2EB", "#FFE4C1", "#03A9F4", "#00BCD4"];

const OffboardingSummary = ({ offboardingSummary }: Props) => {
  const separationSummaryArray = [
    {
      summaryType: "Total Employees",
      count: offboardingSummary.total_employees,
    },
    {
      summaryType: "Resignation Approval Pending",
      count: offboardingSummary.resignation_approval_pending,
    },
    {
      summaryType: "On Notice Period",
      count: offboardingSummary.on_notice_period,
    },
  ];
  return (
    <Box display="flex" flexWrap="wrap" gap={4}>
      {separationSummaryArray.map((summary, index) => (
        <Tooltip title={summary.summaryType} placement="top" key={summary.summaryType}>
          <Box
            sx={{
              background: colors[index],
              height: "80px",
              display: "flex",
              minWidth: "180px",
              maxWidth: "400px",
              borderRadius: "8px",
              flexDirection: "column",
              padding: "10px !important",
              justifyContent: "space-between",
              width: "calc(33% - 24px)",
            }}
          >
            <Span sx={{ fontSize: "24px", color: "#000000", fontWeight: 600 }}>{summary.count}</Span>
            <Span
              sx={{
                fontSize: "14px",
                fontWeight: 500,
                color: "#42526B",
                whiteSpace: "nowrap",
                overflow: "hidden",
                textOverflow: "ellipsis",
              }}
            >
              {summary.summaryType}
            </Span>
          </Box>
        </Tooltip>
      ))}
    </Box>
  );
};

export default OffboardingSummary;
