import { Box, Button, Grid2 } from "@mui/material";
import { useMutation } from "@tanstack/react-query";

import { format } from "date-fns";
import React, { useMemo, useState } from "react";
import ContentHeader from "src/modules/Common/ContentHeader/ContentHeader";
import { EmployeeOffboardingTableResposnse } from "src/services/api_definitions/employees";
import employeesService from "src/services/employees.service";
import { DD_MM_YYYY, getIntlTimeToSpecifiedFormat } from "src/utils/dateUtils";
import { RevokeConfirmationModal } from "./ConfirmationModal";
import DetailsSummaryCard from "./DetailsSummaryCard";
import EmployeeOffboardingApprovalForm from "./EmployeeOffboardingApprovalForm";
import { getOffboardingData } from "./EmployeeOffboardingDetails";
import EmployeeOffboardingStatus from "./EmployeeOffboardingStatus";

type Props = {
  employee: EmployeeOffboardingTableResposnse;
  onClose: () => void;
  isManagerView?: boolean;
  isHRAdminView?: boolean;
  refetchAllEmployeeDetails?: () => void;
};

const getOffboardingStatus = (employeeOffboardingDetails: EmployeeOffboardingTableResposnse) => {
  const managerResponse = employeeOffboardingDetails.request_status.find(
    (status) => status.approver_role === "Manager",
  );
  const HRAdminResponse = employeeOffboardingDetails.request_status.find((status) => status.approver_role === "HRBP");
  const isRecinded = employeeOffboardingDetails.status === "Rescinded";
  return [
    {
      title: "Employee Request",
      status: isRecinded ? "Rescinded" : "Resigned",
      date: format(employeeOffboardingDetails?.resignation_date, DD_MM_YYYY),
    },
    {
      title: "Manager Response",
      status: managerResponse?.status || "",
      date: managerResponse?.actioned_at
        ? getIntlTimeToSpecifiedFormat(managerResponse?.actioned_at, DD_MM_YYYY).formattedDate
        : null,
    },
    {
      title: "HR Response",
      status: HRAdminResponse?.status || "",
      date: HRAdminResponse?.actioned_at
        ? getIntlTimeToSpecifiedFormat(HRAdminResponse?.actioned_at, DD_MM_YYYY).formattedDate
        : null,
    },
  ];
};
const submittedStatus = ["Accepted", "Denied"];

const EmployeeOffboardingApprovalDetails = ({
  employee,
  onClose,
  refetchAllEmployeeDetails,
  isManagerView,
  isHRAdminView,
}: Props) => {
  const [offboardingStatus] = useMemo(() => {
    return [getOffboardingStatus(employee)];
  }, [employee]);

  const {
    employeeOffboardingDetailsData: employeeData,
    managerOffboardingDetails: managerData,
    hrOffboardingDetails: HRAdminData,
    employeeRequestDetails: employeeRequestData,
  } = getOffboardingData(employee);

  if (!employee) {
    return null;
  }

  const [showConfirmationModal, setShowConfirmationModal] = React.useState(false);

  const onFormSubmitSuccess = async () => {
    await refetchAllEmployeeDetails?.();
    setShowConfirmationModal(false);
    setIsHRAdminEdit(false);
    setIsManagerEdit(false);
  };

  const revokeMutation = useMutation({
    mutationKey: ["offboarding-revoke"],
    mutationFn: async (comment: string) => employeesService.revokeEmployeeOffboarding(employee.employee_code, comment),
    onSuccess: (success) => {
      if (success) {
        onFormSubmitSuccess();
      }
    },
  });

  const isRequestDenied = employee.status === "Denied" || employee.status === "Rescinded";
  const isEmployeeActive = employee.employment_status !== "Inactive";
  const hasManagerResponse = submittedStatus.includes(offboardingStatus[1].status) || isRequestDenied;
  const hasHRResponse = submittedStatus.includes(offboardingStatus[2].status) || isRequestDenied;

  const [isManagerEdit, setIsManagerEdit] = useState(!hasManagerResponse);
  const [isHRAdminEdit, setIsHRAdminEdit] = useState(!hasHRResponse);

  const hrAdminResponse = employee?.request_status.find((status) => status.approver_role === "HRBP");
  const managerResponse = employee?.request_status.find((status) => status.approver_role === "Manager");
  const hasHRBPApproved = hrAdminResponse?.status === "Accepted";

  const enableHRAdminEdit = () => {
    setIsHRAdminEdit(true);
  };

  const rescindButtonStyle = {
    marginBottom: "10px",
    marginLeft: "auto",
  };

  const actionButton = isHRAdminView && !isRequestDenied && isEmployeeActive && hasHRBPApproved && (
    <Button sx={rescindButtonStyle} variant="contained" color="error" onClick={() => setShowConfirmationModal(true)}>
      Revoke
    </Button>
  );
  return (
    <Box display="flex" flexDirection="column" width="100%" paddingTop="0" padding="6px" height="100%">
      <ContentHeader showBackButton title="Back" goBack={onClose} actions={actionButton} />
      <Grid2 container paddingBottom="30px">
        <Grid2 size={12} mb={3}>
          <DetailsSummaryCard title="" data={employeeData} />
        </Grid2>
        <Grid2 size={12}>
          <EmployeeOffboardingStatus steps={offboardingStatus} title="" />
          <DetailsSummaryCard title="Employee Request" data={employeeRequestData} />
          <Box marginTop="20px">
            {!isRequestDenied && isManagerEdit && isManagerView && (
              <EmployeeOffboardingApprovalForm
                employee_code={employee.employee_code}
                onClose={onFormSubmitSuccess}
                title="Manager Response"
                onCancelEdit={() => setIsManagerEdit(false)}
                formData={managerResponse}
                showCancelButton={hasManagerResponse}
              />
            )}
            {!isManagerEdit && managerData.length > 0 && (
              <DetailsSummaryCard
                showEditButton={false}
                title="Manager Response"
                data={managerData}
                // onEditClick={enableManagerEdit}
              />
            )}
          </Box>

          <Box marginTop="20px">
            {isHRAdminEdit && isHRAdminView && !isRequestDenied && (
              <EmployeeOffboardingApprovalForm
                employee_code={employee.employee_code}
                onClose={onFormSubmitSuccess}
                title="HR Response"
                isHRAdminView
                onCancelEdit={() => setIsHRAdminEdit(false)}
                showCancelButton={hasHRResponse}
                formData={hrAdminResponse}
              />
            )}
            {!isHRAdminEdit && HRAdminData.length > 0 && (
              <DetailsSummaryCard
                showEditButton={isHRAdminView && !isRequestDenied && isEmployeeActive}
                title="HR Response"
                data={HRAdminData}
                onEditClick={enableHRAdminEdit}
              />
            )}
          </Box>
        </Grid2>
        <RevokeConfirmationModal
          showConfirmationModal={showConfirmationModal}
          setShowConfirmationModal={setShowConfirmationModal}
          onSubmit={(comment) => {
            revokeMutation.mutate(comment);
          }}
        />
      </Grid2>
    </Box>
  );
};

export default EmployeeOffboardingApprovalDetails;
