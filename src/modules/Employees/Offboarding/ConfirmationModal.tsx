import { Box, Button } from "@mui/material";
import React from "react";
import CustomTextField from "src/modules/Common/FormInputs/CustomTextField";
import Modal from "src/modules/Common/Modal/Modal";
import Span from "src/modules/Common/Span/Span";
import { applyButtonStyle, cancelButtonStyle } from "./EmployeeOffboardingApprovalForm";

type ConfirmationModalProps = {
  showConfirmationModal: boolean;
  setShowConfirmationModal: React.Dispatch<React.SetStateAction<boolean>>;
  onSubmit: (comment: string) => void;
};
const RevokeConfirmationModal = ({
  showConfirmationModal,
  setShowConfirmationModal,
  onSubmit,
}: ConfirmationModalProps) => {
  const [comment, setComment] = React.useState("");
  return (
    <Modal isOpen={showConfirmationModal} onClose={() => setShowConfirmationModal(false)} maxWidth="630px">
      <Box
        sx={{
          display: "flex",
          flexDirection: "column",
          gap: 1,
          padding: "8px",
        }}
      >
        <Span sx={{ fontSize: "20px" }}>Revoke request</Span>
        <Span sx={{ fontSize: "14px" }}>
          Are you sure you want to revoke this resignation? This will cancel the separation process and keep the
          employee active.
        </Span>
        <CustomTextField
          title="Comments"
          required
          value={comment}
          onChange={(e) => setComment(e.target.value)}
          multiline
          rows={3}
        />
        <Box display="flex" justifyContent="flex-end" sx={{ gap: 2, marginTop: "36px" }}>
          <Button
            variant="text"
            sx={cancelButtonStyle}
            color="secondary"
            onClick={() => setShowConfirmationModal(false)}
          >
            Cancel
          </Button>
          <Button
            variant="contained"
            sx={applyButtonStyle}
            color="error"
            onClick={() => onSubmit(comment)}
            disabled={!comment}
          >
            Revoke
          </Button>
        </Box>
      </Box>
    </Modal>
  );
};

const RescindConfirmationModal = ({ showConfirmationModal, setShowConfirmationModal, onSubmit }: any) => {
  return (
    <Modal isOpen={showConfirmationModal} onClose={() => setShowConfirmationModal(false)} maxWidth="630px">
      <Box
        sx={{
          display: "flex",
          flexDirection: "column",
          gap: 1,
          padding: "8px",
        }}
      >
        <Span sx={{ fontSize: "20px" }}>Rescind request</Span>
        <Span sx={{ fontSize: "14px" }}>Are you sure you want to rescind the resignation request?</Span>
        <Box display="flex" justifyContent="flex-end" sx={{ gap: 2, marginTop: "36px" }}>
          <Button
            variant="text"
            sx={cancelButtonStyle}
            color="secondary"
            onClick={() => setShowConfirmationModal(false)}
          >
            Cancel
          </Button>
          <Button
            variant="contained"
            sx={applyButtonStyle}
            color="error"
            onClick={() => onSubmit()}
            // disabled={!comment}
          >
            Rescind
          </Button>
        </Box>
      </Box>
    </Modal>
  );
};

export { RevokeConfirmationModal, RescindConfirmationModal };
