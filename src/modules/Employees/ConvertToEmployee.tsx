import { useMutation, useQueries } from "@tanstack/react-query";
import React, { useMemo } from "react";
import { BaseObject } from "src/app/global";
import employeesService from "src/services/employees.service";

import EmployeeStepper, { CandidateGlobalFormType, stepsKey } from "./EmployeeStepper";
import LoadingScreen from "./LoadingScreen";
import { INPUT_FIELDS as personalInformationInputFields } from "./config/PersonalInformation";
import {
  CandidateDetails,
  transformCandidateDetails,
  transformUpdateCandidateDetails,
} from "./utils/candidateTransformer";

type Props = {
  setFinishForm: (value: boolean) => void;
  candidateEmail: string;
};

const ConvertToEmployee = ({ setFinishForm, candidateEmail }: Props) => {
  const result = useQueries({
    queries: [
      {
        queryKey: ["get-candidate-details", candidateEmail],
        queryFn: async (): Promise<CandidateDetails | null> => employeesService.getCandidateDetails(candidateEmail),
        retryOnMount: false,
        refetchOnWindowFocus: false,
        enabled: !!candidateEmail,
      },
    ],
  });
  const [{ isLoading: isCandidateDetailsLoading, data: candidateDetails }] = result;

  const mutation = useMutation({
    mutationKey: ["create-new-employee"],
    mutationFn: async (employeeData: BaseObject) => employeesService.createEmployees(employeeData),
    onSuccess: (response) => {
      if (response) setFinishForm(true);
    },
  });

  const handleFinish = (finalForm: CandidateGlobalFormType) => {
    mutation.mutate(transformUpdateCandidateDetails(finalForm, true));
  };

  const steps: string[] = [
    stepsKey.personalInformation,
    stepsKey.employementDetails,
    stepsKey.employeeCompensation,
    stepsKey.employmentHistory,
    stepsKey.bankDetails,
    stepsKey.familyDetails,
    stepsKey.emergencyDetails,
    stepsKey.educationDetails,
    stepsKey.documentUpload,
  ];

  const initialForm = useMemo(
    () => (!isCandidateDetailsLoading && candidateDetails ? transformCandidateDetails(candidateDetails) : {}),
    [candidateDetails],
  );

  if (isCandidateDetailsLoading || !candidateDetails) {
    return <LoadingScreen />;
  }

  const stepsProps = {
    [stepsKey.personalInformation]: {
      disabledInputFields: [
        personalInformationInputFields.FIRST_NAME,
        personalInformationInputFields.LAST_NAME,
        personalInformationInputFields.PERSONAL_EMAIL,
      ],
    },
    [stepsKey.employmentHistory]: {
      isSkip: true,
    },
  };
  return <EmployeeStepper steps={steps} onSubmit={handleFinish} initialForm={initialForm} stepsProps={stepsProps} />;
};

export default ConvertToEmployee;
