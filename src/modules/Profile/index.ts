import { BankInfomation } from "./components/components/BankDetails";
import { EducationDetail } from "./components/components/EducationInformation";
import { EmergencyContactDetail } from "./components/components/EmergencyContacts";
import { WorkExperienceDetail } from "./components/components/EmploymentHistory";
import { FamilyDetails } from "./components/components/FamilyDetails";
import { PersonalInformationMetaData } from "./components/components/PersonalInformation";

export interface ViewProps<T> {
  informationMetaData: T;
  isViewMode?: boolean;
}
export interface HeaderProps<T> {
  informationMetaData: T;
}

export type AccordionPropsTypes = PersonalInformationMetaData &
  EducationDetail[] &
  WorkExperienceDetail[] &
  BankInfomation &
  EmergencyContactDetail[] &
  FamilyDetails[];
