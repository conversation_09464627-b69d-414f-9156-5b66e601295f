import AssignmentTurnedInIcon from "@mui/icons-material/AssignmentTurnedIn";
import BusinessIcon from "@mui/icons-material/Business";
import CakeIcon from "@mui/icons-material/Cake";
import CheckCircleIcon from "@mui/icons-material/CheckCircle";
import LogoutIcon from "@mui/icons-material/Logout";
import MilitaryTechIcon from "@mui/icons-material/MilitaryTech";
import PersonAddIcon from "@mui/icons-material/PersonAdd";
import PersonRemoveIcon from "@mui/icons-material/PersonRemove";
import SwapHorizontalCircleIcon from "@mui/icons-material/SwapHorizontalCircle";
import Timeline from "@mui/lab/Timeline";
import TimelineConnector from "@mui/lab/TimelineConnector";
import TimelineContent from "@mui/lab/TimelineContent";
import TimelineDot from "@mui/lab/TimelineDot";
import TimelineItem from "@mui/lab/TimelineItem";
import TimelineOppositeContent from "@mui/lab/TimelineOppositeContent";
import TimelineSeparator from "@mui/lab/TimelineSeparator";
import { Tooltip, styled } from "@mui/material";
import Typography from "@mui/material/Typography";
import { useQuery } from "@tanstack/react-query";
import * as React from "react";
import NoData from "src/modules/Dashboard/component/QuickViews/components/NoDataScreens/NoData";
import profileService from "src/services/profile.service";
import { DD_MM_YYYY, getIntlTimeToSpecifiedFormat } from "src/utils/dateUtils";
import { getStatusColors } from "src/utils/typographyUtils";

const CustomConnector = styled(TimelineConnector)(() => ({
  border: "none",
  borderLeft: "2.5px dashed #bdbdbd",
  backgroundColor: "white",
}));

type Props = {
  item: {
    event_type: string;
    event_date: string;
    event_details: string;
    old_value: string | null;
    new_value: string | null;
  };
  index: number;
};

const EVENTS = [
  { type: "Joining Day", icon: <PersonAddIcon /> },
  { type: "Probation Confirmation", icon: <CheckCircleIcon /> },
  { type: "Promotion", icon: <MilitaryTechIcon /> },
  { type: "Resignation", icon: <LogoutIcon /> },
  { type: "Manager Change", icon: <SwapHorizontalCircleIcon /> },
  { type: "Department Change", icon: <BusinessIcon /> },
  { type: "Last Working Day", icon: <PersonRemoveIcon /> },
  { type: "Work Anniversary", icon: <CakeIcon /> },
  { type: "Employee Type Change", icon: <AssignmentTurnedInIcon /> },
];

const EmployeeJourneyTimelineItem: React.FC<Props> = ({ item, index }) => {
  const event = React.useMemo(() => EVENTS.find((e) => e?.type === item?.event_type), [item, EVENTS]);
  const isFirstDay = item.event_type === "Joining Day";
  const isLastDay = item.event_type === "Last Working Day";

  const getTooltipTitle = (oldValue: string | null, newValue: string | null) => (
    <>
      {oldValue && <Typography variant="body2">Old: {oldValue}</Typography>}
      {newValue && <Typography variant="body2">New: {newValue}</Typography>}
    </>
  );

  return (
    <TimelineItem key={index} sx={{ minHeight: "110px", paddingBottom: "3px" }}>
      <TimelineOppositeContent sx={{ m: "auto 0" }} align="right" variant="body2" color="rgb(139, 0, 0)">
        {getIntlTimeToSpecifiedFormat(item?.event_date, DD_MM_YYYY)?.formattedDate}
      </TimelineOppositeContent>
      <TimelineSeparator>
        {index == 0 && isFirstDay ? <CustomConnector sx={{ visibility: "hidden" }} /> : <CustomConnector />}
        <Tooltip
          title={getTooltipTitle(item.old_value, item.new_value)}
          arrow
          disableHoverListener={!item.old_value && !item.new_value}
        >
          <TimelineDot
            style={{ backgroundColor: getStatusColors(event?.type as string), color: "#fff", padding: "8px" }}
          >
            {event?.icon}
          </TimelineDot>
        </Tooltip>
        {isLastDay ? <CustomConnector sx={{ visibility: "hidden" }} /> : <CustomConnector />}
      </TimelineSeparator>
      <TimelineContent sx={{ py: "12px", px: 2, justifyContent: "center", display: "flex", flexDirection: "column" }}>
        <Typography variant="h6" component="span" fontWeight="bold">
          {item.event_type}
        </Typography>
        <Typography>{item.event_details}</Typography>
      </TimelineContent>
    </TimelineItem>
  );
};

export default function EmployeeJourney({
  isAdminFlow = false,
  employeeCode,
}: { isAdminFlow?: boolean; employeeCode?: string }) {
  const { data: employeeJourney } = useQuery({
    queryKey: ["get-employee-journey", employeeCode],
    queryFn: async () =>
      isAdminFlow ? profileService.getEmployeeJourneyAdmin(employeeCode || "") : profileService.fetchEmployeeJourney(),
    refetchOnWindowFocus: false,
    refetchOnMount: false,
    enabled: !!employeeCode || !isAdminFlow,
  });

  if (employeeJourney?.length === 0) {
    return <NoData title="No Journey Found" />;
  }

  return (
    <Timeline position="alternate">
      {employeeJourney?.map((item, index) => (
        <EmployeeJourneyTimelineItem key={`${item?.event_type}_${index}`} item={item} index={index} />
      ))}
    </Timeline>
  );
}
