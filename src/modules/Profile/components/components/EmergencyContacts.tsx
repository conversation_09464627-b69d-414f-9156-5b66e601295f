import { Grid, Typography } from "@mui/material";
import React from "react";
import Span from "src/modules/Common/Span/Span";
import { ViewProps } from "src/modules/Profile";

export interface EmergencyContactDetail {
  name: string;
  phone: string;
  relation: string;
  primary: boolean;
}

interface EmergencyContactsProps extends ViewProps<EmergencyContactDetail[]> {}

const EmergencyContacts: React.FC<EmergencyContactsProps> = ({ informationMetaData }) => {
  return (
    <Grid container spacing={2} minHeight={130}>
      {informationMetaData.map((item, index) => (
        <Grid item xs={12} key={index}>
          <Grid container justifyContent="space-between" alignItems="flex-start">
            <Grid item sx={{ display: "flex", flexDirection: "column", gap: "4px" }}>
              <Typography variant="subtitle1" display={"inline-block"} fontSize={14} fontWeight={500}>
                {item.name} | <Span color="#667085">{item.relation}</Span>
              </Typography>
              <Typography variant="body2" color="#667085">
                Mobile No: {item.phone}
              </Typography>
            </Grid>
            <Grid item>
              <Typography variant="body2" fontWeight={500}>
                {item.primary ? "Primary" : "Secondary"}
              </Typography>
            </Grid>
          </Grid>
        </Grid>
      ))}
      {informationMetaData.length === 0 && (
        <Span sx={{ width: "100%", textAlign: "center", marginTop: "16px" }}> No Data available</Span>
      )}
    </Grid>
  );
};

export default EmergencyContacts;
