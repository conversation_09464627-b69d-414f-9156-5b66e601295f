import { Grid, Typography } from "@mui/material";
import React from "react";
import Span from "src/modules/Common/Span/Span";
import { ViewProps } from "src/modules/Profile";

export interface FamilyDetails {
  firstName: string;
  lastName: string;
  relation: string;
  dateOfBirth: string;
}

interface FamilyDetailsProps extends ViewProps<FamilyDetails[]> {}

const FamilyDetails: React.FC<FamilyDetailsProps> = ({ informationMetaData }) => {
  return (
    <Grid container spacing={2} minHeight={130}>
      {informationMetaData.map((item, index) => (
        <Grid item xs={12} key={index}>
          <Grid container justifyContent="space-between" alignItems="flex-start">
            <Grid item sx={{ display: "flex", flexDirection: "column", gap: "4px" }}>
              <Typography variant="body2" display={"inline-block"} fontWeight={500}>
                {item.firstName}&nbsp;{item.lastName}
              </Typography>
              <Typography variant="body2" color="#667085">
                DOB: {item.dateOfBirth}
              </Typography>
            </Grid>
            <Grid item>
              <Typography variant="body2" fontWeight={500}>
                {item.relation}
              </Typography>
            </Grid>
          </Grid>
        </Grid>
      ))}
      {informationMetaData.length === 0 && (
        <Span sx={{ width: "100%", textAlign: "center", marginTop: "16px" }}> No Data available</Span>
      )}
    </Grid>
  );
};

export default FamilyDetails;
