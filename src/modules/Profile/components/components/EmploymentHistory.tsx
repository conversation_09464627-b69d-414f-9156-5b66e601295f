import { Grid, Typography } from "@mui/material";
import React from "react";
import Span from "src/modules/Common/Span/Span";
import { ViewProps } from "src/modules/Profile";

export interface WorkExperienceDetail {
  designation: string;
  companyName: string;
  period: string;
}

interface EmploymentHistoryProps extends ViewProps<WorkExperienceDetail[]> {}

const EmploymentHistory: React.FC<EmploymentHistoryProps> = ({ informationMetaData }) => {
  return (
    <Grid container spacing={2} minHeight={140}>
      {informationMetaData.map((item, index) => (
        <Grid item xs={12} key={index}>
          <Grid container justifyContent="space-between" alignItems="flex-start">
            <Grid item sx={{ display: "flex", flexDirection: "column", gap: "4px" }}>
              <Typography variant="body2" display={"inline-block"} fontWeight={500} color="#000000">
                {item.designation}
              </Typography>
              <Typography variant="body2" fontWeight={400} color="#667085">
                {item.companyName}
              </Typography>
            </Grid>
            <Grid item>
              <Typography variant="body2" fontWeight={500} color="#000000">
                {item.period}
              </Typography>
            </Grid>
          </Grid>
        </Grid>
      ))}
      {informationMetaData.length === 0 && (
        <Span sx={{ width: "100%", textAlign: "center", marginTop: "16px" }}> No Data available</Span>
      )}
    </Grid>
  );
};

export default EmploymentHistory;
