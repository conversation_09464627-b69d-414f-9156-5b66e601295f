import { Box, Grid, Typography } from "@mui/material";
import React from "react";
import Span from "src/modules/Common/Span/Span";
import { ViewProps } from "src/modules/Profile";
import { keyToTitleTransformer } from "src/modules/Profile/utils/keyToTitleTransformer";

export interface BankInfomation {
  accountNumber: string;
  IFSC_Code: string;
  bankName: string;
  branch: string;
  address: string;
}

interface BankDetailsProps extends ViewProps<BankInfomation> {}

const BankDetails: React.FC<BankDetailsProps> = ({ informationMetaData }) => {
  if (Object.keys(informationMetaData).length === 0) {
    return (
      <Box sx={{ textAlign: "center" }}>
        <Span sx={{ width: "100%", marginTop: "16px" }}> No Details available</Span>
      </Box>
    );
  }
  return (
    <Grid container spacing={2} minHeight={130}>
      {Object.entries(informationMetaData).map(([key, value]) => (
        <Grid item xs={6} sm={12} md={6} key={key} sx={{ gap: 1, display: "flex", flexDirection: "column" }}>
          <Typography variant="body2" color="#000000" fontWeight={500}>
            {keyToTitleTransformer(key)}
          </Typography>
          <Typography fontWeight={400} variant="body2" color="#667085">
            {value}
          </Typography>
        </Grid>
      ))}
    </Grid>
  );
};

export default BankDetails;
