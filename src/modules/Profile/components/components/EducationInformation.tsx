import { Grid, Typography } from "@mui/material";
import React from "react";
import Span from "src/modules/Common/Span/Span";
import { ViewProps } from "src/modules/Profile";
import DisplayTaglines from "src/modules/Profile/components/DisplayTagline";

export interface EducationDetail {
  degree: string;
  type: string;
  institution: string;
  affiliation: string;
  period: string;
}

interface EducationInformationProps extends ViewProps<EducationDetail[]> {}

const EducationInformation: React.FC<EducationInformationProps> = ({ informationMetaData }) => {
  return (
    <Grid container spacing={2} minHeight={140}>
      {informationMetaData.map((item, index) => (
        <Grid item xs={12} key={index}>
          <Grid container justifyContent="space-between" alignItems="flex-start">
            <Grid item sx={{ display: "flex", flexDirection: "column", gap: "4px" }} maxWidth="80%">
              <DisplayTaglines
                title1={
                  <Span color="#000000" fontWeight={500}>
                    {item.degree}
                  </Span>
                }
                title2={
                  <Span color="#667085" fontWeight={500}>
                    {item.type}
                  </Span>
                }
              />
              <DisplayTaglines
                title1={
                  <Span color="#667085" fontWeight={400}>
                    {item.institution}
                  </Span>
                }
                title2={
                  <Span color="#667085" fontWeight={400}>
                    {item.affiliation}
                  </Span>
                }
              />
            </Grid>
            <Grid item>
              <Typography variant="body2" color="#000000" fontWeight={500}>
                {item.period}
              </Typography>
            </Grid>
          </Grid>
        </Grid>
      ))}
      {informationMetaData.length === 0 && (
        <Span sx={{ width: "100%", textAlign: "center", marginTop: "16px" }}> No Data available</Span>
      )}
    </Grid>
  );
};

export default EducationInformation;
