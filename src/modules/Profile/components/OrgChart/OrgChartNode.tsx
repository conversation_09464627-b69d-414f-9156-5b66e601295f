import { Visibility } from "@mui/icons-material";
import { <PERSON><PERSON>, Box, Card, CardContent, Di<PERSON><PERSON>, Icon<PERSON>utton, <PERSON>lt<PERSON>, Typography } from "@mui/material";
import { Handle, Position } from "@xyflow/react";
import React, { useState } from "react";
import { OrgChartPopover } from "./OrgChartPopover";

interface NodeData {
  name: string;
  image: string;
  designation: string;
  employee_code: string;
  number_of_reportees?: number;
  email?: string;
  department?: string;
  business_unit?: string;
  gender?: string;
  tenure?: string;
}

const NodeCard = ({ data }: { data: NodeData }) => {
  const [anchorEl, setAnchorEl] = useState<HTMLButtonElement | null>(null);

  const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    event.stopPropagation();
    setAnchorEl(event.currentTarget);
  };

  const handleClose = (event: React.MouseEvent<HTMLElement>) => {
    event.stopPropagation();
    setAnchorEl(null);
  };

  return (
    <div style={{ position: "relative" }}>
      <Handle type="target" position={Position.Top} id="top" style={{ background: "#008080", width: 10, height: 10 }} />
      <Card
        sx={{ width: 280, minHeight: 120, borderRadius: 2, boxShadow: 3, overflow: "visible", position: "relative" }}
      >
        <IconButton
          onClick={handleClick}
          sx={{
            position: "absolute",
            right: 8,
            top: 8,
            zIndex: 1,
            backgroundColor: "rgba(255, 255, 255, 0.8)",
            "&:hover": {
              backgroundColor: "rgba(255, 255, 255, 0.9)",
            },
          }}
        >
          <Tooltip title="Details" arrow>
            <Visibility fontSize="small" />
          </Tooltip>
        </IconButton>

        <Box sx={{ height: 4, bgcolor: "success.main", borderTopLeftRadius: "8px", borderTopRightRadius: "8px" }} />
        <CardContent>
          {/* Avatar Section */}
          <Box sx={{ position: "absolute", top: -24, left: "50%", transform: "translateX(-50%)" }}>
            <Avatar src={data.image} sx={{ width: 60, height: 60, border: "4px solid white" }} />
          </Box>

          {/* Name Section */}
          <Typography
            variant="h6"
            sx={{
              mt: 2,
              fontWeight: "bold",
              textAlign: "center",
              overflow: "hidden",
              textOverflow: "ellipsis",
              whiteSpace: "nowrap",
              maxWidth: "100%",
            }}
          >
            {data.name}
          </Typography>
          <Divider sx={{ my: 1 }} />

          {/* Designation and DR Count Section */}
          <Box sx={{ display: "flex", alignItems: "center", justifyContent: "space-between", position: "relative" }}>
            <Typography
              variant="body1"
              color="text.secondary"
              sx={{
                textAlign: "center",
                flexGrow: 1,
                marginLeft: 5,
                overflow: "hidden",
                textOverflow: "ellipsis",
                whiteSpace: "nowrap",
                maxWidth: "calc(100% - 80px)",
              }}
            >
              {data.designation}
            </Typography>
            <Typography
              variant="body1"
              color="text.secondary"
              sx={{
                backgroundColor: "#ecf9f2",
                borderRadius: 1,
                fontWeight: "bold",
                padding: "2px 6px",
                fontSize: "11px",
                textAlign: "center",
                minWidth: "fit-content",
                marginLeft: "auto",
              }}
            >
              (DR: {data.number_of_reportees})
            </Typography>
          </Box>
        </CardContent>
      </Card>

      <OrgChartPopover open={Boolean(anchorEl)} anchorEl={anchorEl} onClose={handleClose} data={data} />

      <Handle
        type="source"
        position={Position.Bottom}
        id="bottom"
        style={{
          width: "10px",
          height: "10px",
          background: "#008080",
          border: "2px solid #008080",
        }}
      />
    </div>
  );
};

export default NodeCard;
