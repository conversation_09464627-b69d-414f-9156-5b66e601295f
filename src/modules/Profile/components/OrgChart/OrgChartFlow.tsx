import {
  Background,
  BackgroundV<PERSON>t,
  <PERSON>s,
  Edge,
  <PERSON>erType,
  ReactFlow,
  useEdgesState,
  useNodesState,
} from "@xyflow/react";
import "@xyflow/react/dist/style.css";
import { ArrowBack } from "@mui/icons-material";
import FullscreenIcon from "@mui/icons-material/Fullscreen";
import FullscreenExitIcon from "@mui/icons-material/FullscreenExit";
import { Box, IconButton } from "@mui/material";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { useCallback, useEffect, useMemo, useState } from "react";
import React from "react";
import { useParams } from "react-router-dom";
import useToggle from "src/customHooks/useToggle";
import { ProfileViewModes } from "src/pages/Profile";
import { JobTitle } from "src/services/api_definitions/employees";
import profileService from "src/services/profile.service";
import { calculateNodePositions } from "src/utils/nodePositionUtils";
import { OrgChartProps } from "../OrgChart";
import NodeCard from "./OrgChartNode";

export type CustomNode = {
  id: string;
  type: string;
  data: {
    name: string;
    designation: string | JobTitle;
    image: string;
    employee_code: string;
    number_of_reportees: number;
    email?: string;
    department?: string;
    business_unit?: string;
    gender?: string;
    tenure?: string;
  };
  position: { x: number; y: number };
  draggable: boolean;
};

export type EmployeeData = {
  display_name: string;
  display_pic?: string;
  employee_code: string;
  job_title?: string;
  email?: string;
  gender?: string;
  department?: string;
  business_unit?: string;
  number_of_reportees?: number;
  manager?: {
    employee_code: string;
    display_name: string;
    job_title: string;
    display_pic?: string;
    email: string;
    gender: string;
    department: string;
    business_unit: string;
    number_of_reportees: number;
    tenure: string;
  };
  reportees?: Reportee[];
};

export interface Reportee {
  employee_code: string;
  display_name: string;
  job_title: string;
  display_pic?: string;
  email: string;
  gender: string;
  department: string;
  business_unit: string;
  number_of_reportees: number;
  tenure: string;
}

const defaultEdgeStyle = {
  stroke: "#006D5B",
  strokeWidth: 2,
  // type: 'smoothstep',
};

const HORIZONTAL_SPACING = 350;
const VERTICAL_SPACING = 210;

const layoutConfig = {
  horizontalSpacing: HORIZONTAL_SPACING,
  verticalSpacing: VERTICAL_SPACING,
};

function FlowComponent({ setCurrentViewMode }: OrgChartProps) {
  const queryClient = useQueryClient();
  const [nodes, setNodes, onNodesChange] = useNodesState<CustomNode>([]);
  const [edges, setEdges, onEdgesChange] = useEdgesState<Edge>([]);
  const [expandedNodes, setExpandedNodes] = useState<Set<string>>(new Set());

  const { toggle: isFullScreen, toggleState: toggleExpandFullScreen } = useToggle();

  const { employeeId = null } = useParams();

  const { data: profileData } = useQuery({
    queryKey: ["get-profile-data", employeeId],
    queryFn: async () => {
      if (employeeId) {
        return profileService.fetchProfileData(employeeId);
      }
      return profileService.fetchProfileData();
    },
    refetchOnWindowFocus: false,
    refetchOnMount: false,
  });

  const createEmployeeQuery = (employeeId: string) => ({
    queryKey: ["get-employee-details", employeeId],
    queryFn: () => profileService.fetchProfileData(employeeId),
    staleTime: Infinity,
    cacheTime: 1000 * 60 * 60,
  });

  const nodeTypes = useMemo(
    () => ({
      custom: NodeCard,
    }),
    [],
  );

  const getEmployeeById = useCallback(
    (id: string): EmployeeData | undefined => {
      return queryClient.getQueryData<EmployeeData>(["get-employee-details", id]);
    },
    [queryClient],
  );

  const getAllDescendants = useCallback(
    (employeeCode: string): string[] => {
      const employee = getEmployeeById(employeeCode);
      if (!employee || !employee.reportees) return [];

      const descendants: string[] = [];
      const queue = [...employee.reportees];

      while (queue.length > 0) {
        const current = queue.shift();
        if (current) {
          descendants.push(current.employee_code);
          const currentEmployee = getEmployeeById(current.employee_code);
          if (currentEmployee?.reportees) {
            queue.push(...currentEmployee.reportees);
          }
        }
      }
      return descendants;
    },
    [getEmployeeById],
  );

  useEffect(() => {
    if (profileData) {
      setNodes([
        {
          id: profileData.employee_code,
          type: "custom",
          data: {
            name: profileData.display_name,
            designation: profileData.job_title || "N/A",
            image: profileData.display_pic || "",
            employee_code: profileData.employee_code,
            number_of_reportees: profileData.reportees?.length || 0,
            email: profileData.email,
            department: profileData?.department || "",
            business_unit: profileData.business_unit || "",
            gender: profileData.gender,
            tenure: profileData.tenure,
          },
          position: { x: 250, y: 5 },
          draggable: true,
        },
      ]);
    }
  }, [profileData, setNodes]);

  useEffect(() => {
    return () => {
      //Clear Cache when OrgChart unmounts
      queryClient.removeQueries({ queryKey: ["get-employee-details"] });
    };
  }, [queryClient]);

  const updateNodesAndEdges = useCallback(
    (clickedNode: CustomNode, employeeData: EmployeeData) => {
      const newNodes = [...nodes];
      const newEdges = [...edges];

      if (employeeData.reportees && employeeData.reportees?.length > 0) {
        const reporteePositions = calculateNodePositions(clickedNode, employeeData.reportees, nodes, layoutConfig);

        employeeData.reportees.forEach((reportee: any, index: number) => {
          if (!nodes.some((n) => n.id === reportee.employee_code)) {
            newNodes.push({
              id: reportee.employee_code,
              type: "custom",
              data: {
                name: reportee.display_name,
                designation: reportee.job_title,
                image: reportee.display_pic || "",
                employee_code: reportee.employee_code,
                number_of_reportees: reportee.number_of_reportees,
                email: reportee.email,
                department: reportee.department,
                business_unit: reportee.business_unit,
                gender: reportee.gender,
                tenure: reportee.tenure,
              },
              position: reporteePositions[index],
              draggable: true,
            });

            newEdges.push({
              id: `e-${clickedNode.id}-${reportee.employee_code}`,
              // type: "smoothstep",
              source: clickedNode.id,
              target: reportee.employee_code,
              sourceHandle: "bottom",
              targetHandle: "top",
              style: defaultEdgeStyle,
              markerEnd: {
                type: MarkerType.Arrow,
                width: 20,
                height: 20,
                color: "#006D5B",
              },
            });
          }
        });
      }

      if (employeeData.manager?.employee_code) {
        const managerY = clickedNode.position.y - VERTICAL_SPACING;

        if (!nodes.some((n) => n.id === employeeData.manager?.employee_code)) {
          newNodes.push({
            id: employeeData.manager.employee_code,
            type: "custom",
            data: {
              name: employeeData.manager.display_name,
              designation: employeeData.manager.job_title,
              image: employeeData.manager.display_pic || "",
              employee_code: employeeData.manager.employee_code,
              number_of_reportees: employeeData.manager.number_of_reportees,
              email: employeeData.manager.email,
              department: employeeData.manager.department,
              business_unit: employeeData.manager.business_unit,
              gender: employeeData.manager.gender,
              tenure: employeeData.manager.tenure,
            },
            position: {
              x: clickedNode.position.x,
              y: managerY,
            },
            draggable: true,
          });

          newEdges.push({
            id: `e-${employeeData.manager.employee_code}-${clickedNode.id}`,
            // type: "smoothstep",
            source: employeeData.manager.employee_code,
            target: clickedNode.id,
            sourceHandle: "bottom",
            targetHandle: "top",
            style: defaultEdgeStyle,
            markerEnd: {
              type: MarkerType.Arrow,
              width: 20,
              height: 20,
              color: "#006D5B",
            },
          });
        }
      }

      setNodes(newNodes);
      setEdges(newEdges);
    },
    [nodes, edges, setNodes, setEdges, calculateNodePositions],
  );

  const onNodeClick = useCallback(
    async (_: any, clickedNode: CustomNode) => {
      // Handle collapse
      if (expandedNodes.has(clickedNode.id)) {
        const descendantIds = getAllDescendants(clickedNode.data.employee_code);

        setNodes((nodes) => nodes.filter((node) => !descendantIds.includes(node.id)));
        setEdges((edges) =>
          edges.filter((edge) => !descendantIds.includes(edge.source) && !descendantIds.includes(edge.target)),
        );

        setExpandedNodes((prev) => {
          const next = new Set(prev);
          next.delete(clickedNode.id);
          descendantIds.forEach((id) => next.delete(id));
          return next;
        });
        return;
      }

      // Handle expansion
      const cachedData = queryClient.getQueryData<EmployeeData>([
        "get-employee-details",
        clickedNode.data.employee_code,
      ]);

      if (cachedData) {
        updateNodesAndEdges(clickedNode, cachedData);
        setExpandedNodes((prev) => new Set(prev).add(clickedNode.id));
      } else {
        const employeeData: EmployeeData = await queryClient.fetchQuery(
          createEmployeeQuery(clickedNode.data.employee_code),
        );

        if (employeeData) {
          updateNodesAndEdges(clickedNode, employeeData);
          setExpandedNodes((prev) => new Set(prev).add(clickedNode.id));
        }
      }
    },
    [nodes, edges, expandedNodes, getAllDescendants, queryClient, updateNodesAndEdges],
  );

  return (
    <Box
      style={{
        position: isFullScreen ? "fixed" : "relative",
        top: isFullScreen ? 0 : "auto",
        left: isFullScreen ? 0 : "auto",
        width: isFullScreen ? "100vw" : "100%",
        height: isFullScreen ? "100vh" : "80vh",
        background: "white",
        zIndex: isFullScreen ? 99999 : "auto",
      }}
    >
      <ReactFlow
        nodes={nodes}
        edges={edges}
        onNodesChange={onNodesChange}
        onEdgesChange={onEdgesChange}
        onNodeClick={onNodeClick}
        nodeTypes={nodeTypes}
        fitView
        className="bg-teal-50"
        minZoom={0.1}
        maxZoom={2}
      >
        <Controls position="top-right" showZoom={true} showFitView={false} showInteractive={false} />
        {isFullScreen && (
          <IconButton
            onClick={() => setCurrentViewMode(ProfileViewModes.MY_PROFILE)}
            sx={{
              position: "absolute",
              top: "10px",
              left: "10px",
              padding: "4px",
              zIndex: 9999,
              color: "grey",
              backgroundColor: "white",
            }}
          >
            <ArrowBack />
          </IconButton>
        )}
        <IconButton
          onClick={toggleExpandFullScreen}
          sx={{
            position: "absolute",
            top: "70px",
            right: "12px",
            padding: "4px",
            zIndex: 9999,
            color: "grey",
            backgroundColor: "white",
          }}
        >
          {isFullScreen ? <FullscreenExitIcon /> : <FullscreenIcon />}
        </IconButton>
        <Background variant={BackgroundVariant.Cross} gap={600}></Background>
      </ReactFlow>
    </Box>
  );
}

export default FlowComponent;
