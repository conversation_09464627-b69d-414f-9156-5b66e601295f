import { ContentCopy } from "@mui/icons-material";
import { Avatar, Box, Divider, IconButton, Popover, Typography } from "@mui/material";
import React, { useState } from "react";

interface OrgChartPopoverProps {
  open: boolean;
  anchorEl: HTMLButtonElement | null;
  onClose: (event: React.MouseEvent<HTMLElement>) => void;
  data: {
    name: string;
    image: string;
    designation: string;
    employee_code: string;
    email?: string;
    department?: string;
    business_unit?: string;
    gender?: string;
    number_of_reportees?: number;
    tenure?: string;
  };
}

export const OrgChartPopover: React.FC<OrgChartPopoverProps> = ({ open, anchorEl, onClose, data }) => {
  const [copySuccess, setCopySuccess] = useState<string>("");

  const handleCopyText = (text: string, event: React.MouseEvent<HTMLButtonElement>) => {
    event.stopPropagation();
    navigator.clipboard.writeText(text);
    setCopySuccess("Copied!");
    setTimeout(() => setCopySuccess(""), 2000);
  };

  return (
    <Popover
      open={open}
      anchorEl={anchorEl}
      onClose={onClose}
      onClick={(e) => e.stopPropagation()}
      anchorOrigin={{
        vertical: "center",
        horizontal: "right",
      }}
      transformOrigin={{
        vertical: "center",
        horizontal: "left",
      }}
      sx={{
        zIndex: 100000,
        "& .MuiPopover-paper": {
          zIndex: 100000,
          border: "2px solid #2e7d32",
          borderRadius: 2,
          bgcolor: "#f5f5f5",
          minWidth: 300,
          maxWidth: "max-content",
          position: "relative",
          "&::before": {
            content: '""',
            position: "absolute",
            left: -10,
            top: "50%",
            transform: "translateY(-50%)",
            borderWidth: 5,
            borderStyle: "solid",
            borderColor: "transparent #2e7d32 transparent transparent",
          },
          "&::after": {
            content: '""',
            position: "absolute",
            left: -7,
            top: "50%",
            transform: "translateY(-50%)",
            borderWidth: 4,
            borderStyle: "solid",
            borderColor: "transparent #f5f5f5 transparent transparent",
          },
        },
      }}
    >
      <Box sx={{ p: 2, position: "relative" }}>
        <Box sx={{ display: "flex", alignItems: "center", gap: 2, mb: 1 }}>
          <Avatar src={data.image} sx={{ width: 64, height: 64 }} />
          <Box sx={{ flexGrow: 1 }}>
            <Typography variant="h6" sx={{ fontWeight: "bold", fontSize: "1.1rem" }}>
              {data.name}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              {data.designation}
            </Typography>
          </Box>
        </Box>

        <Divider sx={{ my: 1 }} />

        <Box
          sx={{
            display: "flex",
            flexDirection: "column",
            gap: 1,
            backgroundColor: "white",
            p: 1.5,
            borderRadius: 1,
            boxShadow: "inset 0 0 5px rgba(0,0,0,0.1)",
          }}
        >
          <Typography variant="body2">
            <strong>Employee Code:</strong> {data.employee_code}
          </Typography>

          <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
            <Typography
              variant="body2"
              sx={{
                flexGrow: 1,
                whiteSpace: "nowrap",
                overflow: "hidden",
                textOverflow: "ellipsis",
              }}
            >
              <strong>Email:</strong> {data.email}
            </Typography>
            <IconButton
              size="small"
              onClick={(e) => handleCopyText(data.email || "", e)}
              sx={{
                padding: "2px",
                flexShrink: 0,
                backgroundColor: "rgba(46, 125, 50, 0.1)",
                "&:hover": {
                  backgroundColor: "rgba(46, 125, 50, 0.2)",
                },
              }}
            >
              <ContentCopy sx={{ fontSize: "0.9rem" }} />
            </IconButton>
          </Box>

          <Typography variant="body2">
            <strong>Department:</strong> {data.department}
          </Typography>

          <Typography variant="body2">
            <strong>Business Unit:</strong> {data.business_unit}
          </Typography>

          <Typography variant="body2">
            <strong>Gender:</strong> {data.gender}
          </Typography>

          <Typography variant="body2">
            <strong>Direct Reportee(s):</strong> {data.number_of_reportees || 0}
          </Typography>
          <Typography variant="body2">
            <strong>Tenure:</strong> {data.tenure || "N/A"}
          </Typography>
        </Box>

        {copySuccess && (
          <Typography
            variant="caption"
            sx={{
              position: "absolute",
              right: 8,
              top: 8,
              color: "success.main",
              fontWeight: "bold",
            }}
          >
            {copySuccess}
          </Typography>
        )}
      </Box>
    </Popover>
  );
};
