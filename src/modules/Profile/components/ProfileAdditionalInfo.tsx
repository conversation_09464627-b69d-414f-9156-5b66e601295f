import { Box, Grid, Grid2, Typography } from "@mui/material";
import React from "react";
import { subtitle } from "src/modules/Common/ContentHeader/styles";
import { getStatusColors } from "src/utils/typographyUtils";

export interface InfoItemType {
  title: string;
  value: string;
}

export interface InfoList {
  jobTitle: InfoItemType[];
  jobStatus: InfoItemType[];
}

const InfoItem: React.FC<InfoItemType & { fontSize?: number }> = ({ title, value, fontSize = 16 }) => (
  <Box>
    {title && (
      <Typography color="text.secondary" display="flex" mr={1} fontSize={fontSize}>
        {title}
      </Typography>
    )}
    {subtitle && (
      <Typography display="flex" fontSize={fontSize} fontWeight={600} color={getStatusColors(value)}>
        {value}
      </Typography>
    )}
  </Box>
);

const ProfileAdditionalInfo = ({ infoList }: { infoList: InfoItemType[] }) => {
  return (
    <Grid2 container spacing={2}>
      {infoList.map((listItem, idx) => (
        <Grid2 key={`${listItem}${idx}`} size={3}>
          <InfoItem title={listItem.title} value={listItem.value} fontSize={14} />
        </Grid2>
      ))}
    </Grid2>
  );
};

export default ProfileAdditionalInfo;
