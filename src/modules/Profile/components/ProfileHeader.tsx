import { Box, Container, Grid } from "@mui/material";
import React from "react";
import LoadingScreen from "src/modules/Common/LoadingScreen/LoadingScreen";
import { ProfileViewModes } from "src/pages/Profile";
import { EmployeeDetails } from "src/services/api_definitions/employees";
import profileTransform from "src/services/data_transformers/profile.transform";
import { HeaderProps } from "..";
import { ProfileDivider } from "../styles";
import ProfileAdditionalInfo from "./ProfileAdditionalInfo";
import ProfileCard from "./ProfileCard";

interface ProfileHeaderProps extends HeaderProps<EmployeeDetails | null | undefined> {
  isFetched: boolean;
  setCurrentViewMode?: (viewMode: ProfileViewModes) => void;
  isEmployeeSearch?: boolean;
}

const ProfileHeader: React.FC<ProfileHeaderProps> = ({
  informationMetaData,
  isFetched,
  setCurrentViewMode,
  isEmployeeSearch = false,
}) => {
  if (!isFetched) return <LoadingScreen />;

  return (
    <Box display={"flex"} flexDirection="column" gap={2}>
      <ProfileCard
        isEmployeeSearch={isEmployeeSearch}
        goToOrgCharts={() => setCurrentViewMode?.(ProfileViewModes.ORG_CHART)}
        informationMetaData={profileTransform.profileCardDataAbstractor(informationMetaData)}
      />
      <Box bgcolor={"#F8F8F8"} p={2} borderRadius={2}>
        <ProfileAdditionalInfo infoList={profileTransform.profileAdditionalInfo(informationMetaData)} />
      </Box>
    </Box>
  );
};

export default ProfileHeader;
