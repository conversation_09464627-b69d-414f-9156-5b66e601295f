import { Box, Tooltip, Typography, TypographyOwnProps } from "@mui/material";
import React from "react";
import { ReactNode } from "react";
import { ProfileDivider } from "../styles";

interface DisplayTaglinesProps {
  title1: ReactNode;
  title2: ReactNode;
  isBold?: boolean;
  variant?: TypographyOwnProps["variant"];
  seprator?: boolean;
  semiColon?: boolean;
  showTooltip?: boolean;
}

const textEllipsisStyles = {
  singleLine: {
    overflow: "hidden",
    whiteSpace: "nowrap",
    textOverflow: "ellipsis",
  },
  multiLine: {
    display: "-webkit-box",
    WebkitLineClamp: 3, // Change this number to set the number of lines
    WebkitBoxOrient: "vertical",
    overflow: "hidden",
  },
};

const DisplayTaglines = ({
  title1,
  title2,
  isBold,
  variant = "body2",
  seprator = true,
  semiColon = false,
  showTooltip = false,
}: DisplayTaglinesProps) => (
  <Box display="flex" alignItems="center" gap={1}>
    <Tooltip title={showTooltip ? title1 : ""} placement="top">
      <Typography sx={textEllipsisStyles.singleLine} component="div" variant={variant} fontWeight={isBold ? 600 : 400}>
        {title1}
        {semiColon ? ":" : ""}
      </Typography>
    </Tooltip>
    {seprator && <ProfileDivider />}
    <Tooltip title={showTooltip ? title2 : ""} placement="top">
      <Typography sx={textEllipsisStyles.singleLine} component="div" variant={variant} fontWeight={isBold ? 600 : 400}>
        {title2}
      </Typography>
    </Tooltip>
  </Box>
);

export default DisplayTaglines;
