import { Box } from "@mui/material";
import { useMutation } from "@tanstack/react-query";
import React from "react";
import Modal from "src/modules/Common/Modal/Modal";
import { ModalControllerStyles, containerStyle } from "src/modules/Employees/EmployeeModalStyles";
import EmployeeStepper, { CandidateGlobalFormType, stepsKey } from "src/modules/Employees/EmployeeStepper";
import { INPUT_FIELDS as personalInformationInputFields } from "src/modules/Employees/config/PersonalInformation";
import {
  CandidateDetails,
  transformCandidateDetails,
  transformEducationDetailsData,
  transformEmergencyContactsData,
  transformEmployeeBankDetailsData,
  transformEmployeeHistoryData,
  transformEmployeePersonalDetails,
  transformFamilyDetailsData,
} from "src/modules/Employees/utils/candidateTransformer";
import { EmployeeDetails } from "src/services/api_definitions/employees";
import {
  BankInformationPayload,
  EducationDetailsPayload,
  EmergencyContactsPayload,
  FamilyDetailsPayload,
  PersonalInformationPayload,
  WorkExperiencePayload,
} from "src/services/api_definitions/profile.service";
import profileService from "src/services/profile.service";

type Props = {
  title: string;
  onClose: () => void;
  currentForm: string;
  formData?: EmployeeDetails | null;
  refetcProfileDetails: () => void;
};

type ProfilDetailsForm = PersonalInformationPayload &
  BankInformationPayload &
  EducationDetailsPayload &
  EmergencyContactsPayload &
  WorkExperiencePayload &
  FamilyDetailsPayload;

const profileUpadteApi = {
  [stepsKey.personalInformation]: profileService.updatePersonalInformation,
  [stepsKey.bankDetails]: profileService.updateBankInformation,
  [stepsKey.educationDetails]: profileService.updateEducationDetails,
  [stepsKey.employmentHistory]: profileService.updateWorkExperience,
  [stepsKey.emergencyDetails]: profileService.updateEmergencyContacts,
  [stepsKey.familyDetails]: profileService.updateFamilyDetails,
};

const profileUpdateTransformer = {
  [stepsKey.personalInformation]: transformEmployeePersonalDetails,
  [stepsKey.bankDetails]: transformEmployeeBankDetailsData,
  [stepsKey.educationDetails]: transformEducationDetailsData,
  [stepsKey.employmentHistory]: transformEmployeeHistoryData,
  [stepsKey.emergencyDetails]: transformEmergencyContactsData,
  [stepsKey.familyDetails]: transformFamilyDetailsData,
};

const stepsProps = {
  [stepsKey.personalInformation]: {
    disabledInputFields: [
      personalInformationInputFields.FIRST_NAME,
      personalInformationInputFields.LAST_NAME,
      personalInformationInputFields.AADHAAR,
      personalInformationInputFields.PAN,
      personalInformationInputFields.DATE_OF_BIRTH,
      personalInformationInputFields.GENDER,
      personalInformationInputFields.BLOOD_GROUP,
      personalInformationInputFields.NATIONALITY,
    ],
  },
};

const ProfileEditorForm = ({ title, onClose, currentForm, formData, refetcProfileDetails }: Props) => {
  const mutation = useMutation({
    mutationKey: ["updatePersonalInformation", currentForm],
    mutationFn: async (employeeData: ProfilDetailsForm) => profileUpadteApi[currentForm](employeeData),
    onSuccess: (response) => {
      if (response) {
        refetcProfileDetails();
        onClose();
      }
    },
  });

  const handleFinish = (finalForm: CandidateGlobalFormType) => {
    const requestBody = profileUpdateTransformer[currentForm](finalForm) as ProfilDetailsForm;
    mutation.mutate(requestBody);
  };

  const initialForm = formData ? transformCandidateDetails(formData as unknown as CandidateDetails) : {};
  return (
    <Modal
      isOpen
      title={title}
      onClose={onClose}
      showBackButton
      maxWidth="unset"
      sx={ModalControllerStyles.root}
      PaperProps={{ style: ModalControllerStyles.paper }}
    >
      <Box sx={containerStyle}>
        <EmployeeStepper
          steps={[currentForm]}
          onSubmit={handleFinish}
          initialForm={initialForm}
          stepsProps={stepsProps}
        />
      </Box>
    </Modal>
  );
};

export default ProfileEditorForm;
