import { ExpandMore } from "@mui/icons-material";
import { Accordion, AccordionDetails, AccordionSummary } from "@mui/material";
import React from "react";
import { useMasterData } from "src/customHooks/useMasterData";
import ContentHeader from "src/modules/Common/ContentHeader/ContentHeader";
import NoData from "src/modules/Dashboard/component/QuickViews/components/NoDataScreens/NoData";
import { Country } from "src/services/api_definitions/location.service";
import { PayrollTemplateV2 } from "src/services/api_definitions/payroll.service";
import EmployeeCompensation from "./EmployeeCompensation";

interface Props {
  compensations?: Pick<
    PayrollTemplateV2,
    "components" | "effective_date" | "template_name" | "effective_from" | "effective_to" | "ctc" | "gross"
  >[];
  residentCountry: string;
}

const getCompensationTitles = (compensations: PayrollTemplateV2[], index: number) => {
  const isFuture = new Date(compensations[0]?.effective_from) > new Date();
  if (isFuture) {
    if (index === 0) {
      return `Future Compensation Details`;
    }
    if (index === 1) {
      return `Current Compensation Details`;
    }
    if (index === 2) {
      return `Previous Compensation Details`;
    }
    return `Past Compensation Details`;
  } else {
    if (index === 0) {
      return `Current Compensation Details`;
    }
    if (index === 1) {
      return `Previous Compensation Details`;
    }
    return `Past Compensation Details`;
  }
};

const EmployeeCompensations: React.FC<Props> = ({ compensations = [], residentCountry }) => {
  const { data: countries } = useMasterData<Country>("Country");
  const formatLocale = countries?.find((country: Country) => country.name === residentCountry)?.code;

  if (!compensations || compensations?.length === 0) {
    <NoData title="No compensations found" />;
  }

  return (
    <>
      {[...compensations]?.map((payroll, index: number) => (
        <Accordion key={`${payroll.template_name}-${index}`} slotProps={{ transition: { unmountOnExit: true } }}>
          <AccordionSummary expandIcon={<ExpandMore />} aria-controls="panel1-content" id="panel1-header">
            <ContentHeader title={getCompensationTitles(compensations as PayrollTemplateV2[], index)} />
          </AccordionSummary>
          <AccordionDetails>
            <EmployeeCompensation
              compensation={payroll?.components || []}
              locale={formatLocale}
              currency={payroll?.components?.[0]?.compensation_component?.currency}
              ctc={payroll?.ctc}
              gross={payroll?.gross}
              effectiveDate={payroll?.effective_from}
              effectiveTo={index !== 0 ? payroll?.effective_to : null}
            />
          </AccordionDetails>
        </Accordion>
      ))}
    </>
  );
};

export default EmployeeCompensations;
