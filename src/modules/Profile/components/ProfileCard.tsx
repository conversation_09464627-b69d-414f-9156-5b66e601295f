import { ReduceCapacityRounded } from "@mui/icons-material";
import { <PERSON>, Card, CardContent, IconButton, Tooltip, Typography } from "@mui/material";
import React from "react";
import Span from "src/modules/Common/Span/Span";
import { formatDateToDayMonthYear } from "src/utils/dateUtils";
import { HeaderProps } from "..";
import { ProfileDivider } from "../styles";
import ProfilePhoto from "./components/ProfilePhoto";
import DisplayTaglines from "./DisplayTagline";

export interface ProfileMetaData {
  name: string;
  jobTitle: string;
  department: string;
  companyName: string;
  email: string;
  dateOfJoining: string;
  officeLocation: string;
  displayPic: string;
  tenure: string;
}

interface ProfileCardProps extends HeaderProps<ProfileMetaData> {
  // onEditClick: () => void;
  goToOrgCharts: () => void;
  isEmployeeSearch?: boolean;
}

const ProfileCard: React.FC<ProfileCardProps> = ({ informationMetaData, goToOrgCharts, isEmployeeSearch = false }) => {
  return (
    <Box
      display="flex"
      alignItems="center"
      gap={4}
      justifyContent={"space-between"}
      bgcolor={"#F8F8F8"}
      p={2}
      position={"relative"}
      borderRadius={2}
    >
      <ProfilePhoto employeeDisplayPicURL={informationMetaData?.displayPic} isEmployeeSearch={isEmployeeSearch} />
      <Box flexGrow={1}>
        <Box display="flex" alignItems="center" gap={1}>
          <Typography variant="h6" component="div" fontWeight={600}>
            {informationMetaData.name}
          </Typography>
          <Tooltip title="View Organisation Chart">
            <IconButton onClick={() => goToOrgCharts()}>
              <ReduceCapacityRounded sx={{ marginLeft: "auto" }} color="primary" fontSize="small" />
            </IconButton>
          </Tooltip>
        </Box>
        <Box display="flex" flexDirection="column" gap={1}>
          <DisplayTaglines
            variant="body1"
            title1={<Span color="primary.main">{informationMetaData.jobTitle}</Span>}
            title2={informationMetaData.department}
          />
          <DisplayTaglines
            variant="body2"
            title1={informationMetaData.companyName}
            title2={informationMetaData.email}
            isBold={true}
            showTooltip
          />
          <Box display="flex" alignItems="center" gap={1}>
            <DisplayTaglines
              variant="caption"
              title1="DOJ"
              title2={formatDateToDayMonthYear(informationMetaData.dateOfJoining)}
              isBold={true}
              semiColon
              seprator={false}
            />
            <ProfileDivider sx={{ margin: "4px 0px" }} />
            <DisplayTaglines
              variant="caption"
              title1="Tenure"
              title2={informationMetaData.tenure}
              isBold={true}
              semiColon
              seprator={false}
            />
          </Box>
          <Tooltip title="Office Address" placement="left">
            <Typography variant="caption" fontWeight={500}>
              {informationMetaData.officeLocation}
            </Typography>
          </Tooltip>
        </Box>
      </Box>
    </Box>
  );
};

export default ProfileCard;
