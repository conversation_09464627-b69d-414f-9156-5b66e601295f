import { ArrowBack } from "@mui/icons-material";
import { Box, Divider, IconButton } from "@mui/material";
import { ReactFlowProvider } from "@xyflow/react";
import React from "react";
import ContentHeader from "src/modules/Common/ContentHeader/ContentHeader";
import { ProfileViewModes } from "src/pages/Profile";
import OrgChartFlow from "./OrgChart/OrgChartFlow";

export interface OrgChartProps {
  setCurrentViewMode: (currentViewMode: ProfileViewModes) => void;
}

const OrgChart: React.FC<OrgChartProps> = ({ setCurrentViewMode }) => {
  return (
    <Box display="flex" flexDirection="column" gap={3}>
      <Box display="flex" alignItems="center" gap={3}>
        <IconButton onClick={() => setCurrentViewMode(ProfileViewModes.MY_PROFILE)}>
          <ArrowBack color="action" />
        </IconButton>
        <ContentHeader title="Organisation Chart" subtitle="View your organisation chart" />
      </Box>
      <Divider />
      <ReactFlowProvider>
        <OrgChartFlow setCurrentViewMode={setCurrentViewMode} />
      </ReactFlowProvider>
    </Box>
  );
};

export default OrgChart;
