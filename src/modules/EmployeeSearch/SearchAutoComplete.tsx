import { Autocomplete, InputAdornment, ListItem, Paper } from "@mui/material";
import { UseQueryResult, useQuery } from "@tanstack/react-query";
/* eslint-disable react/prop-types */
import React, { useState } from "react";
import { SearchIcon } from "src/assets/icons.svg";
import useDebounce from "src/customHooks/useDebounce";
import {
  OmniSearchResponse,
  OmniSearchResult,
  OmniSearchResultKeys,
} from "src/services/api_definitions/search.service";
import SearchServiceAPI from "src/services/search.service";
import CustomTextField from "../Common/FormInputs/CustomTextField";
import { SearchBarStyles } from "./styles";

interface SearchBarPaperProps {
  children?: React.ReactNode;
}

const SearchBarPaper: React.FC<SearchBarPaperProps> = ({ children }) => {
  return <Paper sx={SearchBarStyles.paper.root}>{children}</Paper>;
};

interface SearchBarProps {
  getData?: OmniSearchResultKeys;
  size?: "small" | "medium";
  width?: number | string;
  placeholder?: string;
  searchInputValue: string;
  setSearchInputValue: (arg0: string, arg1?: OmniSearchResult) => void;
  disabled?: boolean;
  searchField?: OmniSearchResultKeys;
}

const SearchAutoComplete: React.FC<SearchBarProps> = ({
  getData = "value",
  searchInputValue,
  setSearchInputValue,
  placeholder = "Search",
  width = "100%",
  size = "medium",
  disabled = false,
  searchField = "value",
  ...otherProps
}) => {
  const debouncedValue = useDebounce(searchInputValue, 400);
  const [selectedValue, setSelectedValue] = useState("");

  const { data }: UseQueryResult<OmniSearchResponse> = useQuery({
    queryKey: ["search", debouncedValue],
    queryFn: () => {
      if (debouncedValue) {
        return SearchServiceAPI.searchValue(debouncedValue);
      }
      return null;
    },
    refetchOnWindowFocus: false,
  });

  const handleSearchInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const newInputValue = event.target.value;
    setSearchInputValue(newInputValue);

    // If user clears input manually, clear selected value
    if (newInputValue === "") {
      setSelectedValue("");
    }
  };

  const inputValue = data?.find((item) => item[searchField] === searchInputValue)?.[getData] || searchInputValue || "";
  return (
    <Autocomplete
      disableClearable
      freeSolo
      size={size}
      options={data || []}
      disabled={disabled}
      onBlur={() => {
        if (selectedValue !== searchInputValue) {
          setSearchInputValue("");
        }
      }}
      getOptionLabel={(option) => (option as OmniSearchResult)?.value || ""}
      sx={{ ...SearchBarStyles.root, width }}
      PaperComponent={SearchBarPaper}
      inputValue={inputValue || ""}
      onChange={(_, newValue) => {
        if (typeof newValue === "string") {
          setSearchInputValue(newValue);
          setSelectedValue(newValue);
        } else if (newValue && typeof newValue === "object") {
          setSearchInputValue(newValue[getData], newValue);
          setSelectedValue(newValue[getData]);
        } else {
          setSearchInputValue("");
          setSelectedValue("");
        }

        // Blur the input after selection
        setTimeout(() => {
          (document.activeElement as HTMLElement)?.blur();
        }, 0);
      }}
      renderInput={(params) => (
        <CustomTextField
          placeholder={placeholder}
          type="search"
          {...params}
          disabled={disabled}
          onChange={handleSearchInputChange}
          InputProps={{
            ...params.InputProps,
            startAdornment: (
              <InputAdornment position="end" sx={{ marginLeft: "6px", marginRight: "0.2rem" }}>
                <SearchIcon />
              </InputAdornment>
            ),
          }}
          {...otherProps}
        />
      )}
      renderOption={(props, option) => (
        <ListItem
          {...props}
          style={{
            ...(props?.style || {}),
            ...SearchBarStyles.listItems,
          }}
        >
          {option.value}
        </ListItem>
      )}
    />
  );
};

export { SearchAutoComplete };
