import { TextField, styled } from "@mui/material";

export const SearchField = styled(TextField)(({ theme }) => ({
  [theme.breakpoints.up("sm")]: {
    minWidth: 200,
  },
  [theme.breakpoints.up("md")]: {
    minWidth: 400,
  },
  width: "100%",
  input: {
    color: "#282B2A",
    fontWeight: 400,
    wordWrap: "break-word",
    "&::placeholder": {
      color: "#282B2A",
      opacity: 1,
    },
  },
  "& .MuiOutlinedInput-root": {
    color: "#282B2A",
    backgroundColor: "transparent",
    padding: 0,
    borderRadius: "5px",
    "& fieldset": {
      borderColor: "#E6E3E3",
    },
    "&:hover fieldset": {
      borderColor: "success.main",
    },
    "&.Mui-focused fieldset": {
      borderColor: "success.main",
    },
    "& .MuiInputBase-input.Mui-disabled": {
      backgroundColor: "#F5F5F5",
    },
  },
}));

export const SearchBarStyles = {
  root: {
    "& .MuiAutocomplete-inputRoot": {
      padding: "4px 8px",
    },
  },
  paper: {
    root: {
      mt: 1,
      font: "Poppins",
      color: "black",
      boxShadow: "0px 4px 15px rgba(0, 0, 0, 0.10)",
      borderRadius: "5px",
    },
    text: {
      normal: {
        fontWeight: 400,
      },
      bold: {
        fontWeight: 700,
      },
    },
  },
  listItems: {
    borderRadius: "10px",
    padding: "10px 16px",
    margin: 0,
    fontSize: "14px",
  },
};
