import { initializeApp } from "firebase/app";
import { getMessaging, getToken, onMessage } from "firebase/messaging";
import { useEffect } from "react";
import { showToast } from "src/utils/toast";

import { useAppSelector } from "./customHooks/useAppSelector";
import notificationService from "./services/notification.service";

const firebaseConfig = {
  apiKey: "AIzaSyC6GNlwamXz-Zb4V0ph2b5n_gKOORqSuPE",
  authDomain: "effihr-push-notification.firebaseapp.com",
  projectId: "effihr-push-notification",
  storageBucket: "effihr-push-notification.appspot.com",
  messagingSenderId: "98227897032",
  appId: "1:98227897032:web:2f2f9ad5738a3188ba5bfd",
  measurementId: "G-B9NX0TVJYC",
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);

type Props = {
  onNewNotificationReceived?: () => void;
};

function NotificationFirebase({ onNewNotificationReceived }: Props) {
  const { userRoles, userDetails } = useAppSelector((state) => state.userManagement);

  useEffect(() => {
    if (userRoles?.includes("Employee") && userDetails?.organisations?.length > 0) {
      const messaging = getMessaging(app);
      Notification.requestPermission().then(async (permission) => {
        if (permission === "granted") {
          getToken(messaging, { vapidKey: process.env.NEXT_PUBLIC_FIREBASE_VAPID_KEY })
            .then(async (currentToken) => {
              if (currentToken) {
                const previousToken = window.localStorage.getItem("notification-token") || "";
                if (previousToken !== currentToken) {
                  const isSuccess = await notificationService.registerNotificationToken(currentToken);
                  if (isSuccess) {
                    window.localStorage.setItem("notification-token", currentToken);
                  }
                }
              } else {
                console.log("No registration token available. Request permission to generate one.");
              }
            })
            .catch((err) => {
              console.log("An error occurred while retrieving token. ", err);
            });
        } else {
          console.log("Unable to get permission to notify.");
        }
      });
    }
  }, [userRoles, userDetails]);

  useEffect(() => {
    const messaging = getMessaging(app);
    onMessage(messaging, (payload) => {
      onNewNotificationReceived?.();
      if (payload.notification?.body) {
        showToast(payload.notification?.body, {
          type: "success",
        });
      }
    });
  }, []);

  useEffect(() => {
    const channel = new BroadcastChannel("notifications-channel");
    const handleMessage = (event: MessageEvent<any>) => {
      if (event.data && event.data.type === "NEW_NOTIFICATION") {
        onNewNotificationReceived?.();
        showToast(event.data.payload.notification?.body, {
          type: "success",
        });
      }
    };

    channel.addEventListener("message", handleMessage);
    return () => {
      channel.removeEventListener("message", handleMessage);
      channel.close();
    };
  }, []);

  return null;
}

export default NotificationFirebase;
