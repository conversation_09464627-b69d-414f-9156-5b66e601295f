import React from "react";

function withAnchorElement<T>(WrappedComponent: React.ComponentType<T>) {
  return (props: T) => {
    const [anchorEl, setAnchorEl] = React.useState<null | HTMLElement>(null);

    const openPoperHandler = (event: React.MouseEvent<HTMLElement>) => {
      setAnchorEl(anchorEl ? null : event.currentTarget);
    };

    const open = Boolean(anchorEl);
    const poperId = open ? "simple-popper" : undefined;

    return (
      <WrappedComponent
        poperId={poperId}
        open={open}
        anchorEl={anchorEl}
        setAnchorEl={setAnchorEl}
        openPoperHandler={openPoperHandler}
        {...props}
      />
    );
  };
}
export default withAnchorElement;
