import { Box } from "@mui/material";
import React, { useMemo } from "react";
import { useAppSelector } from "src/customHooks/useAppSelector";
import CustomTabs, { TabType } from "src/modules/Common/CustomTabs/CustomTabs";
import ActivityLogRequests from "src/modules/EmployeeAttendance/components/ActivityLogRequests";
import RegularisationApprovals from "src/modules/EmployeeAttendance/components/RegularisationApprovals";
import RegularisationRequests from "src/modules/EmployeeAttendance/components/RegularisationRequests";

const tabs: TabType[] = [
  {
    id: 0 as unknown as string,
    label: "Activity Logs",
    component: <ActivityLogRequests />,
  },
  {
    id: 1 as unknown as string,
    label: "Regularisation Requests",
    component: <RegularisationRequests />,
  },
];

const managerTabs = [
  ...tabs,
  {
    id: 2 as unknown as string,
    label: "Approvals",
    component: <RegularisationApprovals />,
  },
];

const EmployeeAttendance = () => {
  const { userDetails } = useAppSelector((state) => state.userManagement);
  const tabsToDisplay = useMemo(() => (userDetails?.is_manager ? managerTabs : tabs), [userDetails?.is_manager]);

  return (
    <Box>
      <CustomTabs
        tabs={userDetails?.is_manager ? managerTabs : tabs}
        currentTabIndex={tabsToDisplay[0].id as unknown as number}
      />
    </Box>
  );
};

export default EmployeeAttendance;
