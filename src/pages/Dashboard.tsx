import { <PERSON><PERSON><PERSON>utlined, DragIndicator } from "@mui/icons-material";
import {
  Box,
  CircularProgress,
  Divider,
  Grid2,
  IconButton,
  ListItemIcon,
  ListItemText,
  Menu,
  MenuItem,
  MenuList,
  Paper,
  Switch,
  Tooltip,
  Typography,
} from "@mui/material";
import { useMutation } from "@tanstack/react-query";
import React, { MouseEventHandler, Suspense, useEffect, useMemo, useState } from "react";
import { queryClient } from "src/app/App";
import { FilterIcon } from "src/assets/icons.svg";
import { useAppSelector } from "src/customHooks/useAppSelector";
import useSubroutes from "src/customHooks/useSubroutes";
import ContentHeader from "src/modules/Common/ContentHeader/ContentHeader";
import PreviewCard from "src/modules/Dashboard/component/PreviewCard";
import {
  LazyAttendanceQuickView,
  LazyCalendarQuickView,
  LazyEmployeeAttendanceView,
  LazyEmployeeLeaveReportsView,
  LazyEmployeeLeavesView,
  LazyEmployeeSeperations,
  LazyEventsView,
  LazyHolidaysQuickView,
  LazyNewJoinersView,
  LazyTeamStatusQuckView,
} from "src/modules/Dashboard/component/QuickViews";
import { HeaderContainer } from "src/modules/Dashboard/component/QuickViews/style";
import { PATH_CONFIG } from "src/modules/Routing/config";
import { UserSettings } from "src/services/api_definitions/userManagement";
import userManagementService from "src/services/userManagement.service";

const getWelcomeText = () => {
  const today = new Date();
  const curHr = today.getHours();

  if (curHr < 12) {
    return "Good Morning!";
  }

  if (curHr < 18) {
    return "Good Afternoon!";
  }

  return "Good Evening!";
};

const menuOptions = [
  {
    name: "Time Sheet",
    key: PATH_CONFIG.DASHBOARD_TIME_SHEETS.key,
    isHidden: false,
    isDefault: true,
    component: () => <LazyAttendanceQuickView />,
  },
  {
    name: "Holiday",
    key: PATH_CONFIG.DASHBOARD_HOLIDAYS.key,
    isHidden: false,
    isDefault: true,
    component: () => <LazyHolidaysQuickView />,
  },
  {
    name: "Calendar",
    key: PATH_CONFIG.DASHBOARD_CALENDAR.key,
    isHidden: false,
    component: () => <LazyCalendarQuickView />,
  },
  {
    name: "Celebration",
    key: PATH_CONFIG.DASHBOARD_CELEBRATION.key,
    isHidden: false,
    isDefault: true,
    component: () => <LazyEventsView />,
  },
  {
    name: "Today’s Team Status",
    key: PATH_CONFIG.DASHBOARD_TODAYS_TEAM_STATUS.key,
    isDefault: true,
    isHidden: false,
    component: () => <LazyTeamStatusQuckView />,
  },
  {
    name: "Leave Reports",
    key: PATH_CONFIG.DASHBOARD_LEAVE_REPORTS.key,
    isHidden: false,
    component: () => <LazyEmployeeLeaveReportsView />,
  },
  {
    name: "Leaves",
    key: PATH_CONFIG.DASHBOARD_LEAVES.key,
    isHidden: false,
    component: () => <LazyEmployeeLeavesView />,
  },
  {
    name: "Attendance",
    key: PATH_CONFIG.DASHBOARD_ATTENDANCE.key,
    isHidden: false,
    component: () => <LazyEmployeeAttendanceView />,
  },
  {
    name: "New Joiners",
    key: PATH_CONFIG.DASHBOARD_NEW_JOINEES.key,
    isDefault: true,
    isHidden: false,
    component: () => <LazyNewJoinersView />,
  },
  {
    name: "Employee Separations",
    key: PATH_CONFIG.DASHBOARD_EMPLOYEE_SEPERATIONS.key,
    isDefault: true,
    isHidden: false,
    component: () => <LazyEmployeeSeperations />,
  },
];

type MenuOptionProps = typeof menuOptions;

export default function Dashboard() {
  const {
    selectedRole,
    userDetails: { display_name: userName } = { display_name: "NA" },
  } = useAppSelector((state) => state.userManagement);
  const lookupKey = useMemo(() => `${selectedRole}`, [selectedRole]);
  const subRoutes = useSubroutes(PATH_CONFIG.HOME.key);
  const { userSettings } = useAppSelector((state) => state.userManagement);

  const roleWiseMenus = useMemo(
    () =>
      menuOptions.filter((menuOption) => {
        const subRouteKeys = subRoutes.map((subRoute) => subRoute.key);
        return subRouteKeys.includes(menuOption.key);
      }),
    [menuOptions, subRoutes],
  );

  const [features, setFeatures] = useState<MenuOptionProps>(roleWiseMenus);

  const [anchorEl, setAnchorElement] = useState<Element | null>(null);
  const open = useMemo(() => Boolean(anchorEl), [anchorEl]);
  const userSettingsMutation = useMutation({
    mutationKey: ["insert-user-settings"],
    mutationFn: async (userSettings: UserSettings) => userManagementService.insertUserSettings(userSettings),
    onSuccess: () => {
      queryClient.invalidateQueries(["user-settings"]);
    },
  });

  useEffect(() => {
    if (!userSettings?.quickActions || userSettings?.quickActions?.[lookupKey]?.length === 0) {
      const hideAllOptions = roleWiseMenus.map((feature) => ({
        ...feature,
        isHidden: !feature?.isDefault,
      }));
      setFeatures(hideAllOptions);
      return;
    }
    setFeatures((prevFeatures) => {
      return prevFeatures.map((feature) => ({
        ...feature,
        isHidden: !userSettings?.quickActions?.[lookupKey]?.includes(feature.key) && !feature.isDefault,
      }));
    });
  }, [userSettings, roleWiseMenus]);

  const enableShowAll = useMemo(() => features?.every((feature) => !feature.isHidden), [features]);

  const onShowAllClick: MouseEventHandler<HTMLButtonElement> | undefined = (ev) => {
    const { checked } = ev.target as never;
    setFeatures((prevFeatures) => {
      const updatedFeatures = prevFeatures.map((eachFeature) => ({
        ...eachFeature,
        isHidden: !checked && !eachFeature.isDefault,
      }));

      userSettingsMutation.mutate({
        ...userSettings,
        quickActions: {
          ...userSettings?.quickActions,
          [lookupKey]: updatedFeatures.filter((feature) => !feature.isHidden).map((feature) => feature.key),
        },
      });

      return updatedFeatures;
    });
  };

  const defaultOptions = [
    {
      name: "Show All",
      key: "Show All",
      onClick: onShowAllClick,
    },
    // {
    //   name: "Reset Order",
    //   key: "Reset Order",
    //   onClick: onResetOrderClick,
    // },
  ];

  const handleClose = () => {
    setAnchorElement(null);
  };

  const onChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const { id } = event.target;
    const updatedFeatures = features.map((feature) => {
      if (feature.key === id) {
        return {
          ...feature,
          isHidden: !feature?.isHidden,
        };
      }
      return {
        ...feature,
      };
    });
    userSettingsMutation.mutate({
      ...userSettings,
      quickActions: {
        ...userSettings?.quickActions,
        [lookupKey]: updatedFeatures.filter((feature) => !feature.isHidden).map((feature) => feature.key),
      },
    });
    setFeatures(updatedFeatures);
  };

  const featuresToDisplay = useMemo(() => features.filter((feature) => !feature.isHidden), [features, roleWiseMenus]);

  return (
    <Box display="flex" flexDirection="column">
      <HeaderContainer>
        <ContentHeader
          title={`${getWelcomeText()} ${userName}`}
          Icon={
            <IconButton onClick={(ev) => setAnchorElement(ev.currentTarget)} id="dashboard-filter" color="primary">
              <FilterIcon color="primary" />
            </IconButton>
          }
        />
        <Menu
          id="basic-menu"
          anchorEl={anchorEl}
          open={open}
          onClose={handleClose}
          transformOrigin={{
            vertical: "top",
            horizontal: "center",
          }}
          MenuListProps={{
            "aria-labelledby": "basic-button",
          }}
        >
          <Paper elevation={0} sx={{ width: 550, maxWidth: "100%" }}>
            <Box display="flex" justifyContent="space-between" alignItems="center" sx={{ padding: "0px 20px" }}>
              <Typography fontWeight={600} sx={{ fontSize: "20px" }}>
                Customise Widgets
              </Typography>
              <IconButton size="large" onClick={handleClose}>
                <CancelOutlined fontSize="large" />
              </IconButton>
            </Box>
            <Divider />
            <MenuList sx={{ background: "#F8FFFE" }}>
              {defaultOptions.map((menu) => (
                <MenuItem key={menu.key}>
                  {/* This is to create default spacing */}
                  <ListItemIcon />
                  <ListItemText>{menu.name}</ListItemText>
                  <Switch checked={enableShowAll} onClick={menu?.onClick} />
                </MenuItem>
              ))}
            </MenuList>
            <MenuList>
              {features.map((menu) => (
                <MenuItem key={menu.key}>
                  <ListItemIcon>
                    <Tooltip title="Column ordering coming soon!">
                      <IconButton>
                        <DragIndicator sx={{ cursor: "grab" }} />
                      </IconButton>
                    </Tooltip>
                  </ListItemIcon>
                  <ListItemText>{menu.name}</ListItemText>
                  <Switch disabled={menu.isDefault} id={menu.key} onChange={onChange} checked={!menu.isHidden} />
                </MenuItem>
              ))}
            </MenuList>
          </Paper>
        </Menu>
      </HeaderContainer>
      <Divider sx={{ margin: "25px 0px" }} />
      <Grid2 container spacing={3} alignItems="stretch">
        {featuresToDisplay.map((feature) => (
          <Grid2 size={{ xs: 12, sm: 12, md: 6, lg: 4 }} key={feature.key}>
            <PreviewCard>
              <Suspense fallback={<CircularProgress />}>{feature.component()}</Suspense>
            </PreviewCard>
          </Grid2>
        ))}
      </Grid2>
    </Box>
  );
}
