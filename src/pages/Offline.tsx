import CloudOffIcon from "@mui/icons-material/CloudOff";
import { Box, Button, Container, Typography } from "@mui/material";
import React from "react";

const OfflinePage: React.FC = () => {
  const handleReload = () => {
    window.location.reload();
  };

  return (
    <Box
      sx={{
        display: "flex",
        flexDirection: "column",
        justifyContent: "center",
        alignItems: "center",
        height: "100vh",
        textAlign: "center",
        bgcolor: "background.default",
        color: "text.primary",
        padding: 3,
      }}
    >
      <Container maxWidth="sm">
        <CloudOffIcon sx={{ fontSize: 100, color: "primary.main", mb: 3 }} />
        <Typography variant="h4" sx={{ fontWeight: "bold", mb: 2 }}>
          You’re Offline
        </Typography>
        <Typography variant="body1" sx={{ mb: 4 }}>
          It seems you’ve lost your internet connection. Please check your connection and try again.
        </Typography>
        <Button
          variant="contained"
          color="primary"
          size="large"
          onClick={handleReload}
          sx={{
            textTransform: "none",
            paddingX: 4,
            paddingY: 1.5,
            fontSize: "1rem",
          }}
        >
          Retry Connection
        </Button>
      </Container>
    </Box>
  );
};

export default OfflinePage;
