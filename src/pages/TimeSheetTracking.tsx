import { Box } from "@mui/material";
import React, { useEffect, useState } from "react";
import { useNavigate, useSearchParams } from "react-router-dom";
import { useAppSelector } from "src/customHooks/useAppSelector";
import CustomTabs, { TabType } from "src/modules/Common/CustomTabs/CustomTabs";
import TimeSheetApproval from "src/modules/ProjectTracking/TimeSheetApproval";
import TimeSheetRequests from "src/modules/ProjectTracking/TimeSheetRequests";
import TimesheetEntry from "src/modules/ProjectTracking/TimesheetEntries";

const tabs: TabType[] = [
  {
    id: 0 as unknown as string,
    label: "My Timesheet",
    component: <TimesheetEntry />,
  },
  {
    id: 1 as unknown as string,
    label: "Timesheet Requests",
    component: <TimeSheetRequests />,
  },
];

const managerTabs = [
  ...tabs,
  {
    id: 2 as unknown as string,
    label: "Timesheet Approvals",
    component: <TimeSheetApproval />,
  },
];

const TimeSheetTracking = () => {
  const { userDetails } = useAppSelector((state) => state.userManagement);

  const [searchParams] = useSearchParams();
  const tab = searchParams.get("tab");
  const navigate = useNavigate();
  const [currentTabIndex, setCurrentTabIndex] = useState(0);

  useEffect(() => {
    if (tab) {
      setCurrentTabIndex(parseInt(tab));
    }
  }, [tab]);

  return (
    <Box>
      <CustomTabs
        tabs={userDetails?.is_manager ? managerTabs : tabs}
        currentTabIndex={currentTabIndex}
        onChange={(_e, index) => {
          setCurrentTabIndex(index);
          navigate(`/time-log?tab=${index}`);
        }}
      />
    </Box>
  );
};

export default TimeSheetTracking;
