import { Box } from "@mui/material";
import React from "react";
import { useAppSelector } from "src/customHooks/useAppSelector";
import CustomTabs, { TabType } from "src/modules/Common/CustomTabs/CustomTabs";
import { CompanyDocuments } from "src/modules/EmployeeDocuments/CompanyDocuments";
import { MyDocuments } from "src/modules/EmployeeDocuments/MyDocuments";

const tabs: TabType[] = [
  {
    id: 0 as unknown as string,
    label: "My Documents",
    component: <MyDocuments />,
  },
  {
    id: 1 as unknown as string,
    label: "Company Documents",
    component: <CompanyDocuments />,
  },
];
const hrTabs: TabType[] = [
  {
    id: 1 as unknown as string,
    label: "Company Documents",
    component: <CompanyDocuments />,
  },
];

const EmployeeDocuments = () => {
  const { selectedRole } = useAppSelector((state) => state.userManagement);
  const tabsToDisplay = selectedRole === "Employee" ? tabs : hrTabs;
  return (
    <Box>
      <CustomTabs tabs={tabsToDisplay} currentTabIndex={0} />
    </Box>
  );
};

export default EmployeeDocuments;
