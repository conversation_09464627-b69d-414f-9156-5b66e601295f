import React, { lazy, Suspense, useEffect } from "react";

import { Box, Button, LinearProgress } from "@mui/material";
import { useQuery } from "@tanstack/react-query";
import { useLocation } from "react-router-dom";
import languageConfig from "src/configs/language/en.lang";
import ContentHeader from "src/modules/Common/ContentHeader/ContentHeader";
import EmployeesView from "src/modules/Employees/components/EmployeesView";
import EmployeesServiceAPI from "src/services/employees.service";

const LazyModalController = lazy(() => import("src/modules/Employees/components/Modals/ModalController"));
export default function Employees() {
  const [modalId, setModalId] = React.useState<string | null>(null);
  const isModalOpen = React.useMemo(() => modalId !== null, [modalId]);
  const { search } = useLocation();

  const {
    data = [],
    refetch,
    isLoading,
    isFetching,
  } = useQuery({
    queryKey: ["employees-details"],
    queryFn: async () => await EmployeesServiceAPI.getEmployeesAPI(),
    retryOnMount: true,
    refetchInterval: false,
    refetchOnWindowFocus: false,
  });

  const handleOpen = (id: string | null) => {
    setModalId(id);
  };

  const handleClose = () => {
    setModalId(null);
    refetch();
  };
  useEffect(() => {
    if (search.includes("addEmployee")) {
      handleOpen("add");
    }
  }, [search]);

  return (
    <Box display="flex" flexDirection="column" gap={2}>
      <ContentHeader
        title={languageConfig.employees.title}
        subtitle={languageConfig.employees.subtitle}
        buttonTitle={languageConfig.employees.button.addEmployee}
        secondaryButtonTitle={languageConfig.employees.button.import}
        primaryAction={() => handleOpen("add")}
        secondaryAction={() => handleOpen("import")}
        actions={
          <Button onClick={() => handleOpen("export")} variant="outlined">
            Export
          </Button>
        }
      />
      <EmployeesView data={data || []} isLoading={isLoading} isFetching={isFetching} refetchEmployeeList={refetch} />
      {isModalOpen && (
        <Suspense fallback={<LinearProgress />}>
          <LazyModalController open={isModalOpen} modalId={modalId} handleClose={handleClose} />
        </Suspense>
      )}
    </Box>
  );
}
