import { useQuery } from "@tanstack/react-query";
import React, { useState } from "react";
import ClientView from "src/modules/TimesheetManagement/components/ClientView";
import ProjectView from "src/modules/TimesheetManagement/components/ProjectView";
import { Project, TimesheetTracking } from "src/services/api_definitions/timesheetTracking.service";
import timesheetTrackingService from "src/services/timesheetTracking.service";

export enum ClientActionStates {
  VIEW_CLIENT = "view-client",
  VIEW_PROJECT = "view-project",
  CREATE_CLIENT = "create-client",
  EDIT_CLIENT = "edit-client",
  CREATE_PROJECT = "create-project",
  EDIT_PROJECT = "edit-project",
  DELETE_PROJECT = "delete-project",
  VIEW_ASSIGNEES = "view-assignees",
}

export const getTitle = (modalStates: ClientActionStates | null) => {
  switch (modalStates) {
    case ClientActionStates.CREATE_CLIENT:
      return "Create Client";
    case ClientActionStates.EDIT_CLIENT:
      return "Edit Client";
    case ClientActionStates.CREATE_PROJECT:
      return "Create Project";
    case ClientActionStates.EDIT_PROJECT:
      return "Edit Project";
    case ClientActionStates.VIEW_ASSIGNEES:
      return "View Assignees";
    default:
      return "";
  }
};

const TimesheetTracking: React.FC = () => {
  const [selectedRow, setSelectedRow] = useState<(Project | null) | (TimesheetTracking | null)>(null);
  const [modalStates, setModalStates] = useState<ClientActionStates | null>(null);
  const [currentSelectedView, setCurrentSelectedView] = useState<
    ClientActionStates.VIEW_CLIENT | ClientActionStates.VIEW_PROJECT
  >(ClientActionStates.VIEW_CLIENT);
  const [selectedClient, setSelectedClient] = useState<TimesheetTracking | null>(null);

  const {
    data: clients,
    isLoading,
    refetch,
  } = useQuery(
    ["get-clients"],
    async () => {
      const timesheetClients = await timesheetTrackingService.getClients();
      if (selectedClient) {
        const timesheetClient = timesheetClients.find((client) => client.code === selectedClient.code);
        if (!timesheetClient) {
          setSelectedClient(null);
          setCurrentSelectedView(ClientActionStates.VIEW_CLIENT);
          return timesheetClients;
        }
        setSelectedClient(timesheetClient as unknown as TimesheetTracking);
      }
      return timesheetClients;
    },
    {
      refetchOnMount: true,
      refetchOnWindowFocus: false,
    },
  ) as { data: TimesheetTracking[] | undefined; isLoading: boolean; refetch: () => Promise<any> };

  const renderSelectedView = (view: ClientActionStates.VIEW_CLIENT | ClientActionStates.VIEW_PROJECT) => {
    if (view === ClientActionStates.VIEW_CLIENT) {
      return (
        <ClientView
          clients={clients || []}
          isLoading={isLoading}
          refetch={refetch}
          selectedRow={selectedRow as TimesheetTracking}
          setSelectedRow={setSelectedRow}
          setCurrentSelectedView={setCurrentSelectedView}
          setSelectedClient={setSelectedClient}
          modalStates={modalStates}
          setModalStates={setModalStates}
        />
      );
    }
    return (
      <ProjectView
        isLoading={isLoading}
        refetch={refetch}
        selectedRow={selectedRow as Project}
        setSelectedRow={setSelectedRow}
        modalStates={modalStates}
        setModalStates={setModalStates}
        selectedClient={selectedClient}
        setCurrentSelectedView={setCurrentSelectedView}
        setSelectedClient={setSelectedClient}
      />
    );
  };

  return renderSelectedView(currentSelectedView);
};

export default TimesheetTracking;
