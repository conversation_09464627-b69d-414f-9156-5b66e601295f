import { Close } from "@mui/icons-material";
import { Box, Dialog, DialogContent, IconButton } from "@mui/material";
import React from "react";

interface DocumentPreviewProps {
  open: boolean;
  onClose: () => void;
  documentUrl: string;
}
export const DocumentPreview = ({ open, onClose, documentUrl }: DocumentPreviewProps) => {
  // Return empty div if no valid URL
  if (!documentUrl || documentUrl.trim() === "" || documentUrl === "data:text/html,<html><body></body></html>") {
    return <div></div>;
  }

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="lg"
      fullWidth
      PaperProps={{ sx: { height: "90vh", maxHeight: "90vh" } }}
    >
      <Box display="flex" justifyContent="flex-end" p={1}>
        <IconButton onClick={onClose}>
          <Close />
        </IconButton>
      </Box>
      <DialogContent sx={{ p: 0, height: "100%", overflow: "hidden" }}>
        <iframe
          src={`${documentUrl}#toolbar=0`}
          width="100%"
          height="100%"
          style={{ border: "none" }}
          title="Document Preview"
        />
      </DialogContent>
    </Dialog>
  );
};
