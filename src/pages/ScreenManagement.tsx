/* eslint-disable react/prop-types */
/* eslint-disable @typescript-eslint/no-explicit-any */
import { Box, Paper, Tab, Tabs } from "@mui/material";
import React, { useMemo } from "react";
import ContentHeader from "src/modules/Common/ContentHeader/ContentHeader";
import { PATH_CONFIG } from "src/modules/Routing/config";
import ManageScreenURIS from "src/modules/ScreenManagement/components/ManageScreenURIS";
import ManageScreens from "src/modules/ScreenManagement/components/ManageScreens";

const screenManagementTabs = [
  {
    key: PATH_CONFIG.SCREEN_MANAGEMENT_MANAGE_SCREENS.key,
    title: "Manage Screens",
    description: "Add/Edit/Delete screens",
    component: <ManageScreens />,
    id: 0,
  },
  {
    key: PATH_CONFIG.SCREEN_MANAGEMENT_MANAGE_SCREEN_URIS.key,
    title: "Manage Screen URIs",
    description: "Manage screen to URI Mapping",
    buttonLabel: "Add Role",
    component: <ManageScreenURIS />,
    id: 1,
  },
];

const ScreenManagement = () => {
  const [tabId, setTabId] = React.useState(screenManagementTabs?.[0]?.id);

  const handleChange = (_event: React.SyntheticEvent, newValue: number) => {
    setTabId(newValue);
  };

  const selectedTab = useMemo(() => {
    return screenManagementTabs[tabId];
  }, [tabId]);

  return (
    <Box display="flex" flexDirection="column" gap={2}>
      <ContentHeader title="Screen Management" subtitle={selectedTab?.description} />
      <Tabs component={Paper} value={tabId} onChange={handleChange}>
        {screenManagementTabs?.map((tab) => (
          <Tab
            sx={{
              textTransform: "none",
            }}
            label={tab?.title}
            tabIndex={tab?.id}
            key={tab?.id}
          />
        ))}
      </Tabs>
      <Box display="flex" flexDirection="column" gap={2}>
        {selectedTab?.component}
      </Box>
    </Box>
  );
};

export default ScreenManagement;
