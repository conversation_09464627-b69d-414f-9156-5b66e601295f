import React, { useMemo } from "react";
import { useAppSelector } from "src/customHooks/useAppSelector";
import EmployeeView from "src/modules/PerformanceManagement/components/EmployeeView";
import HRBPView from "src/modules/PerformanceManagement/components/HRBP/HRBPView";
import ManagerView from "src/modules/PerformanceManagement/components/ManagerView";

const PerformanceManagement = () => {
  const { userDetails } = useAppSelector((state) => state.userManagement);
  const { selectedRole } = useAppSelector((state) => state.userManagement);

  const getViewsToDisplay = useMemo(() => {
    if (selectedRole !== "Employee") {
      return <HRBPView />;
    }
    if (userDetails?.is_manager) {
      return <ManagerView />;
    }
    return <EmployeeView />;
  }, [userDetails?.is_manager, selectedRole]);

  return <>{getViewsToDisplay}</>;
};

export default PerformanceManagement;
