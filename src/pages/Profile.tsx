import { Container } from "@mui/material";
import { useQuery } from "@tanstack/react-query";
import React, { useMemo } from "react";
import Tabs, { TabType } from "src/modules/Common/CustomTabs/CustomTabs";
import EmployeeCompensations from "src/modules/Profile/components/EmployeeCompensation/EmployeeCompensations";
import Employee<PERSON><PERSON>ney from "src/modules/Profile/components/EmployeeJourney";
import OrgChart from "src/modules/Profile/components/OrgChart";
import ProfileHeader from "src/modules/Profile/components/ProfileHeader";
import ProfileView from "src/modules/Profile/components/ProfileView";
import profileService from "src/services/profile.service";

export enum ProfileViewModes {
  MY_PROFILE = "MY_PROFILE",
  ORG_CHART = "ORG_CHART",
  COMPENSATION = "COMPENSATION",
}

export default function Profile() {
  const [currentViewMode, setCurrentViewMode] = React.useState(ProfileViewModes.MY_PROFILE);

  const {
    data: profileData,
    isFetched,
    refetch,
  } = useQuery(["get-profile-data"], {
    queryFn: async () => profileService.fetchProfileData(),
    refetchOnWindowFocus: false,
    refetchOnMount: false,
  });

  const residentCountry = useMemo(
    () => profileData?.office_address?.country || "India",
    [profileData?.office_address?.country],
  );

  const render = useMemo(() => {
    if (currentViewMode === ProfileViewModes.ORG_CHART) {
      return <OrgChart setCurrentViewMode={setCurrentViewMode} />;
    }
    return (
      <>
        <ProfileHeader
          informationMetaData={profileData}
          isFetched={isFetched}
          setCurrentViewMode={setCurrentViewMode}
        />
        <ProfileView informationMetaData={profileData} isFetched={isFetched} refetch={refetch} />
      </>
    );
  }, [currentViewMode, isFetched, profileData]);

  const tabsToShow: TabType[] = [
    {
      id: "personal-details",
      label: "Personal Details",
      component: (
        <Container disableGutters maxWidth={false}>
          {render}
        </Container>
      ),
    },
    {
      id: "employee-compensation",
      label: "Compensation",
      component: <EmployeeCompensations compensations={profileData?.compensations} residentCountry={residentCountry} />,
    },
    {
      id: "employment-journey",
      label: "Employment Journey",
      component: (
        <Container disableGutters maxWidth="xl">
          <EmployeeJourney />
        </Container>
      ),
    },
  ];

  return <Tabs tabs={tabsToShow} />;
}
