import { Box, LinearProgress } from "@mui/material";
import { useQuery } from "@tanstack/react-query";
import React, { lazy, Suspense, useCallback } from "react";
import LeadsHeader from "src/modules/Leads/components/LeadsHeader";
import LeadsView from "src/modules/Leads/components/LeadsView";
import { PATH_CONFIG } from "src/modules/Routing/config";
import LeadsServiceAPI from "src/services/leads.service";
import { getACLFromFeaturekey } from "src/utils/screenUtils";
const LazyModalController = lazy(() => import("src/modules/Leads/components/Modals/ModalController"));

const setDatafromAPI = async () => {
  const dataFromAPI = await LeadsServiceAPI.getLeadsAPI();
  return dataFromAPI;
};

export default function Leads() {
  const leadsACL = getACLFromFeaturekey(PATH_CONFIG.LEADS.key);
  const [modalId, setModalId] = React.useState<string | null>(null);
  const isModalOpen = React.useMemo(() => modalId !== null, [modalId]);

  const {
    data = [],
    refetch,
    isLoading,
    isFetching,
  } = useQuery({
    queryKey: ["leads"],
    queryFn: setDatafromAPI,
    retryOnMount: false,
    refetchInterval: false,
    refetchOnWindowFocus: true,
  });

  const handleOpen = (id: string | null) => {
    setModalId(id);
  };

  const handleClose = useCallback(() => {
    setModalId(null);
  }, []);

  return (
    <>
      {leadsACL.canWrite && (
        <Suspense fallback={<LinearProgress />}>
          <LazyModalController open={isModalOpen} modalId={modalId} handleClose={handleClose} refetch={refetch} />
        </Suspense>
      )}
      <Box gap={2} display="flex" flexDirection="column">
        <LeadsHeader handleOpen={handleOpen} />
        <LeadsView data={data} handleOpen={handleOpen} isLoading={isLoading} isFetching={isFetching} />
      </Box>
    </>
  );
}
