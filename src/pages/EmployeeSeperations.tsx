import { Logout, VisibilityOutlined } from "@mui/icons-material";
import { Box, Button, IconButton } from "@mui/material";
import { useQuery } from "@tanstack/react-query";
import React, { useCallback, useEffect, useMemo, useState } from "react";
import { BaseObject } from "src/app/global";
import CustomTabs, { TabType } from "src/modules/Common/CustomTabs/CustomTabs";
import DataTable from "src/modules/Common/Table/DataTable";
import FnfConfirmationModal from "src/modules/EmployeeOffboarding/FnfConfiramationModal";
import {
  EmployeeSeperations,
  EmployeeSeperations as TypeEmployeeSeperations,
} from "src/services/api_definitions/offboarding.service";
import offboardingService from "src/services/offboarding.service";
import { formatDateToDayMonthYear } from "src/utils/dateUtils";
import { toTableHeaderTitleCase } from "src/utils/typographyUtils";
import LeaveBalanceModal from "./LeaveBalanceModal";

const convertLeaveBalance = (row: BaseObject): unknown[] => {
  return Object.keys(row).map((eachRow) => ({
    leave_type: eachRow,
    count: row[eachRow],
  }));
};

const EmployeeSeperations = ({ stage, enableFnfAction }: { stage: string; enableFnfAction: boolean }) => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedRow, setSelectedRow] = useState([]);
  const [rowSelection, setRowSelection] = useState({});
  const [selectedRows, setSelectedRows] = useState<TypeEmployeeSeperations[]>([]);
  const [isConfirmationModalOpen, setIsConfirmationModalOpen] = useState(false);

  const {
    data: employeeSeperations,
    isFetched,
    refetch,
  } = useQuery({
    queryKey: ["employee-seperations", stage],
    queryFn: async () => offboardingService.getEmployeeSeperations(stage),
    refetchOnWindowFocus: false,
    refetchOnMount: true,
  });

  const onViewLeaveMapClick = (row: BaseObject) => {
    setIsModalOpen(true);
    setSelectedRow((convertLeaveBalance(row) as never[]) || []);
  };

  const onFnfRowClick = (row: TypeEmployeeSeperations) => {
    setSelectedRows([row]);
    openConfirmationModal();
  };

  const columnAccessors = useCallback((row: TypeEmployeeSeperations, key: string) => {
    if (typeof row[key as keyof TypeEmployeeSeperations] === "boolean") {
      return row[key as keyof TypeEmployeeSeperations] ? "Yes" : "No";
    }

    if (key === "leave_balance") {
      return (
        <IconButton color="primary" onClick={() => onViewLeaveMapClick(row?.leave_balance || {})}>
          <VisibilityOutlined />
        </IconButton>
      );
    }
    if (key === "date_of_separation") {
      return formatDateToDayMonthYear(row?.date_of_separation as string);
    }
    if (key === "action") {
      return (
        <IconButton color="primary" onClick={() => onFnfRowClick(row)} disabled={row?.status !== "Accepted"}>
          <Logout />
        </IconButton>
      );
    }
    return row[key as keyof TypeEmployeeSeperations];
  }, []);

  const columnSize = {
    notice_period_waived_off: 210,
    notice_period_served: 180,
    eligible_for_re_hire: 170,
  };

  const getColumnsToDisplay = useMemo(() => {
    if (employeeSeperations?.length) {
      const employeeObject = enableFnfAction ? { ...employeeSeperations[0], action: "" } : employeeSeperations[0];
      const headers = Object.keys(employeeObject) as string[];
      return headers.map((header) => ({
        header: toTableHeaderTitleCase(header),
        accessorFn: (row: TypeEmployeeSeperations) => columnAccessors(row, header as keyof TypeEmployeeSeperations),
        size: columnSize[header as keyof typeof columnSize],
      }));
    }
    return [];
  }, [employeeSeperations]);

  const openConfirmationModal = () => {
    setIsConfirmationModalOpen(true);
  };

  const getSelectedRows = (rowSelection: any) => {
    return Object.keys(rowSelection).map(
      (key) => employeeSeperations?.[key as unknown as number],
    ) as EmployeeSeperations[];
  };

  useEffect(() => {
    setSelectedRows(getSelectedRows(rowSelection));
  }, [rowSelection]);

  return (
    <Box display="flex" flexDirection="column" gap={2}>
      {enableFnfAction && (
        <Button
          sx={{ alignSelf: "flex-end" }}
          onClick={openConfirmationModal}
          variant="contained"
          disabled={Object.keys(rowSelection).length === 0}
        >
          Initiate F&F
        </Button>
      )}
      <DataTable
        data={employeeSeperations || []}
        columns={getColumnsToDisplay}
        state={{
          showSkeletons: !isFetched,
          rowSelection,
        }}
        enableRowSelection={
          enableFnfAction
            ? (rowSelection) => {
                return rowSelection.original?.status === "Accepted";
              }
            : false
        }
        onRowSelectionChange={setRowSelection}
      />
      <LeaveBalanceModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        leaveBalanceData={selectedRow || []}
      />
      <FnfConfirmationModal
        isOpen={isConfirmationModalOpen}
        onClose={() => setIsConfirmationModalOpen(false)}
        selectedRows={selectedRows}
        refetch={refetch}
        setRowSelection={setRowSelection}
      />
    </Box>
  );
};

const allTabs: TabType[] = [
  {
    id: 1,
    label: "Active",
    component: <EmployeeSeperations stage="active" enableFnfAction={false} />,
  },
  {
    id: 2,
    label: "Separated",
    component: <EmployeeSeperations stage="separated" enableFnfAction={true} />,
  },
  {
    id: 3,
    label: "Completed",
    component: <EmployeeSeperations stage="completed" enableFnfAction={false} />,
  },
];

const EmployeeSeperationsTabs = () => {
  return <CustomTabs tabs={allTabs} currentTabIndex={0} />;
};

export default EmployeeSeperationsTabs;
