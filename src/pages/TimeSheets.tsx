import { Box } from "@mui/material";
import { format } from "date-fns";
import React, { useState } from "react";
import Header from "src/modules/TimeSheets/components/Header";
import TimeSheetView from "src/modules/TimeSheets/components/TimeSheetView";

const DEFAULT_SELECTED_MONTH: string = format(new Date(), "MM-yyyy");

const TimeSheets = () => {
  const [selectedDate, setSelectedDate] = useState(DEFAULT_SELECTED_MONTH);

  return (
    <Box display="flex" flexDirection="column" gap={3}>
      <Header setSelectedDate={setSelectedDate} selectedDate={selectedDate} />
      <TimeSheetView selectedDate={selectedDate} key={selectedDate} />
    </Box>
  );
};

export default TimeSheets;
