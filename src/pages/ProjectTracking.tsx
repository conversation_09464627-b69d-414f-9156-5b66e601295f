import { Box } from "@mui/material";
import React, { useMemo } from "react";
import { useAppSelector } from "src/customHooks/useAppSelector";
import CustomTabs, { TabType } from "src/modules/Common/CustomTabs/CustomTabs";

import { ProjectTracking as ProjectTrackingComponent } from "src/modules/ProjectTracking";

const tabs: TabType[] = [
  {
    id: 0 as unknown as string,
    label: "My Projects",
    component: <ProjectTrackingComponent isManagerView={false} />,
  },
];

const managerTabs = [
  ...tabs,
  {
    id: 2 as unknown as string,
    label: "Team Projects",
    component: <ProjectTrackingComponent isManagerView={true} />,
  },
];

const ProjectTracking = () => {
  const { userDetails } = useAppSelector((state) => state.userManagement);
  const tabsToDisplay = useMemo(() => (userDetails?.is_manager ? managerTabs : tabs), [userDetails?.is_manager]);

  return (
    <Box>
      <CustomTabs
        tabs={userDetails?.is_manager ? managerTabs : tabs}
        currentTabIndex={tabsToDisplay[0].id as unknown as number}
      />
    </Box>
  );
};

export default ProjectTracking;
