import { Box } from "@mui/material";
import React from "react";
import { useAppSelector } from "src/customHooks/useAppSelector";
import MyCalendar from "src/modules/Calendar";
import CustomTabs, { TabType } from "src/modules/Common/CustomTabs/CustomTabs";
import TeamCalendar from "src/modules/TeamCalendar";

const calendarTabs: TabType[] = [
  {
    id: 0 as unknown as string,
    label: "My Calendar",
    component: <MyCalendar />,
  },
  {
    id: 1 as unknown as string,
    label: "Team Calendar",
    component: <TeamCalendar />,
  },
];

const Calendar = () => {
  const { userDetails } = useAppSelector((state) => state.userManagement);

  return (
    <Box>
      {userDetails?.is_manager ? (
        <CustomTabs tabs={calendarTabs} currentTabIndex={calendarTabs[0].id as unknown as number} />
      ) : (
        <MyCalendar />
      )}
    </Box>
  );
};

export default Calendar;
