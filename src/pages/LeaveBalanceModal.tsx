import { Box } from "@mui/material";
import React from "react";
import Modal from "src/modules/Common/Modal/Modal";
import DataTable from "src/modules/Common/Table/DataTable";
import { LeaveBalanceItems } from "src/services/api_definitions/employees.d";

interface LeaveBalanceModalProps {
  isOpen: boolean;
  onClose: () => void;
  leaveBalanceData: LeaveBalanceItems[];
  title?: string;
}

const LeaveBalanceModal: React.FC<LeaveBalanceModalProps> = ({
  isOpen,
  onClose,
  leaveBalanceData,
  title = "Leave Balance",
}) => {
  return (
    <Modal title={title} showBackButton showDivider onClose={onClose} isOpen={isOpen}>
      <Box>
        <DataTable
          data={leaveBalanceData}
          columns={[
            {
              header: "Leave Type",
              accessorKey: "leave_type",
            },
            {
              header: "Remaining Day(s)",
              accessorKey: "count",
            },
          ]}
        />
      </Box>
    </Modal>
  );
};

export default LeaveBalanceModal;
