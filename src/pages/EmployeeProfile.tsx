import { Container } from "@mui/material";
import { useQuery } from "@tanstack/react-query";
import React, { useMemo } from "react";
import { useParams } from "react-router-dom";
import { useAppDispatch } from "src/customHooks/useAppDispatch";
import OrgChart from "src/modules/Profile/components/OrgChart";
import ProfileHeader from "src/modules/Profile/components/ProfileHeader";
import ProfileView from "src/modules/Profile/components/ProfileView";
import profileService from "src/services/profile.service";
import { setBreadcrumbs } from "src/store/slices/breadcrumbs.slice";
import { ProfileViewModes } from "./Profile";

export default function EmployeeProfile() {
  const { employeeId = "" } = useParams();
  const dispatch = useAppDispatch();
  const [currentViewMode, setCurrentViewMode] = React.useState(ProfileViewModes.MY_PROFILE);

  const {
    data: profileData,
    isFetched,
    refetch,
  } = useQuery(["get-profile-data", employeeId], {
    queryFn: async () => {
      dispatch(setBreadcrumbs([]));
      return profileService.fetchProfileData(employeeId);
    },
    refetchOnWindowFocus: false,
    refetchOnMount: false,
  });

  const render = useMemo(() => {
    if (currentViewMode === ProfileViewModes.ORG_CHART) {
      return <OrgChart setCurrentViewMode={setCurrentViewMode} />;
    }
    return (
      <>
        <ProfileHeader
          informationMetaData={profileData}
          isFetched={isFetched}
          setCurrentViewMode={setCurrentViewMode}
          isEmployeeSearch={true}
        />
        <ProfileView informationMetaData={profileData} isFetched={isFetched} refetch={refetch} isViewMode />
      </>
    );
  }, [currentViewMode, employeeId, profileData]);

  if (!employeeId || !profileData) {
    return null;
  }

  return (
    <Container disableGutters maxWidth={false}>
      {render}
    </Container>
  );
}
