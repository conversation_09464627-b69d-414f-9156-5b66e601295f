import { Button, <PERSON>alogA<PERSON>, FormControl, FormControlLabel, Radio, RadioGroup } from "@mui/material";
import React from "react";

import Modal from "src/modules/Common/Modal/Modal";

export default function SelectRoleModal({
  isOpen,
  roles,
  selectedRole,
  setSelectedRole,
  onClose,
}: {
  isOpen: boolean;
  roles: string[];
  selectedRole: string;
  setSelectedRole: (role: string) => void;
  onClose: () => void;
}) {
  const [customSelectedRole, setCustomSelectedRole] = React.useState(selectedRole);
  return (
    <Modal
      isOpen={isOpen}
      title="Login As"
      onClose={() => {}}
      sx={{ backdropFilter: "blur(15px)" }}
      actions={
        <DialogActions>
          <Button
            variant="contained"
            onClick={() => {
              setSelectedRole(customSelectedRole);
              localStorage.setItem("showRoleSelectionModal", "false");
              onClose();
            }}
          >
            Submit
          </Button>
        </DialogActions>
      }
    >
      <FormControl sx={{ width: "100%", display: "flex", flexDirection: "column", gap: 1 }}>
        <RadioGroup
          aria-labelledby="demo-controlled-radio-buttons-group"
          name="controlled-radio-buttons-group"
          value={customSelectedRole}
          onChange={(e) => setCustomSelectedRole(e.target.value)}
          sx={{ display: "flex", flexDirection: "column", gap: 1 }}
        >
          {roles?.map((role) => (
            <FormControlLabel
              key={role}
              value={role}
              control={<Radio />}
              label={role}
              labelPlacement="start"
              sx={{
                justifyContent: "space-between",
                margin: 0,
                border: "1px solid #D0D5DD",
                borderRadius: 4,
                padding: 1,
              }}
            />
          ))}
        </RadioGroup>
      </FormControl>
    </Modal>
  );
}
