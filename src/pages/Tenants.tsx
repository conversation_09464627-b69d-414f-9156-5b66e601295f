import React from "react";
import { Navigate, Route, Routes } from "react-router-dom";
import { useRouteConfig } from "src/contexts/RoutesConfigContext";
import useSubroutes from "src/customHooks/useSubroutes";
import Container from "src/modules/Common/Container/Container";
import { PATH_CONFIG } from "src/modules/Routing/config";

const Tenants = () => {
  const subRoutes = useSubroutes(PATH_CONFIG.TENANTS.key);
  const routesConfig = useRouteConfig();

  return (
    <Container>
      <Routes>
        {[...subRoutes]?.map((route) => (
          <Route key={route.key} path={route.pathname} element={routesConfig?.[route.key]} />
        ))}
      </Routes>
      <Navigate to={PATH_CONFIG.TENANTS_LIST.path} />
    </Container>
  );
};

export default Tenants;
