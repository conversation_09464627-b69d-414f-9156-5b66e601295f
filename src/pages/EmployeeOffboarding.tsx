import React from "react";
import { useAppSelector } from "src/customHooks/useAppSelector";
import {
  EmployeeOffboardingView,
  ManagerOffboardingView,
} from "src/modules/Employees/Offboarding/OffboardingScreenView";

const EmployeeOffboarding = () => {
  const {
    userDetails: { is_manager: isManager },
  } = useAppSelector((state) => state.userManagement);

  if (isManager) {
    return <ManagerOffboardingView />;
  }
  return <EmployeeOffboardingView />;
};

export default EmployeeOffboarding;
