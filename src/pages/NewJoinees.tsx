import React, { lazy, Suspense } from "react";

import { Box, LinearProgress } from "@mui/material";
import { useQuery } from "@tanstack/react-query";
import NewJoineesHeader from "src/modules/NewJoinees/components/NewJoineesHeader";
import NewJoineesView from "src/modules/NewJoinees/components/NewJoineesView";
import candidateService from "src/services/candidate.service";

const LazyModalController = lazy(() => import("src/modules/NewJoinees/components/Modals/ModalController"));

export default function NewJoinees() {
  const [modalId, setModalId] = React.useState<string | null>(null);
  const isModalOpen = React.useMemo(() => modalId !== null, [modalId]);

  const { data, refetch, isLoading, isFetching } = useQuery({
    queryKey: ["view-all-candidates"],
    queryFn: () => candidateService.getAllCandidateDetailsAPI(),
    retryOnMount: false,
    refetchInterval: false,
    refetchOnWindowFocus: false,
  });

  const handleOpen = (id: string | null) => {
    setModalId(id);
  };

  const handleModalId = (id: string) => {
    setModalId(id);
  };

  const handleClose = () => {
    setModalId(null);
    refetch();
  };

  return (
    <Box display="flex" flexDirection="column" gap={2}>
      {isModalOpen && (
        <Suspense fallback={<LinearProgress />}>
          <LazyModalController
            open={isModalOpen}
            modalId={modalId}
            handleModalId={handleModalId}
            handleClose={handleClose}
          />
        </Suspense>
      )}
      <NewJoineesHeader handleOpen={handleOpen} />
      <NewJoineesView data={data || []} isLoading={isLoading} isFetching={isFetching} refetch={refetch} />
    </Box>
  );
}
