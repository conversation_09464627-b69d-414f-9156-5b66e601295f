import { useMemo } from "react";
import { SubRoute } from "src/configs/app.config";
import { useAppSelector } from "./useAppSelector";

const useSubroutes = (parentKey: string) => {
  const { authorisedScreens } = useAppSelector((state) => state.userManagement);
  const getSubRoutesByParentKey = (menus: SubRoute[], parentKey: string): SubRoute[] => {
    for (const menu of menus) {
      if (menu.key === parentKey) {
        return menu.subRoutes || [];
      }
      if (menu.subRoutes) {
        const subRoutes = getSubRoutesByParentKey(menu.subRoutes, parentKey);
        if (subRoutes.length) {
          return subRoutes;
        }
      }
    }
    return [];
  };

  return useMemo(() => getSubRoutesByParentKey(authorisedScreens, parentKey), [authorisedScreens, parentKey]);
};

export default useSubroutes;
