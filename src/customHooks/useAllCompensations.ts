import { useQuery } from "@tanstack/react-query";
import payrollService from "src/services/payroll.service";

type UseAllCompensationsProps = {
  country: string;
  statutory?: boolean;
  active?: boolean;
  employeeTypes?: string;
};

export const useAllCompensations = ({
  country = "India",
  statutory = false,
  active,
  employeeTypes,
}: UseAllCompensationsProps) => {
  console.log({ country, statutory, active, employeeTypes });
  const allComponentsQueryResult = useQuery(
    [
      `get-all-components-${employeeTypes}-${statutory}-${active}-${country}`,
      country,
      employeeTypes,
      statutory,
      active,
    ],
    async () =>
      await payrollService.getAllCompensationComponents({
        country,
        statutory,
        active,
        employee_types: employeeTypes,
      }),
    {
      refetchOnWindowFocus: false,
      refetchOnMount: true,
      refetchOnReconnect: true,
    },
  );
  return allComponentsQueryResult;
};
