import { useTheme } from "@mui/material";
import { useEffect, useState } from "react";
import { useAppDispatch } from "src/customHooks/useAppDispatch";
import { setContentHeight } from "src/store/slices/app.slice";

export const useContentHeight = () => {
  const theme = useTheme();
  const dispatch = useAppDispatch();
  const [contentHeight, setCalculatedContentHeight] = useState<number>(0);

  useEffect(() => {
    const handleResize = () => {
      const topBarHeight = Number(theme.mixins.toolbar.minHeight); // Get top bar height from theme
      const windowHeight = window.innerHeight;
      const contentPadding = 30;

      const calculatedHeight = windowHeight - topBarHeight - contentPadding * 2;
      setCalculatedContentHeight(calculatedHeight);
      dispatch(setContentHeight(calculatedHeight));
    };

    handleResize();
    window.addEventListener("resize", handleResize);

    return () => window.removeEventListener("resize", handleResize);
  }, [theme, dispatch]);

  return contentHeight;
};
