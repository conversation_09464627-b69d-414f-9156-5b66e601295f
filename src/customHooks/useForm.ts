/* eslint-disable @typescript-eslint/no-explicit-any */
import { useEffect, useMemo, useState } from "react";
import { ValidatorReturnType } from "src/utils/validators";

type Validator<T> = (value: any, formDetails: T | T[]) => ValidatorReturnType;

export type FormError<T> = {
  [K in keyof T]: Validator<any>[];
};

interface UseFormProps<T> {
  initialState: T | T[];
  validations: FormError<T>;
  isBulk?: boolean;
}

const createEmptyErrorObject = <T extends object>(
  initialState: T | T[],
): Record<keyof T, string> | Record<keyof T, string>[] => {
  const EMPTY_STRING = "";
  if (Array.isArray(initialState)) {
    return initialState.map(() =>
      Object.keys(initialState[0]).reduce(
        (res, currObjKey) => ({ ...res, [currObjKey]: EMPTY_STRING }),
        {} as Record<keyof T, string>,
      ),
    );
  } else {
    return Object.keys(initialState).reduce(
      (res, currObjKey) => ({ ...res, [currObjKey]: EMPTY_STRING }),
      {} as Record<keyof T, string>,
    );
  }
};

const useForm = <T extends object>({ initialState, validations, isBulk = false }: UseFormProps<T>) => {
  const [formDetails, setFormDetails] = useState<T | T[]>(initialState);
  const [formErrors, setFormErrors] = useState<Record<keyof T, string> | Record<keyof T, string>[]>(
    createEmptyErrorObject(initialState),
  );

  useEffect(() => {
    setFormDetails(initialState);
  }, [initialState]);

  const validate = (key: keyof T, value: string): ValidatorReturnType[] => {
    return validations[key]?.map((validation) => validation(value, formDetails as never)) || [];
  };

  const handleChange = (event: React.ChangeEvent<HTMLInputElement>, index?: number): void => {
    const { id, value } = event.target;
    if (isBulk && typeof index !== "undefined") {
      setFormErrors((prevErrors) => {
        const newErrors = [...(prevErrors as Record<keyof T, string>[])];
        newErrors[index] = {
          ...newErrors[index],
          [id]: validate(id as keyof T, value)
            .map((error) => error?.message || null)
            .filter((val) => val)
            .toString(),
        };
        return newErrors;
      });
      setFormDetails((prevFormDetails) => {
        const newFormDetails = [...(prevFormDetails as T[])];
        newFormDetails[index] = {
          ...newFormDetails[index],
          [id]: value,
        };
        return newFormDetails;
      });
    } else {
      setFormErrors((prevErrors) => ({
        ...prevErrors,
        [id]: validate(id as keyof T, value)
          .map((error) => error?.message || null)
          .filter((val) => val)
          .toString(),
      }));
      setFormDetails((prevFormDetails) => ({
        ...prevFormDetails,
        [id]: value,
      }));
    }
  };

  const handleSelectChange = (event: React.ChangeEvent<HTMLSelectElement>, key: keyof T, index?: number): void => {
    if (isBulk && typeof index !== "undefined") {
      setFormErrors((prevErrors) => {
        const newErrors = [...(prevErrors as Record<keyof T, string>[])];
        newErrors[index] = {
          ...newErrors[index],
          [key]: validate(key, event.target.value).toString(),
        };
        return newErrors;
      });
      setFormDetails((prevFormDetails) => {
        const newFormDetails = [...(prevFormDetails as T[])];
        newFormDetails[index] = {
          ...newFormDetails[index],
          [key]: event.target.value,
        };
        return newFormDetails;
      });
    } else {
      setFormErrors((prevErrors) => ({
        ...prevErrors,
        [key]: validate(key, event.target.value).toString(),
      }));
      setFormDetails((prevFormDetails) => ({
        ...prevFormDetails,
        [key]: event.target.value,
      }));
    }
  };

  const setFormDetail = (name: string, value: unknown, index?: number) => {
    if (isBulk && typeof index !== "undefined") {
      setFormErrors((prevErrors) => {
        const newErrors = [...(prevErrors as Record<keyof T, string>[])];
        newErrors[index] = {
          ...newErrors[index],
          [name]: validate(name as keyof T, value as string)
            .map((error) => error?.message || null)
            .filter((val) => val)
            .toString(),
        };
        return newErrors;
      });
      setFormDetails((prevFormDetails) => {
        const newFormDetails = [...(prevFormDetails as T[])];
        newFormDetails[index!] = {
          ...newFormDetails[index!],
          [name]: value,
        };
        return newFormDetails;
      });
    } else {
      setFormErrors((prevErrors) => ({
        ...prevErrors,
        [name]: validate(name as keyof T, value as string)
          .map((error) => error?.message || null)
          .filter((val) => val)
          .toString(),
      }));
      setFormDetails((prevFormDetails) => ({
        ...prevFormDetails,
        [name]: value,
      }));
    }
  };

  const addNewFormDetailRow = (initialValues = initialState) => {
    const newFormDetails = [...(formDetails as T[])];
    const newFormDetailErrors: Record<keyof T, string>[] = [...(formErrors as Record<keyof T, string>[])];

    if (initialValues && Array.isArray(initialValues)) {
      newFormDetails.push(initialValues[0]);
      newFormDetailErrors.push((createEmptyErrorObject(initialValues) as Record<keyof T, string>[])[0]);
    }
    setFormDetails(newFormDetails);
    setFormErrors(newFormDetailErrors);
  };

  const deleteFormDetails = (index: number) => {
    const filteredFormDetails = (formDetails as T[]).filter((_, rowId) => rowId !== index);
    const filteredErrors = (formErrors as Record<keyof T, string>[]).filter((_, rowId) => rowId !== index);
    setFormDetails(filteredFormDetails);
    setFormErrors(filteredErrors);
  };

  // const areFormDetailsValid = useMemo(() => {
  //   if (isBulk) {
  //     return (formErrors as Record<keyof T, string>[]).every((errors) =>
  //       Object.values(errors).every((error) => !error),
  //     );
  //   } else {
  //     return Object.values(formErrors).every((error) => !error);
  //   }
  // }, [formErrors]);

  const areFormDetailsValid = useMemo(() => {
    if (isBulk) {
      return (formDetails as Record<keyof T, string>[]).every((state) => {
        return Object.entries(state).every(([key, value]) => {
          const error = validate(key as keyof T, value as string)
            .map((error) => error?.message || null)
            .filter((val) => val)
            .toString();
          return !error;
        });
      });
    } else {
      return Object.entries(formDetails).every(([key, value]) => {
        const error = validate(key as keyof T, value)
          .map((error) => error?.message || null)
          .filter((val) => val)
          .toString();
        return !error;
      });
    }
  }, [formDetails, validations]);

  return {
    formDetails,
    formErrors,
    handleSelectChange,
    handleChange,
    setFormDetail,
    addNewFormDetailRow,
    deleteFormDetails,
    areFormDetailsValid,
    setFormErrors,
    setFormDetails,
  };
};
type UseFormReturnType<T extends Record<string, string>> = ReturnType<typeof useForm<T>>;

export { useForm, UseFormReturnType };
