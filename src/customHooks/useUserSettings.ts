import { useQuery } from "@tanstack/react-query";
import userManagementService from "src/services/userManagement.service";
import { setUserSettings } from "src/store/slices/userManagement.slice";
import { useAppDispatch } from "./useAppDispatch";

const useUserSettings = ({ shouldFetch }: { shouldFetch: boolean }) => {
  const dispatch = useAppDispatch();
  return useQuery(
    ["user-settings"],
    async () => {
      const resp = await userManagementService.getUserSettings();
      dispatch(setUserSettings(resp));
      return resp;
    },
    {
      enabled: shouldFetch,
      refetchOnWindowFocus: false,
      refetchOnMount: false,
    },
  );
};

export { useUserSettings };
