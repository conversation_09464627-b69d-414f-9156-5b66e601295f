import { UseQueryOptions, UseQueryResult, useQuery } from "@tanstack/react-query";
import masterdataService from "src/services/masterdata.service";

export const useMasterData = <T extends object | string>(
  id: string,
  options?: UseQueryOptions<any, any, T[], any>,
): UseQueryResult<T[]> => {
  return useQuery(["master-data", id], async () => masterdataService.getACLs<T>(id), {
    refetchOnMount: false,
    refetchOnWindowFocus: false,
    cacheTime: 3600,
    ...options,
  });
};
