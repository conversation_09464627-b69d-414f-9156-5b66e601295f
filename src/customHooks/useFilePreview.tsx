import React, { useState } from "react";
import { DocumentPreview } from "src/pages/DocumentPreview";
import fileuploaderService from "src/services/fileuploader.service";

const useFilePreview = () => {
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const handlePreviewClick = async (s3Link: string | null) => {
    if (!s3Link) {
      setPreviewUrl(null);
      return;
    }
    // Don't set previewUrl until we have the actual URL
    const previewUrl = await fileuploaderService.getDocumentPreviewUrl(s3Link);
    if (previewUrl) {
      setPreviewUrl(previewUrl);
    }
  };
  return {
    handlePreviewClick,
    previewUrl,
    documentPreview: (
      <DocumentPreview
        open={!!previewUrl}
        onClose={() => setPreviewUrl(null)}
        documentUrl={previewUrl || "data:text/html,<html><body></body></html>"}
      />
    ),
  };
};
export { useFilePreview };
