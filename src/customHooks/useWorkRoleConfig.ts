import { useQuery } from "@tanstack/react-query";
import { useCallback, useMemo } from "react";
import { getBandGradeLevelV2 } from "src/modules/Employees/utils/utils";
import departmentService from "src/services/department.service";
import { getCurrentTenantId } from "src/utils/authUtils";
import workRoleUtils from "src/utils/workRoleUtils";

type UseWorkRoleConfigProps = {
  businessUnit?: string;
  department?: string;
};

const useWorkRoleConfig = ({ businessUnit, department }: UseWorkRoleConfigProps = {}) => {
  const { data: roleHierarchies } = useQuery(
    ["role-hierarchies-performance-management"],
    async () => departmentService.getWorkRoleHierarchy(getCurrentTenantId()),
    {
      refetchOnMount: false,
      refetchOnWindowFocus: false,
    },
  );

  const { data: jobTitles } = useQuery(
    ["job-titles-performance-management"],
    async () => departmentService.getJobTitleDetails(businessUnit || "", department || ""),
    {
      refetchOnMount: false,
      refetchOnWindowFocus: false,
      enabled: !!businessUnit && !!department,
    },
  );

  const bandGradeLevelConfig = useMemo(() => {
    return getBandGradeLevelV2(roleHierarchies || []);
  }, [roleHierarchies]);

  const getConfig = useCallback(() => {
    const { isBand, isBandGrade, isBandLevel, isBandLevelGrade, isGrade, isLevel, isLevelGrade } = bandGradeLevelConfig;

    if (isBandLevelGrade) {
      return {
        hierarchy: ["band", "level", "grade", "jobTitle"],
      };
    }

    if (isBandLevel) {
      return {
        hierarchy: ["band", "level", "jobTitle"],
      };
    }

    if (isBandGrade) {
      return {
        hierarchy: ["band", "grade", "jobTitle"],
      };
    }

    if (isLevelGrade) {
      return {
        hierarchy: ["level", "grade", "jobTitle"],
      };
    }

    if (isBand) {
      return {
        hierarchy: ["band", "jobTitle"],
      };
    }

    if (isLevel) {
      return {
        hierarchy: ["level", "jobTitle"],
      };
    }

    if (isGrade) {
      return {
        hierarchy: ["grade", "jobTitle"],
      };
    }

    return {
      hierarchy: [],
    };
  }, [bandGradeLevelConfig]);

  return {
    ...bandGradeLevelConfig,
    jobTitles,
    getConfig,
    ...workRoleUtils,
  };
};

export default useWorkRoleConfig;
