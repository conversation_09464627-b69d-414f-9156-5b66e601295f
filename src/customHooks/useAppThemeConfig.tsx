import { createTheme, PaletteOptions } from "@mui/material";
import "@mui/material/styles";

declare module "@mui/material/styles" {
  interface TypeText {
    darkText: string;
    primaryText: string;
    link: string;
  }
}

type Props = {
  mode?: PaletteOptions["mode"];
};

const useAppThemeConfig = ({ mode = "light" }: Props) => {
  const theme = createTheme({
    breakpoints: {
      values: {
        xs: 0,
        sm: 820,
        md: 1024,
        lg: 1200,
        xl: 1536,
      },
    },
    palette: {
      mode: mode,
      primary: {
        light: "#009381",
        main: "#007F6F",
        contrastText: "#FFFFFF",
      },
      secondary: {
        main: "#061C3D",
      },
      info: {
        light: "#D5D7D8",
        main: "#95B4E3",
      },
      text: {
        darkText: "#404958",
        primaryText: "#15050B",
        link: "#007AFF",
      },
    },
    typography: {
      allVariants: {
        fontFamily: ["Poppins", "sans-serif"].join(","),
      },
    },
    components: {
      MuiInputLabel: {
        styleOverrides: {
          asterisk: {
            color: "#E13333",
          },
        },
      },
      MuiButton: {
        styleOverrides: {
          root: ({ ownerState }) => ({
            textTransform: "none",
            borderRadius: 25,
            fontSize: "1rem",
            fontWeight: 400,
            ...(ownerState.size === "medium" && {
              minWidth: 150, // Override width for size="medium"
              height: 48, // Override height for size="medium"
            }),
          }),
        },
      },
      MuiTextField: {
        styleOverrides: {
          root: {
            "& input[type=number]": {
              MozAppearance: "textfield",
            },
            "& input[type=number]::-webkit-outer-spin-button": {
              WebkitAppearance: "none",
              margin: 0,
            },
            "& input[type=number]::-webkit-inner-spin-button": {
              WebkitAppearance: "none",
              margin: 0,
            },
          },
        },
      },
      MuiInputBase: {
        styleOverrides: {
          root: {
            "&.Mui-disabled": {
              "& .MuiInputBase-input": {
                background: "rgba(0, 0, 0, 0.05)",
                color: "rgba(0, 0, 0, 0.05)",
              },
              "& .MuiOutlinedInput-notchedOutline": {
                borderColor: "#EBEBE4", // Change this to your desired border color
              },
            },
          },
        },
      },
      MuiCssBaseline: {
        styleOverrides: `
              @font-face: {
                font-family: Poppins, sans-serif;
              }
            `,
      },
    },
  });

  return {
    theme,
  };
};

export default useAppThemeConfig;
