// useAuthorizedScreens.js
import { useQuery } from "@tanstack/react-query";
import { NavigateFunction, useLocation, useNavigate } from "react-router-dom";
import { useAppDispatch } from "src/customHooks/useAppDispatch";
import { useAppSelector } from "src/customHooks/useAppSelector";
import { useAuth } from "src/modules/Login/LoginHook";
import { PATH_CONFIG } from "src/modules/Routing/config";
import authService from "src/services/auth.service";
import { setAuthorisedScreens } from "src/store/slices/userManagement.slice";
import { deleteCookie } from "src/utils/cookieUtils";
import { matchUrl } from "src/utils/urlUtils";

const handleCurrentPathAuthorized = (navigate: NavigateFunction) => {
  navigate(window.location.pathname + window.location.search);
};

const handleRedirectToAuthorizedScreen = (resp: string[], navigate: NavigateFunction) => {
  const routeKey = Object.keys(PATH_CONFIG).find((key) =>
    resp.some(
      (respScreen) =>
        respScreen === PATH_CONFIG[key as keyof typeof PATH_CONFIG].key &&
        PATH_CONFIG[key as keyof typeof PATH_CONFIG]?.isRoot,
    ),
  );
  navigate(PATH_CONFIG[routeKey as keyof typeof PATH_CONFIG].path + window.location.search);
};

const useAuthorizedScreens = ({ shouldFetch }: { shouldFetch: boolean }) => {
  const { selectedRole } = useAppSelector((state) => state.userManagement);
  const { isAuthenticated, logout } = useAuth();
  const dispatch = useAppDispatch();
  const navigate = useNavigate();
  const location = useLocation();

  return useQuery(
    ["authorized-screens", selectedRole],
    async () => {
      const resp = await authService.authenticatedScreens();
      if (resp) {
        dispatch(setAuthorisedScreens(resp));
      }
      const routeKey = Object.keys(PATH_CONFIG).find((key) =>
        matchUrl(PATH_CONFIG[key as keyof typeof PATH_CONFIG].path, location.pathname),
      );

      const screens: string[] = resp.map((screen) => screen.screen);

      if (screens.length === 0) {
        // handleNoAuthorizedScreens(logout, navigate);
      } else if (
        routeKey &&
        (screens.includes(PATH_CONFIG[routeKey as keyof typeof PATH_CONFIG].key) ||
          PATH_CONFIG[routeKey as keyof typeof PATH_CONFIG].isInternal)
      ) {
        handleCurrentPathAuthorized(navigate);
      } else {
        handleRedirectToAuthorizedScreen(screens, navigate);
      }
      return resp;
    },
    {
      enabled: isAuthenticated && shouldFetch,
      retryOnMount: false,
      refetchInterval: false,
      refetchOnWindowFocus: false,
      onError: () => {
        deleteCookie("org");
        deleteCookie("role");
        logout();
        window.location.href = PATH_CONFIG.LOGIN.path;
      },
      retry: false,
    },
  );
};

export { useAuthorizedScreens };
