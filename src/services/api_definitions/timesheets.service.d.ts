import { ActivityLogDetails } from "./employeeAttendance.service";

export interface TimeSheet {
  employee_code: string;
  employee_name: string;
  job_title: string;
  working_days: number;
  present_days: number;
  absent_days: number;
  attendances: ActivityLogDetails[];
  display_pic: string;
}

export interface Attendance {
  login_date: string;
  check_in_time: string;
  check_out_time: string;
  status: string;
  location: string;
  duration: string;
}

export interface CheckTime {
  hour: number;
  minute: number;
  second: number;
  nano: number;
}
