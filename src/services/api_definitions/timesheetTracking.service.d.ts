export interface Employee {
  id: string;
  display_name: string;
  employee_code: string;
  employee_search_code: string;
  job_title: string;
  display_pic: string;
}

export interface TimesheetTracking {
  name: string;
  code: string;
  contract_start_date?: Date;
  contract_end_date?: Date;
  internal: boolean;
  status: string;
  projects: Project[];
  created_by: string;
  duration: "Weekly" | "Monthly";
  raised_by: Employee;
  billable_recorded_time: string;
  non_billable_recorded_time: string;
  total_recorded_time: string;
  timesheet_request_id: string;
  timesheet: Timesheet[];
  start_date: string;
  end_date: string;
}

export interface Project {
  id: string;
  project_id: string;
  project_name: string;
  project_code: string;
  start_date: string;
  end_date: string;
  created_by: string;
  internal: boolean;
  billable: boolean;
  hourly_rate?: number;
  currency_code?: string;
  assignees?: ResourceDetails[];
}

export interface ResourceDetails {
  employee_search_code?: string;
  employee_code: string;
  employee_name: string;
  hourly_rate: number;
  currency_code: string;
  min_daily_hours_committed?: string;
  assigned?: boolean;
}

export interface CreateProjectRequest {
  project_id?: string;
  project_name: string;
  project_code: string;
  client_code: string;
  start_date?: string | Date;
  end_date?: string | Date;
  billable: boolean;
  billing_type?: string;
  hourly_rate?: number;
  currency_code?: string;
  assignees?: ResourceDetails[];
}

export interface CreateClientRequest {
  name: string;
  code: string;
  contract_start_date?: Date | null;
  contract_end_date?: Date | null;
  internal: boolean;
}
