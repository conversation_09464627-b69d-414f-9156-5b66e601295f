export type AllRolesResponse = {
  default_roles: string[];
  custom_roles: string[];
};

export type AllUserDetailsResponse = {
  email: string;
  roles: string[];
};

export interface RoleScreensResponse {
  default_role_screens: DefaultRoleScreen[];
  custom_role_screens: DefaultRoleScreen[];
}

export interface DefaultRoleScreen {
  role: Role;
  screen: string;
  screen_description: string;
  acl: ACL;
}

export enum ACL {
  ReadWrite = "Read-Write",
  ReadOnly = "Read-Only",
  None = "None",
}
