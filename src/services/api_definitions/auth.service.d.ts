export type AccessControlTypes = "Read-Only" | "Read-Write";

export interface Screens {
  role: string;
  screen: string;
  acl: AccessControlTypes;
  redirect_url?: string | null;
}

export type TenantResponse = {
  logo: string;
  tenant_id: string;
  tenant_url: string;
  status: string;
};

export type UserDetails = {
  email: string;
  first_name: string;
  last_name: string;
};

export type RefreshTokenResponse = {
  token: string;
  token_type: string;
};

export type PasswordLoginRequest = {
  username: string;
  password: string;
  tenant_id: string;
};

export type PasswordLoginResponse = {
  token: string;
  user: UserDetails;
};

export type GenerateOtpRequest = {
  username: string;
  tenant_id: string;
};

export type VerifyOtpRequest = {
  username: string;
  password: string;
  tenant_id: string;
  otp: string;
};

export type VerifyOtpResponse = {
  token: string;
  user: UserDetails;
};

export type GenerateOtpResponse = {
  otp: string;
};
