export type TenantStatuses = "Pending" | "In Progress" | "Active";

export interface TenantDetailsModel {
  name: string;
  domains: string;
  logo: string;
  /*
   * @deprecated use `sso_types` instead
   */
  sso_type: string;
  sso_types: string[];
  modules: Module[];
  organisation: Organisation;
  auth_signatory_name: string;
  auth_signatory_email: string;
  auth_signatory_mobile: Phone;
  tenant_url?: string;
  address_line1: string;
  address_line2?: string;
  city: string;
  state: string;
  country: string;
  zip_code: string;
  status: string;
  created_by: string;
  organisation_name: string;
  name: string;
  tenant_id: string;
  activated_at: string;
}

type Phone = {
  country_code: string;
  number: string;
};

export interface Module {
  module_type: string;
  enabled: boolean;
}

export interface Organisation {
  id: number;
  name: string;
}

export type CostCenterDetail = {
  code: string;
};

export type CostCenterDetailResponse = CostCenterDetail[];

export interface WorkRoleDetails {
  band: Band;
  level: Band;
  grade: Band;
}

export interface Band {
  name: string;
}

export interface TenantUpdateRequest {
  tenant_url?: string;
  logo?: string;
  name?: string;
  auth_signatory_email?: string;
  auth_signatory_name?: string;
  auth_signatory_mobile?: string;
  address_line1?: string;
  address_line2?: string;
  city?: string;
  state?: string;
  country?: string;
  zip_code?: string;
  domains?: string;
  modules: string[];
  sso_types: string[];
}

export interface IntegrationScreen {
  screen_name: string;
  redirect_url: string | null;
}

export type TenantSubscription = {
  id: string;
  plan: string;
  start_date: Date;
  end_date: Date;
  status: string;
  monthly_charge_per_employee: number;
  onboarding_charge: number;
  currency: string;
  monthly_employee_counts: MonthlyEmployeeCount[];
};

export type MonthlyEmployeeCount = {
  month: string;
  employee_count: number;
};

export interface CreateTenantSubscription {
  plan: string;
  start_date: string;
  end_date: string;
  monthly_charge_per_employee: number;
  onboarding_charge: number;
}

export interface CancelSubscription {
  subscription_id: string;
  effective_date: string;
}
