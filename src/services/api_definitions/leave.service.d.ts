export type LeaveRequestResponse = {
  request_id: string;
  reason: string;
  comment: string | null;
  status: string;
  raised_on: string;
  raised_by: string;
  approver: string;
  leave_type: string | null;
  start_date: string;
  end_date: string;
  duration: number;
  duration_type: string;
  applied_dates: string[];
  actioned_on: string;
};

export type LeaveSummaryResponse = {
  leave_type: string;
  num_of_leaves: number;
  total_leaves: number;
  info: string[];
  paid: boolean;
};

export type LeaveApprovalResponse = {
  request_id: string;
  reason: string;
  comment: string | null;
  status: string;
  raised_on: string;
  raised_by: {
    display_name: string;
    job_title: string;
    display_pic: string;
  };
  approver: string;
  leave_type: string | null;
  start_date: string;
  end_date: string;
  duration: number;
  duration_type: string;
  applied_dates: string[];
  actioned_on: string;
};

export type ApplyLeaveRequest = {
  leave_type: string;
  duration_type: string;
  reason: string;
  start_date: string;
  end_date: string;
};

export type EditLeaveRequestType = ApplyLeaveRequest & {
  request_id: string;
};

export type LeaveTransactionType = "Accrued" | "Availed" | "Canceled" | "Lapsed";

export interface LeaveTransactionConfig {
  icon: React.ReactElement;
  color: string;
  backgroundColor: string;
  lightBackground: string;
}

export interface LeaveTransactionDate {
  transaction_date: string;
}

export interface LeaveTransaction {
  transaction_type: LeaveTransactionType;
  transaction_dates: LeaveTransactionDate[];
  created_at: string;
  leave_days: number;
}

export interface LeaveTransactionModalProps {
  isModalOpen: boolean;
  onClose: () => void;
  leaveDetails: {
    leaveType: string;
    totalLeaves: number;
    noOfLeaves: number;
  };
}
