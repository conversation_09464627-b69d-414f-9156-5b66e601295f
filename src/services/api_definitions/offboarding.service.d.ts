import { BaseObject } from "src/app/global";

export interface Offboarding {
  notice_period_in_days: number;
}

export interface Separation {
  probation_period_in_days: number;
}

export interface EmployeeSeperations {
  name: string;
  employeeCode: string;
  email: string;
  date_of_separation: Date | string;
  eligibleForReHire: boolean;
  reason: string;
  type: string;
  status: string;
  comments: string;
  notice_period_served_in_days: number;
  notice_period_waived_off: boolean;
  leave_balance: BaseObject;
  employee_code: string;
}
