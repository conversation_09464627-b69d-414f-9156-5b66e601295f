export interface DashboardAttendanceDetails {
  work_start_time: string | null;
  work_end_time: string | null;
  check_in_time: string | null;
  check_out_time: string | null;
  location: string;
  duration: string | null;
  last_check_in_time: string | null;
  details: AttendanceDetail[];
}

type AttendanceDetail = {
  check_in_time: Date;
  check_out_time: Date;
  location: string;
};

export interface DashboardHolidayDetails {
  name: string;
  type: string;
  date: string;
  is_mandatory: boolean;
}

export interface DashboardEventDetails {
  display_pic: string;
  event_type: string;
  title: string;
  sub_title: string;
  event_date: string;
}

export interface DashboardTeamStatus {
  team_member: {
    employee_code: string;
    display_name: string;
    job_title: string;
    display_pic: string;
  };
  status: string;
}

export interface DashboardNewJoiners {
  first_name: string;
  last_name: string;
  personal_email: string;
  date_of_joining: string;
}

export interface DashboardSeperations {
  name: string;
  employee_code: string;
  email: string;
}
