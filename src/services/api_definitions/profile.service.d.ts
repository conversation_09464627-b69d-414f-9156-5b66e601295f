import {
  BankAccount,
  Dependent,
  EducationDetail,
  EmergencyContact,
  WorkExperience,
} from "../api_definitions/employees";

export interface PersonalInformationPayload {
  aadhar: string;
  passport: string;
  marital_status: string;
  personal_email: string;
  phone: string;
  blood_group: string;
}

export type EmergencyContactsPayload = EmergencyContact[];
export type WorkExperiencePayload = WorkExperience[];
export type EducationDetailsPayload = EducationDetail[];
export type FamilyDetailsPayload = Dependent[];

export { BankAccount as BankInformationPayload };
