import Attendance from "./timesheets.service";

export interface BaseCalendarEventResponse {
  event_type: string;
  title: string;
  sub_title: string | null;
  event_date: Date;
  reason: string;
  duration_type: string | null;
  comment: string | null;
  check_in_time: string | null;
  check_out_time: string | null;
  duration: string | null;
  allDay: boolean;
  is_paid: boolean;
}

export interface BaseApiResponse {
  week_offs: string[];
  events: BaseCalendarEventResponse[];
}

export interface CalendarEventProps {
  type: string;
  title: string;
  subTitle: string | null;
  start: Date;
  end: Date;
  reason: string | null;
  durationType: string | null;
  comment: string | null;
  checkinTime: string | null;
  checkoutTime: string | null;
  duration: string | null;
  allDay: boolean;
  isPaid: boolean;
}

export interface DefaultCalendarState {
  myCalendarEvents: CalendarEventProps[];
  weekOffs: string[];
}
export interface CalendarEventPayloadProps {
  start_date: string;
  end_date: string;
}

export interface CalendarDateRangeProps {
  start: Date;
  end: Date;
}

export interface AttendanceConfig {
  week_off: string[];
}

export interface EventResponseProps {
  week_offs: string[];
  events: CalendarEventProps[];
}

interface ReporteeProps {
  employee_code: string;
  display_name: string;
  job_title: string;
  display_pic: string;
}

export interface TeamCalendarProps {
  reportee: ReporteeProps;
  calendar: {
    weekOffs: string[];
    events: CalendarEventProps[];
  };
  attendance: Attendance[];
}

export interface TeamCalendarReponseProps {
  reportee: ReporteeProps;
  calendar: BaseApiResponse;
  attendances: Attendance[];
}
