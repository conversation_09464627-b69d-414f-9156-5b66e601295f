import { BaseObject } from "src/app/global";
import { apiRegister } from "src/services";
import { httpClient } from "src/utils/httpClient";
import { BaseResponse } from "./api_definitions/default.service";
import { CompanyDocument, EmployeeDocument } from "./api_definitions/documents.service";

class DocumentsServiceAPI {
  getEmployeeDocuments = async () => {
    const endpoint = apiRegister.DOCUMENTS.paths["get-employee-documents"];
    const resp = await httpClient<BaseResponse<EmployeeDocument[]>>(endpoint);
    return resp.data.response;
  };

  getCompanyDocuments = async () => {
    const endpoint = apiRegister.DOCUMENTS.paths["get-company-documents"];
    const resp = await httpClient<BaseResponse<CompanyDocument[]>>(endpoint);
    return resp.data.response;
  };

  createCompanyDocument = async (requestObject: BaseObject) => {
    const endpoint = apiRegister.DOCUMENTS.paths["create-company-document"];
    const resp = await httpClient<BaseResponse<BaseObject[]>>(endpoint, { method: "POST", data: requestObject });
    return resp.data.response;
  };

  updateCompanyDocument = async (requestObject: BaseObject) => {
    const endpoint = apiRegister.DOCUMENTS.paths["update-company-document"];
    const resp = await httpClient<BaseResponse<BaseObject[]>>(endpoint, { method: "PATCH", data: requestObject });
    return resp.data.response;
  };

  deleteCompanyDocument = async (requestObject: BaseObject) => {
    const endpoint = apiRegister.DOCUMENTS.paths["delete-company-document"];
    const resp = await httpClient<BaseResponse<BaseObject[]>>(endpoint, { method: "DELETE", data: requestObject });
    return resp.data.response;
  };
}

export default new DocumentsServiceAPI();
