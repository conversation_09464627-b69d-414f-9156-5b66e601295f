import { httpClient } from "src/utils/httpClient";
import { httpLoginClient } from "src/utils/httpLoginClient";
import { apiRegister } from ".";
import {
  GenerateOtpRequest,
  GenerateOtpResponse,
  PasswordLoginRequest,
  PasswordLoginResponse,
  RefreshTokenResponse,
  Screens,
  TenantResponse,
  UserDetails,
  VerifyOtpRequest,
  VerifyOtpResponse,
} from "./api_definitions/auth.service";
import { BaseResponse } from "./api_definitions/default.service";
import { TenantDetailsModel } from "./api_definitions/tenants";
class AuthService {
  getTenantDetailsForAuth = async (tenantUrl: string) => {
    const tenantDetailsResp = await httpClient<BaseResponse<Partial<TenantResponse> | Partial<TenantDetailsModel>>>(
      apiRegister.AUTH.paths["get-auth-tenant-details"],
      {
        params: {
          tenant_url: tenantUrl,
        },
      },
    );
    return tenantDetailsResp?.data.response;
  };
  authenticatedScreens = async (): Promise<Screens[]> => {
    const endpoint = apiRegister.AUTH.paths["get-authorised-screens"];
    const resp = await httpClient<BaseResponse<Screens[]>>(endpoint);
    return resp.data.response;
  };
  getUserDetails = async (): Promise<BaseResponse<UserDetails>> => {
    const endpoint = apiRegister.USER.paths["get-user-details"];
    const resp = await httpLoginClient<BaseResponse<UserDetails>>(endpoint);
    return resp.data;
  };

  refreshToken = async (): Promise<BaseResponse<RefreshTokenResponse>> => {
    const endpoint = apiRegister.USER.paths["refresh-token"];
    const resp = await httpLoginClient<BaseResponse<RefreshTokenResponse>>(endpoint, {
      method: "POST",
      withCredentials: true,
    });
    return resp.data;
  };

  signOut = async (): Promise<void> => {
    const endpoint = apiRegister.USER.paths["sign-out"];
    await httpLoginClient<void>(endpoint, { method: "POST", withCredentials: true });
  };

  passwordLogin = async (requestBody: PasswordLoginRequest): Promise<BaseResponse<PasswordLoginResponse>> => {
    const endpoint = apiRegister.USER.paths["custom-login"];
    const resp = await httpLoginClient<BaseResponse<PasswordLoginResponse>>(endpoint, {
      method: "POST",
      withCredentials: true,
      data: requestBody,
    });
    return resp.data;
  };

  generateOtp = async (requestBody: GenerateOtpRequest): Promise<BaseResponse<GenerateOtpResponse>> => {
    const endpoint = apiRegister.USER.paths["generate-otp"];
    const resp = await httpLoginClient<BaseResponse<GenerateOtpResponse>>(endpoint, {
      method: "POST",
      withCredentials: true,
      data: requestBody,
    });
    return resp.data;
  };

  verifyOtp = async (requestBody: VerifyOtpRequest): Promise<BaseResponse<VerifyOtpResponse>> => {
    const endpoint = apiRegister.USER.paths["verify-otp"];
    const resp = await httpLoginClient<BaseResponse<VerifyOtpResponse>>(endpoint, {
      method: "POST",
      withCredentials: true,
      data: requestBody,
    });
    return resp.data;
  };
}

export default new AuthService();
