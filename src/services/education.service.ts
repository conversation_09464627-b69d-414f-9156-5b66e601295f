import { apiRegister } from "src/services";
import { httpClient } from "src/utils/httpClient";
import { BaseResponse } from "./api_definitions/default.service";

export type GetAllInstitutesResponse = string[];
export type GetAvailableDegreesResponse = string[];

class EducationServiceAPI {
  getAllInstitutes = async function () {
    try {
      const resp = await httpClient<BaseResponse<GetAllInstitutesResponse>>(
        `${apiRegister.EMPLOYEES.paths["get-all-institutes"]}`,
      );
      if (resp.data.success === false) {
        // No search results found
        return null;
      }
      const data = resp.data.response;
      return data;
    } catch (_err) {
      return null;
    }
  };

  getAvailableDegrees = async function () {
    try {
      const resp = await httpClient<BaseResponse<GetAvailableDegreesResponse>>(
        `${apiRegister.EMPLOYEES.paths["get-all-degrees"]}`,
      );
      if (resp.data.success === false) {
        // No search results found
        return null;
      }
      const data = resp.data.response;
      return data;
    } catch (_error) {
      return null;
    }
  };
}

export default new EducationServiceAPI();
