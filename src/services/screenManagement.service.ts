import { apiRegister } from "src/services";
/* eslint-disable @typescript-eslint/no-explicit-any */
import { httpClient } from "src/utils/httpClient";
import { BaseResponse } from "./api_definitions/default.service";
import { CreateScreen, Screens, UpdateScreen } from "./api_definitions/screenManagement";

class ScreenManagement {
  getScreenURIs = async () => {
    try {
      const { data } = await httpClient<BaseResponse<string[]>>(apiRegister.SCREEN_MANAGEMENT.paths["get-screen-uris"]);

      if (data?.errors?.length > 0) {
        return [];
      }
      return data.response;
    } catch (_err) {
      return [];
    }
  };

  getAllScreens = async () => {
    try {
      const { data } = await httpClient<BaseResponse<Screens[]>>(
        apiRegister.SCREEN_MANAGEMENT.paths["get-all-screens"],
      );

      if (data?.errors?.length > 0) {
        return [];
      }
      return data.response;
    } catch (_err) {
      return [];
    }
  };

  getAllURIs = async () => {
    try {
      const { data } = await httpClient<BaseResponse<string[]>>(apiRegister.SCREEN_MANAGEMENT.paths["get-all-uris"]);

      if (data?.errors?.length > 0) {
        return [];
      }
      return data.response;
    } catch (_err) {
      return [];
    }
  };

  saveScreenURISettings = async (requestData: Record<string, any>[]) => {
    try {
      const resp = await httpClient<BaseResponse<string>>(apiRegister.SCREEN_MANAGEMENT.paths["update-screen-uri"], {
        method: "PUT",
        data: requestData,
      });
      return resp?.data?.response;
    } catch (_err) {
      return null;
    }
  };

  createScreen = async (createScreenPayload: CreateScreen) => {
    const { data } = await httpClient<string>(apiRegister.SCREEN_MANAGEMENT.paths["create-screen"], {
      method: "POST",
      data: createScreenPayload,
    });

    if (data) {
      return data;
    }
    return null;
  };

  updateScreen = async (updateScreenPayload: UpdateScreen) => {
    const { data } = await httpClient<string>(apiRegister.SCREEN_MANAGEMENT.paths["update-screen"], {
      method: "PATCH",
      data: updateScreenPayload,
    });

    if (data) {
      return data;
    }
    return null;
  };

  deleteScreen = async (name: string) => {
    const { data } = await httpClient<string>(apiRegister.SCREEN_MANAGEMENT.paths["delete-screen"], {
      method: "DELETE",
      data: {
        name,
      },
    });

    if (data) {
      return data;
    }
    return null;
  };
}

export default new ScreenManagement();
