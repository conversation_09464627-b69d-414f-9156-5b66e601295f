import { apiRegister } from "src/services";
import { httpClient } from "src/utils/httpClient";
import { showToast } from "src/utils/toast";
import { BaseResponse } from "./api_definitions/default.service";

export type ZIPCODE_APIResponse = {
  city: string;
  state: string;
  country: string;
};

export interface CountryListResponse {
  name: string;
  mobile_prefix: string;
  flag: string;
  code: string;
}
[];

export type IFSCResponseKeys = "city" | "state" | "country";

class LocationServiceAPI {
  getAddressDetailsFromZipcode = async function (zipcode: string) {
    try {
      const resp = await httpClient<BaseResponse<ZIPCODE_APIResponse>>(
        `${apiRegister.EMPLOYEES.paths["get-address-from-pincode"]}/${zipcode}`,
      );
      if (resp.data.success === false) {
        showToast(`Could not fetch details for ${zipcode}`, {
          type: "error",
          progress: 0,
        });
        // No search results found
        return null;
      }
      showToast(`Details fetched successfully ${zipcode}`, {
        type: "success",
        progress: 0,
      });
      return resp.data.response;
    } catch (_error) {
      return null;
    }
  };
  getCountryList = async function () {
    try {
      const resp = await httpClient<BaseResponse<CountryListResponse[]>>(
        `${apiRegister.GENERAL.paths["get-country-list"]}`,
      );
      return resp.data.response;
    } catch (_error) {
      return [];
    }
  };
}

export default new LocationServiceAPI();
