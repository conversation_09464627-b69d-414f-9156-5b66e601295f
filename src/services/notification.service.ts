import { apiRegister } from "src/services";
import { httpClient } from "src/utils/httpClient";
import { BaseResponse } from "./api_definitions/default.service";
import { NotificationResponse, NotificationSettings } from "./api_definitions/notification.service";

class NotificationService {
  registerNotificationToken = async (token: string) => {
    try {
      const resp = await httpClient<BaseResponse<string>>(
        apiRegister.NOTIFICATIONS.paths["register-notification-token"],
        {
          method: "POST",
          data: { token },
        },
      );
      return resp.status === 200;
    } catch (_error) {
      return false;
    }
  };

  getAllNotifications = async (noOfDays: string) => {
    try {
      const resp = await httpClient<BaseResponse<NotificationResponse[]>>(
        apiRegister.NOTIFICATIONS.paths["get-all-notification"].replace(":numberOfDays", noOfDays),
      );
      if (!resp.data.success) {
        return [];
      }
      return resp.data.response;
    } catch (_error) {
      return [];
    }
  };

  markNotificationAsRead = async (notificationId: string) => {
    try {
      const endpoint = apiRegister.NOTIFICATIONS.paths["mark-notification-as-read"].replace(
        ":notificationId",
        notificationId,
      );
      const resp = await httpClient<BaseResponse<string>>(endpoint, { method: "POST" });
      return resp.data.success;
    } catch (_error) {
      return false;
    }
  };

  markAllNotificationAsRead = async (date: string) => {
    try {
      const url = `${apiRegister.NOTIFICATIONS.paths["mark-all-notification-as-read"]}?before=${date}`;
      const resp = await httpClient<BaseResponse<string>>(url, { method: "POST" });
      return resp.data.success;
    } catch (_error) {
      return false;
    }
  };

  clearAllNotifications = async () => {
    try {
      const resp = await httpClient<BaseResponse<string>>(apiRegister.NOTIFICATIONS.paths["clear-all-notifications"], {
        method: "POST",
      });
      return resp.data.success;
    } catch (_error) {
      return false;
    }
  };

  getUnreadNotification = async () => {
    try {
      const resp = await httpClient<BaseResponse<NotificationResponse[]>>(
        apiRegister.NOTIFICATIONS.paths["get-unread-notification"],
      );
      return resp.data.response;
    } catch (_error) {
      return [];
    }
  };

  getUnreadNotificationCount = async () => {
    try {
      const resp = await httpClient<BaseResponse<number>>(
        apiRegister.NOTIFICATIONS.paths["get-unread-notification-count"],
      );
      return resp.data.response;
    } catch (_error) {
      return 0;
    }
  };

  pushNotification = async () => {
    try {
      const resp = await httpClient<BaseResponse<string>>(apiRegister.NOTIFICATIONS.paths["push-notification"], {
        method: "POST",
        data: {},
      });
      return resp.data.success;
    } catch (_error) {
      return false;
    }
  };

  updateNotificationSettings = async (notificationType: string, checked: boolean) => {
    try {
      const resp = await httpClient<BaseResponse<string>>(
        apiRegister.NOTIFICATIONS.paths["update-notification-settings"],
        {
          method: "PUT",
          data: [
            {
              notification_type: notificationType,
              enabled_channels: checked ? ["Push Notification"] : [],
            },
          ],
        },
      );
      return resp.data.success;
    } catch (_error) {
      return false;
    }
  };

  getNotificationSettings = async () => {
    try {
      const resp = await httpClient<BaseResponse<NotificationSettings[]>>(
        apiRegister.NOTIFICATIONS.paths["get-notification-settings"],
      );
      return resp.data.response;
    } catch (_error) {
      return null;
    }
  };
}

export default new NotificationService();
