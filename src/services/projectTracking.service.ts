import { BaseObject } from "src/app/global";
import { apiRegister } from "src/services";
import { httpClient } from "src/utils/httpClient";
import { BaseResponse } from "./api_definitions/default.service";
// import { ProjectTrackingTask } from "./api_definitions/projectTracking";

const getParentId = (taskType: string, projectId: string, taskId: string) => {
  if (taskType === "Task") {
    return projectId;
  }
  if (taskType === "SubTask") {
    return taskId;
  }
  return null;
};

const getProjectId = (tasks: any[], taskId: string, taskType: string) => {
  if (taskType === "Task" || !taskType) return taskId;
  const parentNode = tasks.find((task) => task.id === taskId);
  const parentId = parentNode?.parent_id;
  const parentTaskType = parentNode?.task_type;
  return getProjectId(tasks, parentId, parentTaskType);
};

class ProjectTrackingServiceAPI {
  getTasks = async (employeeCode?: string) => {
    const endpoint = apiRegister.PROJECT_TRACKING.paths["get-tasks"];
    const resp = await httpClient<BaseResponse<any[]>>(endpoint, {
      params: {
        code: employeeCode,
      },
    });
    const convertedResponse = resp.data.response.map((task) => ({
      taskType: task.task_type,
      dueDate: task.due_date,
      priority: task.priority,
      estimatedHours: task.estimated_hours,
      elapsedHours: task.elapsed_hours,
      trackingDetails: task.tracking_details,
      parentId: task.parent_id,
      showClockIn: !task.is_running,
      clockedHours: task.clocked_hours,
      clientCode: task.meta_data?.client_code,
      id: task.id,
      name: task.name,
      taskId: task?.task_type === "SubTask" ? task.parent_id : null,
      projectId: getProjectId(resp.data.response, task.parent_id, task.task_type),
      status: task.status,
    }));
    return convertedResponse;
  };

  addTask = async (requestObject: BaseObject, employeeCode?: string) => {
    const endpoint = apiRegister.PROJECT_TRACKING.paths["add-task"];
    const transformedRequestObject = {
      name: requestObject.name,
      parent_id: getParentId(
        requestObject.taskType as string,
        requestObject.projectId as string,
        requestObject.taskId as string,
      ),
      task_type: requestObject.taskType,
      due_date: requestObject.dueDate,
      priority: requestObject.priority,
      estimated_hours: requestObject.estimatedHours,
      client_code: requestObject.clientCode,
    };
    const resp = await httpClient<BaseResponse<BaseObject[]>>(endpoint, {
      method: "POST",
      data: transformedRequestObject,
      params: {
        code: employeeCode,
      },
    });
    return resp.data.success;
  };

  updateTask = async (requestObject: BaseObject, employeeCode?: string) => {
    const endpoint = apiRegister.PROJECT_TRACKING.paths["update-task"].replace(
      ":taskId",
      requestObject?.taskId as string,
    );
    const transformedRequestObject = {
      name: requestObject.name,
      parent_id: getParentId(
        requestObject.taskType as string,
        requestObject.projectId as string,
        requestObject.taskId as string,
      ),
      task_type: requestObject.taskType,
      due_date: requestObject.dueDate,
      priority: requestObject.priority,
      estimated_hours: requestObject.estimatedHours,
      client_code: requestObject.clientCode,
      id: requestObject.id,
    };
    const resp = await httpClient<BaseResponse<BaseObject[]>>(endpoint, {
      method: "PATCH",
      data: transformedRequestObject,
      params: {
        code: employeeCode,
      },
    });
    return resp.data.success;
  };

  deleteTask = async (id: string | null) => {
    const endpoint = apiRegister.PROJECT_TRACKING.paths["delete-task"].replace(":taskId", id as string);
    const resp = await httpClient<BaseResponse<BaseObject[]>>(endpoint, { method: "DELETE" });
    return resp.data.response;
  };

  clockIn = async (id: string | null) => {
    const endpoint = apiRegister.PROJECT_TRACKING.paths["clock-in"].replace(":taskId", id as string);
    const resp = await httpClient<BaseResponse<BaseObject[]>>(endpoint, { method: "POST" });
    return resp.data.response;
  };

  clockOut = async (id: string | null) => {
    const endpoint = apiRegister.PROJECT_TRACKING.paths["clock-out"].replace(":taskId", id as string);
    const resp = await httpClient<BaseResponse<BaseObject[]>>(endpoint, { method: "POST" });
    return resp.data.response;
  };

  markComplete = async (id: string | null) => {
    const endpoint = apiRegister.PROJECT_TRACKING.paths["mark-complete"].replace(":taskId", id as string);
    const resp = await httpClient<BaseResponse<BaseObject[]>>(endpoint, { method: "POST" });
    return resp.data.response;
  };

  reopenTask = async (id: string | null, employeeCode?: string) => {
    const endpoint = apiRegister.PROJECT_TRACKING.paths["reopen-task"].replace(":taskId", id as string);
    const resp = await httpClient<BaseResponse<BaseObject[]>>(endpoint, {
      method: "POST",
      params: {
        code: employeeCode,
      },
    });
    return resp.data.response;
  };
}

export default new ProjectTrackingServiceAPI();
