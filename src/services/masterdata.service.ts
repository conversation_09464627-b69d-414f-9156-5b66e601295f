import { httpClient } from "src/utils/httpClient";
import { apiRegister } from ".";

export type MasterDataEnumKeys = "ACL";

class MasterDataService {
  getACLs = async <T extends object | string>(enumKey: string): Promise<T[] | string[]> => {
    const resp = await httpClient<string[]>(`${apiRegister.MASTER_DATA.paths["get-master-data"]}/${enumKey}`);
    return resp.data;
  };
}

export default new MasterDataService();
