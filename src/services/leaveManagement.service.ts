import { keysToCamelCase } from "src/utils/dataUtils";
import {
  ValidFileExtensions,
  createPseudoLinkAndDownload,
  getFilenameFromContentDisposition,
} from "src/utils/fileUtils";
import { httpClient } from "src/utils/httpClient";
import { apiRegister } from ".";
import { BaseResponse, FileUploadResponse } from "./api_definitions/default.service";
import {
  CreateHoliday,
  CreateLeaveType,
  CreateOrganisationHoliday,
  DeleteHoliday,
  DeleteLeavePolicyDetails,
  DeleteLeaveType,
  HolidayResponse,
  LeaveDetails,
  LeavePolicyDetails,
  OrganisationHolidays,
  UpdateLeaveType,
  UpdateOrganisationHoliday,
} from "./api_definitions/leaveManagement.service";
import fileuploaderService from "./fileuploader.service";

class HolidayService {
  getOrganisationHolidays = async () => {
    const { data } = await httpClient<BaseResponse<OrganisationHolidays[]>>(
      apiRegister.HOLIDAY.paths["get-organisation-holidays"],
    );
    const { response, errors } = data;

    if (errors?.length > 0) {
      return [];
    }
    return response;
  };

  getHolidays = async (year: string, country: string) => {
    const { data } = await httpClient<BaseResponse<HolidayResponse[]>>(apiRegister.HOLIDAY.paths["get-holidays"], {
      params: {
        year,
        country,
      },
    });
    const { response, errors } = data;

    if (errors?.length > 0) {
      return [];
    }
    return response;
  };

  getLeaveDetails = async () => {
    try {
      const { data } = await httpClient<BaseResponse<LeaveDetails>>(apiRegister.HOLIDAY.paths["get-leave-details"]);
      const { response, errors } = data;

      if (errors?.length > 0) {
        return {
          default_leaves: [],
          custom_leaves: [],
        };
      }
      return response;
    } catch (_err) {
      return {
        default_leaves: [],
        custom_leaves: [],
      };
    }
  };

  createOrganisationHoliday = async ({
    country,
    location,
    year,
    mandatory_holidays,
    restricted_holidays,
  }: CreateOrganisationHoliday) => {
    const resp = await httpClient<BaseResponse<string>>(apiRegister.HOLIDAY.paths["create-organisation-holiday"], {
      method: "POST",
      data: {
        country,
        location,
        year,
        mandatory_holidays,
        restricted_holidays,
      },
    });
    if (resp?.data?.errors?.length > 0) {
      throw new Error("Error creating holiday");
    }
    return resp?.data?.response;
  };

  updateOrganisationHoliday = async ({
    country,
    location,
    year,
    mandatory_holidays,
    restricted_holidays,
  }: UpdateOrganisationHoliday) => {
    const resp = await httpClient<BaseResponse<string>>(apiRegister.HOLIDAY.paths["update-organisation-holiday"], {
      method: "PATCH",
      data: {
        country,
        location,
        year,
        mandatory_holidays,
        restricted_holidays,
      },
    });
    if (resp?.data?.errors?.length > 0) {
      throw new Error("Error updating holiday");
    }
    return resp?.data?.response;
  };

  createHoliday = async ({ name, date, country }: CreateHoliday) => {
    const resp = await httpClient<BaseResponse<string>>(apiRegister.HOLIDAY.paths["create-holiday"], {
      method: "POST",
      data: {
        name,
        date,
        country,
      },
    });
    return resp?.data?.response;
  };

  deleteHoliday = async ({ country, location, year }: DeleteHoliday) => {
    const resp = await httpClient<BaseResponse<string>>(apiRegister.HOLIDAY.paths["delete-holiday"], {
      method: "DELETE",
      data: {
        country,
        location,
        year,
      },
    });
    return resp?.data?.response;
  };

  createLeaveType = async ({ type, description, isPaid, approver, short_name }: CreateLeaveType) => {
    const resp = await httpClient<BaseResponse<string>>(apiRegister.HOLIDAY.paths["create-leave-type"], {
      method: "POST",
      data: {
        type,
        short_name,
        description,
        paid: isPaid,
        approver,
      },
    });
    if (resp?.data?.errors?.length > 0) {
      throw new Error("Error creating leave type");
    }
    return resp?.data?.response;
  };

  editLeaveType = async ({ type, new_type, description, isPaid, approver, short_name }: UpdateLeaveType) => {
    const resp = await httpClient<BaseResponse<string>>(apiRegister.HOLIDAY.paths["update-leave-type"], {
      method: "PATCH",
      data: {
        type,
        new_type,
        short_name,
        description,
        paid: isPaid,
        approver,
      },
    });
    if (resp?.data?.errors?.length > 0) {
      throw new Error("Error updating leave type");
    }
    return resp?.data?.response;
  };

  deleteLeaveType = async ({ type, approver, description, isPaid }: DeleteLeaveType) => {
    const resp = await httpClient<BaseResponse<string>>(apiRegister.HOLIDAY.paths["delete-leave-type"], {
      method: "DELETE",
      data: {
        type,
        approver,
        description,
        paid: isPaid,
      },
    });
    if (resp?.data?.errors?.length > 0) {
      throw new Error("Error deleting leave type");
    }
    return resp?.data?.response;
  };

  getLeavePolicies = async () => {
    const { data } = await httpClient<BaseResponse<LeavePolicyDetails[]>>(
      apiRegister.HOLIDAY.paths["get-leave-policies"],
    );
    const { response, errors } = data;

    if (errors?.length > 0) {
      return [];
    }
    return keysToCamelCase(response);
  };

  createLeavePolicies = async (payload: Record<string, unknown>) => {
    const resp = await httpClient<BaseResponse<string>>(apiRegister.HOLIDAY.paths["create-leave-policies"], {
      method: "POST",
      data: { ...payload },
    });
    if (resp?.data?.errors) {
      throw new Error("Error while creating leave policies");
    }
    return resp?.data?.response;
  };

  updateLeavePolicies = async (payload: Record<string, unknown>) => {
    const resp = await httpClient<BaseResponse<string>>(apiRegister.HOLIDAY.paths["update-leave-policies"], {
      method: "PATCH",
      data: { ...payload },
    });
    if (resp?.data?.errors) {
      throw new Error("Error while updating leave policies");
    }
    return resp?.data?.response;
  };

  deleteLeavePolicy = async (payload: DeleteLeavePolicyDetails) => {
    const resp = await httpClient<BaseResponse<string>>(apiRegister.HOLIDAY.paths["delete-leave-policy"], {
      method: "DELETE",
      data: { ...payload },
    });
    return resp?.data?.response;
  };

  downloadPdf = async (country: string, location: string, year: string) => {
    try {
      const resp = await httpClient<Blob>(apiRegister.HOLIDAY.paths["download-org-pdf"], {
        responseType: "blob",
        params: {
          country,
          location,
          year,
        },
      });

      const [fileName, extention] = (
        getFilenameFromContentDisposition(resp?.headers["content-disposition"]) as string
      ).split(".");

      if (fileName && extention) {
        createPseudoLinkAndDownload(resp.data, `.${extention}` as ValidFileExtensions, fileName);
      }

      return [];
    } catch (_err) {
      return [];
    }
  };

  downloadLeavesTemplate = async () => {
    try {
      const resp = await httpClient<Blob>(apiRegister.HOLIDAY.paths["download-template"], {
        responseType: "blob",
      });

      const [fileName, extention] = (
        getFilenameFromContentDisposition(resp?.headers["content-disposition"]) as string
      ).split(".");

      if (fileName && extention) {
        createPseudoLinkAndDownload(resp.data, `.${extention}` as ValidFileExtensions, fileName);
      }

      return [];
    } catch (_err) {
      return [];
    }
  };

  uploadFile = async (file: File) => {
    const formData = new FormData();
    formData.append("file", file);
    const resp: FileUploadResponse<string | string[]> = await fileuploaderService.uploadFile(
      apiRegister.HOLIDAY.paths["upload-leaves"],
      formData,
    );
    return resp;
  };
}

export default new HolidayService();
