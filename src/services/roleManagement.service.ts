import { httpClient } from "src/utils/httpClient";
import { apiRegister } from ".";
import { BaseResponse } from "./api_definitions/default.service";
import { AllRolesResponse, AllUserDetailsResponse, RoleScreensResponse } from "./api_definitions/roleManagement";
import roleManagementTransform from "./data_transformers/roleManagement.transform";

class RoleManagementService {
  getUserRoles = async () => {
    const resp = await httpClient<BaseResponse<AllRolesResponse>>(apiRegister.ROLE_MANAGEMENT.paths["get-user-roles"]);

    if (resp?.data?.errors?.length > 0) {
      return [];
    }
    return roleManagementTransform.transformFetchedRoles(resp.data.response);
  };

  createUserRoles = async (role: string) => {
    try {
      const resp = await httpClient<string>(apiRegister.ROLE_MANAGEMENT.paths["create-user-role"], {
        method: "POST",
        data: {
          name: role,
        },
      });
      return resp.data;
    } catch (_err) {
      return null;
    }
  };

  updateUserRoles = async (oldRole: string, updatedRole: string) => {
    try {
      const resp = await httpClient<BaseResponse<string>>(apiRegister.ROLE_MANAGEMENT.paths["update-user-roles"], {
        method: "PATCH",
        data: {
          old_name: oldRole,
          new_name: updatedRole,
        },
      });
      return resp?.data?.response;
    } catch (_err) {
      return null;
    }
  };

  deleteUserRoles = async (roleToDelete: string) => {
    try {
      const resp = await httpClient<string>(apiRegister.ROLE_MANAGEMENT.paths["delete-user-roles"], {
        method: "DELETE",
        data: {
          name: roleToDelete,
        },
      });
      return resp.data;
    } catch (_err) {
      return null;
    }
  };

  getAllRoles = async () => {
    try {
      const resp = await httpClient<BaseResponse<RoleScreensResponse>>(
        apiRegister.ROLE_MANAGEMENT.paths["get-all-role-screens"],
      );
      const { errors, response } = resp.data;

      if (errors?.length > 0) {
        return [];
      }
      return roleManagementTransform.getRolesScreens(response);
    } catch (_err) {
      return null;
    }
  };

  getAllRoles1 = async () => {
    try {
      const resp = await httpClient<BaseResponse<RoleScreensResponse>>(
        apiRegister.ROLE_MANAGEMENT.paths["get-all-role-screens"],
      );
      const { errors, response } = resp.data;

      if (errors?.length > 0) {
        return [];
      }
      return response;
    } catch (_err) {
      return null;
    }
  };

  upsertACLForRoleScreens = async (aclUpdate: Record<string, string>[]) => {
    try {
      const resp = await httpClient<BaseResponse<string>>(apiRegister.ROLE_MANAGEMENT.paths["upsert-acl-role-screen"], {
        method: "PATCH",
        data: aclUpdate,
      });
      return resp?.data?.response;
    } catch (_err) {
      return null;
    }
  };

  //APIs for updating a role of a user, of a perticular tenant, by super admin.

  getAllUserDetails = async () => {
    try {
      const resp = await httpClient<BaseResponse<AllUserDetailsResponse[]>>(
        apiRegister.USER_ROLE_MANAGEMENT.paths["get-all-user-roles"],
      );
      return resp.data.response;
    } catch (_err) {
      return null;
    }
  };

  getAllAllowedRoles = async () => {
    try {
      const resp = await httpClient<BaseResponse<string[]>>(
        apiRegister.ROLE_MANAGEMENT.paths["get-user-allowed-roles"],
      );
      return resp.data.response;
    } catch (_err) {
      return null;
    }
  };

  updateUserRole = async (payload: { email: string; roles: string[] }) => {
    try {
      const resp = await httpClient<BaseResponse<AllRolesResponse>>(
        apiRegister.USER_ROLE_MANAGEMENT.paths["update-user-role"],
        {
          method: "PUT",
          data: payload,
        },
      );
      return resp.data.response;
    } catch (_err) {
      return null;
    }
  };

  createUserRole = async (payload: { email: string; roles: string[] }) => {
    try {
      const resp = await httpClient<BaseResponse<AllRolesResponse>>(
        apiRegister.USER_ROLE_MANAGEMENT.paths["create-user-role"],
        {
          method: "POST",
          data: payload,
        },
      );
      return resp.data.response;
    } catch (_err) {
      return null;
    }
  };

  deleteUserRole = async (email: string) => {
    try {
      const resp = await httpClient<BaseResponse<AllRolesResponse>>(
        apiRegister.USER_ROLE_MANAGEMENT.paths["delete-user-role"],
        {
          method: "DELETE",
          data: {
            email,
          },
        },
      );
      return resp.data.response;
    } catch (_err) {
      return null;
    }
  };
}

export default new RoleManagementService();
