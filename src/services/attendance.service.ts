import { httpClient } from "src/utils/httpClient";
import { apiRegister } from ".";
import { BaseResponse } from "./api_definitions/default.service";
import { EmployeeAttendanceConfiguration } from "./api_definitions/employeeAttendance.service";

class AttendanceService {
  getEmployeeAttendanceDetails = async (code?: string) => {
    const resp = await httpClient<BaseResponse<EmployeeAttendanceConfiguration>>(
      apiRegister.TENANTS.paths["get-employee-attendance-details"],
      {
        method: "GET",
        params: {
          code,
        },
      },
    );
    if (resp?.data?.errors?.length > 0) {
      throw new Error("Error while fetching employee attendance details");
    }
    return resp?.data?.response;
  };

  upsertEmployeeAttendanceConfig = async (
    payload: EmployeeAttendanceConfiguration,
    employeeCode: string,
    replicateForAllEmployees = false,
  ) => {
    const resp = await httpClient<BaseResponse<EmployeeAttendanceConfiguration>>(
      apiRegister.TENANTS.paths["upsert-employee-attendance-details"],
      {
        method: "POST",
        data: payload,
        params: {
          code: employeeCode,
          replicate: replicateForAllEmployees,
        },
      },
    );
    if (resp?.data?.errors?.length > 0) {
      throw new Error("Error while upserting employee attendance details");
    }
    return resp?.data?.response;
  };

  deleteEmployeeAttendanceConfig = async (employeeCode?: string, replicate?: boolean) => {
    const resp = await httpClient<BaseResponse<EmployeeAttendanceConfiguration>>(
      apiRegister.TENANTS.paths["delete-employee-attendance-details"],
      {
        method: "DELETE",
        params: {
          code: employeeCode,
          replicate,
        },
      },
    );
    if (resp?.data?.errors?.length > 0) {
      throw new Error("Error while deleting employee attendance details");
    }
    return resp?.data?.response;
  };
}

export default new AttendanceService();
