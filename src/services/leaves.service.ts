import { BaseObject } from "src/app/global";
import { apiRegister } from "src/services";
import { httpClient } from "src/utils/httpClient";

import { BaseResponse } from "./api_definitions/default.service";
import {
  ApplyLeaveRequest,
  EditLeaveRequestType,
  LeaveApprovalResponse,
  LeaveRequestResponse,
  LeaveSummaryResponse,
} from "./api_definitions/leave.service";
import { LeaveTransaction } from "./api_definitions/leave.service";
import leaveTransform from "./data_transformers/leave.transforms";

class LeaveServiceAPI {
  getLeaveSummary = async () => {
    try {
      const resp = await httpClient<BaseResponse<LeaveSummaryResponse[]>>(
        apiRegister.LEAVES.paths["get-leave-summary"],
      );
      if (!resp.data.success) {
        return [];
      }
      return leaveTransform.transformLeaveSummary(resp.data.response);
    } catch (_error) {
      return [];
    }
  };
  getLeaveRequests = async () => {
    try {
      const resp = await httpClient<BaseResponse<LeaveRequestResponse[]>>(
        apiRegister.LEAVES.paths["get-leave-requests"],
      );
      if (resp.data.success === false) {
        return [];
      }
      return leaveTransform.transformLeaveRequests(resp.data.response);
    } catch (_error) {
      return [];
    }
  };
  applyLeave = async (payload: ApplyLeaveRequest) => {
    try {
      const resp = await httpClient<BaseResponse<string>>(apiRegister.LEAVES.paths["apply-leave"], {
        method: "POST",
        data: payload,
      });
      if (resp.data.success === false) {
        throw "Error applying Leave";
      }
      return true;
    } catch (_error) {
      throw "Error applying Leave";
    }
  };
  editLeaveRequest = async (payload: EditLeaveRequestType) => {
    try {
      const resp = await httpClient<BaseResponse<string>>(apiRegister.LEAVES.paths["edit-leave-request"], {
        method: "PATCH",
        data: payload,
      });
      if (resp.data.success === false) {
        throw "Error editing Leave";
      }
      return true;
    } catch (_error) {
      throw "Error editing Leave";
    }
  };
  deleteLeaveRequest = async (requestId: string) => {
    try {
      const resp = await httpClient<BaseResponse<string>>(apiRegister.LEAVES.paths["delete-leave-request"], {
        method: "DELETE",
        data: {
          request_id: requestId,
        },
      });
      if (resp.data.success === false) {
        return false;
      }
      return true;
    } catch (_error) {
      return false;
    }
  };
  getLeaveRequestToApprove = async () => {
    try {
      const resp = await httpClient<BaseResponse<LeaveApprovalResponse[]>>(
        apiRegister.LEAVES.paths["leave-request-approval"],
      );
      if (resp.data.success === false) {
        return [];
      }
      return leaveTransform.transformLeaveApprovals(resp.data.response);
    } catch (_error) {
      return [];
    }
  };
  approveLeaveRequest = async (requestId: string) => {
    try {
      const resp = await httpClient<BaseResponse<BaseObject>>(apiRegister.LEAVES.paths["approve-leave-request"], {
        method: "PUT",
        data: {
          request_id: requestId,
        },
      });
      if (resp.data.success === false) {
        return false;
      }
      return true;
    } catch (_error) {
      return false;
    }
  };
  rejectLeaveRequest = async (payload: { request_id: string; comment: string }) => {
    try {
      const resp = await httpClient<BaseResponse<BaseObject>>(apiRegister.LEAVES.paths["reject-leave-request"], {
        method: "PUT",
        data: payload,
      });
      if (resp.data.success === false) {
        return false;
      }
      return true;
    } catch (_error) {
      return false;
    }
  };
  getLeaveTransactions = async (leaveType: string): Promise<LeaveTransaction[] | null> => {
    try {
      const resp = await httpClient<BaseResponse<LeaveTransaction[]>>(
        `${apiRegister.LEAVES.paths["get-leave-transactions"]}?leave_type=${leaveType}`,
      );
      if (!resp.data.success) {
        return null;
      }
      return resp.data.response;
    } catch (_error) {
      return null;
    }
  };

  approveBatchLeaveRequests = async (requestIds: string[]) => {
    try {
      const resp = await httpClient<BaseResponse<BaseObject>>(
        apiRegister.LEAVES.paths["approve-batch-leave-requests"],
        {
          method: "PUT",
          data: {
            request_ids: requestIds,
          },
        },
      );
      if (resp.data.success === false) {
        return false;
      }
      return true;
    } catch (_error) {
      return false;
    }
  };

  rejectBatchLeaveRequests = async (payload: { request_ids: string[]; comment: string }) => {
    try {
      const resp = await httpClient<BaseResponse<BaseObject>>(apiRegister.LEAVES.paths["reject-batch-leave-requests"], {
        method: "PUT",
        data: payload,
      });
      if (resp.data.success === false) {
        return false;
      }
      return true;
    } catch (_error) {
      return false;
    }
  };
}

export default new LeaveServiceAPI();
