import {
  ValidFileExtensions,
  createPseudoLinkAndDownload,
  getFilenameFromContentDisposition,
} from "src/utils/fileUtils";
import { httpClient } from "src/utils/httpClient";
import { apiRegister } from ".";
import { BaseResponse } from "./api_definitions/default.service";
import { TimeSheet } from "./api_definitions/timesheets.service";

class TimeSheets {
  getTimesheets = async (yearMonth: string) => {
    const { data } = await httpClient<BaseResponse<TimeSheet[]>>(apiRegister.TIMESHEETS.paths["get-timesheets"], {
      params: { yearMonth },
    });

    if (data?.errors?.length > 0) {
      return;
    }
    return data?.response;
  };

  exportTimesheets = async (yearMonth: string) => {
    const resp = await httpClient<Blob>(apiRegister.TIMESHEETS.paths["export-timesheets"], {
      responseType: "blob",
      params: { yearMonth },
    });
    const [fileName, extention] = (
      getFilenameFromContentDisposition(resp?.headers["content-disposition"]) as string
    ).split(".");
    if (fileName && extention) {
      createPseudoLinkAndDownload(resp.data, `.${extention}` as ValidFileExtensions, fileName);
    }
    return resp.data;
  };

  assignProjects = async (employeeCode: string, projectCodes: string[]) => {
    const endpoint = apiRegister.TIMESHEET_TRACKING.paths["assign-projects"].replace(":employeeCode", employeeCode);
    const { data } = await httpClient<BaseResponse<TimeSheet[]>>(endpoint, {
      data: projectCodes,
      method: "PUT",
    });
    return data?.success;
  };
}

export default new TimeSheets();
