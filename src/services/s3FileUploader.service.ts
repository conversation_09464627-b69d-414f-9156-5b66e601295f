import { httpClient } from "src/utils/httpClient";
import { showToast } from "src/utils/toast";
import { apiRegister } from ".";
import { BaseResponse } from "./api_definitions/default.service";

class S3FileUploader {
  getPreSignedURL = async (path: string) => {
    const { data } = await httpClient<BaseResponse<string>>(apiRegister.AWS_S3.paths["get-public-presigned-url"], {
      params: { key: path },
    });

    if (data?.errors?.length > 0) {
      data?.errors?.forEach((error) =>
        showToast(error, {
          type: "error",
        }),
      );
      return null;
    }
    return data?.response;
  };
}

export default new S3FileUploader();
