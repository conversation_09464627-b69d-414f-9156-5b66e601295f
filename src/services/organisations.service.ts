import { apiRegister } from "src/services";
import { httpClient } from "src/utils/httpClient";
import { BaseResponse } from "./api_definitions/default.service";
import {
  CreateOrganisationDetail,
  OrganisationDetails,
  UpdateOrganisationDetail,
} from "./api_definitions/organisations.service";

class Organisations {
  getOrganisations = async () => {
    const registeredAPIs = apiRegister["ORGANISATIONS"];
    const { data } = await httpClient<BaseResponse<OrganisationDetails>>(registeredAPIs.paths["get-organisations"]);

    if (data?.errors?.length > 0) {
      return [];
    }
    return data.response;
  };

  updateOrganisation = async (details: UpdateOrganisationDetail) => {
    const { data } = await httpClient<BaseResponse<OrganisationDetails>>(
      apiRegister.ORGANISATIONS.paths["update-organisation"],
      {
        method: "PATCH",
        data: details,
      },
    );
    if (!data?.success) {
      throw new Error("Error while creating organisation");
    }
    return data?.success;
  };

  createOrganisation = async (details: CreateOrganisationDetail) => {
    const { data } = await httpClient<BaseResponse<OrganisationDetails>>(
      apiRegister.ORGANISATIONS.paths["create-organisation"],
      {
        method: "POST",
        data: details,
      },
    );
    if (!data?.success) {
      throw new Error("Error while creating organisation");
    }
    return data?.success;
  };

  deleteOrganisation = async (name: string) => {
    const { data } = await httpClient(apiRegister.ORGANISATIONS.paths["delete-organisation"], {
      method: "DELETE",
      data: { name },
    });
    return data;
  };
}

export default new Organisations();
