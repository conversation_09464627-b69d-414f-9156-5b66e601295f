import { format } from "date-fns";
import { httpClient } from "src/utils/httpClient";
import { apiRegister } from ".";
import { BaseResponse } from "./api_definitions/default.service";
import {
  CreateClientRequest,
  CreateProjectRequest,
  Project,
  TimesheetTracking,
} from "./api_definitions/timesheetTracking.service";

class TimesheetTrackingService {
  getClients = async () => {
    const resp = await httpClient<BaseResponse<TimesheetTracking[]>>(
      apiRegister.TIMESHEET_TRACKING.paths["get-all-client-projects"],
    );
    if (resp?.data?.errors?.length > 0) {
      throw new Error("Error fetching clients");
    }
    return resp.data.response;
  };

  createClient = async (client: CreateClientRequest) => {
    const resp = await httpClient<BaseResponse<string>>(apiRegister.TIMESHEET_TRACKING.paths["create-client"], {
      method: "POST",
      data: client,
    });
    if (resp?.data?.errors?.length > 0) {
      throw new Error("Error creating client");
    }
    return resp.data.response;
  };

  updateClient = async (client: CreateClientRequest) => {
    const resp = await httpClient<BaseResponse<string>>(apiRegister.TIMESHEET_TRACKING.paths["update-client"], {
      method: "PATCH",
      data: client,
    });
    if (resp?.data?.errors?.length > 0) {
      throw new Error("Error creating client");
    }
    return resp.data.response;
  };

  createProject = async (project: CreateProjectRequest) => {
    const resp = await httpClient<BaseResponse<string>>(apiRegister.TIMESHEET_TRACKING.paths["create-project"], {
      method: "POST",
      data: project,
    });
    if (resp?.data?.errors?.length > 0) {
      throw new Error("Error creating project");
    }
    return resp.data.response;
  };

  updateProject = async (client: CreateProjectRequest) => {
    const resp = await httpClient<BaseResponse<string>>(apiRegister.TIMESHEET_TRACKING.paths["update-project"], {
      method: "PATCH",
      data: client,
    });
    if (resp?.data?.errors?.length > 0) {
      throw new Error("Error creating client");
    }
    return resp.data.response;
  };

  deleteProject = async (projectId: string) => {
    const resp = await httpClient<BaseResponse<string>>(
      apiRegister.TIMESHEET_TRACKING.paths["delete-project"].replace(":projectId", projectId),
      {
        method: "DELETE",
      },
    );
    if (resp?.data?.errors?.length > 0) {
      throw new Error("Error deleting project");
    }
    return resp.data.response;
  };

  getAllTimesheets = async (start: string, end: string) => {
    const resp = await httpClient<BaseResponse<TimesheetTracking[]>>(
      apiRegister.TIMESHEET_TRACKING.paths["get-all-timesheets"],
      {
        params: {
          start_date: format(start, "yyyy-MM-dd"),
          end_date: format(end, "yyyy-MM-dd"),
        },
      },
    );
    if (resp?.data?.errors?.length > 0) {
      throw new Error("Error fetching timesheets");
    }
    return resp.data.response;
  };

  getAllTimesheetRequests = async () => {
    const resp = await httpClient<BaseResponse<TimesheetTracking[]>>(
      apiRegister.TIMESHEET_TRACKING.paths["get-all-timesheet-requests"],
    );
    if (resp?.data?.errors?.length > 0) {
      throw new Error("Error fetching timesheet requests");
    }
    return resp.data.response;
  };

  getAllTimesheetApprovals = async () => {
    const resp = await httpClient<BaseResponse<TimesheetTracking[]>>(
      apiRegister.TIMESHEET_TRACKING.paths["get-timesheet-approval"],
    );
    if (resp?.data?.errors?.length > 0) {
      throw new Error("Error fetching timesheet approvals");
    }
    return resp.data.response;
  };

  getAllProjects = async (type: "employee" | "manager") => {
    const resp = await httpClient<BaseResponse<Project[]>>(
      apiRegister.TIMESHEET_TRACKING.paths["get-all-projects"].replace(":type", type),
    );
    if (resp?.data?.errors?.length > 0) {
      throw new Error("Error fetching projects");
    }
    return resp.data.response;
  };

  updateTimesheet = async (projectDetails: any[]) => {
    const resp = await httpClient<BaseResponse<string>>(apiRegister.TIMESHEET_TRACKING.paths["get-all-timesheets"], {
      method: "PUT",
      data: projectDetails,
    });
    // if (resp?.data?.errors?.length > 0) {
    //   throw new Error("Error updating timesheet");
    // }
    return resp?.data?.success;
  };

  publishTimesheet = async (projectDetails: any[]) => {
    const resp = await httpClient<BaseResponse<string>>(apiRegister.TIMESHEET_TRACKING.paths["publish-timesheet"], {
      method: "POST",
      data: projectDetails,
    });
    // if (resp?.data?.errors?.length > 0) {
    //   throw new Error("Error publishing timesheet");
    // }
    return resp?.data?.success;
  };

  approveTimesheet = async (timesheetIds: string[]) => {
    const resp = await httpClient<BaseResponse<string>>(apiRegister.TIMESHEET_TRACKING.paths["approve-timesheet"], {
      method: "PUT",
      data: {
        request_ids: timesheetIds,
      },
    });
    // if (resp?.data?.errors?.length > 0) {
    //   throw new Error("Error approving timesheet");
    // }
    return resp?.data?.response;
  };

  rejectTimesheet = async (timesheetIds: string[]) => {
    const resp = await httpClient<BaseResponse<string>>(apiRegister.TIMESHEET_TRACKING.paths["reject-timesheet"], {
      method: "PUT",
      data: { request_ids: timesheetIds },
    });
    // if (resp?.data?.errors?.length > 0) {
    //   throw new Error("Error rejecting timesheet");
    // }
    return resp?.data?.response;
  };

  deleteTimesheetRequest = async (timesheetRequestID: string) => {
    const resp = await httpClient<BaseResponse<string>>(
      apiRegister.TIMESHEET_TRACKING.paths["delete-timesheet-request"],
      {
        method: "DELETE",
        data: { request_id: timesheetRequestID },
      },
    );
    return resp?.data?.success;
  };
}

export default new TimesheetTrackingService();
