import { httpClient } from "src/utils/httpClient";
import { apiRegister } from ".";
import { BaseResponse } from "./api_definitions/default.service";
import { ActivityLogDetails, RegularisationRequest } from "./api_definitions/employeeAttendance.service";
import { transform } from "./data_transformers/employeeAttendance.transform";

class EmployeeAttendance {
  getAttendanceActivityLog = async (monthYear: string) => {
    const { data } = await httpClient<BaseResponse<ActivityLogDetails[]>>(
      apiRegister.EMPLOYEE_ATTENDANCE.paths["get-attendance-activity-logs"],
      {
        params: {
          yearMonth: monthYear,
        },
      },
    );

    if (data?.errors?.length > 0) {
      return [];
    }
    return transform(data?.response, "activity_log") as ActivityLogDetails[];
  };

  getAttendanceRegularisationRequests = async () => {
    const { data } = await httpClient<BaseResponse<RegularisationRequest[]>>(
      apiRegister.EMPLOYEE_ATTENDANCE.paths["get-regularization-requests"],
    );

    if (data?.errors?.length > 0) {
      return [];
    }
    return transform(data?.response, "regularisation_request") as RegularisationRequest[];
  };

  getAttendanceRegularisationApprovals = async () => {
    const { data } = await httpClient<BaseResponse<RegularisationRequest[]>>(
      apiRegister.EMPLOYEE_ATTENDANCE.paths["get-regularization-approvals"],
    );

    if (data?.errors?.length > 0) {
      return [];
    }
    return transform(data?.response, "regularisation_request") as RegularisationRequest[];
  };

  applyForRegularisation = async (appliedDates: string[], reason: string) => {
    try {
      const resp = await httpClient<string>(apiRegister.EMPLOYEE_ATTENDANCE.paths["apply-regularisation"], {
        method: "POST",
        data: {
          applied_dates: appliedDates,
          reason,
        },
      });
      return resp;
    } catch (_err) {
      return null;
    }
  };

  approveRegularisationRequest = async (requestId: string, appliedDate: string) => {
    try {
      const resp = await httpClient<string>(apiRegister.EMPLOYEE_ATTENDANCE.paths["approve-regularisation"], {
        method: "PUT",
        data: {
          request_id: requestId,
          applied_date: appliedDate,
        },
      });
      return resp;
    } catch (_err) {
      return null;
    }
  };

  rejectRegularisationRequest = async (requestId: string, appliedDate: string, comment: string) => {
    try {
      const resp = await httpClient<string>(apiRegister.EMPLOYEE_ATTENDANCE.paths["reject-regularisation"], {
        method: "PUT",
        data: {
          request_id: requestId,
          applied_date: appliedDate,
          comment,
        },
      });
      return resp;
    } catch (_err) {
      return null;
    }
  };

  bulkApproveRegularisationRequest = async (requests: { request_id: string; applied_date: string }[]) => {
    try {
      const resp = await httpClient<string>(apiRegister.EMPLOYEE_ATTENDANCE.paths["bulk-approve-regularisation"], {
        method: "PUT",
        data: { requests },
      });
      return resp;
    } catch (_err) {
      return null;
    }
  };

  bulkRejectRegularisationRequest = async (
    requests: { request_id: string; applied_date: string }[],
    comment: string,
  ) => {
    try {
      const resp = await httpClient<string>(apiRegister.EMPLOYEE_ATTENDANCE.paths["bulk-reject-regularisation"], {
        method: "PUT",
        data: {
          requests,
          comment,
        },
      });
      return resp;
    } catch (_err) {
      return null;
    }
  };
}

export default new EmployeeAttendance();
