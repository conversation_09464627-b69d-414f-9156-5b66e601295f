import { apiRegister } from "src/services";
import { httpClient } from "src/utils/httpClient";
import { BaseResponse } from "./api_definitions/default.service";
import { EmployeeDetails } from "./api_definitions/employees";
import employeesTransform from "./data_transformers/employees.transform";

class CandidatesServiceAPI {
  getAllCandidateDetailsAPI = async () => {
    try {
      const resp = await httpClient<BaseResponse<EmployeeDetails[]>>(apiRegister.NEWHIRES.paths["get-all-candidates"]);
      if (resp.data.success === false) {
        return null;
      }
      return employeesTransform.transform(resp.data.response);
    } catch (_error) {
      return null;
    }
  };

  getCandidateDetailsAPI = async () => {
    try {
      const resp = await httpClient<BaseResponse<EmployeeDetails[]>>(
        apiRegister.NEWHIRES.paths["get-candidates-details"],
      );
      if (resp.data.success === false) {
        return null;
      }
      return employeesTransform.transform(resp.data.response);
    } catch (_error) {
      return null;
    }
  };

  createCandidateAPI = async (payload: {
    firstName: string;
    lastName: string;
    personalEmail: string;
    joiningDate: string;
  }) => {
    try {
      const resp = await httpClient<string>(apiRegister.NEWHIRES.paths["create-new-candidate"], {
        method: "post",
        data: {
          first_name: payload.firstName,
          last_name: payload.lastName,
          personal_email: payload.personalEmail,
          date_of_joining: payload.joiningDate,
        },
      });
      return resp.data;
    } catch (_error) {
      return null;
    }
  };

  updateCandidateDetailsAPI = async (payload: { [key: string]: unknown }) => {
    try {
      const resp = await httpClient<string>(apiRegister.NEWHIRES.paths["update-candidate-details"], {
        method: "patch",
        params: {
          email: payload.personalEmail,
        },
        data: {
          first_name: payload.firstName,
          last_name: payload.lastName,
          personal_email: payload.personalEmail,
          date_of_joining: payload.joiningDate,
        },
      });
      return resp.data;
    } catch (_error) {
      return null;
    }
  };

  rejectCandidateAPI = async (personal_email: string) => {
    try {
      const resp = await httpClient<string>(apiRegister.NEWHIRES.paths["post-reject-candidate"], {
        method: "POST",
        params: {
          email: personal_email,
        },
      });
      return resp.data;
    } catch (_error) {
      return null;
    }
  };

  resendLinktoCandidateAPI = async (personal_email: string) => {
    try {
      const resp = await httpClient<string>(apiRegister.NEWHIRES.paths["post-resend-link"], {
        method: "POST",
        params: {
          email: personal_email,
        },
      });
      return resp.data;
    } catch (_error) {
      return null;
    }
  };
}

export default new CandidatesServiceAPI();
