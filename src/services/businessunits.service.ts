import { BaseObject } from "src/app/global";
import { BusinessDepartmentList } from "src/modules/Employees/types/employeeTypes";
import { httpClient } from "src/utils/httpClient";
import { apiRegister } from ".";
import { BaseResponse } from "./api_definitions/default.service";

type BusinessUnitRequest = {
  name: unknown;
  cost_center: unknown;
  description: unknown;
}[];

class BusinessUnitsService {
  setBusinessUnitDetails = async (payload: BusinessUnitRequest) => {
    const endpoint = apiRegister.TENANTS.paths["set-business-unit-details"];
    try {
      const response = await httpClient<string>(endpoint, { method: "POST", data: payload });
      return response?.data;
    } catch (_error) {
      return null;
    }
  };
  updateBusinessUnitDetails = async (payload: BaseObject) => {
    const endpoint = apiRegister.TENANTS.paths["update-business-unit-details"];
    try {
      const response = await httpClient<string>(endpoint, { method: "PATCH", data: payload });
      return response?.data;
    } catch (_error) {
      return null;
    }
  };

  deleteBusinessUnitDetails = async (payload: BaseObject) => {
    const endpoint = apiRegister.TENANTS.paths["delete-business-unit-details"];
    try {
      const response = await httpClient<string>(endpoint, { method: "DELETE", data: payload });
      return response?.data;
    } catch (_error) {
      return null;
    }
  };

  getBusinessUnitDetails = async () => {
    const endpoint = apiRegister.TENANTS.paths["get-all-business-units"];
    try {
      const resp = await httpClient<BaseResponse<BusinessDepartmentList>>(endpoint, { method: "GET" });
      return resp.data.response;
    } catch (_error) {
      return null;
    }
  };
}

export default new BusinessUnitsService();
