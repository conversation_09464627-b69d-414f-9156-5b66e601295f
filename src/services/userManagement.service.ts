import { AxiosError } from "axios";
import { apiRegister } from "src/services";
import { httpClient } from "src/utils/httpClient";
import { BaseResponse } from "./api_definitions/default.service";
import { UserDetails, UserSettings, UserSettingsResponse } from "./api_definitions/userManagement";

class UserManagementService {
  insertUserSettings = async (userSettings: UserSettings = {}) => {
    try {
      await httpClient<string>(apiRegister.USER_MANAGEMENT.paths["insert-user-settings"], {
        method: "POST",
        data: {
          ...userSettings,
        },
      });
    } catch (err) {
      console.error({ insertUserSettings: err });
    }
  };

  getUserSettings = async () => {
    try {
      const resp = await httpClient<BaseResponse<UserSettingsResponse>>(
        apiRegister.USER_MANAGEMENT.paths["get-user-saved-settings"],
      );
      if (resp?.data?.errors?.length > 0) {
        this.insertUserSettings({});
        return {};
      }
      return resp.data.response.settings;
    } catch (err) {
      const errorResponse = err as AxiosError<never>;
      if (errorResponse?.response?.status === 404) {
        this.insertUserSettings({});
      }
      return {};
    }
  };

  getUserDetails = async () => {
    try {
      const resp = await httpClient<BaseResponse<UserDetails>>(apiRegister.USER_MANAGEMENT.paths["user-basic-details"]);
      return resp.data.response;
    } catch (_err) {
      return null;
    }
  };
}

export default new UserManagementService();
