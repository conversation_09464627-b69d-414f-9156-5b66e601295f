import { apiRegister } from "src/services";
import { formatDateTime } from "src/utils/dateUtils";
import { httpClient } from "src/utils/httpClient";
import { BaseResponse } from "./api_definitions/default.service";
import { LeadDetails } from "./api_definitions/leads";

class LeadsServiceAPI {
  sendLinkAPI = async (tenantName: string, tenantSpocEmail: string) => {
    try {
      const resp = await httpClient<string>(apiRegister.LEADS.paths["post-generate-magiclink"], {
        method: "POST",
        data: {
          tenant_name: tenantName,
          tenant_spoc_email: tenantSpocEmail,
        },
      });
      return resp;
    } catch (_error) {
      return null;
    }
  };

  getLeadsAPI = async () => {
    try {
      const resp = await httpClient<BaseResponse<LeadDetails[]>>(apiRegister.LEADS.paths["get-tenant-leads"]);
      if (!resp.data.success) {
        return;
      }
      const data = resp.data.response;
      return data
        .sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())
        .map((item) => {
          return {
            organisation_name: item.tenant_name ? item.tenant_name : "N/A",
            email_id: item.tenant_spoc_email ? item.tenant_spoc_email : "N/A",
            // TODO: Use library for formatting date-time
            created_by: item.created_by ? item.created_by : "N/A",
            created_on: item.created_at ? formatDateTime(item.created_at) : "",
          };
        });
    } catch (_error) {
      return null;
    }
  };
}

export default new LeadsServiceAPI();
