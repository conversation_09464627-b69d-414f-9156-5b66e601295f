import { apiRegister } from "src/services";
import { httpClient } from "src/utils/httpClient";
import { BaseResponse } from "./api_definitions/default.service";
import { EmployeeDetails, Employee<PERSON>ourney } from "./api_definitions/employees";
import {
  BankInformationPayload,
  EducationDetailsPayload,
  EmergencyContactsPayload,
  FamilyDetailsPayload,
  PersonalInformationPayload,
  WorkExperiencePayload,
} from "./api_definitions/profile.service";

class ProfileAPIService {
  fetchProfileData = async (employeeCode?: string) => {
    try {
      const endpoint = apiRegister.PROFILE.paths["get-employee-details"];
      const finalEndpoint = employeeCode ? `${endpoint}?code=${employeeCode}` : endpoint;
      const response = await httpClient<BaseResponse<EmployeeDetails>>(finalEndpoint);
      return response.data.response;
    } catch (_err) {
      return null;
    }
  };
  updatePersonalInformation = async (payload: PersonalInformationPayload) => {
    try {
      const response = await httpClient<string>(apiRegister.PROFILE.paths["update-employee-personal-details"], {
        method: "PATCH",
        data: payload,
      });
      return response.data;
    } catch (_err) {
      return null;
    }
  };
  updateBankInformation = async (payload: BankInformationPayload) => {
    try {
      const response = await httpClient<string>(apiRegister.PROFILE.paths["update-employee-bank-account"], {
        method: "PATCH",
        data: payload,
      });
      return response.data;
    } catch (_err) {
      return null;
    }
  };
  updateEducationDetails = async (payload: EducationDetailsPayload) => {
    try {
      const response = await httpClient<string>(apiRegister.PROFILE.paths["update-employee-education-details"], {
        method: "PATCH",
        data: payload,
      });
      return response.data;
    } catch (_err) {
      return null;
    }
  };
  updateWorkExperience = async (payload: WorkExperiencePayload) => {
    try {
      const response = await httpClient<string>(apiRegister.PROFILE.paths["update-employee-work-experience"], {
        method: "PATCH",
        data: payload,
      });
      return response.data;
    } catch (_err) {
      return null;
    }
  };
  updateEmergencyContacts = async (payload: EmergencyContactsPayload) => {
    try {
      const response = await httpClient<string>(apiRegister.PROFILE.paths["update-employee-emergency-contacts"], {
        method: "PATCH",
        data: payload,
      });
      return response.data;
    } catch (_err) {
      return null;
    }
  };
  updateFamilyDetails = async (payload: FamilyDetailsPayload) => {
    try {
      const response = await httpClient<string>(apiRegister.PROFILE.paths["update-employee-family-details"], {
        method: "PATCH",
        data: payload,
      });
      return response.data;
    } catch (_err) {
      return null;
    }
  };
  updateProfilePicture = async (s3Link: string) => {
    try {
      const response = await httpClient<string>(apiRegister.PROFILE.paths["upload-display-pic"], {
        method: "PATCH",
        data: {
          display_pic: s3Link,
        },
      });
      return response.data;
    } catch (_err) {
      return null;
    }
  };

  fetchEmployeeJourney = async (employeeCode?: string) => {
    try {
      const endpoint = apiRegister.PROFILE.paths["get-employee-journey"];
      const finalEndpoint = employeeCode ? `${endpoint}?code=${employeeCode}` : endpoint;
      const response = await httpClient<BaseResponse<EmployeeJourney[]>>(finalEndpoint);
      return response.data.response;
    } catch (_err) {
      return null;
    }
  };
  getEmployeeJourneyAdmin = async (employeeCode: string) => {
    try {
      const endpoint = apiRegister.PROFILE.paths["get-employee-journey-admin"];
      const finalEndpoint = `${endpoint}?code=${employeeCode}`;

      const response = await httpClient<BaseResponse<EmployeeJourney[]>>(finalEndpoint);
      return response.data.response;
    } catch (_err) {
      return null;
    }
  };
}

export default new ProfileAPIService();
