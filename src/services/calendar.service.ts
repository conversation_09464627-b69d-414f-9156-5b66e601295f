import { apiRegister } from "src/services";
import {
  BaseApiResponse,
  CalendarEventPayloadProps,
  TeamCalendarReponseProps,
} from "src/services/api_definitions/calendar";
import { httpClient } from "src/utils/httpClient";
import { BaseResponse } from "./api_definitions/default.service";
import calendarTransform from "./data_transformers/calendar.transform";

class CalendarServiceAPI {
  getCalendarEvents = async (payload: CalendarEventPayloadProps) => {
    try {
      const { data } = await httpClient<BaseResponse<BaseApiResponse>>(
        `${apiRegister.CALENDAR.paths["get-my-calendar"]}?start_date=${payload.start_date}&end_date=${payload.end_date}`,
      );
      return {
        events: calendarTransform.transform(data?.response.events),
        week_offs: data?.response?.week_offs,
      };
    } catch (_error) {
      return null;
    }
  };

  getTeamCalendarEvents = async (payload: CalendarEventPayloadProps) => {
    try {
      const { data } = await httpClient<BaseResponse<TeamCalendarReponseProps[]>>(
        `${apiRegister.CALENDAR.paths["get-team-calendar"]}?start_date=${payload.start_date}&end_date=${payload.end_date}`,
      );
      return calendarTransform.teamCalendar(data?.response);
    } catch (_error) {
      return null;
    }
  };
}

export default new CalendarServiceAPI();
