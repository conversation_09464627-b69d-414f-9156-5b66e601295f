import { AxiosError, AxiosRequestConfig } from "axios";
import { httpClient } from "src/utils/httpClient";
import { apiRegister } from ".";
import { BaseResponse, FileUploadResponse } from "./api_definitions/default.service";

class FileUploader {
  uploadFile = async (
    url: string,
    formData: FormData,
    params?: Record<string, string> | undefined,
    restOptions: AxiosRequestConfig<FormData> = {},
    hideErrorToasts?: boolean,
  ) => {
    try {
      const resp = await httpClient<BaseResponse<string[]>>(url, {
        params,
        data: formData,
        headers: {
          "Content-Type": "multipart/form-data",
        },
        method: "POST",
        hideErrorToasts,
        ...restOptions,
      });
      if (resp instanceof AxiosError) {
        return { type: "error", message: resp.response?.data.errors.join(", ") } as FileUploadResponse<string>;
      }
      if (resp.data.errors && resp.data.errors.length > 0) {
        //TODO: Need to check if this block is executed at all or not
        return { type: "error", message: resp.data.errors.join("<br/>") } as FileUploadResponse<string>;
      }
      return { type: "success", message: resp.data.response } as FileUploadResponse<string[]>;
    } catch (error) {
      const err = error as AxiosError<BaseResponse<string> | string>;
      return {
        type: "error",
        message: (err.response?.data as BaseResponse<string>)?.errors || (err?.response?.data as string),
      } as FileUploadResponse<string | string[]>;
    }
  };

  uploadFileToS3 = async (url: string, file: File) => {
    try {
      const response = await fetch(url, {
        method: "PUT",
        body: file,
        headers: {
          "Content-Type": file.type,
        },
      });

      if (response.ok) {
        console.log("File uploaded successfully");
      } else {
        console.error("File upload failed");
      }
    } catch (error) {
      console.error("Error uploading file:", error);
    }
  };

  downloadDocumentS3 = async (s3Url: string) => {
    try {
      const resp = await httpClient<BaseResponse<string>>(apiRegister.AWS_S3.paths["download-presigned-url"], {
        params: {
          s3_link: s3Url,
        },
      });
      const url = resp.data.response;
      window.open(url, "_blank");
    } catch (_err) {
      return null;
    }
  };

  getDocumentPreviewUrl = async (s3Url: string) => {
    try {
      const resp = await httpClient<BaseResponse<string>>(apiRegister.AWS_S3.paths["get-document-preview-url"], {
        params: {
          s3_link: s3Url,
        },
      });
      return resp.data.response;
    } catch (_err) {
      return null;
    }
  };

  saveDocument = async ({ formData }: { formData: any[] }) => {
    const resp = await httpClient<BaseResponse<string>>(apiRegister.EMPLOYEES.paths["save-document"], {
      method: "PATCH",
      data: formData,
    });
    return resp;
  };
}

export default new FileUploader();
