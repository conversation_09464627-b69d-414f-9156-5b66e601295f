import { formatDateNormal } from "src/utils/dateUtils";
import { LeaveApprovalResponse, LeaveRequestResponse, LeaveSummaryResponse } from "../api_definitions/leave.service";

export type LeaveRequestData = {
  requestId: string;
  reason: string;
  comment: string | null;
  status: string;
  raisedOn: string;
  approver: string;
  requestType: string | null;
  startDate: string;
  endDate: string;
  duration: string;
  durationType: string;
  raisedBy?: {
    displayName: string;
    designation: string;
    display_pic: string;
  };
  actionedOn?: string;
};

export type LeaveSummaryData = {
  leaveType: string;
  noOfLeaves: number;
  totalLeaves: number;
  info: string[];
  paid: boolean;
};

export type LeaveApprovalData = {
  requestId: string;
  reason: string;
  comment: string | null;
  status: string;
  raisedOn: string;
  raisedBy: {
    displayName: string;
    designation: string;
    display_pic: string;
  };
  approver: string;
  requestType: string | null;
  startDate: string;
  endDate: string;
  duration: string;
  durationType: string;
  actionedOn?: string;
};

class LeaveTransformer {
  transformLeaveRequests = (data: LeaveRequestResponse[]): LeaveRequestData[] => {
    return data.map((leaveRequest) => ({
      requestId: leaveRequest.request_id,
      reason: leaveRequest.reason,
      comment: leaveRequest.comment,
      status: leaveRequest.status,
      raisedOn: formatDateNormal(leaveRequest.raised_on),
      approver: leaveRequest.approver,
      requestType: leaveRequest.leave_type,
      startDate: leaveRequest.start_date,
      endDate: leaveRequest.end_date,
      duration: `${leaveRequest.duration} ${leaveRequest.duration > 1 ? "days" : "day"}`,
      durationType: leaveRequest.duration_type,
      actionedOn: leaveRequest.actioned_on ? formatDateNormal(leaveRequest.actioned_on) : "",
    }));
  };
  transformLeaveSummary = (data: LeaveSummaryResponse[]): LeaveSummaryData[] => {
    return data.map((leaveSummary) => ({
      leaveType: leaveSummary.leave_type,
      noOfLeaves: leaveSummary.num_of_leaves,
      totalLeaves: leaveSummary.total_leaves,
      info: leaveSummary.info,
      paid: leaveSummary.paid,
    }));
  };
  transformLeaveApprovals = (data: LeaveApprovalResponse[]): LeaveApprovalData[] => {
    return data.map((leaveRequest) => ({
      requestId: leaveRequest.request_id,
      reason: leaveRequest.reason,
      comment: leaveRequest.comment,
      status: leaveRequest.status,
      raisedOn: leaveRequest.raised_on,
      raisedBy: {
        displayName: leaveRequest.raised_by.display_name,
        designation: leaveRequest.raised_by.job_title,
        display_pic: leaveRequest.raised_by.display_pic,
      },
      approver: leaveRequest.approver,
      requestType: leaveRequest.leave_type,
      startDate: leaveRequest.start_date,
      endDate: leaveRequest.end_date,
      duration: `${leaveRequest.duration} ${leaveRequest.duration > 1 ? "days" : "day"}`,
      durationType: leaveRequest.duration_type,
      actionedOn: leaveRequest.actioned_on ? formatDateNormal(leaveRequest.actioned_on) : "",
    }));
  };
}

export default new LeaveTransformer();
