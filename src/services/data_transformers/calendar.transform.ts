import { BaseCalendarEventResponse, TeamCalendarReponseProps } from "src/services/api_definitions/calendar";

class CalendarTransformer {
  getDate = (date: Date) => (date ? new Date(date) : new Date());

  transformCalendar = (calendarEvent: BaseCalendarEventResponse) => {
    return {
      title: calendarEvent?.title,
      subTitle: calendarEvent?.sub_title,
      type: calendarEvent?.event_type,
      reason: calendarEvent?.reason || "",
      comment: calendarEvent?.comment || "",
      start: this.getDate(calendarEvent?.event_date),
      end: this.getDate(calendarEvent?.event_date),
      durationType: calendarEvent?.duration_type || "",
      checkinTime: calendarEvent?.check_in_time,
      checkoutTime: calendarEvent?.check_out_time,
      duration: calendarEvent.duration,
      allDay: true,
      isPaid: calendarEvent?.is_paid,
    };
  };

  transformAttendance = (attendance: any) => {
    return {
      login_date: attendance.login_date,
      status: attendance.status,
      details: attendance.details,
      check_in_time: attendance.check_in_time,
      check_out_time: attendance.check_out_time,
      location: attendance.location,
      duration: attendance.duration,
      last_check_in_time: attendance.last_check_in_time,
    };
  };

  transform = (calendarEvents: BaseCalendarEventResponse[]) => {
    return calendarEvents?.map(this.transformCalendar) ?? [];
  };

  transformTeamCalendar = (calendarDetails: TeamCalendarReponseProps) => {
    return {
      reportee: calendarDetails.reportee,
      calendar: {
        weekOff: calendarDetails.calendar.week_offs,
        events: this.transform(calendarDetails.calendar.events),
      },
      attendance: calendarDetails.attendances?.map(this.transformAttendance) ?? [],
    };
  };

  teamCalendar = (calendarData: TeamCalendarReponseProps[]) => {
    return calendarData?.map(this.transformTeamCalendar) ?? [];
  };
}

export default new CalendarTransformer();
