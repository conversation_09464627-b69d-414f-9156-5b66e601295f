import { compareAsc, parse } from "date-fns";
import { convertTimeToAMPMWithZonedTime, formatDateNormal } from "src/utils/dateUtils";
import { ActivityLogDetails, RegularisationRequest } from "../api_definitions/employeeAttendance.service";

const NOT_AVAILABLE = "--";

export const transform = (
  data: ActivityLogDetails[] | RegularisationRequest[],
  type: "regularisation_request" | "activity_log",
): ActivityLogDetails[] | RegularisationRequest[] => {
  switch (type) {
    case "regularisation_request": {
      return (data as RegularisationRequest[])?.map((regularisationRequest: RegularisationRequest) => ({
        request_id: regularisationRequest?.request_id,
        reason: regularisationRequest?.reason || NOT_AVAILABLE,
        comment: regularisationRequest?.comment,
        status: regularisationRequest?.status ? regularisationRequest?.status : null,
        raised_on: regularisationRequest?.raised_on || NOT_AVAILABLE,
        raised_by: regularisationRequest?.raised_by || NOT_AVAILABLE,
        approver: regularisationRequest?.approver || NOT_AVAILABLE,
        check_in_time: regularisationRequest?.check_in_time
          ? convertTimeToAMPMWithZonedTime(regularisationRequest?.check_in_time)
          : NOT_AVAILABLE,
        check_out_time: regularisationRequest?.check_out_time
          ? convertTimeToAMPMWithZonedTime(regularisationRequest?.check_out_time)
          : NOT_AVAILABLE,
        applied_date: regularisationRequest?.applied_date ? regularisationRequest?.applied_date : NOT_AVAILABLE,
        duration: `${regularisationRequest?.duration} Hrs`,
        details: regularisationRequest?.details || [],
        leave_type: regularisationRequest?.leave_type || NOT_AVAILABLE,
        actioned_on: regularisationRequest?.actioned_on ? formatDateNormal(regularisationRequest?.actioned_on) : "",
      })) as RegularisationRequest[];
    }
    default:
      return (data as ActivityLogDetails[])
        ?.map((activityLog) => ({
          check_in_time: activityLog?.check_in_time
            ? convertTimeToAMPMWithZonedTime(activityLog?.check_in_time)
            : NOT_AVAILABLE,
          check_out_time: activityLog?.check_out_time
            ? convertTimeToAMPMWithZonedTime(activityLog?.check_out_time)
            : NOT_AVAILABLE,
          status: activityLog?.status ? activityLog?.status : null,
          login_date: activityLog?.login_date ? activityLog.login_date : NOT_AVAILABLE,
          location: activityLog?.location ? activityLog.location : NOT_AVAILABLE,
          duration: `${activityLog?.duration} Hrs`,
          details: activityLog?.details || [],
        }))
        ?.sort((a, b) => {
          const dateA = parse(a.login_date as string, "dd/MM/yyyy", new Date());
          const dateB = parse(b.login_date as string, "dd/MM/yyyy", new Date());
          return compareAsc(dateA, dateB);
        }) as ActivityLogDetails[];
  }
};
