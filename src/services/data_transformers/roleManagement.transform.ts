import { BaseObject } from "src/app/global";
import { AllRolesResponse, RoleScreensResponse } from "../api_definitions/roleManagement";

type TransformedData = {
  name: string;
  isDefaultRole: boolean;
};

class RoleManagementTransformer {
  transformFetchedRoles = (data: AllRolesResponse): TransformedData[] => {
    const transformedDefaultRoles = data?.default_roles
      ?.map((role) => ({
        name: role,
        isDefaultRole: true,
      }))
      ?.sort((a, b) => a.name.localeCompare(b.name));

    const transformedCustomRoles = data?.custom_roles
      ?.map((role) => ({
        name: role,
        isDefaultRole: false,
      }))
      ?.sort((a, b) => a.name.localeCompare(b.name));

    return [...transformedDefaultRoles, ...transformedCustomRoles];
  };

  getRolesScreens = (data: RoleScreensResponse) => {
    const screens: Record<string, BaseObject> = {};
    const allScreens = [...data.default_role_screens, ...data.custom_role_screens];
    const allRoles = [...new Set(allScreens.map((val) => val.role))];

    // Process defaultRoleScreens
    data.default_role_screens.forEach((screen) => {
      if (!screens[screen.screen_description]) {
        screens[screen.screen_description] = {};
      }
      screens[screen?.screen_description][screen.role] = {
        acl: screen.acl,
        isDefault: true,
        screen: screen.screen,
      };
    });

    // Process customRoleScreens
    data.custom_role_screens.forEach((screen) => {
      if (!screens[screen.screen_description]) {
        screens[screen.screen_description] = {};
      }
      if (!screens[screen.screen_description][screen.role]) {
        screens[screen.screen_description][screen.role] = {
          acl: "None",
          isDefault: false,
          screen: screen.screen,
        };
      }
      screens[screen.screen_description][screen.role] = {
        acl: screen.acl,
        isDefault: false,
        screen: screen.screen,
      };
    });

    // Add missing roles with ACL None
    Object.values(screens).forEach((screen) => {
      allRoles.forEach((role) => {
        if (!Object.hasOwn(screen, role)) {
          screen[role] = {
            acl: "None",
            isDefault: false,
          };
        }
      });
    });

    // Convert screens object to array of objects
    const result = Object.keys(screens).map((screenDescription) => {
      const screenObj: Record<string, unknown> = { screenDescription };
      const roles = screens[screenDescription];
      Object.keys(roles).forEach((role) => {
        screenObj[role] = roles[role];
      });
      return screenObj;
    });

    return result.map((screen) => ({
      ...screen,
      screenName: allScreens.find((dataScreen) => screen.screenDescription === dataScreen.screen_description)?.screen,
    }));
  };
}

export default new RoleManagementTransformer();
