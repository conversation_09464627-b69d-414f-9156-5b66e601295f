import {
  Emergency<PERSON>ontact,
  EmployeeDeta<PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Reportee,
  TransformedEmployee,
} from "../api_definitions/employees";

class EmployeesTransformer {
  extractAddress = (address: EntAddress | undefined): string | null => {
    if (!address) return null;
    return address.display_address || "N/A";
  };

  formatEmergencyContacts = (contacts: EmergencyContact[]): string | null => {
    if (contacts && contacts.length === 0) return null;
    return contacts
      ?.map((contact) => {
        const phoneNumber =
          (typeof contact?.phone === "string"
            ? contact?.phone
            : contact?.phone?.country_code + contact?.phone?.number) || "-";
        return `${contact.name} (Ph no.: ${phoneNumber})`;
      })
      .join(", ");
  };

  formatReportees = (reportees: Reportee[]): string | null => {
    if (reportees && reportees.length === 0) return null;

    return reportees?.map((reportee) => `${reportee.display_name} (${reportee.employee_code})`).join(", ");
  };

  transformEmployee = (employee: EmployeeDetails) => ({
    employee_name: employee.display_name || "N/A",
    employee_code: employee.employee_code || "N/A",
    cost_center: employee.cost_center || "N/A",
    employee_type: employee.employee_type || "N/A",
    employment_status: employee.employment_status || "N/A",
    aadhar: employee.aadhaar || "N/A",
    bank_account: employee.bank_account?.account_number || "N/A",
    account_holder_name: employee.bank_account?.account_holder_name || "N/A",
    blood_group: employee.blood_group || "N/A",
    current_address: this.extractAddress(employee.current_address) || "N/A",
    date_of_birth: employee.date_of_birth || "N/A",
    date_of_joining: employee.date_of_joining || "N/A",
    emergency_contacts: this.formatEmergencyContacts(employee.emergency_contacts) || "N/A",
    email: employee.email || "N/A",
    gender: employee.gender || "N/A",
    job_title: employee.job_title || "N/A",
    department: employee.department || "N/A",
    business_unit: employee.business_unit || "N/A",
    reporting_manager: employee.manager?.display_name || "N/A",
    location: employee.location || "N/A",
    marital_status: employee.marital_status || "N/A",
    nationality: employee.nationality || "N/A",
    organisation: employee.organisation || "N/A",
    pan: employee.pan || "N/A",
    passport: employee.passport || "N/A",
    permanent_address: this.extractAddress(employee.permanent_address) || "N/A",
    personal_email: employee.personal_email || "N/A",
    office_address: employee?.office_address || {},
    phone:
      (typeof employee?.phone === "string"
        ? employee?.phone
        : employee?.phone?.country_code + employee?.phone?.number) || "N/A",
    reportees: this.formatReportees(employee.reportees) || "N/A",
    uan: employee.uan || "N/A",
    first_name: employee.first_name || "N/A",
    last_name: employee.last_name || "N/A",
    originalData: employee,
    hrbpEmail: employee.hrbp || "N/A",
    date_of_confirmation: employee.date_of_confirmation || "N/A",
    compensations: employee?.compensations || [],
    current_compensation: employee?.current_compensation || {},
    form_status: employee.form_status || "N/A",
    actionMenu: employee?.action_menu || [],
    leave_balance: employee?.leave_balance || {},
  });

  transform = (data: EmployeeDetails[]): TransformedEmployee[] => {
    return data.map(this.transformEmployee) as unknown as TransformedEmployee[];
  };
}

export default new EmployeesTransformer();
