import { httpClient } from "src/utils/httpClient";
import { apiRegister } from ".";
import { BaseResponse } from "./api_definitions/default.service";
import {
  EmployeeGoalSettingDetail,
  Goal,
  LinkedGoalObjective,
  Objective,
  PeerNominationRequest,
  PeerNominations,
  PerformanceReview,
  PerformanceReviewRequest,
  RatingsConfiguration,
  Requests,
  ReviewCycle,
  ReviewerTypes,
  UpdateReviewCycle,
} from "./api_definitions/performanceManagement.service";

class PerformanceManagementService {
  getReviewCyclesConfigDetails = async () => {
    const resp = await httpClient<BaseResponse<ReviewCycle[]>>(
      apiRegister.PERFORMANCE_MANAGEMENT.paths["get-review-cycle-config-details"],
    );

    if (resp?.data?.errors?.length > 0) {
      return [];
    }
    return resp?.data?.response;
  };

  createReviewCycle = async (requestObject: ReviewCycle) => {
    const resp = await httpClient<BaseResponse<ReviewCycle[]>>(
      apiRegister.PERFORMANCE_MANAGEMENT.paths["create-review-cycle-config-details"],
      {
        method: "POST",
        data: requestObject,
      },
    );
    if (resp?.data?.errors?.length > 0) {
      throw new Error("Some error while creating review cycle config");
    }
    return resp?.data?.response;
  };

  updateReviewCycle = async (requestObject: UpdateReviewCycle) => {
    const resp = await httpClient<BaseResponse<ReviewCycle[]>>(
      apiRegister.PERFORMANCE_MANAGEMENT.paths["update-review-cycle-config-details"],
      {
        method: "PATCH",
        data: requestObject,
      },
    );
    if (resp?.data?.errors?.length > 0) {
      throw new Error("Some error while updating review cycle config");
    }
    return resp?.data?.response;
  };

  getRatingsConfiguration = async () => {
    const resp = await httpClient<BaseResponse<RatingsConfiguration[]>>(
      apiRegister.PERFORMANCE_MANAGEMENT.paths["get-ratings-configuration"],
    );
    if (resp?.data?.errors?.length > 0) {
      return [];
    }
    return resp?.data?.response;
  };

  createRatingsConfiguration = async (requestObject: RatingsConfiguration[]) => {
    const resp = await httpClient<BaseResponse<ReviewCycle[]>>(
      apiRegister.PERFORMANCE_MANAGEMENT.paths["create-ratings-configuration"],
      {
        method: "POST",
        data: requestObject,
      },
    );
    if (resp?.data?.errors?.length > 0) {
      throw new Error("Some error while creating ratings config");
    }
    return resp?.data?.response;
  };

  updateRatingsConfiguration = async (requestObject: RatingsConfiguration[]) => {
    const resp = await httpClient<BaseResponse<ReviewCycle[]>>(
      apiRegister.PERFORMANCE_MANAGEMENT.paths["update-ratings-configuration"],
      {
        method: "PUT",
        data: requestObject,
      },
    );
    if (resp?.data?.errors?.length > 0) {
      throw new Error("Some error while updating ratings config");
    }
    return resp?.data?.response;
  };

  getGoals = async () => {
    const resp = await httpClient<BaseResponse<Goal[]>>(apiRegister.PERFORMANCE_MANAGEMENT.paths["get-goals"]);
    if (resp?.data?.errors?.length > 0) {
      return [];
    }
    return resp?.data?.response;
  };

  updateGoal = async (requestObject: Goal) => {
    const resp = await httpClient<BaseResponse<Goal[]>>(apiRegister.PERFORMANCE_MANAGEMENT.paths["update-goal"], {
      method: "PATCH",
      data: requestObject,
    });
    if (resp?.data?.errors?.length > 0) {
      throw new Error("Some error while updating goal");
    }
    return resp?.data?.response;
  };

  getGoalRequests = async () => {
    const resp = await httpClient<BaseResponse<Requests[]>>(
      apiRegister.PERFORMANCE_MANAGEMENT.paths["get-goal-requests"],
    );
    if (resp?.data?.errors?.length > 0) {
      return [];
    }
    return resp?.data?.response;
  };

  approveGoalRequest = async (goalId: string, objectives: Objective[]) => {
    const resp = await httpClient<BaseResponse<Requests[]>>(
      apiRegister.PERFORMANCE_MANAGEMENT.paths["approve-goal-request"],
      {
        method: "PUT",
        data: {
          goal_id: goalId,
          objectives,
        },
      },
    );
    if (resp?.data?.errors?.length > 0) {
      throw new Error("Some error while approving goal request");
    }
    return resp?.data?.response;
  };

  sendBackGoalRequest = async (goalId: string, comment: string) => {
    const resp = await httpClient<BaseResponse<Requests[]>>(
      apiRegister.PERFORMANCE_MANAGEMENT.paths["sendback-goal-request"],
      {
        method: "PUT",
        data: {
          goal_id: goalId,
          comment,
        },
      },
    );
    if (resp?.data?.errors?.length > 0) {
      throw new Error("Some error while sending back goal request");
    }
    return resp?.data?.response;
  };

  getPerformanceReviews = async () => {
    const resp = await httpClient<BaseResponse<PerformanceReview[]>>(
      apiRegister.PERFORMANCE_MANAGEMENT.paths["get-employee-reviews"],
    );
    if (resp?.data?.errors?.length > 0) {
      return [];
    }
    return resp?.data?.response;
  };

  updatePerformanceReviewsByReviewerType = async (reviewerType: string, payload: any) => {
    const resp = await httpClient<BaseResponse<PerformanceReview[]>>(
      apiRegister.PERFORMANCE_MANAGEMENT.paths["update-performance-review"].replace(":reviewerType", reviewerType),
      {
        method: "PATCH",
        data: { ...payload }, // payload
      },
    );
    if (resp?.data?.errors?.length > 0) {
      throw new Error("Some error while updating performance reviews");
    }
    return resp?.data?.response;
  };

  getPerformanceManagementReviewRequests = async (reviewerType: ReviewerTypes) => {
    const resp = await httpClient<BaseResponse<PerformanceReviewRequest[]>>(
      apiRegister.PERFORMANCE_MANAGEMENT.paths["get-employee-review-requests"].replace(":reviewerType", reviewerType),
    );

    if (resp?.data?.errors?.length > 0) {
      return [];
    }
    return resp?.data?.response;
  };

  getPeerNominations = async () => {
    const resp = await httpClient<BaseResponse<PeerNominations[]>>(
      apiRegister.PERFORMANCE_MANAGEMENT.paths["get-peer-nominations"],
    );

    if (resp?.data?.errors?.length > 0) {
      return [];
    }
    return resp?.data?.response;
  };

  updatePeerNominations = async (goalId: string, emails: string[]) => {
    const resp = await httpClient<BaseResponse<PeerNominations[]>>(
      apiRegister.PERFORMANCE_MANAGEMENT.paths["update-peer-nominations"],
      {
        method: "PUT",
        data: {
          performance_review_id: goalId,
          nominee_emails: emails,
        },
      },
    );
    if (resp?.data?.errors?.length > 0) {
      throw new Error("Some error while updating peer nominations");
    }
    return resp?.data?.response;
  };

  getPeerNominationRequests = async () => {
    const resp = await httpClient<BaseResponse<PeerNominationRequest[]>>(
      apiRegister.PERFORMANCE_MANAGEMENT.paths["get-peer-nomination-requests"],
    );

    if (resp?.data?.errors?.length > 0) {
      return [];
    }
    return resp?.data?.response;
  };

  approvePeerNominations = async (goalId: string, emails: string[]) => {
    const resp = await httpClient<BaseResponse<PeerNominations[]>>(
      apiRegister.PERFORMANCE_MANAGEMENT.paths["approve-peer-nomination"],
      {
        method: "PUT",
        data: {
          performance_review_id: goalId,
          nominee_emails: emails,
        },
      },
    );
    if (resp?.data?.errors?.length > 0) {
      throw new Error("Some error while approving peer nominations");
    }
    return resp?.data?.response;
  };

  sendBackPerformanceReview = async (reviewerType: ReviewerTypes, reviewId: string, comment: string) => {
    const resp = await httpClient<BaseResponse<PeerNominations[]>>(
      apiRegister.PERFORMANCE_MANAGEMENT.paths["send-back-performance-review"].replace(":reviewerType", reviewerType),
      {
        method: "PUT",
        data: {
          review_id: reviewId,
          comment,
        },
      },
    );
    if (resp?.data?.errors?.length > 0) {
      throw new Error("Some error while sending back performance review");
    }
    return resp?.data?.response;
  };

  getManagerGoalObjectives = async (goalId: string) => {
    const resp = await httpClient<BaseResponse<LinkedGoalObjective[]>>(
      apiRegister.PERFORMANCE_MANAGEMENT.paths["get-manager-goal-objectives"],
      {
        method: "GET",
        params: {
          goal_id: goalId,
        },
      },
    );
    if (resp?.data?.errors?.length > 0) {
      throw new Error("Some error while fetching manager goal objectives");
    }
    return resp?.data?.response;
  };

  enableGoalSettingsForEmployee = async (employeeCode: string) => {
    const resp = await httpClient<BaseResponse<string>>(
      apiRegister.PERFORMANCE_MANAGEMENT.paths["enable-goal-setting-for-employee"],
      {
        method: "PUT",
        params: {
          code: employeeCode,
        },
      },
    );
    if (resp?.data?.errors?.length > 0) {
      throw new Error("Some error while enabling goal settings for employee");
    }
    return resp?.data?.response;
  };

  getResourceAllocation = async () => {
    const resp = await httpClient<BaseResponse<any>>(
      apiRegister.PERFORMANCE_MANAGEMENT.paths["get-resource-allocation"],
    );
    if (resp?.data?.errors?.length > 0) {
      return [];
    }
    return resp?.data?.response;
  };

  getEmployeeGoalSettingStatus = async () => {
    const resp = await httpClient<BaseResponse<EmployeeGoalSettingDetail[]>>(
      apiRegister.PERFORMANCE_MANAGEMENT.paths["get-employee-goal-setting-status"],
    );
    if (resp?.data?.errors?.length > 0) {
      throw new Error("Some error while fetching employee goal setting status");
    }
    return resp?.data?.response;
  };

  getEmployeePerformanceReviewstatus = async () => {
    const resp = await httpClient<BaseResponse<PerformanceReviewRequest[]>>(
      apiRegister.PERFORMANCE_MANAGEMENT.paths["get-employee-performance-review-status"],
    );
    if (resp?.data?.errors?.length > 0) {
      throw new Error("Some error while fetching employee performance review status");
    }
    return resp?.data?.response;
  };
}

export default new PerformanceManagementService();
