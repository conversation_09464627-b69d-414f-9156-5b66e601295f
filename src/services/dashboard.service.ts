import { format } from "date-fns";
import { httpClient } from "src/utils/httpClient";
import { apiRegister } from ".";
import {
  DashboardAttendanceDetails,
  DashboardEventDetails,
  DashboardHolidayDetails,
  DashboardNewJoiners,
  DashboardSeperations,
  DashboardTeamStatus,
} from "./api_definitions/dashboard.service";
import { BaseResponse } from "./api_definitions/default.service";

class DashboardService {
  getAttendanceDetails = async () => {
    // const requestDate = convertToZonedDateTime(new Date());
    const convertedDate = format(new Date(), "yyyy-MM-dd");
    const { data } = await httpClient<BaseResponse<DashboardAttendanceDetails>>(
      apiRegister.DASHBOARD.paths["get-attendance-details"],
      {
        params: {
          date: convertedDate,
        },
      },
    );

    if (data?.errors?.length > 0) {
      return null;
    }

    return data?.response;
  };

  checkIn = async (location: string) => {
    const convertedDate = format(new Date(), "yyyy-MM-dd");
    const resp = await httpClient<string>(apiRegister.DASHBOARD.paths["check-in"].replace(":location", location), {
      method: "POST",
      params: {
        date: convertedDate,
      },
    });
    return resp?.data;
  };

  checkOut = async () => {
    const convertedDate = format(new Date(), "yyyy-MM-dd");
    const resp = await httpClient<string>(apiRegister.DASHBOARD.paths["check-out"], {
      method: "POST",
      params: {
        date: convertedDate,
      },
    });
    return resp?.data;
  };

  getHolidays = async (year: string) => {
    const { data } = await httpClient<BaseResponse<DashboardHolidayDetails[]>>(
      apiRegister.DASHBOARD.paths["get-holiday-list"].replace(":year", year),
      {
        params: {
          year,
        },
      },
    );

    if (data?.errors?.length > 0) {
      return null;
    }

    return data?.response;
  };

  getEvents = async () => {
    const { data } = await httpClient<BaseResponse<DashboardEventDetails[]>>(
      apiRegister.DASHBOARD.paths["get-org-events"],
    );
    return data?.response;
  };

  getTeamStatus = async () => {
    try {
      const { data } = await httpClient<BaseResponse<DashboardTeamStatus[]>>(
        apiRegister.DASHBOARD.paths["get-team-status"],
      );

      if (data?.errors?.length > 0) {
        return [];
      }
      return data?.response;
    } catch (_err) {
      return [];
    }
  };

  getNewJoiners = async () => {
    try {
      const { data } = await httpClient<BaseResponse<DashboardNewJoiners[]>>(
        apiRegister.DASHBOARD.paths["get-new-joiners"],
      );

      if (data?.errors?.length > 0) {
        return [];
      }
      return data?.response;
    } catch (_err) {
      return [];
    }
  };

  getEmployeeSeperations = async () => {
    try {
      const { data } = await httpClient<BaseResponse<DashboardSeperations[]>>(
        apiRegister.DASHBOARD.paths["get-employee-seperations"],
      );

      if (data?.errors?.length > 0) {
        return [];
      }
      return data?.response;
    } catch (_err) {
      return [];
    }
  };
}

export default new DashboardService();
