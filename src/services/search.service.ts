import { apiRegister } from "src/services";
import { httpClient } from "src/utils/httpClient";
import { BaseResponse } from "./api_definitions/default.service";
import { OmniSearchResponse } from "./api_definitions/search.service";

class SearchServiceAPI {
  searchValue = async function (term: string) {
    try {
      const resp = await httpClient<BaseResponse<OmniSearchResponse>>(apiRegister.PROFILE.paths["get-search-terms"], {
        params: {
          term: term,
        },
      });
      if (resp.data.success === false) {
        // No search results found
        return [];
      }
      let data = resp.data.response;
      if (data) data = data.filter((a) => a.value !== undefined && a.href !== undefined && a.email != undefined);
      return data;
    } catch (_error) {
      return null;
    }
  };
}

export default new SearchServiceAPI();
