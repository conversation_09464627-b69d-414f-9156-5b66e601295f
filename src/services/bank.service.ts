import { apiRegister } from "src/services";
import { httpClient } from "src/utils/httpClient";
import { BaseResponse } from "./api_definitions/default.service";

export type IFSC_APIResponse = {
  bank: string;
  branch: string;
  address: string;
};

export type IFSCResponseKeys = "bank" | "branch" | "address";

class BankServiceAPI {
  getBankDetails = async function (term: string) {
    try {
      const resp = await httpClient<BaseResponse<IFSC_APIResponse>>(
        `${apiRegister.EMPLOYEES.paths["get-ifsc-bank-details"]}/${term}`,
      );
      if (resp.data.success === false) {
        // No search results found
        return null;
      }
      return resp.data.response;
    } catch (_error) {
      return null;
    }
  };
}

export default new BankServiceAPI();
