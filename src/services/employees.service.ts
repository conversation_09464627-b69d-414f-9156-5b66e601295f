import { BaseObject } from "src/app/global";
import { CandidateDetails } from "src/modules/Employees/utils/candidateTransformer";
import { apiRegister } from "src/services";
import {
  createPseudoLinkAndDownload,
  getFilenameFromContentDisposition,
  ValidFileExtensions,
} from "src/utils/fileUtils";
import { httpClient } from "src/utils/httpClient";
import { BaseResponse } from "./api_definitions/default.service";
import {
  EmployeeDetails,
  EmployeeOffboardingDetailsResposnse,
  EmployeeOffboardingTableResposnse,
  OffboardingSummary,
} from "./api_definitions/employees";
import employeesTransform from "./data_transformers/employees.transform";
import fileuploaderService from "./fileuploader.service";

class EmployeesServiceAPI {
  getEmployeesAPI = async () => {
    try {
      const resp = await httpClient<BaseResponse<EmployeeDetails[]>>(
        apiRegister.EMPLOYEES.paths["get-employees-details"],
      );
      if (resp.data.success === false) {
        return;
      }
      return employeesTransform.transform(resp.data.response);
    } catch (_error) {
      return null;
    }
  };

  downloadSampleImportEmployeesTemplate = async () => {
    const resp = await httpClient<Blob>(apiRegister.EMPLOYEES.paths["download-sample-template"], {
      responseType: "blob",
    });

    const [fileName, extention] = (
      getFilenameFromContentDisposition(resp?.headers["content-disposition"]) as string
    ).split(".");

    if (fileName && extention) {
      createPseudoLinkAndDownload(resp.data, `.${extention}` as ValidFileExtensions, fileName);
    }

    return [];
  };

  uploadFile = async (file: File) => {
    const formData = new FormData();
    formData.append("file", file);
    const resp = await fileuploaderService.uploadFile(
      apiRegister.EMPLOYEES.paths["upload-employees-details"],
      formData,
      undefined,
      undefined,
      true,
    );
    if (resp !== undefined && resp.type === "error") throw resp;
    else return resp;
  };

  createEmployees = async (payload: BaseObject) => {
    try {
      const endpoint = apiRegister.EMPLOYEES.paths["create-new-employee"];
      const resp = await httpClient<BaseResponse<BaseObject[]>>(endpoint, { method: "POST", data: payload });
      return resp.data.success;
    } catch (_error) {
      return null;
    }
  };

  getCandidateDetails = async (email?: string) => {
    try {
      const endpoint = apiRegister.EMPLOYEES.paths["get-candidate-details"];
      const finalEndpoint = email ? `${endpoint}?email=${email}` : endpoint;
      const resp = await httpClient<BaseResponse<CandidateDetails>>(finalEndpoint);
      return resp.data.response;
    } catch (_error) {
      return null;
    }
  };

  updateCandidateDetails = async (payload: BaseObject) => {
    try {
      const endpoint = apiRegister.EMPLOYEES.paths["update-candidate"];
      const resp = await httpClient<BaseResponse<BaseObject[]>>(endpoint, { method: "PATCH", data: payload });
      return resp.data.success;
    } catch (_error) {
      return null;
    }
  };

  draftCandidateDetails = async (payload: BaseObject) => {
    try {
      const endpoint = apiRegister.EMPLOYEES.paths["draft-candidate"];
      const resp = await httpClient<BaseResponse<BaseObject[]>>(endpoint, { method: "PATCH", data: payload });
      return resp.data.success;
    } catch (_error) {
      return null;
    }
  };

  terminateEmployee = async (payload: BaseObject) => {
    try {
      const endpoint = apiRegister.EMPLOYEES.paths["terminate-employee"];
      await httpClient<BaseResponse<BaseObject[]>>(endpoint, { method: "POST", data: payload });
      return true;
    } catch (_error) {
      return null;
    }
  };

  getEmployeeData = async (employeeCode: string) => {
    try {
      const endpoint = apiRegister.EMPLOYEES.paths["get-employee-data"];
      const resp = await httpClient<BaseResponse<CandidateDetails>>(`${endpoint}?code=${employeeCode}`);
      return resp.data.response;
    } catch (_error) {
      return null;
    }
  };

  updateEmployeeData = async (payload: BaseObject) => {
    try {
      const endpoint = apiRegister.EMPLOYEES.paths["update-employee-data"];
      const resp = await httpClient<BaseResponse<BaseObject[]>>(endpoint, { method: "PATCH", data: payload });
      return resp.data.success;
    } catch (_error) {
      return null;
    }
  };

  getEmployeeOffboardingDetails = async (employeeId?: string): Promise<EmployeeOffboardingDetailsResposnse | null> => {
    try {
      const endpoint = apiRegister.EMPLOYEES.paths["offboarding-details"];
      const finalEndpoint = employeeId ? `${endpoint}?employee_code=${employeeId}` : endpoint;
      const resp = await httpClient<BaseResponse<EmployeeOffboardingDetailsResposnse>>(finalEndpoint);
      return resp.data.response;
    } catch (_error) {
      return null;
    }
  };

  initiateOffboarding = async (payload: BaseObject) => {
    try {
      const endpoint = apiRegister.EMPLOYEES.paths["initiate-offboarding"];
      const resp = await httpClient<BaseResponse<BaseObject[]>>(endpoint, { method: "POST", data: payload });
      return resp.data.success;
    } catch (_error) {
      return null;
    }
  };

  getAllEmployeeOffboardingDetails = async (
    isHRAdminView?: boolean,
  ): Promise<EmployeeOffboardingTableResposnse[] | null> => {
    try {
      const endpoint = apiRegister.EMPLOYEES.paths["offboarding-requests"];
      const finalEndpoint = isHRAdminView ? `${endpoint}/HRBP` : `${endpoint}/Manager`;
      const resp = await httpClient<BaseResponse<EmployeeOffboardingTableResposnse[]>>(finalEndpoint);
      return resp.data.response;
    } catch (_error) {
      return null;
    }
  };

  getOffboardingSummary = async (): Promise<OffboardingSummary | null> => {
    try {
      const endpoint = apiRegister.EMPLOYEES.paths["offboarding-overview"];
      const resp = await httpClient<BaseResponse<OffboardingSummary>>(endpoint);
      return resp.data.response;
    } catch (_error) {
      return null;
    }
  };

  approveEmployeeOffboarding = async (payload: BaseObject) => {
    try {
      const endpoint = apiRegister.EMPLOYEES.paths["approve-offboarding"];
      const resp = await httpClient<BaseResponse<BaseObject[]>>(endpoint, { method: "PUT", data: payload });
      return resp.data.success;
    } catch (_error) {
      return null;
    }
  };

  editEmployeeOffboarding = async (payload: BaseObject) => {
    try {
      const endpoint = apiRegister.EMPLOYEES.paths["edit-offboarding"];
      const resp = await httpClient<BaseResponse<BaseObject[]>>(endpoint, { method: "PUT", data: payload });
      return resp.data.success;
    } catch (_error) {
      return null;
    }
  };

  rescindEmployeeOffboarding = async () => {
    try {
      const endpoint = apiRegister.EMPLOYEES.paths["rescind-offboarding"];
      const resp = await httpClient<BaseResponse<BaseObject[]>>(endpoint, {
        method: "PUT",
      });
      return resp.data.success;
    } catch (_error) {
      return null;
    }
  };

  revokeEmployeeOffboarding = async (employeeId: string, comment: string) => {
    try {
      const endpoint = apiRegister.EMPLOYEES.paths["revoke-offboarding"];
      const resp = await httpClient<BaseResponse<BaseObject[]>>(endpoint, {
        method: "PUT",
        data: { comments: comment, employee_code: employeeId },
      });
      return resp.data.success;
    } catch (_error) {
      return null;
    }
  };

  updateDateOfConfirmation = async (employeeId: string, dateOfConfirmation: string) => {
    try {
      const endpoint = apiRegister.EMPLOYEES.paths["change-date-of-confirmation"].replace(":employeeId", employeeId);
      const resp = await httpClient<BaseResponse<string>>(endpoint, {
        method: "PATCH",
        data: {
          date_of_confirmation: dateOfConfirmation,
        },
      });

      if (resp.data.errors?.length > 0) {
        throw new Error("Cannot update date of confirmation");
      }
      return resp.data.response;
    } catch (_error) {
      throw new Error("Cannot update date of confirmation");
    }
  };

  exportEmployees = async (payload?: BaseObject) => {
    const isPayloadEmpty = Object.values(payload || {}).every((val) => !val);
    const requestPayload = isPayloadEmpty
      ? undefined
      : Object.keys(payload || {}).reduce((acc, key) => {
          if (key && payload?.[key]) {
            acc = {
              ...acc,
              [key]: payload[key],
            };
          }
          return acc;
        }, {});

    try {
      const endpoint = apiRegister.EMPLOYEES.paths["export-employees"];
      const resp = await httpClient<Blob>(endpoint, {
        method: "GET",
        responseType: "blob",
        params: !isPayloadEmpty ? { ...requestPayload } : undefined,
      });
      const [fileName, extention] = (
        getFilenameFromContentDisposition(resp?.headers["content-disposition"]) as string
      ).split(".");

      if (fileName && extention) {
        createPseudoLinkAndDownload(resp.data, `.${extention}` as ValidFileExtensions, fileName);
      }
      return [];
    } catch (_error) {
      return null;
    }
  };
}

export default new EmployeesServiceAPI();
