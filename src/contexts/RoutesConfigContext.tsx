import React, { createContext, useContext } from "react";
import routingConfig from "src/configs/routes.config";

const RouteConfigContext = createContext<typeof routingConfig | null>(null);

export const RouteConfigProvider: React.FC<{ routesConfig: typeof routingConfig; children: React.JSX.Element }> = ({
  routesConfig,
  children,
}) => {
  return <RouteConfigContext.Provider value={routesConfig}>{children}</RouteConfigContext.Provider>;
};

export const useRouteConfig = () => {
  return useContext(RouteConfigContext);
};
