# ui-effi-hr

## Tech Stack

- **Rendering type**: CSR (Client Side Rendered)
- **Components library**: MUI
- **Tanstack**
- **CSS styles**: styled-components
- **Bundler**: Webpack
- **Transformer**: Babel
- **Type-check support**: Typescript
- **Store Management**: React Query
- **HTTP Calls**: Axios, React Query
- **Environment Vars configuration**: react-scripts

## Prerequisites

Before you begin, ensure you have the following prerequisites:

- Node version 16+
- Docker
- Docker Compose
- Docker Desktop (optional)
- Nginx (optional)

## How to Run the Project

### Using Node

```sh
npm install
npm start
```

### Using Docker

```sh
docker build -t client/effi-hr .
docker run -p 8080:8080 client/effi-hr
```
### Using Docker Compose

```sh
docker-compose build
docker-compose up -d effi-hr
```

### The project is now hosted at localhost:8080 on your local machine.

**Enjoy!**
