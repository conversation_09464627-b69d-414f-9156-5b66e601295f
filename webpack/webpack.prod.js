const path = require('path');
const HtmlWebpackPlugin = require('html-webpack-plugin');
const webpack = require('webpack');
const Dotenv = require('dotenv-webpack'); // Import dotenv-webpack
// const BundleAnalyzerPlugin = require('webpack-bundle-analyzer').BundleAnalyzerPlugin;
const TerserPlugin = require("terser-webpack-plugin");



const MiniCssExtractPlugin = require('mini-css-extract-plugin');

const isProduction = process.env.NODE_ENV === 'production' || process.env.NODE_ENV === "uat";

const getEnvFilename = () => {
  switch (process.env.NODE_ENV) {
    case 'production': return '.env.prod';
    case 'uat': return '.env.uat';
    case 'development': return '.env.dev';
    default: return '.env.local';
  }
}

console.log({
  dir: __dirname,
  cwd: process.cwd(),
  distPath: path.resolve(process.cwd(), 'dist'),
  env: process.env.NODE_ENV,
  isProduction,
});

const stylesHandler = isProduction ? MiniCssExtractPlugin.loader : 'style-loader';

const getRules = () => {
  return [
    {
      test: /\.(ts|tsx)$/i,
      loader: 'ts-loader',
      options: {
        transpileOnly: true,
      },
      exclude: ['/node_modules/'],
    },
    {
      test: /\.css$/i,
      use: [stylesHandler, 'css-loader'],
    },
    {
      test: /\.(eot|ttf|woff|woff2|png|jpg|jpeg|gif)$/i,
      type: 'asset/resource',
    },
    {
      test: /\.svg$/i,
      type: 'asset/resource',
    },
    {
      test: /\.(js|jsx)$/,
      exclude: /node_modules/,
      use: {
        loader: 'babel-loader',
        options: {
          presets: ['@babel/preset-react'],
        },
      },
    },
    {
      test: /\.html$/,
      use: ['html-loader'], // You can use 'html-loader' to load HTML files
    },
  ];
};

module.exports = {
  mode: isProduction ? 'production' : 'development',
  entry: {
    main: './src/index.js',
  },
  output: {
    path: path.resolve(process.cwd(), 'dist/ui-effi-hr'),
    filename: '[name].[fullhash].effihr.bundle.js',
    publicPath: '/',
    pathinfo: false,
  },
  module: {
    rules: getRules(),
  },
  plugins: [
    new webpack.ProgressPlugin(),
    new HtmlWebpackPlugin({
      template: './public/index.html',
    }),
    new MiniCssExtractPlugin(),
    new Dotenv({
      path: getEnvFilename(), // Specify the .env file based on the environment
    }), // Include the Dotenv plugin
    // new BundleAnalyzerPlugin(),
  ],
  devServer: {
    historyApiFallback: true,
    allowedHosts: 'all',
  },
    performance: {
      hints: false,
      maxEntrypointSize: 512000,
      maxAssetSize: 512000
  },
  optimization: {
    minimize: true,
    minimizer: isProduction ? [new TerserPlugin()] : [],
    splitChunks: {
      chunks: 'all',
      maxInitialRequests: Infinity,
      minSize: 0,
      cacheGroups: {
        vendor: {
          test: /[\\/]node_modules[\\/]/,
          name(module) {
            // get the name. E.g. node_modules/packageName/not/this/part.js
            // or node_modules/packageName
            const packageName = module.context.match(/[\\/]node_modules[\\/](.*?)([\\/]|$)/)?.[1];
            
            if (!packageName) {
              return null;
            }
            return `vendor.${packageName.replace('@', '')}`;
          },
        },
      },
    },
  },
  resolve: {
    extensions: ['.js', '.jsx', '.ts', '.tsx'],
    alias: {
      src: path.resolve(process.cwd(), 'src'),
      modules: path.resolve(process.cwd(), 'src/modules'),
      assets: path.resolve(process.cwd(), 'src/assets'),
      utils: path.resolve(process.cwd(), 'src/utils'),
      packageJSON: path.resolve(process.cwd(), 'package.json'),
      //Add rest of the alises here
    },
  },
};
