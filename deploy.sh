#!/bin/bash

# Step 1: Run npm build
echo "Step 1: Running npm build..."
npm run build:DEV

# Check if npm build succeeded
if [ $? -ne 0 ]; then
    echo "Error: npm build failed."
    exit 1
fi

# Step 2: Navigate to the build directory
echo "Step 2: Navigating to the build directory..."
cd dist

# Step 3: Prepare .well-known and service worker
echo "Step 3: Preparing additional files..."
sudo mkdir -p ui-effi-hr/.well-known
sudo cp -r ../public/assetlinks.json ui-effi-hr/.well-known/
sudo cp -r ../public/firebase-messaging-sw.js ui-effi-hr/

# Step 4.5: Clear old build from nginx directory
echo "Step 4.5: Clearing old build from nginx directory..."
sudo rm -rf /var/www/html/ui-effi-hr

# Step 5: Copy new build to nginx directory
echo "Step 5: Copying new build to nginx directory..."
sudo cp -r ui-effi-hr /var/www/html

# Check if copy succeeded
if [ $? -ne 0 ]; then
    echo "Error: Copying files failed."
    exit 1
fi

# Step 6: Reload nginx
echo "Step 6: Reloading nginx..."
sudo nginx -s reload

# Check if nginx reload succeeded
if [ $? -ne 0 ]; then
    echo "Error: Reloading nginx failed."
    exit 1
fi

echo "Deployment successful."
exit 0
