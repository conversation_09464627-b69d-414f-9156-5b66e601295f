events {
  worker_connections 1024;
}
http {
  server_tokens off;
  server {
    listen 8081;
    root /var/www/html/ui-effi-hr/;
    include mime.types;
    default_type application/octet-stream;
    # Security Headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    add_header Cache-Control "no-store, no-cache, must-revalidate, max-age=0" always;
		add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-eval' https://www.gstatic.com https://maps.googleapis.com https://maps.gstatic.com;  style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https: https://maps.gstatic.com https://maps.googleapis.com; connect-src 'self' https: http: ws: wss: https://maps.googleapis.com https://maps.gstatic.com; frame-src 'self' data: https://*.amazonaws.com https://s3.ap-south-1.amazonaws.com; object-src 'none'; base-uri 'self'; form-action 'self';" always;
    #gzip compression
    gzip on;
    gzip_buffers 16 8k;
    gzip_comp_level 4;
    gzip_http_version 1.0;
    gzip_min_length 1280;
    gzip_types text/plain text/css application/x-javascript text/xml application/xml application/xml+rss text/javascript image/x-icon image/bmp image/svg+xml;
    gzip_vary on;
    location / {
      gzip_static on;
      root /var/www/html/ui-effi-hr/;
      try_files $uri $uri/ /index.html;
    }
    # Serve assetlinks.json for Android App Linking
    location /.well-known/assetlinks.json {
      root /var/www/html/ui-effi-hr; # Location where your assetlinks.json is stored
      default_type application/json;
    }
  }
}