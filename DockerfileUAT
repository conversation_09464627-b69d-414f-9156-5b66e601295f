# Stage 1: Build the Node.js application
FROM node:18-alpine as build

LABEL author="https://github.com/anarchymonkey"

WORKDIR /app

# Copy package.json and package-lock.json
COPY package.json package-lock.json ./

# Install dependencies
RUN npm install

# Copy the rest of the application code
COPY . .

# Set environment variables
ENV NODE_ENV=uat

# Build the application
RUN npm run build:UAT

COPY public/assetLinks.json /app/dist/ui-effi-hr/.well-known/assetlinks.json

# Stage 2: Nginx server
FROM nginx:alpine

# Copy the Nginx configuration file
COPY nginx.conf /etc/nginx/nginx.conf

# Remove the default Nginx web server content and add the built application
RUN rm -rf /usr/share/nginx/html/*

# Copy built files from the build stage
COPY --from=build /app/dist/ui-effi-hr/ /var/www/html/ui-effi-hr/

# Expose ports
EXPOSE 80 8081

# Start Nginx
CMD ["nginx", "-g", "daemon off;"]
